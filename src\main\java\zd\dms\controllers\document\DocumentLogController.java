package zd.dms.controllers.document;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDDateUtils;
import zd.dms.dto.PageResponse;
import zd.dms.entities.Role;
import zd.dms.services.document.DocumentService;

import java.text.ParseException;
import java.util.Date;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "DocumentLogController", description = "文档审计Controller")
@RequestMapping("/document/docLog")
public class DocumentLogController extends ControllerSupport {

    private final DocumentService documentService;

    @Operation(summary = "文档审计列表")
    @PostMapping("/getDocLogsPage")
    @ZDLog("文档审计列表")
    public JSONResultUtils<Object> getDocLogsPage(@RequestBody Map<String, Object> params) {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);

        int pageNumber = MapUtils.getIntValue(params,"pageNumber",1);
        int pageSize = MapUtils.getIntValue(params,"pageSize",10);
        int type = MapUtils.getIntValue(params,"type",0);
        String startDateStr = MapUtils.getString(params,"startDate");
        String endDateStr = MapUtils.getString(params,"endDate");
        String operator = MapUtils.getString(params,"operator");

        Date startDate=null;
        Date endDate=null;
        if(StringUtils.isNotBlank(startDateStr)){
            startDate= ZDDateUtils.parseDate(startDateStr);
        }
        if(StringUtils.isNotBlank(endDateStr)){
            endDate= ZDDateUtils.parseDate(endDateStr);
        }
        Page page = documentService.getDocumentLog(pageNumber,pageSize,type,operator,startDate,endDate);

        return successData(PageResponse.of(page));
    }
}
