package zd.dms.repositories.lendrequest.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.LendRequest;
import zd.dms.repositories.lendrequest.LendRequestRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;

public class LendRequestRepositoryDaoImpl extends BaseRepositoryDaoImpl<LendRequest, Long> implements LendRequestRepository {

    public LendRequestRepositoryDaoImpl(Class<LendRequest> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public Page getRequestsByCreator(int pageNumber, int pageSize, String creator) {
        Specification<LendRequest> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<LendRequest> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("creator", creator);
            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }

    @Override
    public Page getApprovedRequestsByUsername(int pageNumber, int pageSize, String approver) {
        Specification<LendRequest> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<LendRequest> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("approvedByUsername", approver);
            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }

    @Override
    public List<LendRequest> getUnapprovedRequests(String username) {
        Specification<LendRequest> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<LendRequest> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("approved", false);
            specTools.like("approverUsernames", "%**" + username + "**%");
            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public int getUnapprovedCount(String username) {
        Specification<LendRequest> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<LendRequest> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("approved", false);
            specTools.like("approverUsernames", "%**" + username + "**%");
            specTools.desc("id");

            return specTools.getRestriction();
        };

        return (int) count(spec);
    }
}
