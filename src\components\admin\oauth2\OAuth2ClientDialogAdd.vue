<script setup lang="ts">
import {useDialogPluginComponent, useQuasar} from 'quasar';
import {oauth2ClientApis} from 'utils/api/admin/oauth2/oauth2-client-apis';
import {ref} from 'vue';

defineEmits([...useDialogPluginComponent.emits]);

const {dialogRef, onDialogOK, onDialogHide, onDialogCancel} =
  useDialogPluginComponent();

const $q = useQuasar();

const clientName = ref(null);
const clientId = ref(null);
const clientSecret = ref(null);
const enabled = ref(true);
const numIndex = ref(999);

// 保存按钮点击事件处理函数
const onSaveClick = async () => {
  const params = {
    clientName: clientName.value,
    clientId: clientId.value,
    clientSecret: clientSecret.value,
    enabled: enabled.value,
    numIndex: numIndex.value,
  };

  try {
    await oauth2ClientApis.save(params);
  } catch (error: any) {
    return;
  }

  onDialogOK();
};
</script>

<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card class="dialog-card" style="background-color: #F7F7F7;">
      <q-card-section>
        <span class="text-h6">新增OAuth2</span>
      </q-card-section>

      <q-card-section class="overflow-auto" style="max-height: calc(100vh - 190px)">
        <q-card class="full-width border-none box-shadow-none" style="background-color: #F7F7F7;">
          <q-card-section class="q-px-none q-pt-none">
            <q-card class="full-width">
              <q-card-section class="q-pb-none">
                <div class="row justify-evenly">
                  <q-input class="col-5" v-model="clientName" bottom-slots outlined dense>
                    <template #before>
                      <div class="q-input__label">
                        <span class="text-red-10">*&nbsp;</span>
                        <span>名称：</span>
                      </div>
                    </template>
                  </q-input>

                  <div class="col-5">
                    <div class="row items-center">
                      <div class="text-align-end zdInputLabel" style="width: 112px;">是否启动：</div>
                      <div class="col-grow">
                        <q-select 
                          v-model="enabled" 
                          :options="[{label: '启用', value: true}, {label: '禁用', value: false}]" 
                          emit-value
                          map-options
                          outlined 
                          hide-bottom-space
                          dense/>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="row justify-evenly">
                  <q-input class="col-5" v-model="clientId" bottom-slots outlined dense>
                    <template #before>
                      <div class="q-input__label">
                        <span class="text-red-10">*&nbsp;</span>
                        <span>client_id：</span>
                      </div>
                    </template>
                  </q-input>
                  <q-input class="col-5" v-model="clientSecret" bottom-slots outlined dense>
                    <template #before>
                      <div class="q-input__label">
                        <span class="text-red-10">*&nbsp;</span>
                        <span>client_secret：</span>
                      </div>
                    </template>
                  </q-input>
                </div>
              </q-card-section>
            </q-card>
          </q-card-section>
        </q-card>
      </q-card-section>

      <q-card-actions align="right" class="q-pa-md">
        <q-btn label="确认" unelevated color="ng-theme" @click="onSaveClick"/>
        <q-btn label="取消" unelevated class="text-main-btn-cancel-theme" color="main-btn-cancel-theme"
               @click="onDialogCancel"/>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<style scoped lang="scss">
.dialog-card {
  background-color: #F7F7F7;
  width: 800px;
  max-width: 100vw;
}
</style>
