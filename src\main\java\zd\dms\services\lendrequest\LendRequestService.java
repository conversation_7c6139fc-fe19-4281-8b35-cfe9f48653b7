package zd.dms.services.lendrequest;

import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.dms.entities.LendRequest;

import java.util.List;

@Transactional
public interface LendRequestService extends BaseJpaService<LendRequest, Long> {

    int getUnapprovedCount(String username);

    LendRequest getById(long id);

    void create(LendRequest lendRequest);

    void delete(LendRequest lendRequest);

    void deleteById(long id);

    void update(LendRequest lendRequest);

    @Transactional(readOnly = true)
    Page getRequestsByCreator(int pageNumber, int pageSize, String creator);

    @Transactional(readOnly = true)
    Page getApprovedRequestsByUsername(int pageNumber, int pageSize,
                                       String approver);

    @Transactional(readOnly = true)
    List<LendRequest> getUnapprovedRequests(String username);
}
