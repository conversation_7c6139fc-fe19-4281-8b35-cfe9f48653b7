package zd.dms.entities;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "tdms_id")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class DmsId implements Serializable {

	private static final Logger log = LoggerFactory.getLogger(DmsId.class);

	/**
	 * serial
	 */
	private static final long serialVersionUID = 1L;

	@Id
	private String idType;

	private long idValue;

	public DmsId() {
		super();
	}

	public String getIdType() {
		return idType;
	}

	public void setIdType(String filename) {
		this.idType = filename;
	}

	public long getIdValue() {
		return idValue;
	}

	public void setIdValue(long idValue) {
		this.idValue = idValue;
	}
}
