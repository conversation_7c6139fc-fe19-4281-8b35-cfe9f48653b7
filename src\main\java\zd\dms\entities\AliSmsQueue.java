package zd.dms.entities;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractEntity;

@Entity
@Table(name = "aliSmsQueue")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class AliSmsQueue extends AbstractEntity {

    /**
     *
     */
    private static final long serialVersionUID = -4840596415615765027L;

    public static final int UNSENDED = 0;

    public static final int SENDED = 1;

    public static final int SENDING = 2;

    public static final int EXCEPTION = 3;

    private String templateCode;

    private String signName;

    private String phoneNumber;

    private String templateParam;

    private String outId;

    private int sendStatus;

    private String creatorFullname;

    @Column(name = "groups1")
    private String groups;

    private String ip;

    private int sendCount;

    private int retryCount;

    // 创建时间
    @Index(name = "i_aliSmsQueue_cd")
    @Column(nullable = false)
    private Date creationDate;

    public AliSmsQueue() {
        this.creationDate = new Date();
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getSignName() {
        return signName;
    }

    public void setSignName(String signName) {
        this.signName = signName;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getTemplateParam() {
        return templateParam;
    }

    public void setTemplateParam(String templateParam) {
        this.templateParam = templateParam;
    }

    public String getOutId() {
        return outId;
    }

    public void setOutId(String outId) {
        this.outId = outId;
    }

    public int getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(int sendStatus) {
        this.sendStatus = sendStatus;
    }

    public String getCreatorFullname() {
        return creatorFullname;
    }

    public void setCreatorFullname(String creatorFullname) {
        this.creatorFullname = creatorFullname;
    }

    public String getGroups() {
        return groups;
    }

    public void setGroups(String groups) {
        this.groups = groups;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public int getSendCount() {
        return sendCount;
    }

    public void setSendCount(int sendCount) {
        this.sendCount = sendCount;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
