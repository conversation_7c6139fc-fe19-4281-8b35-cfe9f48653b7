package zd.dms.services.document.impl;

import com.itextpdf.text.pdf.PdfReader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.archivers.zip.Zip64Mode;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.lucene.analysus.ja.dict.TaskDictionary;
import org.springframework.stereotype.Service;
import zd.base.context.UserContextHolder;
import zd.base.utils.ZDUtils;
import zd.dms.entities.*;
import zd.dms.io.ZDIOUtils;
import zd.dms.services.document.DocumentService;
import zd.dms.services.document.FolderService;
import zd.dms.services.document.FolderUtils;
import zd.dms.services.document.ImportExportService;
import zd.dms.services.pdf.PDFTextWaterMarkUtils;
import zd.dms.services.security.FolderPermissionUtils;
import zd.dms.utils.*;
import zd.record.entities.RecFolder;
import zd.record.service.folder.RecFolderService;
import zd.record.utils.PDFConverterUtils;
import zd.record.utils.RecordDBUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.io.*;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.zip.Deflater;

import static zd.dms.utils.SettingsUtils.scm;

@RequiredArgsConstructor
@Service
@Slf4j
public class ImportExportServiceImpl implements ImportExportService {

    private final DocumentService documentService;

    private final RecFolderService recFolderService;

    private final FolderService folderService;

    public void batchDownloadZipRecordAttachment(File zipFile, String[] docIds, String encoding, User operator, String ip, RecFolder recFolder) throws IOException {
        batchDownloadZipRecordAttachment(zipFile, docIds, null, encoding, operator, ip, recFolder);
    }

    @Override
    public void batchDownloadZipRecordAttachment(File zipFile, String[] docIds, Map<String, String> docsPrefix,
                                                 String encoding, User operator, String ip, RecFolder recFolder) throws IOException {

        log.debug("batchDownloadZipDocs docsPrefix: {}", docsPrefix);

        ZipArchiveOutputStream zaos = null;

        try {
            zaos = new ZipArchiveOutputStream(zipFile);
            zaos.setEncoding(encoding);
            zaos.setUseZip64(Zip64Mode.AsNeeded);
            zaos.setMethod(ZipArchiveOutputStream.STORED);
            zaos.setLevel(Deflater.NO_COMPRESSION);

            // 将文档加入zip文件
            for (String docId : docIds) {
                docId = docId.trim();
                if (StringUtils.isBlank(docId)) {
                    continue;
                }

                Document d = documentService.getDocumentById(docId);

                // 后台设置启动加密(水印加密)
                boolean systemStartDownloadWatermarkWithPWD = scm
                        .getBooleanProperty("startWaterMarkPWDDownLoad", false);
                String systemwaterMarkPWD = scm.getProperty("waterMarkPWD");
                // 目录启动加密(水印加密)
                String downLoadWatermarkWithPWD = FolderUtils.getPropertyFromAllLevel(recFolder,
                        "downLoadWatermarkWithPWD");
                String watermarkPWD = FolderUtils.getPropertyFromAllLevel(recFolder, "watermarkPWD");

                File docFile = ZDIOUtils.getNewestDocFile(d);
                if (docFile != null && docFile.exists()) {
                    String filename = d.getFilename();

                    if (docsPrefix != null && !docsPrefix.isEmpty()) {
                        String prefix = docsPrefix.get(d.getId());
                        if (StringUtils.isNotBlank(prefix)) {
                            filename = prefix + filename;
                        }
                    }

                    // 处理下载文件名
                    String docFilename = DocDownloadUtils.getDocFilenameWithSuffix(filename, d.getDocVersion(), null);

                    ZipArchiveEntry zipArchiveEntry = new ZipArchiveEntry(docFilename);
                    log.debug("zipDocs加入文档: {}", docFilename);
                    zaos.putArchiveEntry(zipArchiveEntry);

                    InputStream is = null;
                    if (d.isPdf()) {
                        File oldFile = ZDFileUtils.getTempFile(d, null);
                        // 水印加密设置
                        if ("true".equals(downLoadWatermarkWithPWD)) {
                            if (!systemStartDownloadWatermarkWithPWD) {
                                log.debug("后台PDF加密下载未启动");
                                continue;
                            }

                            // 使用目录设置
                            if (StringUtils.isBlank(watermarkPWD)) {
                                log.debug("PDF水印密码为空");
                                continue;
                            }
                            File file = null;
                            try {
                                file = PDFUtils.EncryptionPDF(watermarkPWD, oldFile, d, operator);
                            } catch (Exception e) {
                                log.debug("调用PDF水印加密异常:{}", e);
                            }
                            is = new FileInputStream(file);

                        } else if ("defaultSet".equals(downLoadWatermarkWithPWD)) {
                            if (!systemStartDownloadWatermarkWithPWD) {
                                log.debug("defaultSet后台PDF加密下载未启动");
                                continue;
                            }

                            // 使用系统设置
                            if (StringUtils.isBlank(systemwaterMarkPWD)) {
                                log.debug("系统PDF水印密码为空");
                                continue;
                            }
                            File file = null;
                            try {
                                file = PDFUtils.EncryptionPDF(systemwaterMarkPWD, oldFile, d, operator);
                            } catch (Exception e) {
                                log.debug("调用PDF水印加密异常:{}", e);
                            }
                            is = new FileInputStream(file);

                        } else {
                            is = ZDIOUtils.getDocInputStream(d, d.getNewestVersion());
                        }
                    } else if (d.isWord07() && ZDFileUtils.isNeedAddHeaderAndFooterImage(d, d.getNewestVersion(), "")) {
                        File imagedFile = ZDFileUtils.addHeaderAndFooterImageForDocx(d, d.getNewestVersion(), "");
                        if (imagedFile != null) {
                            is = new FileInputStream(imagedFile);
                        } else {
                            is = ZDIOUtils.getDocInputStream(d, d.getNewestVersion());
                        }
                    } else {
                        is = ZDIOUtils.getDocInputStream(d, d.getNewestVersion());
                    }
                    /*}*/

                    IOUtils.copyLarge(is, zaos);
                    IOUtils.closeQuietly(is);
                    zaos.closeArchiveEntry();

                    documentService.addLog(d, DocumentLog.TYPE_DOWNLOAD, "批量下载文档: " + d.getFilename(),
                            operator.getUsername(), operator.getFullname(), ip);
                }
            }
        } finally {
            try {
                if (zaos != null) {
                    zaos.close();
                }
            } catch (IOException e) {
                log.error("batchDownloadZipDocs ex", e);
            }
        }
    }

    @Override
    public void batchDownloadZipRecordAttachmentIncludesSubRecord(File zipExportFile, String[] docIds, Map<String, String> docsPrefix, String string, User operator, String ipAddress) {
        log.debug("batchDownloadZipDocs docsPrefix: {}", docsPrefix);
        // 临时文件的目录
        String tempDirPath = ZDUtils.getTempDir().getAbsolutePath() + "/allAttachments";
        File tempDoloadDir = new File(tempDirPath);
        if (tempDoloadDir.isDirectory() && tempDoloadDir.exists()) {
            FileUtils.deleteQuietly(tempDoloadDir);
        }

        Map<Long, String> folderIdsAndPath = new LinkedHashMap<Long, String>();

        for (String docId : docIds) {
            docId = docId.trim();
            if (StringUtils.isBlank(docId)) {
                continue;
            }

            Document document = documentService.getDocumentById(docId);

            // 根据ID和表名查数据
            String attachRecTableName = document.getAttachRecTableName();
            long attachRecId = document.getAttachRecId();
            if (StringUtils.isBlank(attachRecTableName) || attachRecId <= 0) {
                continue;
            }
            log.debug("recordIdAndTable -> {}:{}", attachRecTableName, attachRecId);
            Map<String, Object> record = RecordDBUtils.getRecordByTableName(attachRecId, null, attachRecTableName);
            Long folderId = MapUtils.getLong(record, "目录ID");
            RecFolder recFolder = recFolderService.getById(folderId);

            // 处理附件所属数据的目录并存入Map，目录形式为ID:/patentPath/thisPath
            RecFolder parent = recFolder.getParent();
            long parentId = 0L;
            if (parent != null) {
                parentId = parent.getId();
            }
            String parentPath = MapUtils.getString(folderIdsAndPath, parentId, "");
            String folderName = recFolder.getName();
            String folderPath = parentPath + "/" + folderName;
            folderIdsAndPath.put(folderId, folderPath);
            String folderAbsolutePath = tempDirPath + folderPath;
            File fileDirectory = new File(folderAbsolutePath);
            if (!fileDirectory.exists()) {
                fileDirectory.mkdirs();
            }

            // 后台设置启动加密(水印加密)
            boolean systemStartDownloadWatermarkWithPWD = scm.getBooleanProperty("startWaterMarkPWDDownLoad", false);
            String systemwaterMarkPWD = scm.getProperty("waterMarkPWD");
            // 目录启动加密(水印加密)
            String downLoadWatermarkWithPWD = FolderUtils
                    .getPropertyFromAllLevel(recFolder, "downLoadWatermarkWithPWD");
            String watermarkPWD = FolderUtils.getPropertyFromAllLevel(recFolder, "watermarkPWD");

            File docFile = ZDIOUtils.getNewestDocFile(document);
            if (docFile != null && docFile.exists()) {
                String filename = document.getFilename();

                if (docsPrefix != null && !docsPrefix.isEmpty()) {
                    String prefix = docsPrefix.get(document.getId());
                    if (StringUtils.isNotBlank(prefix)) {
                        filename = prefix + filename;
                    }
                }

                InputStream is = null;
                OutputStream outputStream = null;
                try {
                    if (document.isPdf()) {
                        File oldFile = ZDFileUtils.getTempFile(document, null);
                        // 水印加密设置
                        if ("true".equals(downLoadWatermarkWithPWD)) {
                            if (!systemStartDownloadWatermarkWithPWD) {
                                log.debug("后台PDF加密下载未启动");
                                continue;
                            }

                            // 使用目录设置
                            if (StringUtils.isBlank(watermarkPWD)) {
                                log.debug("PDF水印密码为空");
                                continue;
                            }
                            File file = null;
                            try {
                                file = PDFUtils.EncryptionPDF(watermarkPWD, oldFile, document, operator);
                                is = new FileInputStream(file);
                            } catch (Exception e) {
                                log.error("调用PDF水印加密异常:{}", e);
                            }

                        } else if ("defaultSet".equals(downLoadWatermarkWithPWD)) {
                            if (!systemStartDownloadWatermarkWithPWD) {
                                log.debug("defaultSet后台PDF加密下载未启动");
                                continue;
                            }

                            // 使用系统设置
                            if (StringUtils.isBlank(systemwaterMarkPWD)) {
                                log.debug("系统PDF水印密码为空");
                                continue;
                            }
                            File file = null;
                            try {
                                file = PDFUtils.EncryptionPDF(systemwaterMarkPWD, oldFile, document, operator);
                                is = new FileInputStream(file);
                            } catch (Exception e) {
                                log.error("调用PDF水印加密异常:{}", e);
                            }

                        } else {
                            is = ZDIOUtils.getDocInputStream(document, document.getNewestVersion());
                        }
                    } else if (document.isWord07() &&
                            ZDFileUtils.isNeedAddHeaderAndFooterImage(document, document.getNewestVersion(), "")) {
                        File imagedFile = ZDFileUtils.addHeaderAndFooterImageForDocx(document,
                                document.getNewestVersion(), "");
                        if (imagedFile != null) {
                            is = new FileInputStream(imagedFile);
                        } else {
                            is = ZDIOUtils.getDocInputStream(document, document.getNewestVersion());
                        }
                    } else {
                        is = ZDIOUtils.getDocInputStream(document, document.getNewestVersion());
                    }

                    // 进行文件复制到指定目录
                    String filePath = folderAbsolutePath + "/" + filename;
                    log.debug("filePath-> {}", filePath);
                    outputStream = new FileOutputStream(new File(filePath));
                    IOUtils.copyLarge(is, outputStream);
                } catch (Exception e) {
                    log.error("批量下载 FileNotFoundException: {}", e);
                } finally {
                    IOUtils.closeQuietly(outputStream);
                    IOUtils.closeQuietly(is);
                }

                documentService.addLog(document, DocumentLog.TYPE_DOWNLOAD, "批量下载文档: " + document.getFilename(),
                        operator.getUsername(), operator.getFullname(), ipAddress);
            }
        }

        try {
            ZipUtils.zip(new File(tempDirPath), zipExportFile, "GBK");
        } catch (IOException e) {
            log.error("压缩文件夹出错", e);
        }
    }

    public void batchDownloadZipDocs(File zipFile, String[] docIds, Map<String, String> docsPrefix,
                                     User operator, String ip) throws IOException {

        log.debug("batchDownloadZipDocs docsPrefix: {}", docsPrefix);

        ZipArchiveOutputStream zaos = null;

        try {
            zaos = new ZipArchiveOutputStream(zipFile);
            zaos.setEncoding("GBK");
            zaos.setUseZip64(Zip64Mode.AsNeeded);
            zaos.setMethod(ZipArchiveOutputStream.STORED);
            zaos.setLevel(Deflater.NO_COMPRESSION);

            // 将文档加入zip文件
            for (String docId : docIds) {
                docId = docId.trim();
                if (StringUtils.isBlank(docId)) {
                    continue;
                }

                Document d = documentService.getDocumentById(docId);
                if (d == null || d.isDeleted()) {
                    continue;
                }

                // 后台设置启动加密(水印加密)
                boolean systemStartDownloadWatermarkWithPWD = scm
                        .getBooleanProperty("startWaterMarkPWDDownLoad", false);
                String systemwaterMarkPWD = scm.getProperty("waterMarkPWD");
                // 目录启动加密(水印加密)
                String downLoadWatermarkWithPWD = FolderUtils.getPropertyFromAllLevel(d.getFolder(),
                        "downLoadWatermarkWithPWD");
                String watermarkPWD = FolderUtils.getPropertyFromAllLevel(d.getFolder(), "watermarkPWD");

                Document document = documentService.getDocumentById(docId.trim());

                File docFile = ZDIOUtils.getNewestDocFile(d);
                if (docFile != null && docFile.exists()) {
                    String filename = d.getFilename();

                    if (docsPrefix != null && !docsPrefix.isEmpty()) {
                        String prefix = docsPrefix.get(d.getId());
                        if (StringUtils.isNotBlank(prefix)) {
                            filename = prefix + filename;
                        }
                    }

                    // 处理下载文件名
                    String docFilename = DocDownloadUtils.getDocFilenameWithSuffix(filename, d.getDocVersion(), null);
                    if (!document.isPdf() && PDFConverterUtils.canConvertToPdf(document)) {
                        docFilename += ".pdf";
                    }

                    ZipArchiveEntry zipArchiveEntry = new ZipArchiveEntry(docFilename);
                    log.debug("zipDocs加入文档: {}", docFilename);
                    zaos.putArchiveEntry(zipArchiveEntry);

                    InputStream is = null;
					/*
					if (ZDIOUtils.FE()) {
						is = ZDIOUtils.getDIStream(docFile);
					} else {
						is = new FileInputStream(docFile);
					}
					*/
                    if (TaskDictionary.getMainDefinition().hm("pdfwm") &&
                            (d.isPdf() || PDFConverterUtils.canConvertToPdf(d)) &&
                            !FolderPermissionUtils.checkPermission(UserContextHolder.getUser(), d.getFolder(), FolderPermission.ALL) &&
                            StringUtils.isNotBlank(FolderUtils.getPropertyFromAllLevel(d.getFolder(),
                                    "downPdfWaterMarkText"))) {

                        // 动态生成PDF水印
                        log.debug("batchDownloadZipDocs开始生成动态水印：{}，'{}'", d.getFilename(),
                                FolderUtils.getPropertyFromAllLevel(d.getFolder(), "downPdfWaterMarkText"));

                        File diPdfFile = PDFConverterUtils.getDIPdfFile(d, d.getNewestVersion());

                        String pdfText = FolderUtils.getPropertyFromAllLevel(d.getFolder(), "downPdfWaterMarkText");
                        pdfText = PDFTextWaterMarkUtils.replaceVars(UserContextHolder.getUser(), pdfText, d);
                        Folder folder = d.getFolder();
                        // 水印行数
                        int rowNumber = 1;
                        String rNum = FolderUtils.getPropertyFromAllLevel(d.getFolder(), "pdfWatermarkRowNumber");
                        if (StringUtils.isNotBlank(rNum)) {
                            rowNumber = NumberUtils.toInt(rNum);
                        } else {
                            folder.setProperty("pdfWatermarkRowNumber", Integer.toString(rowNumber));
                        }

                        // 水印列数
                        int colNumber = 1;
                        String colNum = FolderUtils.getPropertyFromAllLevel(d.getFolder(), "pdfWatermarkColumnNumber");
                        if (StringUtils.isNotBlank(colNum)) {
                            colNumber = NumberUtils.toInt(colNum);
                        } else {
                            folder.setProperty("pdfWatermarkColumnNumber", Integer.toString(colNumber));
                        }

                        // 水印角度
                        float angle = 45;
                        String picAngle = FolderUtils.getPropertyFromAllLevel(d.getFolder(), "pdfWatermarkAngle");
                        if (StringUtils.isNotBlank(picAngle)) {
                            angle = NumberUtils.toFloat(picAngle);
                        } else {
                            folder.setProperty("pdfWatermarkAngle", Float.toString(angle));
                        }

                        // 水印字体大小
                        float fontSize = 10;
                        String fSize = FolderUtils.getPropertyFromAllLevel(d.getFolder(), "pdfWatermarkFontSize");
                        if (StringUtils.isNotBlank(fSize)) {
                            fontSize = NumberUtils.toFloat(fSize);
                        } else {
                            // 计算字体大小
                            FileInputStream fileInputStream = new FileInputStream(diPdfFile);
                            PdfReader reader = new PdfReader(fileInputStream);
                            // 获取PDF页面宽高数据
                            com.itextpdf.text.Document pdfDoc = new com.itextpdf.text.Document(
                                    reader.getPageSizeWithRotation(1));
                            float width = pdfDoc.getPageSize().getWidth();
                            fontSize = (float) ((width * 1.0) /
                                    ((PDFTextWaterMarkUtils.getLength(pdfText) * colNumber)));
                        }
                        folderService.update(folder);

                        log.debug("batchDownloadZipDocs rowNumber:{},colNumber:{},fontSize:{},angle:{}", new Object[]{
                                rowNumber, colNumber, fontSize, angle});

                        File pdfWatermarkOutFile = PDFTextWaterMarkUtils.genWaterMarkPDFToTempFileInPool(
                                UserContextHolder.getUser(), d, d.getNewestVersion(), pdfText,
                                FolderUtils.getPropertyFromAllLevel(d.getFolder(), "downPdfWaterMarkTextColor"),
                                rowNumber, colNumber, fontSize, angle, diPdfFile);

                        if (pdfWatermarkOutFile == null) {
                            File oldFile = ZDFileUtils.getTempFile(d, null);
                            // 水印加密设置
                            if ("true".equals(downLoadWatermarkWithPWD)) {
                                if (!systemStartDownloadWatermarkWithPWD) {
                                    log.debug("后台PDF加密下载未启动");
                                    continue;
                                }

                                // 使用目录设置
                                if (StringUtils.isBlank(watermarkPWD)) {
                                    log.debug("PDF水印密码为空");
                                    continue;
                                }
                                File file = null;
                                try {
                                    file = PDFUtils.EncryptionPDF(watermarkPWD, oldFile, d, operator);
                                } catch (Exception e) {
                                    log.debug("调用PDF水印加密异常:{}", e);
                                }
                                is = new FileInputStream(file);

                            } else if ("defaultSet".equals(downLoadWatermarkWithPWD)) {
                                if ("false".equals(systemStartDownloadWatermarkWithPWD)) {
                                    log.debug("defaultSet后台PDF加密下载未启动");
                                    continue;
                                }

                                // 使用系统设置
                                if (StringUtils.isBlank(systemwaterMarkPWD)) {
                                    log.debug("系统PDF水印密码为空");
                                    continue;
                                }
                                File file = null;
                                try {
                                    file = PDFUtils.EncryptionPDF(systemwaterMarkPWD, oldFile, d, operator);
                                } catch (Exception e) {
                                    log.debug("调用PDF水印加密异常:{}", e);
                                }
                                is = new FileInputStream(file);

                            } else {
                                is = ZDIOUtils.getDocInputStream(d, d.getNewestVersion());
                            }
                        } else {
                            // 水印加密设置
                            if ("true".equals(downLoadWatermarkWithPWD)) {
                                if ("false".equals(systemStartDownloadWatermarkWithPWD)) {
                                    log.debug("后台PDF加密下载未启动");
                                    continue;
                                }

                                // 使用目录设置
                                if (StringUtils.isBlank(watermarkPWD)) {
                                    log.debug("PDF水印密码为空");
                                    continue;
                                }
                                File file = null;
                                try {
                                    file = PDFUtils.EncryptionPDF(watermarkPWD, pdfWatermarkOutFile, d, operator);
                                } catch (Exception e) {
                                    log.debug("调用PDF水印加密异常:{}", e);
                                }
                                is = new FileInputStream(file);

                            } else if ("defaultSet".equals(downLoadWatermarkWithPWD)) {
                                if ("false".equals(systemStartDownloadWatermarkWithPWD)) {
                                    log.debug("defaultSet后台PDF加密下载未启动");
                                    continue;
                                }

                                // 使用系统设置
                                if (StringUtils.isBlank(systemwaterMarkPWD)) {
                                    log.debug("系统PDF水印密码为空");
                                    continue;
                                }
                                File file = null;
                                try {
                                    file = PDFUtils.EncryptionPDF(systemwaterMarkPWD, pdfWatermarkOutFile, d, operator);
                                } catch (Exception e) {
                                    log.debug("调用PDF水印加密异常:{}", e);
                                }
                                is = new FileInputStream(file);

                            } else {
                                is = new FileInputStream(pdfWatermarkOutFile);
                            }
                        }
                    } else if (d.isPicture() &&
                            !FolderPermissionUtils.checkPermission(UserContextHolder.getUser(), d.getFolder(), FolderPermission.ALL) &&
                            StringUtils.isNotBlank(FolderUtils.getPropertyFromAllLevel(d.getFolder(),
                                    "downPicWaterMarkText"))) {
                        // 水印内容
                        String picText = FolderUtils.getPropertyFromAllLevel(d.getFolder(), "downPicWaterMarkText");
                        picText = PDFTextWaterMarkUtils.replaceVars(UserContextHolder.getUser(), picText, d);
                        Folder folder = document.getFolder();
                        File f = ZDIOUtils.getDocFile(d, d.getNewestVersion(), false);

                        // 水印行数
                        int rowNumber = 1;
                        String rNum = FolderUtils.getPropertyFromAllLevel(d.getFolder(), "picWatermarkRowNumber");
                        if (StringUtils.isNotBlank(rNum)) {
                            rowNumber = NumberUtils.toInt(rNum);
                        } else {
                            folder.setProperty("picWatermarkRowNumber", Integer.toString(rowNumber));
                        }

                        // 水印列数
                        int colNumber = 1;
                        String cNum = FolderUtils.getPropertyFromAllLevel(d.getFolder(), "picWatermarkColumnNumber");
                        if (StringUtils.isNotBlank(cNum)) {
                            colNumber = NumberUtils.toInt(cNum);
                        } else {
                            folder.setProperty("picWatermarkColumnNumber", Integer.toString(colNumber));
                        }

                        Image srcImg = ImageIO.read(f);
                        int srcImageWidth = srcImg.getWidth(null);
                        int srcImageHeight = srcImg.getHeight(null);
                        int imgWith = srcImageWidth / colNumber;
                        int imgHeight = srcImageHeight / rowNumber;
                        double sqrt = Math.sqrt(imgWith * imgWith + imgHeight * imgHeight);

                        // 水印角度
                        double angle = -45;
                        String picAngle = FolderUtils.getPropertyFromAllLevel(d.getFolder(), "picWatermarkAngle");
                        if (StringUtils.isNotBlank(picAngle)) {
                            angle = NumberUtils.toDouble(picAngle);
                        } else {
                            angle = PictureUtils.getDegrees(sqrt, imgWith, imgHeight);
                        }

                        // 水印字体大小
                        int fontSize = 10;
                        String fSize = FolderUtils.getPropertyFromAllLevel(d.getFolder(), "picWatermarkFontSize");
                        if (StringUtils.isNotBlank(fSize)) {
                            fontSize = NumberUtils.toInt(fSize);
                        } else {
                            fontSize = (int) (sqrt * 0.5 / PictureUtils.getTextLength(picText));
                        }
                        folderService.update(folder);

                        log.debug("picture batchDownloadZipDocs rowNumber:{},colNumber:{},angle:{},fontSize:{}",
                                new Object[]{rowNumber, colNumber, angle, fontSize});

                        String colorValue = FolderUtils.getPropertyFromAllLevel(d.getFolder(),
                                "downPicWaterMarkTextColor");
                        File file = null;
                        try {
                            file = PictureUtils.WarterMarkPicture(f, picText, colorValue, d,
                                    UserContextHolder.getUser(), rowNumber, colNumber, angle, fontSize);
                        } catch (Exception e) {
                            log.debug("picture batchDownloadZipDocs 图片加水印异常,e:{}", e);
                        }
                        is = new FileInputStream(file);

                    } else {
                        if (d.isPdf()) {
                            File oldFile = ZDFileUtils.getTempFile(d, null);
                            // 水印加密设置
                            if ("true".equals(downLoadWatermarkWithPWD)) {
                                if ("false".equals(systemStartDownloadWatermarkWithPWD)) {
                                    log.debug("后台PDF加密下载未启动");
                                    continue;
                                }

                                // 使用目录设置
                                if (StringUtils.isBlank(watermarkPWD)) {
                                    log.debug("PDF水印密码为空");
                                    continue;
                                }
                                File file = null;
                                try {
                                    file = PDFUtils.EncryptionPDF(watermarkPWD, oldFile, d, operator);
                                } catch (Exception e) {
                                    log.debug("调用PDF水印加密异常:{}", e);
                                }
                                is = new FileInputStream(file);

                            } else if ("defaultSet".equals(downLoadWatermarkWithPWD)) {
                                if ("false".equals(systemStartDownloadWatermarkWithPWD)) {
                                    log.debug("defaultSet后台PDF加密下载未启动");
                                    continue;
                                }

                                // 使用系统设置
                                if (StringUtils.isBlank(systemwaterMarkPWD)) {
                                    log.debug("系统PDF水印密码为空");
                                    continue;
                                }
                                File file = null;
                                try {
                                    file = PDFUtils.EncryptionPDF(systemwaterMarkPWD, oldFile, d, operator);
                                } catch (Exception e) {
                                    log.debug("调用PDF水印加密异常:{}", e);
                                }
                                is = new FileInputStream(file);

                            } else {
                                is = ZDIOUtils.getDocInputStream(d, d.getNewestVersion());
                            }
                        } else if (d.isWord07() &&
                                ZDFileUtils.isNeedAddHeaderAndFooterImage(d, d.getNewestVersion(), "")) {
                            File imagedFile = ZDFileUtils.addHeaderAndFooterImageForDocx(d, d.getNewestVersion(), "");
                            if (imagedFile != null) {
                                File pageFile = null;
                                if (addApprovalInfoCondition(d)) {
                                    pageFile = addApprovaInfoToFirstPage(imagedFile, d, docId);
                                }
                                if (addAntecedentsInfoCondition(d)) {
                                    pageFile = addAntecedentsInfoToFirstPage(imagedFile, d, docId);
                                }
                                if (pageFile != null) {
                                    imagedFile = pageFile;
                                }
                                is = new FileInputStream(imagedFile);
                            } else {
                                File pageFile = null;
                                if (addApprovalInfoCondition(d)) {
                                    File tempFile = ZDFileUtils.getTempFile(d, null);
                                    pageFile = addApprovaInfoToFirstPage(tempFile, d, docId);
                                }
                                if (addAntecedentsInfoCondition(d)) {
                                    File tempFile = ZDFileUtils.getTempFile(d, null);
                                    pageFile = addAntecedentsInfoToFirstPage(tempFile, d, docId);
                                }
                                if (pageFile != null) {
                                    is = new FileInputStream(pageFile);
                                } else {
                                    is = ZDIOUtils.getDocInputStream(d, d.getNewestVersion());
                                }
                            }
                        } else {
                            if (document.isWord07()) {
                                File pageFile = null;
                                if (addApprovalInfoCondition(d)) {
                                    File tempFile = ZDFileUtils.getTempFile(d, null);
                                    pageFile = addApprovaInfoToFirstPage(tempFile, d, docId);
                                }
                                if (addAntecedentsInfoCondition(d)) {
                                    File tempFile = ZDFileUtils.getTempFile(d, null);
                                    pageFile = addAntecedentsInfoToFirstPage(tempFile, d, docId);
                                }
                                if (pageFile != null) {
                                    is = new FileInputStream(pageFile);
                                } else {
                                    is = ZDIOUtils.getDocInputStream(d, d.getNewestVersion());
                                }
                            } else {
                                is = ZDIOUtils.getDocInputStream(d, d.getNewestVersion());
                            }
                        }
                    }

                    IOUtils.copyLarge(is, zaos);
                    IOUtils.closeQuietly(is);
                    zaos.closeArchiveEntry();

                    documentService.addLog(d, DocumentLog.TYPE_DOWNLOAD, "批量下载文档: " + d.getFilename(),
                            operator.getUsername(), operator.getFullname(), ip);
                }
            }
        } finally {
            try {
                if (zaos != null) {
                    zaos.close();
                }
            } catch (IOException e) {
                log.error("batchDownloadZipDocs ex", e);
            }
        }
    }

    /**
     * 下载审批意见 判断条件
     *
     * @param document
     * @return
     */
    private boolean addApprovalInfoCondition(Document document) {
        String systemSelectFirstPageType = scm.getProperty("systemSelectFirstPageType");// 设置类型
        boolean systemFirstPageDownload = scm.getBooleanProperty("systemFirstPageDownload", false);// 设置开关

        // 获取目录首页下载设置
        String selectFirstPageType = FolderUtils.getPropertyFromAllLevel(document.getFolder(), "selectFirstPageType");// 设置类型
        String firstPageDownload = FolderUtils.getPropertyFromAllLevel(document.getFolder(), "firstPageDownload");// 设置开关
        log.debug(
                "systemSelectFirstPageType:{},systemFirstPageDownload:{},selectFirstPageType:{},firstPageDownload:{}",
                new Object[]{systemSelectFirstPageType, systemFirstPageDownload, selectFirstPageType,
                        firstPageDownload});
        boolean result = false;
        if (systemFirstPageDownload) {
            if ("true".equals(firstPageDownload) || "defaultDownLoadSet".equals(firstPageDownload)) {

                /**
                 * 下载审批意见 1.目录设置默认，系统设置审批意见 2.目录设置为审批意见
                 */
                if (("defaultSystem".equals(selectFirstPageType) && "suggestionDownload"
                        .equals(systemSelectFirstPageType)) || "suggestionDownload".equals(selectFirstPageType)) {
                    result = true;
                }
            }
        }
        return result;
    }

    /**
     * 首页下载审批信息
     *
     * @param actualFile
     * @param document
     * @param docId
     * @return
     */
    private File addApprovaInfoToFirstPage(File actualFile, Document document, String docId) {
        File resultFile = null;
        /*String filepath = actualFile.getPath();
        log.debug("filePath:{}", filepath);
        User currentUser = UserContextHolder.getUser();

        *//**
         * 下载审批意见 1.目录设置默认，系统设置审批意见 2.目录设置为审批意见
         *//*
        Page piHistory = processService.getProcessInstancesByResId(docId, 1, 5, false);
        if (piHistory != null) {
            List piList = piHistory.getResults();
            if (piList != null && piList.size() > 0) {
                SSProcessInstance instance = (SSProcessInstance) piList.get(0);
                String piId = instance.getId();
                tasks = taskService.getTasks(piId);
                questionAndAnswers = questionAndAnswerManager.getQuestionByPiId(piId);
                String displayFolder = document.getDisplayFolderWithoutLink();

                List<Document> documents = new ArrayList<Document>();
                List<String> ids = instance.getResourceDocIdStringList();
                for (String id : ids) {
                    Document doc = documentManager.getDocumentById(id);
                    if (doc != null) {
                        documents.add(doc);
                    }
                }

                *//*
         *根据流程定义获取流程类型（标准式，节点式，阶段式）
         *//*
                SSProcessDefinition pd = processService.getProcessDefinition(instance.getPdId());
                String showSummaryOpinionType = pd.getShowSummaryOpinionType();

                *//**
         * 填充图片类型
         *//*
                File contentTypeTempDir = new File(BootstrapManager.getInstance().getTempDir(), "addImgContentType");
                contentTypeTempDir.mkdirs();
                File contentTargetFile = new File(contentTypeTempDir, currentUser.getId() + "-" +
                        RandomStringUtils.randomAlphanumeric(15));
                try {
                    resultFile = FirstPageInfoUtils.addContentType(filepath, contentTargetFile);
                    filepath = resultFile.getPath();
                } catch (Exception e1) {
                    log.debug("填充分页异常:{}", e1);
                }

                *//**
         * 填充分页
         *//*
                File tempDir = new File(BootstrapManager.getInstance().getTempDir(), "addFirstPage");
                tempDir.mkdirs();
                File targetFile = new File(tempDir, currentUser.getId() + "-" +
                        RandomStringUtils.randomAlphanumeric(15));
                try {
                    resultFile = FirstPageInfoUtils.createFirstPageInfo(filepath, targetFile, null, null, null, null,
                            null, null, null, FirstPageInfoUtils.ADDPAGE);
                    filepath = resultFile.getPath();
                } catch (Exception e1) {
                    log.debug("填充分页异常:{}", e1);
                }

                // 记录签名区图片
                Map<String, String> signatureMap = new LinkedHashMap<>();
                *//**
         * 添加表格
         *//*
                File tabelTempDir = new File(BootstrapManager.getInstance().getTempDir(), "addFirstTable");
                tabelTempDir.mkdirs();
                File tableTargetFile = new File(tabelTempDir, currentUser.getId() + "-" +
                        RandomStringUtils.randomAlphanumeric(15));
                try {
                    resultFile = FirstPageInfoUtils.createFirstPageInfo(filepath, tableTargetFile, tasks,
                            questionAndAnswers, instance, displayFolder, showSummaryOpinionType, documents,
                            signatureMap, FirstPageInfoUtils.ADDTABLE);
                } catch (Exception e) {
                    log.debug("添加表格异常:{}", e);
                }
                filepath = resultFile.getPath();

                if (signatureMap != null && signatureMap.size() > 0) {
                    *//**
         * document.xml.rels文件添加图片索引
         *//*
                    File xmlRelsTempDir = new File(BootstrapManager.getInstance().getTempDir(), "addFirstXmlRel");
                    xmlRelsTempDir.mkdirs();
                    File xmlRelsTargetFile = new File(xmlRelsTempDir, currentUser.getId() + "-" +
                            RandomStringUtils.randomAlphanumeric(15));
                    try {
                        resultFile = FirstPageInfoUtils.addDocumentXmlRels(filepath, xmlRelsTargetFile, signatureMap);
                    } catch (Exception e) {
                        log.debug("添加图片索引异常:{}", e);
                    }
                    filepath = resultFile.getPath();
                    *//**
         * 添加图片
         *//*
                    File picTempDir = new File(BootstrapManager.getInstance().getTempDir(), "addFirstPic");
                    picTempDir.mkdirs();
                    File xmlRelsTableTargetFile = new File(picTempDir, currentUser.getId() + "-" +
                            RandomStringUtils.randomAlphanumeric(15));
                    try {
                        resultFile = FirstPageInfoUtils.addSignaturePic(filepath, xmlRelsTableTargetFile, signatureMap);
                    } catch (Exception e) {
                        log.debug("添加图片索引异常:{}", e);
                    }
                    filepath = resultFile.getPath();
                }
            }
        }*/
        return resultFile;
    }

    /**
     * 下载履历 判断条件
     *
     * @param document
     * @return
     */
    private boolean addAntecedentsInfoCondition(Document document) {
        String systemSelectFirstPageType = scm.getProperty("systemSelectFirstPageType");// 设置类型
        boolean systemFirstPageDownload = scm.getBooleanProperty("systemFirstPageDownload", false);// 设置开关

        // 获取目录首页下载设置
        String selectFirstPageType = FolderUtils.getPropertyFromAllLevel(document.getFolder(), "selectFirstPageType");// 设置类型
        String firstPageDownload = FolderUtils.getPropertyFromAllLevel(document.getFolder(), "firstPageDownload");// 设置开关
        log.debug(
                "systemSelectFirstPageType:{},systemFirstPageDownload:{},selectFirstPageType:{},firstPageDownload:{}",
                new Object[]{systemSelectFirstPageType, systemFirstPageDownload, selectFirstPageType,
                        firstPageDownload});
        boolean result = false;
        if (systemFirstPageDownload) {
            if ("true".equals(firstPageDownload) || "defaultDownLoadSet".equals(firstPageDownload)) {

                /**
                 * 下载履历 1.目录设置默认，系统设置变更履历 2.目录设置为变更履历
                 */
                if (("defaultDownLoadSet".equals(selectFirstPageType) && "resumeDownload"
                        .equals(systemSelectFirstPageType)) || "resumeDownload".equals(selectFirstPageType)) {
                    result = true;
                }
            }
        }
        return result;
    }

    /**
     * 首页下载履历信息
     *
     * @param actualFile
     * @param document
     * @param docId
     * @return
     */
    private File addAntecedentsInfoToFirstPage(File actualFile, Document document, String docId) {
        File resultFile = null;
        /*String filepath = actualFile.getPath();
        log.debug("filePath:{}", filepath);
        User currentUser = UserContextHolder.getUser();

        *//**
         * 下载履历 1.目录设置默认，系统设置变更履历 2.目录设置为变更履历 获取最新发布版
         *//*
        DocPublishVersion publishVersion = docPublishVersionManager.getCurrentPublishVersionByDocId(document.getId(),
                null, null);
        log.debug("docPublishVersionID:{}", publishVersion.getId());
        if (publishVersion != null) {
            String publishRangeWholeDisplay = publishVersion.getPublishRangeWholeDisplay();
            List<SSProcessInstance> publishProcessInstances = null;
            String[] elecPubDisplayLines = null;
            String[] paperPubDisplayLines = null;
            if (StringUtils.isNotBlank(publishRangeWholeDisplay)) {
                elecPubDisplayLines = StringUtils.split(publishRangeWholeDisplay, "##");
            }
            String paperlishRangeWholeDisplay = publishVersion.getPaperlishRangeWholeDisplay();
            if (StringUtils.isNotBlank(paperlishRangeWholeDisplay)) {
                paperPubDisplayLines = StringUtils.split(paperlishRangeWholeDisplay, "##");
            }

            String publishPiId = publishVersion.getPublishPiId();
            String withdrawPiId = publishVersion.getWithdrawPiId();
            String alterPiId = publishVersion.getAlterPiId();
            String invalidPiId = publishVersion.getInvalidPiId();

            publishProcessInstances = new LinkedList<SSProcessInstance>();
            if (StringUtils.isNotBlank(alterPiId)) {
                SSProcessInstance processInstance = processService.getProcessInstance(alterPiId);
                if (processInstance != null) {
                    publishProcessInstances.add(processInstance);
                }
            }
            if (StringUtils.isNotBlank(publishPiId)) {
                SSProcessInstance processInstance = processService.getProcessInstance(publishPiId);
                if (processInstance != null) {
                    publishProcessInstances.add(processInstance);
                }
            }
            if (StringUtils.isNotBlank(withdrawPiId)) {
                SSProcessInstance processInstance = processService.getProcessInstance(withdrawPiId);
                if (processInstance != null) {
                    publishProcessInstances.add(processInstance);
                }
            }
            if (StringUtils.isNotBlank(invalidPiId)) {
                SSProcessInstance processInstance = processService.getProcessInstance(invalidPiId);
                if (processInstance != null) {
                    publishProcessInstances.add(processInstance);
                }
            }

            *//**
         * 填充图片类型
         *//*
            File contentTypeTempDir = new File(BootstrapManager.getInstance().getTempDir(), "addPublishImgContentType");
            contentTypeTempDir.mkdirs();
            File contentTargetFile = new File(contentTypeTempDir, currentUser.getId() + "-" +
                    RandomStringUtils.randomAlphanumeric(15));
            try {
                resultFile = ResumeUtils.addContentType(filepath, contentTargetFile);
                filepath = resultFile.getPath();
            } catch (Exception e1) {
                log.debug("填充图片类型异常:{}", e1);
            }

            *//**
         * 填充分页
         *//*
            File tempDir = new File(BootstrapManager.getInstance().getTempDir(), "addPublishFirstPage");
            tempDir.mkdirs();
            File targetFile = new File(tempDir, currentUser.getId() + "-" + RandomStringUtils.randomAlphanumeric(15));
            try {
                resultFile = ResumeUtils.createResumePageInfo(filepath, targetFile, null, null, null, null, null, null,
                        ResumeUtils.ADDPAGE);
                filepath = resultFile.getPath();
            } catch (Exception e1) {
                log.debug("填充分页异常:{}", e1);
            }

            // 记录签名区图片
            Map<String, String> signatureMap = new LinkedHashMap<>();

            *//**
         * 添加表格
         *//*
            File tabelTempDir = new File(BootstrapManager.getInstance().getTempDir(), "addPublishTable");
            tabelTempDir.mkdirs();
            File tableTargetFile = new File(tabelTempDir, currentUser.getId() + "-" +
                    RandomStringUtils.randomAlphanumeric(15));
            try {
                resultFile = ResumeUtils.createResumePageInfo(filepath, tableTargetFile, document,
                        publishProcessInstances, elecPubDisplayLines, paperPubDisplayLines, publishVersion,
                        signatureMap, ResumeUtils.ADDTABLE);
            } catch (Exception e) {
                log.debug("履历添加表格异常:{}", e);
            }
            filepath = resultFile.getPath();

            if (signatureMap != null && signatureMap.size() > 0) {
                *//**
         * document.xml.rels文件添加图片索引
         *//*
                File xmlRelsTempDir = new File(BootstrapManager.getInstance().getTempDir(), "addPublisgXmlRel");
                xmlRelsTempDir.mkdirs();
                File xmlRelsTargetFile = new File(xmlRelsTempDir, currentUser.getId() + "-" +
                        RandomStringUtils.randomAlphanumeric(15));
                try {
                    resultFile = ResumeUtils.addDocumentXmlRels(filepath, xmlRelsTargetFile, signatureMap);
                } catch (Exception e) {
                    log.debug("履历添加图片索引异常:{}", e);
                }
                filepath = resultFile.getPath();
                *//**
         * 添加图片
         *//*
                File picTempDir = new File(BootstrapManager.getInstance().getTempDir(), "addPublishPic");
                picTempDir.mkdirs();
                File xmlRelsTableTargetFile = new File(picTempDir, currentUser.getId() + "-" +
                        RandomStringUtils.randomAlphanumeric(15));
                try {
                    resultFile = ResumeUtils.addSignaturePic(filepath, xmlRelsTableTargetFile, signatureMap);
                } catch (Exception e) {
                    log.debug("履历添加图片异常:{}", e);
                }
                filepath = resultFile.getPath();
            }
        }*/
        return resultFile;
    }
}
