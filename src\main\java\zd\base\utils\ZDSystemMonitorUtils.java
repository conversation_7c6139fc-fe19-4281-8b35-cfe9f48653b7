package zd.base.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import zd.base.exception.IllegalArgumentException;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.utils.JSONUtils;

import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ZDSystemMonitorUtils {

    private static final SystemConfigManager scm = SystemConfigManager.getInstance();

    public static void startCheck() {
        List<Map<String, Object>> systemMonitors = getSystemMonitors();
        if (CollectionUtils.isEmpty(systemMonitors)) {
            return;
        }

        List<Map<String, Object>> systemMonitorStatusList = getSystemMonitorStatusList();
        Map<String, Map<String, Object>> systemMonitorStatusMap = getSystemMonitorStatusMap(systemMonitorStatusList);
        boolean needSave = false;
        for (Map<String, Object> systemMonitor : systemMonitors) {
            String id = MapUtils.getString(systemMonitor, "id", "");
            String host = MapUtils.getString(systemMonitor, "host", "");
            int port = MapUtils.getIntValue(systemMonitor, "port", 0);

            Map<String, Object> systemMonitorStatus = systemMonitorStatusMap.get(id);
            if (systemMonitorStatus == null) {
                systemMonitorStatus = new HashMap<>();
                systemMonitorStatus.put("id", id);
                systemMonitorStatusList.add(systemMonitorStatus);
                needSave = true;
            }

            String msg = MapUtils.getString(systemMonitorStatus, "msg", "");
            if (StringUtils.isBlank(host) || port <= 0) {
                if (!"参数错误".equals(msg)) {
                    systemMonitorStatus.put("msg", "参数错误");
                    needSave = true;
                }

                continue;
            }

            boolean enabled = MapUtils.getBooleanValue(systemMonitor, "enabled", true);
            if (!enabled) {
                if (!"未启用".equals(msg)) {
                    systemMonitorStatus.put("msg", "未启用");
                    needSave = true;
                }

                continue;
            }

            boolean connStatus = connHostAndPort(host, port);
            if (connStatus) {
                if (!"正常".equals(msg)) {
                    systemMonitorStatus.put("msg", "正常");
                    needSave = true;
                }
            } else {
                if (!"连接失败".equals(msg)) {
                    systemMonitorStatus.put("msg", "连接失败");
                    needSave = true;
                }
            }
        }

        if (needSave) {
            saveSystemMonitorsStatus(systemMonitorStatusList);
        }
    }


    public static boolean connHostAndPort(String host, int port) {
        if (StringUtils.isBlank(host) || port <= 0) {
            return false;
        }

        try {
            Socket socket = new Socket();
            socket.connect(new InetSocketAddress(host, port), 5000);
            socket.setSoTimeout(5000);
            return socket.isConnected();
        } catch (Exception e) {

        }

        return false;
    }

    public static List<Map<String, Object>> getSystemMonitorWithStatus() {
        Map<String, Map<String, Object>> systemMonitorStatusMap = getSystemMonitorStatusMap(null);
        List<Map<String, Object>> systemMonitors = getSystemMonitors();
        for (Map<String, Object> systemMonitor : systemMonitors) {
            String id = MapUtils.getString(systemMonitor, "id", "");

            Map<String, Object> systemMonitorStatus = systemMonitorStatusMap.get(id);
            if (MapUtils.isNotEmpty(systemMonitorStatus)) {
                systemMonitor.put("msg", MapUtils.getString(systemMonitorStatus, "msg", ""));
            }
        }

        return systemMonitors;
    }

    public static List<Map<String, Object>> getSystemMonitors() {
        String systemMonitorsSettings = scm.getProperty("system_monitors");
        if (StringUtils.isBlank(systemMonitorsSettings)) {
            return new ArrayList<>();
        }

        return JSONUtils.parseObject(systemMonitorsSettings, List.class, new ArrayList());
    }

    public static List<Map<String, Object>> getSystemMonitorStatusList() {
        String systemMonitorsStatus = scm.getProperty("system_monitors_status");
        if (StringUtils.isBlank(systemMonitorsStatus)) {
            return new ArrayList<>();
        }

        return JSONUtils.parseObject(systemMonitorsStatus, List.class, new ArrayList<>());
    }

    public static Map<String, Map<String, Object>> getSystemMonitorStatusMap(List<Map<String, Object>> systemMonitorStatusList) {
        if (systemMonitorStatusList == null) {
            systemMonitorStatusList = getSystemMonitorStatusList();
        }

        return systemMonitorStatusList.stream().collect(Collectors.toMap(item -> MapUtils.getString(item, "id", ""), item -> item));
    }

    public static void saveSystemMonitor(Map<String, Object> params) {
        String host = MapUtils.getString(params, "host", "");
        int port = MapUtils.getIntValue(params, "port", 0);

        if (StringUtils.isBlank(host) || port <= 0) {
            throw new IllegalArgumentException("域名/IP 及 端口号必填");
        }
        String id = getId(host, port);
        List<Map<String, Object>> systemMonitors = getSystemMonitors();
        Map<String, Object> systemMonitorById = getSystemMonitorById(systemMonitors, id);
        if (MapUtils.isEmpty(systemMonitorById)) {
            systemMonitorById = new HashMap<>();
            systemMonitorById.put("host", host);
            systemMonitorById.put("port", port);
            systemMonitorById.put("id", id);

            systemMonitors.add(systemMonitorById);
        }

        String name = MapUtils.getString(params, "name", "");
        int numIndex = MapUtils.getIntValue(params, "numIndex", 9999);
        String desc = MapUtils.getString(params, "desc", "");

        systemMonitorById.put("name", name);
        systemMonitorById.put("numIndex", numIndex);
        systemMonitorById.put("desc", desc);
        systemMonitorById.put("enabled", true);

        Collections.sort(systemMonitors, (o1, o2) -> {
            if (o1 == null || o2 == null) {
                return 0;
            }

            int numIndex1 = MapUtils.getIntValue(o1, "numIndex", 9999);
            int numIndex2 = MapUtils.getIntValue(o2, "numIndex", 9999);

            return numIndex1 - numIndex2;
        });

        saveSystemMonitors(systemMonitors);
    }

    public static void changeSystemMonitorEnabled(String id, boolean enabled) {
        if (StringUtils.isBlank(id)) {
            return;
        }

        List<Map<String, Object>> systemMonitors = getSystemMonitors();
        Map<String, Object> systemMonitorById = getSystemMonitorById(systemMonitors, id);
        if (MapUtils.isEmpty(systemMonitorById)) {
            return;
        }

        systemMonitorById.put("enabled", enabled);
        saveSystemMonitors(systemMonitors);
    }

    public static void deleteSystemMonitor(String id) {
        if (StringUtils.isBlank(id)) {
            return;
        }

        List<Map<String, Object>> systemMonitors = getSystemMonitors();
        Iterator<Map<String, Object>> it = systemMonitors.iterator();
        while (it.hasNext()) {
            Map<String, Object> next = it.next();
            String tempId = MapUtils.getString(next, "id", "");
            if (id.equals(tempId)) {
                it.remove();
            }
        }

        saveSystemMonitors(systemMonitors);
    }

    public static void saveSystemMonitorsStatus(List<Map<String, Object>> systemMonitorsStatus) {
        scm.setProperty("system_monitors_status", JSONUtils.toJSONString(systemMonitorsStatus, "[]"));
    }

    public static void saveSystemMonitors(List<Map<String, Object>> systemMonitors) {
        if (systemMonitors == null) {
            systemMonitors = new ArrayList<>();
        }

        scm.setProperty("system_monitors", JSONUtils.toJSONString(systemMonitors, "[]"));
        saveSystemMonitorsStatus(new ArrayList<>());
    }

    public static Map<String, Object> getSystemMonitorById(List<Map<String, Object>> systemMonitors, String id) {
        if (CollectionUtils.isEmpty(systemMonitors) || StringUtils.isBlank(id)) {
            return null;
        }

        for (Map<String, Object> systemMonitor : systemMonitors) {
            String tempId = MapUtils.getString(systemMonitor, "id", "");
            if (id.equals(tempId)) {
                return systemMonitor;
            }
        }

        return null;
    }

    public static String getId(String host, int port) {
        return host + ":" + port;
    }
}
