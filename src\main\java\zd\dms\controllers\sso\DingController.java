package zd.dms.controllers.sso;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.dms.entities.User;
import zd.dms.services.security.AuthenticationService;
import zd.dms.services.security.SecurityInfo;
import zd.dms.services.user.UserService;
import zd.dms.utils.DingUtils;

import java.util.Map;

@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "DingController", description = "钉钉Controller")
@RequestMapping("/sso/ding")
public class DingController extends ControllerSupport {

    private final UserService userService;

    @Operation(summary = "钉钉用户登陆")
    @RequestMapping(value = "/login", method = {RequestMethod.GET, RequestMethod.POST})
    @ZDLog("钉钉用户登陆")
    public JSONResultUtils<Map<String, Object>> login(@RequestParam(required = false) String code, @RequestBody(required = false) Map<String, Object> condition,
                                                      @RequestHeader("User-Agent") String userAgent, HttpServletRequest request, HttpServletResponse response) {

        code = MapUtils.getString(condition, "password", code);
        log.debug("code -> {}", code);

        if (StringUtils.isBlank(code)) {
            return JSONResultUtils.error("code不能为空");
        }

        String userId = DingUtils.getUserIdByCode(code);// 根据授权码获取钉钉的userId
        log.debug("userId: {}", userId);
        if (StringUtils.isBlank(userId)) {
            return JSONResultUtils.error("获取userId失败");
        }

        User u = userService.getUserByDingEmployeeId(userId);
        if (u == null) {
            SecurityInfo.setLoginErrorIP(getIpAddress());
            return JSONResultUtils.error("获取用户失败");
        }

        // 是否可以使用移动端
        if (LoginUtils.isMobile(userAgent)) {
            if (!LoginUtils.isHasMobileAccess(u)) {
                return JSONResultUtils.error("禁止移动端登陆");
            }
        }

        String username = u.getUsername();
        LoginUtils.allowLogin(username);
        LoginUtils.allowAdminLogin(u);

        LoginUtils.login(username, "", false, false, request, response);

        logInfo("用户 " + getCurrentUser().getFullname() + " 通过钉钉登录系统");
        log.debug("用户 {} 通过钉钉登录成功", username);

        Map<String, Object> jsonData = LoginUtils.getUserData(u, request);
        return JSONResultUtils.successWithData(jsonData);
    }
}
