package zd.dms.repositories.admin.impl;

import jakarta.persistence.EntityManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.LogMessage;
import zd.dms.repositories.admin.LogMessageRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.Date;
import java.util.List;

public class LogMessageRepositoryDaoImpl extends BaseRepositoryDaoImpl<LogMessage,Long> implements LogMessageRepository {

    public LogMessageRepositoryDaoImpl(Class<LogMessage> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<LogMessage> getLogMessageByOperator(String operator) {
        Specification<LogMessage> spec = (root, criteriaQuery, criteriaBuilder) -> {

            SpecTools<LogMessage> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);
            if (StringUtils.isNotBlank(operator)) {
                specTools.eq("operator", operator);
            }
            return specTools.getRestriction();
        };
        return findAll(spec);
    }

    public List<LogMessage> getLogMessageByLevel(int level){
        Specification<LogMessage> spec = (root, criteriaQuery, criteriaBuilder) -> {

            SpecTools<LogMessage> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            if (level > 0) {
                specTools.eq("level", level);
            }

            specTools.desc("id");

            return specTools.getRestriction();
        };
        return findAll(spec);
    }

    @Override
    public List<LogMessage> getLogsByDate(Date startDate, Date endDate) {

        Specification<LogMessage> spec = (root, criteriaQuery, criteriaBuilder) -> {

            SpecTools<LogMessage> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            if (startDate != null) {
                specTools.ge("creationDate", startDate);
            }
            if (endDate != null) {
                specTools.le("creationDate", endDate);
            }

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return null;
    }

    @Override
    public void clearAllLogMessage() {
        executeUpdate("delete from LogMessage");
    }
}
