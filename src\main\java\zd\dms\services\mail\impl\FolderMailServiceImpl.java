package zd.dms.services.mail.impl;

import jakarta.mail.AuthenticationFailedException;
import jakarta.mail.MessagingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.base.utils.system.SystemInitUtils;
import zd.base.utils.zdmq.ZDDocMQUtils;
import zd.dms.entities.*;
import zd.dms.repositories.mail.FolderMailConfigRepository;
import zd.dms.services.document.DocumentService;
import zd.dms.services.document.DocumentUtils;
import zd.dms.services.document.FolderService;
import zd.dms.services.document.exception.DocumentAmountExceededException;
import zd.dms.services.document.exception.ForbidExtException;
import zd.dms.services.im.InstantMessageService;
import zd.dms.services.mail.FolderMailService;
import zd.dms.services.mail.IMAPUtils;
import zd.dms.services.rule.FolderRuleService;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.services.user.UserService;

import java.io.File;
import java.io.IOException;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class FolderMailServiceImpl extends BaseJpaServiceImpl<FolderMailConfig, String> implements FolderMailService {

    private final FolderMailConfigRepository folderMailConfigRepository;

    private final FolderService folderService;

    private final UserService userService;

    private final InstantMessageService instantMessageService;

    private final DocumentService documentService;

    private final FolderRuleService folderRuleService;

    @Override
    public BaseRepository<FolderMailConfig, String> getBaseRepository() {
        return folderMailConfigRepository;
    }

    @Override
    public FolderMailConfig getFolderMailConfig(String id) {
        return folderMailConfigRepository.get(id);
    }

    @Override
    public void deleteFolderMailConfig(String id) {
        folderMailConfigRepository.deleteById(id);
    }

    @Override
    public void deleteFolderMailconfigByFolderId(long folderId) {
        folderMailConfigRepository.deleteByFolderId(folderId);
    }

    @Override
    public void updateFolderMailConfig(FolderMailConfig config) {
        folderMailConfigRepository.update(config);
    }

    @Override
    public void addFolderMailConfig(FolderMailConfig config) {
        folderMailConfigRepository.save(config);
    }

    @Override
    public void receiveMails(String username, String ipAddress) {
        log.debug("receiveMails 收取所有目录的邮件附件");
        List<FolderMailConfig> folderMailConfigs = folderMailConfigRepository.getAll();
        for (int i = 0; i < folderMailConfigs.size(); i++) {
            FolderMailConfig c = folderMailConfigs.get(i);
            Folder f = folderService.getById(c.getFolderId());
            if (f != null) {
                try {
                    receiveMail(c, username, ipAddress);
                } catch (Throwable t) {
                    log.error("receiveMails ex", t);
                }
            }
        }
    }

    @Override
    public void receiveMails(long folderId, String username, String ipAddress) {
        List<FolderMailConfig> folderMailConfigs = folderMailConfigRepository.getMailConfigsById(folderId);
        for (int i = 0; i < folderMailConfigs.size(); i++) {
            FolderMailConfig c = folderMailConfigs.get(i);
            try {
                receiveMail(c, username, ipAddress);
            } catch (Throwable t) {
                log.error("receiveMails ex", t);
            }
        }
    }

    @Override
    public void receiveMail(FolderMailConfig config, String username, String ipAddress) throws AuthenticationFailedException, MessagingException, IOException {
        if (config == null) {
            return;
        }

        Folder folder = folderService.getById(config.getFolderId());
        if (folder == null) {
            log.error("receiveMail null folder: " + config.getFolderId());
            return;
        }

        File saveDir = new File(SystemInitUtils.getTempDir(), "foldermail-" + config.getId());
        boolean result = IMAPUtils.receiveMail(config.getHost(), config.isUseSsl(), config.getUsername(),
                config.getPassword(), saveDir, config.getSubject());

        if (result) {
            File[] files = saveDir.listFiles();
            for (File f : files) {
                if (f != null && f.isFile() && f.length() > 0) {
                    try {
                        saveDocument(config, f.getName(), f, folder, username, ipAddress);
                    } catch (Throwable t) {
                        log.debug("receiveMail saveDocument ex", t);
                    }
                }
            }
        }
    }

    private void saveDocument(FolderMailConfig config, String docUploadFileName, File docUpload, Folder folder,
                              String username, String ipAddress) throws IOException, DocumentAmountExceededException {

        if (StringUtils.isBlank(username)) {
            username = UserGroupUtils.ADMIN_USERNAME;
        }
        User user = userService.getUserByUsername(username);
        if (user == null) {
            log.error("saveDocument null user: " + username);
            return;
        }

        Document document = new Document();

        String filename = FilenameUtils.getBaseName(docUploadFileName);
        String extension = StringUtils.lowerCase(FilenameUtils.getExtension(docUploadFileName));
        if (StringUtils.isNotBlank(extension)) {
            filename = filename + "." + extension;
        }

        document.setFilename(filename);
        document.setExtension(StringUtils.lowerCase(FilenameUtils.getExtension(docUploadFileName)));
        document.setCreator(username);
        document.setCreatorFullname(user.getFullname());
        document.setFolderId(folder.getId());

        try {
            documentService.saveNewDoc(document, docUpload);

            documentService.addLog(document, DocumentLog.TYPE_CREATE, "上传文档: " + document.getFilename() + ", 来自邮箱: " +
                    config.getMailAddress(), username, user.getFullname(), ipAddress);

            // 发布提示信息
            instantMessageService.sendFolderSubcribeMessage(user, document,
                    " 上传文档：<b>" + DocumentUtils.getDocumentNoticeUrl(document) + "</b>");

            // 执行规则
            folderRuleService.executeRule(FolderRule.ACTION_UPLOAD, document, user, ipAddress);

            // 执行提取文字线程
            ZDDocMQUtils.addDocExtractMq(document.getId(), document.getNewestVersion(), user.getUsername());
        } catch (ForbidExtException fee) {
            // ignore
        }
    }

    @Override
    public List<FolderMailConfig> getMailConfigsById(long folderId) {
        return folderMailConfigRepository.getMailConfigsById(folderId);
    }
}