//                    佛祖保佑       永无BUG
//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                           O\  =  /O
//                        ____/`---'\____
//                      .'  \\|     |//  `.
//                     /  \\|||  :  |||//  \
//                    /  _||||| -:- |||||-  \
//                    |   | \\\  -  /// |   |
//                    | \_|  ''\---/''  |   |
//                    \  .-\__  `-`  ___/-. /
//                  ___`. .'  /--.--\  `. . __
//               ."" '<  `.___\_<|>_/___.'  >'"".
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |
//              \  \ `-.   \_ __\ /__ _/   .-` /  /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
package zd.dms.services.document.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import zd.base.exception.IllegalArgumentException;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.model.BeanUtils;
import zd.dms.entities.FolderArea;
import zd.dms.repositories.document.FolderAreaRepository;
import zd.dms.services.document.FolderAreaService;
import zd.dms.services.user.UserGroupUtils;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Service
@Slf4j
public class FolderAreaServiceImpl extends BaseJpaServiceImpl<FolderArea, Long> implements FolderAreaService {

    private final FolderAreaRepository folderAreaRepository;

    @Override
    public BaseRepository<FolderArea, Long> getBaseRepository() {
        return folderAreaRepository;
    }

    @Override
    public List<FolderArea> getAllFolderAreas() {
        return folderAreaRepository.getAll();
    }

    @Override
    public Page getAllFolderAreasPage(int pageNumber, int pageSize) {
        return folderAreaRepository.getAllFolderAreaPage(pageNumber,pageSize);
    }

    @Override
    public void createDefaultArea() {
        try {
            boolean needCreateDocArea = true;
            boolean needCreateDataArea = true;

            List<FolderArea> defaultAreas = getDefaultAreas();
            if (CollectionUtils.isNotEmpty(defaultAreas)) {
                for (FolderArea folderArea : defaultAreas) {
                    String areaType = folderArea.getAreaType();
                    if (FolderArea.TYPE_DOC.equals(areaType)) {
                        needCreateDocArea = false;
                    } else if (FolderArea.TYPE_DATA.equals(areaType)) {
                        needCreateDataArea = false;
                    }
                }
            }

            if (needCreateDocArea) {
                FolderArea folderArea = new FolderArea();

                folderArea.setAreaName("文档管理");
                folderArea.setAreaType(FolderArea.TYPE_DOC);
                folderArea.setIconFilename("folder");
                folderArea.setNumIndex(2000);
                folderArea.setEnabled(true);
                folderArea.setCreator(UserGroupUtils.ADMIN_USERNAME);
                folderArea.setCreatorFullname("系统管理员");
                folderArea.setTreeName(FolderArea.TREENAME_DOC);

                folderArea.setDefaultArea(true);

                folderAreaRepository.update(folderArea);
            }

            if (needCreateDataArea) {
                FolderArea folderArea = new FolderArea();

                folderArea.setAreaName("档案管理");
                folderArea.setAreaType(FolderArea.TYPE_DATA);
                folderArea.setIconFilename("folder_zip");
                folderArea.setNumIndex(1000);
                folderArea.setEnabled(true);
                folderArea.setCreator(UserGroupUtils.ADMIN_USERNAME);
                folderArea.setCreatorFullname("系统管理员");
                folderArea.setTreeName(FolderArea.TREENAME_DATA);

                folderArea.setDefaultArea(true);

                folderAreaRepository.update(folderArea);
            }
        } catch (Exception e) {
            log.error("createDefaultArea error", e);
        }
    }

    @Override
    public List<FolderArea> getDefaultAreas() {
        return folderAreaRepository.getDefaultAreas();
    }

    @Override
    public void saveFolderArea(FolderArea folderArea) {
        folderAreaRepository.save(folderArea);

        String treeName = "";
        if (FolderArea.TYPE_DOC.equals(folderArea.getAreaType())) {
            treeName = FolderArea.TREENAME_DOC + folderArea.getId();
        } else if (FolderArea.TYPE_DATA.equals(folderArea.getAreaType())) {
            treeName = FolderArea.TREENAME_DATA + folderArea.getId();
        }
        folderArea.setTreeName(treeName);
        folderAreaRepository.update(folderArea);
    }

    @Override
    public FolderArea getFolderAreaById(Long id) {
        return folderAreaRepository.get(id);
    }

    @Override
    public void deleteFolderAreaById(FolderArea folderArea) {
        folderAreaRepository.delete(folderArea);
    }

    @Override
    public void updateFolderArea(FolderArea folderArea) {
        String treeName = "";
        if (FolderArea.TYPE_DOC.equals(folderArea.getAreaType())) {
            treeName = FolderArea.TREENAME_DOC + folderArea.getId();
        } else if (FolderArea.TYPE_DATA.equals(folderArea.getAreaType())) {
            treeName = FolderArea.TREENAME_DATA + folderArea.getId();
        }
        folderArea.setTreeName(treeName);
        folderAreaRepository.update(folderArea);
    }

    @Override
    public List<FolderArea> getFolderAreasByTypeAndIsEnabled(String areaType, Boolean enabled, boolean asc) {
        return folderAreaRepository.getFolderAreasByType(areaType, enabled, asc);
    }

    @Override
    public List<FolderArea> getFolderAreasByTypeAndIsEnabledAndIsoArea(String areaType, boolean enabled, boolean isoArea) {
        return folderAreaRepository.getFolderAreasByTypeAndIsEnabledAndIsoArea(areaType, enabled, isoArea);
    }

    @Override
    public FolderArea getFolderAreaByTreeName(String treeName) {
        return folderAreaRepository.getFolderAreaByTreeName(treeName);
    }

    @Override
    public JSONResultUtils<Object> createFolderArea(@RequestBody Map<String, Object> params) {

        String areaName = MapUtils.getString(params, "areaName", "");
        if (StringUtils.isBlank(areaName)) {
            throw new IllegalArgumentException("请填写分区名称");
        }

        String areaType = MapUtils.getString(params, "areaType", "");
        if (StringUtils.isBlank(areaType)) {
            throw new IllegalArgumentException("请填写分区类型");
        }

        String iconFilename = MapUtils.getString(params, "iconFilename", "");
        String creatorFullname = MapUtils.getString(params, "creatorFullname", "");
        int numIndex = MapUtils.getIntValue(params, "numIndex", 9999);
        FolderArea folderArea = BeanUtils.map2Bean(params, FolderArea.class);

        folderArea.setAreaName(areaName);
        folderArea.setAreaType(areaType);
        folderArea.setIconFilename(iconFilename);
        folderArea.setNumIndex(numIndex);
        folderArea.setCreatorFullname(creatorFullname);

        try {
            folderAreaRepository.save(folderArea);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        String treeName = "";
        if (FolderArea.TYPE_DOC.equals(folderArea.getAreaType())) {
            treeName = FolderArea.TREENAME_DOC + folderArea.getId();
        } else if (FolderArea.TYPE_DATA.equals(folderArea.getAreaType())) {
            treeName = FolderArea.TREENAME_DATA + folderArea.getId();
        }
        folderArea.setTreeName(treeName);

        folderAreaRepository.save(folderArea);

        return JSONResultUtils.success();
    }

    @Override
    public JSONResultUtils<Object> updateFolderArea(Map<String, Object> params) {
        String id = MapUtils.getString(params, "id", "");
        FolderArea folderArea = folderAreaRepository.get(NumberUtils.toLong(id));
        if (folderArea == null) {
            throw new IllegalArgumentException("目录分区不存在");
        }

        params.remove("id");

        BeanUtils.map2Bean(params, folderArea);

        String treeName = "";
        if (FolderArea.TYPE_DOC.equals(folderArea.getAreaType())) {
            treeName = FolderArea.TREENAME_DOC + folderArea.getId();
        } else if (FolderArea.TYPE_DATA.equals(folderArea.getAreaType())) {
            treeName = FolderArea.TREENAME_DATA + folderArea.getId();
        }
        folderArea.setTreeName(treeName);

        folderAreaRepository.update(folderArea);

        return JSONResultUtils.success();
    }
}
