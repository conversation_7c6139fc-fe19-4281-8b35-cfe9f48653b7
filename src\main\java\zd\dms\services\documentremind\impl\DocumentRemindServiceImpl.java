package zd.dms.services.documentremind.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.base.utils.ZDDateUtils;
import zd.dms.entities.*;
import zd.dms.repositories.document.DocumentRemindRepository;
import zd.dms.services.document.FolderService;
import zd.dms.services.documentremind.DocumentRemindService;
import zd.dms.services.mail.MailService;
import zd.dms.services.rule.FolderRuleService;
import zd.dms.services.user.GroupService;
import zd.dms.services.user.UserService;

import java.text.ParseException;
import java.util.*;

@RequiredArgsConstructor
@Service
@Slf4j
public class DocumentRemindServiceImpl extends BaseJpaServiceImpl<DocumentRemind, Long> implements DocumentRemindService {

    private static final int DELETE_DAYS = 60;

    private final DocumentRemindRepository documentRemindRepository;

    private final UserService userService;

    private final GroupService groupService;

    private final FolderRuleService folderRuleService;

    private final FolderService folderService;

    private final MailService mailService;

    @Override
    public BaseRepository<DocumentRemind, Long> getBaseRepository() {
        return documentRemindRepository;
    }

    @Override
    public void createRemind(DocumentRemind documentRemind) {
        documentRemindRepository.save(documentRemind);
    }

    @Override
    public void deleteRemind(DocumentRemind documentRemind) {
        documentRemindRepository.delete(documentRemind);
    }

    @Override
    public void deleteRemindById(long id) {
        documentRemindRepository.deleteById(id);
    }

    @Override
    public void deleteRemindByDocument(Document document) {
        documentRemindRepository.deleteByDocument(document);
    }

    @Override
    public void updateRemind(DocumentRemind documentRemind) {
        documentRemindRepository.update(documentRemind);
    }

    @Override
    public void createRelativeRemind(Document doc, int relativeNumber, String relativeUnit, String comment, String[] selectedUserGroups, User operator) {
        if (doc == null || StringUtils.isBlank(relativeUnit) || relativeNumber <= 0 || operator == null) {
            log.debug("错误的提醒参数");
            return;
        }

        DocumentRemind dr = new DocumentRemind();
        dr.setCreator(operator.getUsername());
        dr.setCreatorFullname(operator.getFullname());
        dr.setDocId(doc.getId());
        dr.setReminded(false);
        dr.setComment(comment);

        dr.setRemindType("modifiedDate");
        dr.setRelativeRemindNumber(relativeNumber);
        dr.setRelativeRemindUnit(relativeUnit);

        log.debug("{} 的提醒时间已设置为：{}", doc.getFilename(), dr.getRelativeRemindNumber() + dr.getRelativeRemindUnit());

        setUserIdsAndNames(selectedUserGroups, dr);

        createRemind(dr);
    }

    private void setUserIdsAndNames(String[] selectedUserGroups, DocumentRemind dr) {
        List<String> userGroupIds = new LinkedList<String>();
        List<String> userGroupNames = new LinkedList<String>();
        if (selectedUserGroups != null && selectedUserGroups.length > 0) {
            for (String selected : selectedUserGroups) {
                if (StringUtils.isNotBlank(selected)) {
                    selected = selected.trim();
                    if (selected.startsWith("u-")) {
                        String userId = selected.substring(2);
                        User u = userService.getUserById(userId);
                        if (u != null) {
                            userGroupIds.add("**" + userId + "**");
                            userGroupNames.add("[人员]" + u.getFullname());
                        }
                    } else if (selected.startsWith("g-")) {
                        long groupId = NumberUtils.toLong(selected.substring(2));
                        Group g = groupService.getGroupById(groupId);
                        if (g != null) {
                            userGroupIds.add("**" + selected + "**");
                            userGroupNames.add("[部门]" + g.getDisplayFullname());
                        }
                    }
                }
            }
        }

        dr.setUserIds(StringUtils.join(userGroupIds, ","));
        dr.setFullnames(StringUtils.join(userGroupNames, ","));
    }

    @Override
    public void createRemind(Document doc, String date, String comment, String[] selectedUserGroups, User operator) {
        if (doc == null || StringUtils.isBlank(date) || operator == null) {
            log.debug("错误的提醒参数");
            return;
        }

		/*
		if (getRemindByDocument(doc) != null) {
			log.debug("提醒已存在");
			return;
		}
		*/

        DocumentRemind dr = new DocumentRemind();
        dr.setCreator(operator.getUsername());
        dr.setCreatorFullname(operator.getFullname());
        dr.setDocId(doc.getId());
        dr.setReminded(false);
        dr.setComment(comment);

        Calendar c = Calendar.getInstance();
        try {
            Date remindDate = DateUtils.parseDate(date, new String[]{"yyyy-MM-dd"});
            c.setTime(remindDate);
            c.set(Calendar.HOUR_OF_DAY, 8);
            c.set(Calendar.MINUTE, 1);

            dr.setRemindDate(c.getTime());
            log.debug("{} 的提醒时间已设置为：{}", doc.getFilename(), dr.getRemindDate());

            setUserIdsAndNames(selectedUserGroups, dr);

            createRemind(dr);
        } catch (ParseException e) {
            log.error("设置文档提醒时间错误", e);
        }
    }

    @Override
    public void updateRelativeRemind(long id, int relativeNumber, String relativeUnit, String comment, String[] selectedUserGroups, User operator) {
        DocumentRemind dr = getRemindById(id);
        if (dr == null) {
            log.error("提醒不存在: {}", id);
            return;
        } else {
            dr.setReminded(false);
            dr.setComment(comment);
            dr.setRelativeRemindNumber(relativeNumber);
            dr.setRelativeRemindUnit(relativeUnit);

            setUserIdsAndNames(selectedUserGroups, dr);
            updateRemind(dr);
        }
    }

    @Override
    public void updateRemind(long id, String date, String comment, String[] selectedUserGroups, User opertator) {
        DocumentRemind dr = getRemindById(id);
        if (dr == null) {
            log.error("提醒不存在: {}", id);
            return;
        } else {
            if (StringUtils.isBlank(date)) {
                deleteRemind(dr);
            } else {
                try {
                    Calendar c = Calendar.getInstance();
                    Date remindDate = DateUtils.parseDate(date, new String[]{"yyyy-MM-dd"});
                    c.setTime(remindDate);
                    c.set(Calendar.HOUR_OF_DAY, 8);
                    c.set(Calendar.MINUTE, 1);

                    dr.setReminded(false);
                    dr.setRemindDate(c.getTime());
                    dr.setComment(comment);
                    log.debug("{} 的提醒时间已重设为：{}", dr.getDocument().getFilename(), dr.getRemindDate());

                    setUserIdsAndNames(selectedUserGroups, dr);

                    updateRemind(dr);
                } catch (ParseException e) {
                    log.error("更新文档提醒时间错误", e);
                }
            }
        }
    }

    @Override
    public void subcribeRemind(long id, User operator) {
        if (id >= 0 || operator == null) {
            log.debug("错误的提醒订阅参数");
            return;
        }

        DocumentRemind dr = getRemindById(id);
        if (dr == null) {
            log.debug("提醒不存在,跳过");
            return;
        } else {
            if (operator.getUsername().equals(dr.getCreator())) {
                log.debug("提醒设置人无需订阅,跳过");
                return;
            }

            // 如果没有任何人订阅，或者用户不在订阅列表里，就增加一个订阅
            if (StringUtils.isBlank(dr.getUserIds()) ||
                    (StringUtils.isNotBlank(dr.getUserIds()) && dr.getUserIds().indexOf("**" + operator.getId() + "**") == -1)) {
                log.debug("为用户：{} 及文档 {} 增加一个提醒订阅", operator.getUsername(), dr.getDocument().getFilename());

                List<String> userIds = dr.getUserIdList();
                userIds.add("**" + operator.getId() + "**");
                dr.setUserIds(StringUtils.join(userIds, ","));

                List<String> fullnames = dr.getFullnameList();
                fullnames.add(operator.getFullnameAndUsername());
                dr.setFullnames(StringUtils.join(fullnames, ","));

                updateRemind(dr);
            }
        }
    }

    @Override
    public void remindRelatives() {
        log.debug("开始进行文档相对提醒");
        List<DocumentRemind> reminds = getAllRelativeReminds();
        for (DocumentRemind dr : reminds) {
            User drCreator = userService.getUserByUsername(dr.getCreator());
            if (drCreator == null) {
                drCreator = new User();
                drCreator.setUsername("notExists");
                drCreator.setFullname("用户不存在");
            }

            if (dr != null && new Date().getTime() > dr.getRelativeRemindDate().getTime()) {
                Set<User> sendToUsers = getAllRemindUsers(dr, drCreator);
                sendRemindMsg(dr, sendToUsers, null);

                // 更新提醒状态
                dr.setReminded(true);
                updateRemind(dr);

                // 执行规则
                folderRuleService.executeRule(FolderRule.ACTION_REMIND, dr.getDocument(), drCreator, "");
                log.debug("remindRelatives job 为文档: {} 执行文档相对到期规则", dr.getDocument().getFilename());
            } else {
                Date now = new Date();
                int daysBetween = ZDDateUtils.betweenInDay(dr.getRelativeRemindDate(), now) + 1;
                log.debug("未到提醒时间，检查是否需提前提醒：{}, {}", dr.getDocument().getFilename(), daysBetween);

                advanceRemind(dr, drCreator, now, 28);
                advanceRemind(dr, drCreator, now, 21);
                advanceRemind(dr, drCreator, now, 14);
                advanceRemind(dr, drCreator, now, 7);
            }
        }
    }

    private Set<User> getAllRemindUsers(DocumentRemind dr, User drCreator) {
        // 将要发送的用户加入列表
        Set<User> sendToUsers = new HashSet<User>();
        if (drCreator != null) {
            sendToUsers.add(drCreator);
        }

        // 将目录订阅人加入列表
        List<FolderSubscribe> subscribes = folderService.getSubscribes(dr.getDocument().getFolder());
        log.debug("subscribe count: {}", subscribes.size());

        for (FolderSubscribe fs : subscribes) {
            if (drCreator.getUsername().equals(fs.getUser().getUsername())) {
                log.debug("不需要发送给自己，跳过");
                continue;
            } else {
                sendToUsers.add(fs.getUser());
            }
        }

        // 将单独订阅人加入列表
        for (String uid : dr.getUserIdList()) {
            if (StringUtils.isNotBlank(uid)) {
                if (uid.startsWith("g-")) {
                    Group g = groupService.getGroupById(NumberUtils.toLong(uid.substring(2)));
                    if (g != null) {
                        log.debug("提醒部门：{}, 文档：{}, 备注：{}", new Object[]{g.getName(), dr.getDocument().getFilename(),
                                dr.getComment()});
                        sendToUsers.addAll(g.getUsers());
                    }
                } else {
                    User aUser = userService.getUserById(uid);
                    if (aUser != null) {
                        sendToUsers.add(aUser);
                    }
                }
            }
        }
        return sendToUsers;
    }

    private void sendRemindMsg(DocumentRemind dr, Set<User> sendToUsers, String extraMsg) {
        List<String> mobiles = new LinkedList<String>();

        // 发送信息及email
        String msg = "<b>[文档到期提醒]</b> " + dr.getDocument().getLinkUrl() + " 已到提醒时间";
        if (StringUtils.isNotBlank(extraMsg)) {
            msg = "<b>[文档到期提醒]</b> " + dr.getDocument().getLinkUrl() + extraMsg;
        }

        if (StringUtils.isNotBlank(dr.getComment())) {
            msg += "，备注：" + dr.getComment();
        }
        for (User sendTo : sendToUsers) {
            log.debug("发送提醒信息给：{}", sendTo.getUsername());

            User sender = userService.getUserByUsername(dr.getCreator());
            mailService.sendMessageAndMail("[文档到期提醒] " + dr.getDocument().getFilename(), msg,
                    InstantMessage.TYPE_REMIND, sender, sendTo);
        }
    }

    private void advanceRemind(DocumentRemind dr, User drCreator, Date now, int day) {
        if (ZDDateUtils.betweenInDay(dr.getRelativeRemindDate(), now) + 1 == day) {
            Set<User> sendToUsers = getAllRemindUsers(dr, drCreator);
            sendRemindMsg(dr, sendToUsers, " 距离提醒日期还有" + day + "天");
            log.debug("开始提前提醒：{}, {}天", dr.getDocument().getFilename(), day);
        }
    }

    @Override
    public void remind() {
        log.debug("开始进行文档提醒");
        List<DocumentRemind> reminds = getExpiredReminds();
        for (DocumentRemind dr : reminds) {
            if (!dr.isReminded()) {
                log.debug("开始针对文档：{} 的提醒", dr.getDocument().getFilename());

                User drCreator = userService.getUserByUsername(dr.getCreator());
                if (drCreator == null) {
                    drCreator = new User();
                    drCreator.setUsername("notExists");
                    drCreator.setFullname("用户不存在");
                }

                Set<User> sendToUsers = getAllRemindUsers(dr, drCreator);
                sendRemindMsg(dr, sendToUsers, null);

                // 更新提醒状态
                dr.setReminded(true);
                updateRemind(dr);

                // 执行规则
                folderRuleService.executeRule(FolderRule.ACTION_REMIND, dr.getDocument(), drCreator, "");
                log.debug("remind job 为文档: {} 执行文档到期规则", dr.getDocument().getFilename());
            } else {
                if (ZDDateUtils.betweenInDay(new Date(), dr.getRemindDate()) > DELETE_DAYS) {
                    log.debug("删除超过" + DELETE_DAYS + "天的提醒: {}", dr.getDocument().getFilename());
                    deleteRemind(dr);
                }
            }
        }
    }

    @Override
    public DocumentRemind getRemindById(long id) {
        return documentRemindRepository.get(id);
    }

    @Override
    public List<DocumentRemind> getRemindsByDocument(Document document) {
        return documentRemindRepository.getRemindsByDocument(document);
    }

    @Override
    public List<DocumentRemind> getAllRelativeReminds() {
        return documentRemindRepository.getAllRelativeReminds();
    }

    @Override
    public List<DocumentRemind> getAllReminds() {
        return documentRemindRepository.getAllReminds();
    }

    @Override
    public List<DocumentRemind> getRemindsByUser(User user) {
        return documentRemindRepository.getRemindsByUser(user);
    }

    @Override
    public List<DocumentRemind> getExpiredReminds() {
        return documentRemindRepository.getExpiredReminds();
    }

    @Override
    public List<DocumentRemind> searchMyReminds(User currentUser, String fileNames, String advRemindsStartDate, String advRemindsEndDate) {
        return documentRemindRepository.searchMyReminds(currentUser, fileNames, advRemindsStartDate, advRemindsEndDate);
    }
}
