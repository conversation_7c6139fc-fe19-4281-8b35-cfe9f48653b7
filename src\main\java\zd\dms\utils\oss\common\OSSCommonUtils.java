package zd.dms.utils.oss.common;

import lombok.extern.slf4j.Slf4j;
import zd.base.utils.system.SpringUtils;

import java.io.File;
import java.io.InputStream;

@Slf4j
public class OSSCommonUtils {

    public static AbstractOSSUtils abstractOSSUtils;

    static {
        init();
    }

    private static void init() {
        String ossSdkType = SpringUtils.getProps("zd.oss.config.sdk_type", "aws3");
        log.debug("init ossSdkType:{}", ossSdkType);
        if ("minio".equals(ossSdkType)) {
            abstractOSSUtils = new OSSMinIOUtils();
        } else {
            abstractOSSUtils = new OSSAWS3Utils();
        }
    }

    public static boolean doUploadToOSS(File uploadFile, String objectKey, int count) {
        return abstractOSSUtils.doUploadToOSS(uploadFile, objectKey, count);
    }

    public static File downloadFileFromOSS(String objectKey) {
        return abstractOSSUtils.downloadFileFromOSS(objectKey);
    }

    public static InputStream downloadInputStreamFromOSS(String objectKey) {
        return abstractOSSUtils.downloadInputStreamFromOSS(objectKey);
    }

    public static boolean deleteFileFromOSS(String objectKey) {
        return abstractOSSUtils.deleteFileFromOSS(objectKey);
    }

    public static void deleteUserTasksFile() {
        abstractOSSUtils.deleteUserTasksFile();
    }

    public static boolean exists(String objectKey) {
        return abstractOSSUtils.exists(objectKey);
    }

    public static long getLastModifiedEpochMilli(String objectKey) {
        return abstractOSSUtils.getLastModifiedEpochMilli(objectKey);
    }
}
