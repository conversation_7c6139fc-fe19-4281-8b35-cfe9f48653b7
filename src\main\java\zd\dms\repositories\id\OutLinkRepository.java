package zd.dms.repositories.id;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.OutLink;

import java.util.List;

public interface OutLinkRepository extends BaseRepository<OutLink, String> {

    List<OutLink> getExpired();

    List<OutLink> getReachedLimit();

    List<OutLink> getOutLinksByUser(String username);

    OutLink getOutLinkByUrl(String url);

    Page getOutLinksPageByUser(String username, int pageNumber, int pageSize);

    Page getOutLinksPage(int pageNumber, int pageSize);
}
