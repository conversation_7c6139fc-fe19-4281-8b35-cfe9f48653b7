package zd.dms.repositories.mail.impl;

import jakarta.persistence.EntityManager;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.MailQueueItem;
import zd.dms.repositories.mail.MailQueueRepository;
import zd.dms.utils.PageableUtils;

import java.util.List;

public class MailQueueRepositoryDaoImpl extends BaseRepositoryDaoImpl<MailQueueItem, String> implements MailQueueRepository {

    public MailQueueRepositoryDaoImpl(Class<MailQueueItem> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<MailQueueItem> getWaitFailedMailQueue() {
        return findAll("from MailQueueItem where status = 0 or status = 3");
    }

    @Override
    public void clearQueue() {
        executeUpdate("delete from MailQueueItem");
    }

    @Override
    public int getFailedMailQueueCount() {
        return NumberUtils.toInt(String.valueOf(findObject("select count(*) from MailQueueItem where status = 3",
                new Object[]{})));
    }

    @Override
    public Page getPageMailQueue(int pageNumber, int pageSize) {
        return findAll(PageableUtils.getPageable(pageNumber, pageSize));
    }
}
