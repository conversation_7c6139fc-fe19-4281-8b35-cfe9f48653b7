package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.entities.AbstractEntity;

import java.util.Date;


/**
 * 文档模板
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "doctpl")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class DocumentTemplate extends AbstractEntity {

	private static final Logger log = LoggerFactory.getLogger(DocumentTemplate.class);

	/**
	 * serial
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 流程定义名称
	 */
	private String filename;

	private String creatorFullname;

	@Column(length = Length.LOB_DEFAULT)
	private String permissions;

	@Column(length = Length.LOB_DEFAULT)
	private String permissionsDisplay;

	/**
	 * 创建时间
	 */
	@Index(name = "i_dtpl_cd")
	@Column(nullable = false)
	private Date creationDate;

	public DocumentTemplate() {
		super();
		creationDate = new Date();
	}

	public String getExtension() {
		String ext = FilenameUtils.getExtension(filename);
		if (StringUtils.isNotBlank(ext)) {
			return StringUtils.lowerCase(ext);
		} else {
			return "";
		}
	}

	public String getFilename() {
		return filename;
	}

	public void setFilename(String filename) {
		this.filename = filename;
	}

	public String getCreatorFullname() {
		return creatorFullname;
	}

	public void setCreatorFullname(String creatorFullname) {
		this.creatorFullname = creatorFullname;
	}

	public String getPermissions() {
		return permissions;
	}

	public void setPermissions(String permissions) {
		this.permissions = permissions;
	}

	public String getPermissionsDisplay() {
		return permissionsDisplay;
	}

	public void setPermissionsDisplay(String permissionsDisplay) {
		this.permissionsDisplay = permissionsDisplay;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}
}
