package zd.base.utils.constant;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

public class ErrorCodeUtils {

	public static String getUndefinedErrorCode() {
		return ErrorCodeDefine.UNDEFINED_ERROR;
	}

	public static boolean hasDefined(String errorCode) {
		return ErrorCodeDefine.ERROR_CODE_MSG.containsKey(errorCode);
	}

	public static String getMsg(String errorCode) {
		if (ErrorCodeDefine.ERROR_CODE_MSG.containsKey(errorCode)) {
			return StringUtils.defaultIfBlank(MapUtils.getString(ErrorCodeDefine.ERROR_CODE_MSG, errorCode), errorCode);
		}

		return getMsg(ErrorCodeDefine.UNDEFINED_ERROR);
	}

}
