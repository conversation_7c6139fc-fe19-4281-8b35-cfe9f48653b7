package zd.dms.repositories.document.impl;

import jakarta.persistence.EntityManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.DocumentControlledVersion;
import zd.dms.repositories.document.DocumentControlledVersionRepository;
import zd.dms.utils.repositories.SpecTools;

public class DocumentControlledVersionRepositoryDaoImpl extends BaseRepositoryDaoImpl<DocumentControlledVersion, Long> implements DocumentControlledVersionRepository {

    public DocumentControlledVersionRepositoryDaoImpl(Class<DocumentControlledVersion> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public DocumentControlledVersion getDocControlledVersion(String docId, int revisionNumber, String publishVersion) {
        Specification<DocumentControlledVersion> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentControlledVersion> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.desc("creationDate");
            specTools.eq("docId", docId);
            if (revisionNumber > 0) {
                specTools.eq("revisionNumber", revisionNumber);
            }

            if (StringUtils.isNotBlank(publishVersion)) {
                specTools.eq("publishVersion", publishVersion);
            }

            return specTools.getRestriction();
        };

        return findSingleObject(spec);
    }
}
