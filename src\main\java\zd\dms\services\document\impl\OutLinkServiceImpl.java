package zd.dms.services.document.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.dms.entities.OutLink;
import zd.dms.repositories.id.OutLinkRepository;
import zd.dms.services.document.OutLinkService;

import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class OutLinkServiceImpl extends BaseJpaServiceImpl<OutLink, String> implements OutLinkService {

    private final OutLinkRepository outLinkRepository;

    @Override
    public BaseRepository<OutLink, String> getBaseRepository() {
        return outLinkRepository;
    }

    @Override
    public OutLink getOutLinkByUrl(String url) {
        return outLinkRepository.getOutLinkByUrl(url);
    }

    @Override
    public List<OutLink> getOutLinksByUser(String username) {
        return outLinkRepository.getOutLinksByUser(username);
    }

    @Override
    public Page getOutLinksPageByUser(String username, int pageNumber, int pageSize) {
        return outLinkRepository.getOutLinksPageByUser(username, pageNumber, pageSize);
    }

    @Override
    public Page getOutLinksPage(int pageNumber, int pageSize) {
        return outLinkRepository.getOutLinksPage(pageNumber, pageSize);
    }

    @Override
    public List<OutLink> getExpired() {
        return outLinkRepository.getExpired();
    }

    @Override
    public List<OutLink> getReachedLimit() {
        return outLinkRepository.getReachedLimit();
    }

    @Override
    public void deleteUseless() {
        List<OutLink> exps = getExpired();
        List<OutLink> reached = getReachedLimit();

        for (OutLink o : exps) {
            deleteOutLinkById(o.getId());
        }
        log.debug("deleteUseless exps: {}", exps.size());

        for (OutLink o : reached) {
            deleteOutLinkById(o.getId());
        }
        log.debug("deleteUseless reached: {}", reached.size());
    }

    @Override
    public void createOutLink(OutLink outLink) {
        String url = StringUtils.lowerCase(RandomStringUtils.random(4, true,
                true));
        while (getOutLinkByUrl(url) != null) {
            url = StringUtils
                    .lowerCase(RandomStringUtils.random(4, true, true));
        }
        outLink.setUrl(url);
        outLinkRepository.save(outLink);
    }

    @Override
    public void deleteOutLinkById(String id) {
        outLinkRepository.deleteById(id);
    }

    @Override
    public void updateOutLink(OutLink outLink) {
        try {
            outLinkRepository.update(outLink);
        } catch (Throwable t) {
            log.error("updateOutLink ex", t);
        }
    }
}
