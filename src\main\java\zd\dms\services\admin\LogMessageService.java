package zd.dms.services.admin;

import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.dms.entities.LogMessage;

import java.util.Date;
import java.util.List;

@Transactional
public interface LogMessageService extends BaseJpaService<LogMessage,Long> {

    @Transactional(readOnly = true)
    LogMessage getLogMessageById(Long id);

    List<LogMessage> getAllLogMessage();

    void saveLogMessage(LogMessage logMessage);

    void deleteLogMessage(LogMessage logMessage);

    void updateLogMessage(LogMessage logMessage);

    List<LogMessage> getLogMessageByOperator(String operator);

    List<LogMessage> getLogMessageByLevel(int level);

    List<LogMessage> getLogsByDate(Date startDate, Date endDate);

    void clearLogMessage();
}
