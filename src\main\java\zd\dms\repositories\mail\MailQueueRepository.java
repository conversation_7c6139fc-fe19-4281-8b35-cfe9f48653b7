package zd.dms.repositories.mail;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.MailQueueItem;

import java.util.List;

public interface MailQueueRepository extends BaseRepository<MailQueueItem, String> {

    List<MailQueueItem> getWaitFailedMailQueue();

    void clearQueue();

    int getFailedMailQueueCount();

    Page getPageMailQueue(int pageNumber, int pageSize);
}
