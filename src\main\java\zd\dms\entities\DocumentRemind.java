package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.entities.AbstractSequenceEntity;
import zd.base.utils.ZDDateUtils;
import zd.base.utils.system.SpringUtils;
import zd.dms.services.document.DocumentService;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * DocumentRemind Domain Object
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "document_remind")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class DocumentRemind extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = 1L;

    private static final Logger log = LoggerFactory.getLogger(DocumentRemind.class);

    /**
     * 提醒设定者全名
     */
    private String creatorFullname;

    /**
     * 订阅提醒用户id列表 使用**userid**分隔
     */
    @Column(length = Length.LOB_DEFAULT)
    private String userIds;

    /**
     * 阅读者全名
     */
    @Column(length = Length.LOB_DEFAULT)
    private String fullnames;

    /**
     * 提醒时间
     */
    private Date remindDate;

    /**
     * 是否已提醒
     */
    private boolean reminded;

    /**
     * 提醒类型，如果是modifiedDate，则比较修改时间
     */
    private String remindType;

    /**
     * 相对提醒时间，根据修改时间或其他条件，格式：1天,2月
     */
    private int relativeRemindNumber;
    private String relativeRemindUnit;

    /**
     * 提醒备注
     */
    @Column(name = "comment1")
    private String comment;

    private String docId;

    /**
     * 创建时间
     */
    @Index(name = "i_dremind_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 默认构造器
     */
    public DocumentRemind() {
        super();
        this.reminded = false;
        creationDate = new Date();
    }

    public Document getDocument() {
        return SpringUtils.getBean(DocumentService.class).getDocumentById(docId);
    }

    public String getRemindTypeString() {
        if (remindDate != null) {
            return "absoluteDate";
        } else if ("modifiedDate".equals(remindType)) {
            return "relativeModifiedDate";
        }

        return "";
    }

    public String getDisplayRemindDate() {
        if ("absoluteDate".equals(getRemindTypeString())) {
            return DateFormatUtils.format(remindDate, "yyyy年MM月dd日") + " 早9点";
        } else if ("relativeModifiedDate".equals(getRemindTypeString())) {
            return "更新时间 " + relativeRemindNumber + relativeRemindUnit + " 后";
        }

        return "";
    }

    public Date getRelativeRemindDate() {
        Document document = getDocument();
        if (document == null) {
            return null;
        }

        Date modifiedDate = document.getModifiedDate();

        if (relativeRemindNumber > 0 && StringUtils.isNotBlank(relativeRemindUnit)) {
            if ("天".equals(relativeRemindUnit)) {
                modifiedDate = DateUtils.addDays(modifiedDate, relativeRemindNumber);
            } else if ("月".equals(relativeRemindUnit)) {
                modifiedDate = DateUtils.addMonths(modifiedDate, relativeRemindNumber);
            } else if ("周".equals(relativeRemindUnit)) {
                modifiedDate = DateUtils.addWeeks(modifiedDate, relativeRemindNumber);
            } else if ("年".equals(relativeRemindUnit)) {
                modifiedDate = DateUtils.addYears(modifiedDate, relativeRemindNumber);
            }
        }

        modifiedDate = DateUtils.setMinutes(DateUtils.setHours(modifiedDate, 8), 1);

        return modifiedDate;
    }

    public int getRemainDays() {
        Date compareRemindDate = null;
        if ("absoluteDate".equals(getRemindTypeString())) {
            compareRemindDate = remindDate;
        } else if ("relativeModifiedDate".equals(getRemindTypeString())) {
            compareRemindDate = getRelativeRemindDate();
        }

        int remainDays = ZDDateUtils.betweenInDay(compareRemindDate, new Date());

        if (remainDays == 0) {
            remainDays = 1;
        } else if (remainDays < 0) {
            remainDays = 0;
        } else if (remainDays > 0) {
            remainDays += 1;
        }

        return remainDays;
    }

    public List<String> getFullnameList() {
        List<String> result = new LinkedList<String>();
        if (StringUtils.isBlank(getFullnames())) {
            return result;
        }

        String[] fullnameArray = getFullnames().split(",");
        for (String fullname : fullnameArray) {
            if (StringUtils.isNotBlank(fullname)) {
                fullname = fullname.trim();
                result.add(fullname);
            }
        }

        return result;
    }

    public List<String> getUserIdList() {
        List<String> result = new LinkedList<String>();
        if (StringUtils.isBlank(getUserIds())) {
            return result;
        }

        String[] userIdArray = getUserIds().split(",");
        for (String uid : userIdArray) {
            uid = uid.trim();
            if (uid.startsWith("**") && uid.endsWith("**")) {
                result.add(uid.substring(2, uid.length() - 2));
            }
        }

        return result;
    }

    public String getUserIds() {
        return userIds;
    }

    public void setUserIds(String usernames) {
        this.userIds = usernames;
    }

    public String getFullnames() {
        return fullnames;
    }

    public void setFullnames(String fullnames) {
        this.fullnames = fullnames;
    }

    public Date getRemindDate() {
        return remindDate;
    }

    public void setRemindDate(Date endDate) {
        this.remindDate = endDate;
    }

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getCreatorFullname() {
        return creatorFullname;
    }

    public void setCreatorFullname(String creatorFullname) {
        this.creatorFullname = creatorFullname;
    }

    public boolean isReminded() {
        return reminded;
    }

    public void setReminded(boolean reminded) {
        this.reminded = reminded;
    }

    public String getRemindType() {
        return remindType;
    }

    public void setRemindType(String remindType) {
        this.remindType = remindType;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = StringUtils.substring(comment, 0, 200);
    }

    public String getRelativeRemindUnit() {
        return relativeRemindUnit;
    }

    public void setRelativeRemindUnit(String relativeRemindUnit) {
        this.relativeRemindUnit = relativeRemindUnit;
    }

    public int getRelativeRemindNumber() {
        return relativeRemindNumber;
    }

    public void setRelativeRemindNumber(int relativeRemindNumber) {
        this.relativeRemindNumber = relativeRemindNumber;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
