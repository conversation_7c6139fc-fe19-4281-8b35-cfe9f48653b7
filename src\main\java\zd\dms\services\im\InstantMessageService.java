package zd.dms.services.im;

import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.dms.entities.Document;
import zd.dms.entities.InstantMessage;
import zd.dms.entities.User;
import zd.record.entities.RecFolder;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Transactional
public interface InstantMessageService extends BaseJpaService<InstantMessage, Long> {

    void sendMessage(String type, String sender, String senderFullname, String target, String targetFullname,
                     String title, String msg, User sendTo, String qywxLinkMsg, String redirectUrl);

    List<InstantMessage> searchMessages(String keyword, String type, String username);

    void receiveAllMsg(String username);

    void sendMessage(String type, String sender, String senderFullname, String target, String targetFullname,
                     String msg, User sendTo);

    void sendMessage(String type, String sender, String senderFullname, String target, String targetFullname,
                     String title, String msg, User sendTo);

    void pushDing(String type, String sendUserFullname, String targetDingEmployeeId, String targetUserFullname, String title,
                  String msg, String redirectUrl);

    void sendFolderSubcribeMessage(User sender, Document document, String msg);

    void deleteMessage(InstantMessage im);

    void deleteMessage(long id);

    void deleteMessagesByUsername(String username);

    void deleteMessagesBeforeDate(Date date);

    @Transactional(readOnly = true)
    List<InstantMessage> getMessagesUnread(String username);

    void setReceived(InstantMessage im);

    @Transactional(readOnly = true)
    Page getMessages(String type, String username, int pageNumber, int pageSize);

    @Transactional(readOnly = true)
    Page getReceviedMessages(String type, String username, int pageNumber, int pageSize);

    void sendRecSubcribeMessage(User sender, long recId, RecFolder recFolder, Map<String, Object> recordData,
                                String mainDisplayColValue, String msg);

    void sendRecSubcribeMessage(User sender, RecFolder recFolder, Map<String, Object> recordData, String msg);
}
