package zd.dms.repositories.document;

import zd.base.repositories.BaseRepository;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentNote;
import zd.dms.entities.User;

import java.util.Date;
import java.util.List;

/**
 * Log Data Access Object
 *
 * <AUTHOR>
 * @version $Revision$
 */
public interface DocumentNoteRepository extends BaseRepository<DocumentNote, Long> {

    List<DocumentNote> getDocumentNoteByDocument(Document doc);

    void deleteAllNotesByDocument(Document doc);

    void deleteNoteById(long id);

    int getUserNoteCountByDate(User user, Date startDate, Date endDate);
}
