<script setup lang="ts">
import {useDialogPluginComponent, useQuasar} from 'quasar';
import {onMounted, ref} from 'vue';
import {oauth2ClientApis} from 'utils/api/admin/oauth2/oauth2-client-apis';

const $props = defineProps({
  id: {
    type: String,
    default: '',
  }
})

defineEmits([...useDialogPluginComponent.emits]);

const {dialogRef, onDialogOK, onDialogHide, onDialogCancel} =
  useDialogPluginComponent();

const $q = useQuasar();

const clientName = ref<string | null>(null);
const clientId = ref<string | null>(null);
const clientSecret = ref<string | null>(null);
const enabled = ref<boolean | null>(null);
const numIndex = ref<number | null>(null);

const oauth2Client = ref<any>(null);

const initOAuth2Client = async () => {
  oauth2Client.value = await oauth2ClientApis.get($props.id);
  clientName.value = oauth2Client.value.clientName;
  clientId.value = oauth2Client.value.clientId;
  enabled.value = oauth2Client.value.enabled;
}

const onUpdateOAuth2Client = async () => {
  const params = {
    id: $props.id,
    clientName: clientName.value,
    clientId: clientId.value,
    clientSecret: clientSecret.value,
    enabled: enabled.value,
  }

  try {
    await oauth2ClientApis.update(params);
  } catch (error: any) {
    return;
  }

  onDialogOK();
}

onMounted(async () => {
  await initOAuth2Client();
});
</script>

<template>
  <q-dialog ref="dialogRef" persistent @hide="onDialogHide">
    <q-card class="dialog-card" style="background-color: #F7F7F7;">
      <q-card-section>
        <span class="text-h6">编辑部门</span>
      </q-card-section>

      <q-card-section class="overflow-auto" style="max-height: calc(100vh - 190px)">
        <q-card class="full-width border-none box-shadow-none" style="background-color: #F7F7F7;">
          <q-card-section class="q-px-none q-pt-none">
            <q-card class="full-width">
              <q-card-section class="q-pb-none">
                <div class="row justify-evenly">
                  <q-input class="col-5" v-model="clientName" bottom-slots outlined dense>
                    <template #before>
                      <div class="q-input__label">
                        <span class="text-red-10">*&nbsp;</span>
                        <span>名称：</span>
                      </div>
                    </template>
                  </q-input>

                  <div class="col-5">
                    <div class="row items-center">
                      <div class="text-align-end zdInputLabel" style="width: 112px;">是否启动：</div>
                      <div class="col-grow">
                        <q-select 
                          v-model="enabled" 
                          :options="[{label: '启用', value: true}, {label: '禁用', value: false}]" 
                          emit-value
                          map-options
                          outlined 
                          hide-bottom-space
                          dense/>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="row justify-evenly">
                  <q-input class="col-5" v-model="clientId" bottom-slots outlined dense>
                    <template #before>
                      <div class="q-input__label">
                        <span class="text-red-10">*&nbsp;</span>
                        <span>client_id：</span>
                      </div>
                    </template>
                  </q-input>
                  <q-input class="col-5" v-model="clientSecret" bottom-slots outlined dense>
                    <template #before>
                      <div class="q-input__label">
                        <span>client_secret：</span>
                      </div>
                    </template>
                  </q-input>
                </div>
              </q-card-section>
            </q-card>
          </q-card-section>
        </q-card>
      </q-card-section>

      <q-card-actions align="right" class="q-pa-md">
        <q-btn label="确认" unelevated color="ng-theme" @click="onUpdateOAuth2Client"/>
        <q-btn label="取消" unelevated class="text-main-btn-cancel-theme" color="main-btn-cancel-theme"
               @click="onDialogCancel"/>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<style scoped lang="scss">
.dialog-card {
  width: 800px;
  max-width: 100vw;
}

.groupLabel {
  min-width: 100px;
  line-height: 36px;
  text-align: right;
  margin-right: 2px;
}
</style>
