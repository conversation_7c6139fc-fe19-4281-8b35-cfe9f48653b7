package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractEntity;
import zd.dms.utils.TextUtils;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "mailqueue")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class MailQueueItem extends AbstractEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = 3399247326203718763L;

    public static final int STATUS_WAIT = 0;

    public static final int STATUS_SUCCESS = 1;

    public static final int STATUS_SENDING = 2;

    public static final int STATUS_FAILED = 3;

    private String sendTo;

    private String replyTo;

    private String subject;

    @Column(length = Length.LOB_DEFAULT)
    private String content;

    private boolean html;

    private String attachments;

    @Column(length = Length.LOB_DEFAULT)
    private String attachments2;

    private int status;

    private String failReason;

    private String creatorFullname;

    private boolean noticeSender;

    private Integer sendingTimes;

    private Date lastSendTime;

    /**
     * 创建时间
     */
    @Index(name = "i_mqi_cd")
    @Column(nullable = false)
    private Date creationDate;

    public MailQueueItem() {
        super();
        this.status = STATUS_WAIT;
        creationDate = new Date();
    }

    public String getSendTo() {
        return sendTo;
    }

    public void setSendTo(String sendTo) {
        this.sendTo = sendTo;
    }

    public String getReplyTo() {
        return replyTo;
    }

    public void setReplyTo(String replyTo) {
        this.replyTo = replyTo;
    }

    public String getSubject() {
        return TextUtils.escapeXml(subject);
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public boolean isHtml() {
        return html;
    }

    public void setHtml(boolean html) {
        this.html = html;
    }

    public String getAttachments() {
        return attachments;
    }

    public void setAttachments(String attachments) {
        this.attachments = attachments;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getCreatorFullname() {
        return creatorFullname;
    }

    public void setCreatorFullname(String creatorFullname) {
        this.creatorFullname = creatorFullname;
    }

    public boolean isNoticeSender() {
        return noticeSender;
    }

    public void setNoticeSender(boolean noticeSender) {
        this.noticeSender = noticeSender;
    }

    public String getAttachments2() {
        return attachments2;
    }

    public void setAttachments2(String attachments2) {
        this.attachments2 = attachments2;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Integer getSendingTimes() {
        if (sendingTimes == null) {
            sendingTimes = 0;
        }

        return sendingTimes;
    }

    public void setSendingTimes(Integer sendingTimes) {
        this.sendingTimes = sendingTimes;
    }

    public Date getLastSendTime() {
        return lastSendTime;
    }

    public void setLastSendTime(Date lastSendTime) {
        this.lastSendTime = lastSendTime;
    }
}
