package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.entities.AbstractEntity;
import zd.dms.utils.TextUtils;

import java.util.Date;

/**
 * 文档签章
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "docsig")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class DocumentSignature extends AbstractEntity {

	private static final Logger log = LoggerFactory.getLogger(DocumentSignature.class);

	/**
	 * serial
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 名称
	 */
	@Column(unique = true, nullable = false)
	private String name;

	/**
	 * 后缀
	 */
	private String extension;

	/**
	 * 密码
	 */
	private String password;

	private String creatorFullname;

	private String position;

	@Column(length = Length.LOB_DEFAULT)
	private String permissions;

	@Column(length = Length.LOB_DEFAULT)
	private String permissionsDisplay;

	/**
	 * 创建时间
	 */
	@Index(name = "i_dsignature_cd")
	@Column(nullable = false)
	private Date creationDate;

	@Index(name = "i_dsignature_bun")
	private String belongUsername;

	public DocumentSignature() {
		super();
		creationDate = new Date();
	}

	public boolean isImage() {
		return "jpg".equalsIgnoreCase(extension) || "bmp".equalsIgnoreCase(extension) ||
				"png".equalsIgnoreCase(extension) || "gif".equalsIgnoreCase(extension);
	}

	public boolean isEsp() {
		return "esp".equalsIgnoreCase(extension);
	}

	public String getName() {
		return TextUtils.escapeXml(name);
	}

	public void setName(String filename) {
		this.name = filename;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String creatorFullname) {
		this.password = creatorFullname;
	}

	public String getPermissions() {
		return permissions;
	}

	public void setPermissions(String permissions) {
		this.permissions = permissions;
	}

	public String getPermissionsDisplay() {
		return permissionsDisplay;
	}

	public void setPermissionsDisplay(String permissionsDisplay) {
		this.permissionsDisplay = permissionsDisplay;
	}

	public String getExtension() {
		return StringUtils.lowerCase(extension);
	}

	public void setExtension(String extension) {
		this.extension = extension;
	}

	public String getCreatorFullname() {
		return creatorFullname;
	}

	public void setCreatorFullname(String creatorFullname) {
		this.creatorFullname = creatorFullname;
	}

	public String getPosition() {
		return TextUtils.escapeXml(position);
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}

	public String getBelongUsername() {
		return TextUtils.escapeXml(belongUsername);
	}

	public void setBelongUsername(String belongUsername) {
		this.belongUsername = belongUsername;
	}
}
