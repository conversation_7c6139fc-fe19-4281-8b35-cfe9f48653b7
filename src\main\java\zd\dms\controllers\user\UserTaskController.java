package zd.dms.controllers.user;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zd.base.annotation.ZDLog;
import zd.base.controllers.BaseController;
import zd.base.service.BaseJpaService;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ResponseEntityUtils;
import zd.dms.dto.PageResponse;
import zd.dms.entities.UserTask;
import zd.dms.services.user.UserTaskService;
import zd.dms.utils.usertask.UserTaskExportRecordsUtils;
import zd.dms.utils.usertask.UserTaskFileUtils;
import zd.dms.utils.usertask.UserTaskImportRecordsUtils;
import zd.dms.utils.usertask.UserTaskUtils;

import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "UserTaskController", description = "用户任务Controller")
@RequestMapping("/user/task")
public class UserTaskController extends BaseController<UserTask, String> {

    private final UserTaskService userTaskService;

    @Override
    public BaseJpaService<UserTask, String> getBaseJpaService() {
        return userTaskService;
    }

    @Operation(summary = "获取分页用户任务")
    @PostMapping("/getUserTaskPage")
    @ZDLog("获取分页用户任务")
    public JSONResultUtils<Object> getUserTaskPage(@RequestBody Map<String, Object> params) {

        String status = MapUtils.getString(params, "status", "");
        String type = MapUtils.getString(params, "type", "");
        int pageNumber = MapUtils.getIntValue(params, "pageNumber", 1);
        int pageSize = MapUtils.getIntValue(params, "pageSize", 10);

        Page userTasks = userTaskService.getUserTasks(getCurrentUsername(), type, status, pageNumber, pageSize);

        return successData(PageResponse.of(userTasks, "list"));
    }

    @Operation(summary = "获取分页用户任务")
    @PostMapping("/getUserTaskTypeCols")
    @ZDLog("获取分页用户任务")
    public JSONResultUtils<Object> getUserTaskTypeCols(@RequestBody Map<String, Object> params) {

        String status = MapUtils.getString(params, "status", "");
        String type = MapUtils.getString(params, "type", "");

        List<Map<String, Object>> userTaskColList = UserTaskUtils.getUserTaskColList(type, status);

        return successData(userTaskColList);
    }

    @Operation(summary = "删除用户任务")
    @PostMapping("/deleteUserTask")
    @ZDLog("删除用户任务")
    public JSONResultUtils<Object> deleteUserTask(@RequestBody Map<String, Object> params) {
        String id = MapUtils.getString(params, "id", "");
        UserTask userTask = userTaskService.getUserTask(id);
        if (userTask == null) {
            return error("任务未找到");
        }

        userTaskService.deleteUserTask(userTask);
        return success();
    }

    @Operation(summary = "重置任务")
    @PostMapping("/restart")
    @ZDLog("重置任务")
    public JSONResultUtils<Object> restart(@RequestBody Map<String, Object> params) {
        String id = MapUtils.getString(params, "id", "");
        UserTask userTask = userTaskService.getUserTask(id);
        if (userTask == null) {
            return error("任务未找到");
        }

        UserTaskUtils.restart(userTask);

        return success();
    }

    @Operation(summary = "下载文件")
    @PostMapping("/downloadFile")
    @ZDLog("下载文件")
    public ResponseEntity<byte[]> downloadFile(@RequestBody Map<String, Object> params) throws Exception {

        String id = MapUtils.getString(params, "id", "");
        if (StringUtils.isBlank(id)) {
            return ResponseEntityUtils.nullResult();
        }

        UserTask userTask = userTaskService.getUserTask(id);
        if (userTask == null) {
            return ResponseEntityUtils.nullResult();
        }

        String type = userTask.getType();
        File file = null;
        String filename = "";
        if (UserTaskImportRecordsUtils.USER_TASK_TYPE_IMPORT_RECORDS.equals(type)) {
            file = UserTaskFileUtils.downloadUserTaskFile(userTask, UserTaskImportRecordsUtils.importFileName);

            Map<String, Object> msgMap = userTask.getMsgMap();
            filename = MapUtils.getString(msgMap, "filename", userTask.getId());
        }

        if (UserTaskExportRecordsUtils.USER_TASK_TYPE_EXPORT_RECORDS.equals(type)) {
            file = UserTaskFileUtils.downloadUserTaskFile(userTask, UserTaskExportRecordsUtils.exportFileName);

            Map<String, Object> msgMap = userTask.getMsgMap();
            filename = MapUtils.getString(msgMap, "filename", userTask.getId());
            filename = filename + "-" + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") + ".xlsx";
        }

        if (file == null) {
            return ResponseEntityUtils.nullResult();
        }

        return ResponseEntityUtils.fileResult(filename, file);
    }
}
