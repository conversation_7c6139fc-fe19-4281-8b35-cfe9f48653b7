package zd.dms.repositories.sms.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.AliSmsQueue;
import zd.dms.repositories.sms.AliSmsQueueRepository;
import zd.dms.utils.repositories.PredicateUtils;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;

public class AliSmsQueueRepositoryDaoImpl extends BaseRepositoryDaoImpl<AliSmsQueue, String> implements AliSmsQueueRepository {

    public AliSmsQueueRepositoryDaoImpl(Class<AliSmsQueue> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<AliSmsQueue> getAllUnSendedAndExceptionSms() {
        Specification<AliSmsQueue> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<AliSmsQueue> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.or(PredicateUtils.equal(root, criteriaBuilder, "sendStatus", 0), PredicateUtils.equal(root, criteriaBuilder, "sendStatus", 3));

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public Page getAliSmsQueuePage(int pageNumber, int pageSize) {
        Specification<AliSmsQueue> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<AliSmsQueue> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }
}
