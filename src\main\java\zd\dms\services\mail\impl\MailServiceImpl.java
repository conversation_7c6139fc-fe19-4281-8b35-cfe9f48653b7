package zd.dms.services.mail.impl;

import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeUtility;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.domain.Page;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import zd.base.utils.system.PropsUtils;
import zd.base.utils.system.SystemInitUtils;
import zd.dms.Constants;
import zd.dms.entities.*;
import zd.dms.io.ZDIOUtils;
import zd.dms.repositories.document.DocumentRepository;
import zd.dms.repositories.mail.MailQueueRepository;
import zd.dms.repositories.mail.MailServerRepository;
import zd.dms.services.im.InstantMessageService;
import zd.dms.services.mail.MailService;
import zd.dms.services.mail.exception.NoMailServerException;
import zd.dms.services.security.RoleService;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.services.user.UserService;
import zd.dms.utils.IMUtils;
import zd.dms.utils.TextUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.util.*;

@RequiredArgsConstructor
@Service
@Slf4j
public class MailServiceImpl implements MailService {

    @Lazy
    @Autowired
    private InstantMessageService instantMessageService;

    private final DocumentRepository documentRepository;

    private final MailServerRepository mailServerRepository;

    private final MailQueueRepository mailQueueRepository;

    @Lazy
    @Autowired
    private UserService userService;

    @Lazy
    @Autowired
    private RoleService roleService;

    @Override
    public void sendMessageAndMailForDocAutoReminds(String title, List<String> recDisplay, String msg, String imType, User sender, User sendTo) {
        try {
            log.debug("sender: {}, sendto: {}", sender, sendTo);
            if (sender == null) {
                sender = new User();
                sender.setUsername(UserGroupUtils.ADMIN_USERNAME);
                sender.setFullname("系统管理员");
            }

            String imMsg = "<b style=\"color:red\">文档到期提醒</b><br/><b>提示标题：</b>" + title + "<br/>" +
                    "<b>提示内容：</b>" +
                    msg + "<br/>" + "<b>到期文档：</b><br/>" + StringUtils.join(recDisplay, "<br/>");
            instantMessageService.sendMessage(imType, sender.getUsername(), sender.getFullname(), sendTo.getUsername(),
                    sendTo.getFullname(), imMsg, sendTo);
        } catch (Throwable t) {
            log.debug("sendMessageAndMailForDocAutoReminds sendMessage error", t);
        }

        if (sendTo.getAcceptDocMailNotice() && StringUtils.isNotBlank(sendTo.getEmail())) {
            StringBuilder recSb = new StringBuilder();
            for (String s : recDisplay) {
                recSb.append(TextUtils.removeHtmlTags(s) + "<br/>");
            }
            String mailMsg = "<b>提示标题：</b>" + title + "<br/>" + "<b>提示内容：</b>" + TextUtils.removeHtmlTags(msg) +
                    "<br/>" + "<b>到期文档：</b><br/>" + recSb.toString() + "<br/><br/>" + "本邮件由" +
                    Constants.getProductShortName() + "系统自动发送，请勿回复。<br/>如需关闭邮件提醒，请在" +
                    Constants.getProductShortName() +
                    "“个人设置”中修改“是否接收提醒邮件”为否。";

            try {
                queueMail(sendTo.getEmail(), null, Constants.getProductShortName() + " [文档到期提醒]" + title, mailMsg,
                        true, null, sender.getFullname(), false);
            } catch (NoMailServerException e) {
                log.debug("sendMessageAndMailForDocAutoReminds No Mail Server! Ignore");
            }
        }
    }

    @Override
    public int resetAllMail() {
        List<MailQueueItem> mailQueue = this.getMailQueue();
        int count = 0;
        if (mailQueue != null) {
            for (MailQueueItem item : mailQueue) {
                if (item == null) {
                    continue;
                }

                if (MailQueueItem.STATUS_FAILED != item.getStatus()) {
                    continue;
                }

                try {
                    item.setStatus(MailQueueItem.STATUS_WAIT);
                    this.updateMailQueueItem(item);
                    count++;
                } catch (Throwable t) {
                    // ignore
                }
            }
        }

        return count;
    }

    @Override
    public void sendMessageAndMail(String mailTitle, String msg, String imType, User sender, User sendTo, String qywxLinkMsg, String redirectUrl) {
        if (sendTo == null) {
            return;
        }

        try {
            log.debug("sender: {}, sendto: {}", sender, sendTo);
            if (sender == null) {
                sender = new User();
                sender.setUsername(UserGroupUtils.ADMIN_USERNAME);
                sender.setFullname("系统管理员");
            }

            instantMessageService.sendMessage(imType, sender.getUsername(), sender.getFullname(), sendTo.getUsername(),
                    sendTo.getFullname(), mailTitle, msg, sendTo, qywxLinkMsg, redirectUrl);
        } catch (Throwable t) {
        }

        if (sendTo.getAcceptDocMailNotice() && StringUtils.isNotBlank(sendTo.getEmail())) {
            String mailMsg = TextUtils.removeHtmlTags(IMUtils.replaceQuestionAndAnswer(msg, false, null)) +
                    "<br/><br/>" + "本邮件由" + Constants.getProductShortName() +
                    "系统自动发送，请勿回复。<br/>如需关闭邮件提醒，请在" +
                    Constants.getProductShortName() + "“个人设置”中修改“是否接收提醒邮件”为否。";

            try {
                queueMail(sendTo.getEmail(), null, Constants.getProductShortName() + " " + mailTitle, mailMsg, true,
                        null, sender.getFullname(), false);
            } catch (NoMailServerException e) {
                log.debug("No Mail Server! Ignore");
            }
        }
    }

    @Override
    public void sendMessageAndMail(String mailTitle, String msg, String imType, User sender, User sendTo) {
        sendMessageAndMail(mailTitle, msg, imType, sender, sendTo, null, null);
    }

    @Override
    public void sendMessageAndMailForAutoReminds(String title, List<String> recDisplay, String msg, String imType, User sender, User sendTo) {
        try {
            log.debug("sender: {}, sendto: {}", sender, sendTo);
            if (sender == null) {
                sender = new User();
                sender.setUsername(UserGroupUtils.ADMIN_USERNAME);
                sender.setFullname("系统管理员");
            }
            String imMsg = "<b style=\"color:red\">数据到期提醒</b><br/><b>提示标题：</b>" + title + "<br/>" +
                    "<b>提示内容：</b>" +
                    msg + "<br/>" + "<b>到期数据：</b><br/>" + StringUtils.join(recDisplay, "<br/>");
            instantMessageService.sendMessage(imType, sender.getUsername(), sender.getFullname(), sendTo.getUsername(),
                    sendTo.getFullname(), imMsg, sendTo);
        } catch (Throwable t) {
        }

        if (sendTo.getAcceptDocMailNotice() && StringUtils.isNotBlank(sendTo.getEmail())) {
            StringBuilder recSb = new StringBuilder();
            for (String s : recDisplay) {
                recSb.append(TextUtils.removeHtmlTags(s) + "<br/>");
            }
            String mailMsg = "<b>提示标题：</b>" + title + "<br/>" + "<b>提示内容：</b>" + TextUtils.removeHtmlTags(msg) +
                    "<br/>" + "<b>到期数据：</b><br/>" + recSb.toString() + "<br/><br/>" + "本邮件由" +
                    Constants.getProductShortName() + "系统自动发送，请勿回复。<br/>如需关闭邮件提醒，请在" +
                    Constants.getProductShortName() +
                    "“个人设置”中修改“是否接收提醒邮件”为否。";

            try {
                queueMail(sendTo.getEmail(), null, Constants.getProductShortName() + " [数据到期提醒]" + title, mailMsg,
                        true, null, sender.getFullname(), false);
            } catch (NoMailServerException e) {
                log.debug("No Mail Server! Ignore");
            }
        }
    }

    @Override
    public boolean doSendMail(MailServerInfo server, MailQueueItem item) {
        List<Document> attachments = new LinkedList<Document>();

        // 先读取旧附件id字段
        if (StringUtils.isNotBlank(item.getAttachments())) {
            String[] docIds = item.getAttachments().split(",");
            addDocAttachmentToList(attachments, docIds);
        }

        // 读取新附件id字段
        if (StringUtils.isNotBlank(item.getAttachments2())) {
            String[] docIds = item.getAttachments2().split(",");
            addDocAttachmentToList(attachments, docIds);
        }

        return doSendMail(server, item.getSendTo(), item.getReplyTo(), item.getSubject(), item.getContent(),
                item.isHtml(), attachments);
    }

    private void addDocAttachmentToList(List<Document> attachments, String[] docIds) {
        for (String docId : docIds) {
            if (StringUtils.isBlank(docId)) {
                continue;
            }

            boolean isLink = false;
            if (docId.startsWith("link_")) {
                docId = docId.substring(5);
                isLink = true;
            }

            Document doc = documentRepository.get(docId.trim());
            if (doc != null) {
                attachments.add(doc);
            }
        }
    }

    @Override
    public boolean doSendMail(MailServerInfo server, String sendTo, String replyTo, String subject, String content, boolean isHtml, List<Document> attachments) {
        log.debug("发送邮件：{}, {}, {}", new Object[]{server.getHost(), sendTo, subject});

        if (StringUtils.isBlank(sendTo)) {
            return false;
        }

        // 组织mail sender
        JavaMailSenderImpl sender = new JavaMailSenderImpl();
        sender.setHost(server.getHost());
        sender.setPort(server.getPort());
        String protocol = server.isUseSsl() ? "smtps" : "smtp";
        sender.setProtocol(protocol);

        if (StringUtils.isNotBlank(server.getUsername())) {
            sender.setUsername(server.getUsername());
        }

        if (StringUtils.isNotBlank(server.getPassword())) {
            sender.setPassword(server.getPassword());
        }

        Properties props = new Properties();
        props.setProperty("mail.smtp.debug", "true");
        if (StringUtils.isNotBlank(server.getUsername())) {
            props.setProperty("mail.smtp.auth", "true");

            // windows系统 如果非WINDOWS操作系统,需要将NTLM修改为digest方式;
            // “535 5.7.3 Authentication unsuccessful“
            if (PropsUtils.getBoolProps("mailMechanismsEnable", false)) {
                String mailMechanisms = PropsUtils.getProps("mailMechanisms", "NTLM");
                props.setProperty("mail.smtp.auth.mechanisms", mailMechanisms);
            }
        }
        if (server.isUseSsl() || server.getUseTls()) {
            props.setProperty("mail.smtp.starttls.enable", "true");
            props.setProperty("mail.smtp.ssl.protocols", "TLSv1.2,TLSv1.3");
        }
        sender.setJavaMailProperties(props);
        sender.setDefaultEncoding("utf-8");

        // 组织邮件
        MimeMessage message = sender.createMimeMessage();
        boolean multipartMail = attachments != null && attachments.size() > 0;
        MimeMessageHelper helper;
        try {
            helper = new MimeMessageHelper(message, multipartMail, "utf-8");
            helper.setSubject(subject);
            helper.setTo(sendTo.split(";"));
            helper.setFrom(server.getFromAddr());
            helper.setText(content, isHtml);

            if (StringUtils.isNotBlank(replyTo) && !"null".equals(replyTo)) {
                helper.setReplyTo(replyTo);
            }
        } catch (Throwable e) {
            log.error("组织邮件时出错", e);
            return false;
        }

        // 添加附件
        if (attachments != null) {
            for (Document d : attachments) {
                File oriFile = ZDIOUtils.getNewestDocFile(d);
                FileOutputStream output = null;

                if (oriFile.exists()) {
                    try {
                        FileSystemResource fileRes = null;
                        if (!ZDIOUtils.FE()) {
                            fileRes = new FileSystemResource(ZDIOUtils.getDocFile(d, d.getNewestVersion(), false));
                        } else {
                            log.debug("doSendMail：开始解密附件: {}", d.getFilename());
                            StopWatch sw1 = new StopWatch();
                            sw1.start();
                            File tempFile = new File(SystemInitUtils.getTempDir(), "mailtemp-" +
                                    RandomStringUtils.randomAlphanumeric(10));
                            output = new FileOutputStream(tempFile);
                            IOUtils.copy(ZDIOUtils.getDocInputStream(d, d.getNewestVersion()), new FileOutputStream(
                                    tempFile));

                            fileRes = new FileSystemResource(tempFile);
                            log.debug("解密耗时: {}ms", sw1.getTime());
                        }

                        // 减少文件名到36字节，防止附件文件名变为tcmime.xxxx
                        String newAttFilename = d.getFilename();
                        newAttFilename = StringUtils.abbreviateMiddle(newAttFilename, "..", 18);
                        helper.addAttachment(MimeUtility.encodeText(newAttFilename, "GBK", "B"), fileRes);
                    } catch (Throwable e) {
                        log.error("添加附件时出错", e);
                        return false;
                    } finally {
                        IOUtils.closeQuietly(output);
                    }
                }
            }
        }

        try {
            sender.send(message);
        } catch (Throwable e) {
            log.error("发送邮件时出错", e);
            return false;
        }

        return true;
    }

    @Override
    public boolean doSendMailUnified(MailServerInfo server, String sendTo, String replyTo, boolean html, List<MailQueueItem> itemList) {
        List<Document> attachments = new LinkedList<Document>();

        StringBuffer content = new StringBuffer();

        for (int i = 1; i <= itemList.size(); i++) {
            MailQueueItem item = itemList.get(i - 1);

            // 先读取旧附件id字段
            if (StringUtils.isNotBlank(item.getAttachments())) {
                String[] docIds = item.getAttachments().split(",");
                addDocAttachmentToList(attachments, docIds);
            }

            // 读取新附件id字段
            if (StringUtils.isNotBlank(item.getAttachments2())) {
                String[] docIds = item.getAttachments2().split(",");
                addDocAttachmentToList(attachments, docIds);
            }

            // 拼接内容
            String itemContent = item.getContent();
            if (itemContent == null) {
                itemContent = "";
            }

            if (i != itemList.size()) {
                String[] split = itemContent.split("<br/>");
                content.append(i + ". " + item.getSubject() + "<br/>" + split[0] + "<br/><br/>");
            } else {
                content.append(i + ". " + item.getSubject() + "<br/>" + itemContent);
            }
        }

        log.debug("doSendMailUnified sendTo:{},content:{}", sendTo, content.toString());
        return doSendMail(server, sendTo, replyTo,
                Constants.getProductShortName() + " 您收到来自系统的" + itemList.size() +
                        "条提醒", content.toString(), html, attachments);
    }

    @Override
    public void addServer(MailServerInfo info) {
        mailServerRepository.save(info);
    }

    @Override
    public void updateServer(MailServerInfo info) {
        mailServerRepository.update(info);
    }

    @Override
    public void deleteServer(MailServerInfo info) {
        mailServerRepository.delete(info);
    }

    public void deleteServer(String id) {
        mailServerRepository.deleteById(id);
    }

    public void deleteServers(List<String> ids) {
        mailServerRepository.deleteAllById(ids);
    }

    @Override
    public MailServerInfo getServerById(String id) {
        return mailServerRepository.get(id);
    }

    @Override
    public List<MailServerInfo> getMailServers() {
        return mailServerRepository.getAll();
    }

    @Override
    public List<MailQueueItem> getMailQueue() {
        return mailQueueRepository.getAll();
    }

    @Override
    public Page getPageMailQueue(int pageNumber, int pageSize) {
        return mailQueueRepository.getPageMailQueue(pageNumber, pageSize);
    }

    @Override
    public MailQueueItem getMail(String id) {
        return mailQueueRepository.get(id);
    }

    @Override
    public void deleteMailQueueItem(String id) {
        try {
            mailQueueRepository.deleteById(id);
        } catch (Throwable t) {
            log.error("deleteMailQueueItem ex", t);
        }
    }

    @Override
    public void deleteMailQueueItems(List<String> ids) {
        try {
            mailQueueRepository.deleteAllById(ids);
        } catch (Throwable t) {
            log.error("deleteMailQueueItems ex", t);
        }
    }

    @Override
    public void deleteAllMailQueueItem() {
        try {
            mailQueueRepository.deleteAll();
        } catch (Throwable t) {
            log.error("deleteAllMailQueueItem ex", t);
        }
    }

    @Override
    public void updateMailQueueItem(MailQueueItem item) {
        try {
            mailQueueRepository.update(item);
        } catch (Throwable t) {
            log.error("updateMailQueueItem ex", t);
        }
    }

    @Override
    public void addMailQueueItem(MailQueueItem item) {
        mailQueueRepository.save(item);
    }

    @Override
    public void queueMail(String sendTo, String replyTo, String subject, String content, boolean html, String attachments, String creatorFullname, boolean noticeSender) throws NoMailServerException {
        if (mailServerRepository.getEnabledServer().size() <= 0) {
            log.debug("没有可用的邮件服务器，不加入队列");
            throw new NoMailServerException();
        }

        if (StringUtils.isBlank(subject)) {
            return;
        }

        if (subject.length() > 255) {
            subject = StringUtils.abbreviate(subject, 250);
        }

        MailQueueItem item = new MailQueueItem();
        item.setSendTo(sendTo);
        item.setReplyTo(replyTo);
        item.setSubject(subject);
        item.setContent(content);
        item.setHtml(html);
        item.setAttachments2(attachments);
        item.setCreatorFullname(creatorFullname);
        item.setNoticeSender(noticeSender);

        try {
            addMailQueueItem(item);
        } catch (Throwable t) {
            log.error("queueMail ex", t);
        }
    }

    @Override
    public void clearQueue() {
        mailQueueRepository.clearQueue();
    }

    @Override
    public void remindFailedMails() {
        // 检查是否有失败
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);

        // 当前小时数与后台设置相同且为当前小时的05分时,执行失败邮件扫描
        if (hour == 9 && minute == 5) {
            int failedMailQueueCount = mailQueueRepository.getFailedMailQueueCount();
            if (failedMailQueueCount > 0) {
                // 得到所有的管理员
                List<User> usersInRole = userService.getUsersInRole();
                if (usersInRole == null || usersInRole.size() == 0) {
                    return;
                }

                for (User user : usersInRole) {
                    boolean systemAdmin = roleService.isUserGroupInRole(user, Role.SYSTEM_ADMIN);
                    boolean limitedAdmin = roleService.isUserGroupInRole(user, Role.LIMITED_ADMIN);
                    if (systemAdmin || limitedAdmin) {
                        String msg = "邮件队列中有 " + failedMailQueueCount +
                                " 封邮件发送失败，请检查邮件服务器设置和收件人邮箱是否正确!";
                        sendMessageAndMail("邮件发送失败警告", msg, InstantMessage.TYPE_NOTICE, null, user);
                        log.debug("remindFailedMails failedMailQueueCount:{}", failedMailQueueCount);
                    }
                }
            }
        }
    }
}