package zd.base.service.impl;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.base.service.BaseJpaService;


public abstract class BaseJpaServiceImpl<T, ID> implements BaseJpaService<T, ID> {

    /**
     * 返回当前类所属得 baseRepository
     *
     * @return baseRepository
     */
    public abstract BaseRepository<T, ID> getBaseRepository();

    @Override
    public Page<T> getBasePage(int pageNumber, int pageSize) {
        return getBaseRepository().getPage(pageNumber, pageSize);
    }

    @Override
    public void delete(T t) {
        getBaseRepository().delete(t);
    }

    @Override
    public void deleteById(ID id) {
        getBaseRepository().deleteById(id);
    }

    @Override
    public void deleteAllById(Iterable<? extends ID> ids) {
        getBaseRepository().deleteAllById(ids);
    }

    @Override
    public T findById(ID id) {
        try {
            return getBaseRepository().findObjectById(id);
        } catch (Exception e) {
            return null;
        }
    }
}
