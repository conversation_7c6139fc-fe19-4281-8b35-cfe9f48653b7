package zd.dms.controllers.api.sso;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class SSOUtils {

    private static final Logger log = LoggerFactory.getLogger(SSOUtils.class);

    private static final Map<String, SSOTicket> SSO_TICKETS = new ConcurrentHashMap<String, SSOTicket>();

    public static SSOTicket getTicket(String id) {
        clearExpiredTickets();
        SSOTicket result = SSO_TICKETS.get(id);
        if (result == null) {
            return null;
        }
        result.setGenDate(new Date());
        return result;
    }

    public static void deleteTicket(String id) {
        SSO_TICKETS.remove(id);
    }

    public static String genTicketObj(String username, String password, int validMin) {
        clearExpiredTickets();

        // 先检查是否已有ticket
        for (SSOTicket t : SSO_TICKETS.values()) {
            if (t.getUsername().equals(username)) {
                log.debug("SSO 用户：{} 使用现有ticket: {}", username, t.getTicketId());
                return t.getTicketId();
            }
        }

        // 如果没有，就重新生成一个
        String ticketId = RandomStringUtils.random(10, true, true);

        while (SSO_TICKETS.containsKey(ticketId)) {
            ticketId = RandomStringUtils.random(10, true, true);
        }

        SSOTicket ticket = new SSOTicket(username, password, validMin);
        ticket.setTicketId(ticketId);
        SSOUtils.addTicketToMap(ticket);

        return ticketId;
    }

    public static boolean addTicketToMap(SSOTicket ticket) {
        if (ticket == null) {
            return false;
        }

        if (StringUtils.isBlank(ticket.getTicketId())) {
            return false;
        }

        SSO_TICKETS.put(ticket.getTicketId(), ticket);
        return true;
    }

    public static void clearExpiredTickets() {
        for (String key : SSO_TICKETS.keySet()) {
            SSOTicket t = SSO_TICKETS.get(key);
            if (t != null && t.isExpired()) {
                SSO_TICKETS.remove(key);
                log.debug("删除过期Ticket: {}, username: {}", t.getTicketId(), t.getUsername());
            }
        }
    }
}
