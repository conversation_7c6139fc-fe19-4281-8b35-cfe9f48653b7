package zd.base.config.oauth2;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 默认授权类型过滤器
 * 用于在请求OAuth2令牌端点时，如果没有传递grant_type参数，则添加默认值
 */
@Slf4j
@Component
public class DefaultGrantTypeFilter extends OncePerRequestFilter {

    public static final String DEFAULT_GRANT_TYPE = "client_credentials";
    private static final String OAUTH2_TOKEN_ENDPOINT = "/oauth2/token";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // 只处理OAuth2令牌端点的请求
        if (isOAuth2TokenEndpoint(request)) {
            // 检查是否有grant_type参数
            String grantType = request.getParameter("grant_type");

            if (grantType == null || grantType.isEmpty()) {
                log.debug("OAuth2令牌请求没有grant_type参数，添加默认值: {}", DEFAULT_GRANT_TYPE);

                // 包装请求，添加默认的grant_type参数
                HttpServletRequest wrappedRequest = new DefaultGrantTypeRequestWrapper(request, DEFAULT_GRANT_TYPE);
                filterChain.doFilter(wrappedRequest, response);
                return;
            }
        }

        // 对于其他请求，直接放行
        filterChain.doFilter(request, response);
    }

    /**
     * 判断是否是OAuth2令牌端点的请求
     */
    private boolean isOAuth2TokenEndpoint(HttpServletRequest request) {
        return request.getRequestURI().equals(OAUTH2_TOKEN_ENDPOINT) &&
                "POST".equalsIgnoreCase(request.getMethod());
    }

    /**
     * 默认授权类型请求包装器
     * 用于在请求参数中添加默认的grant_type参数
     */
    private static class DefaultGrantTypeRequestWrapper extends jakarta.servlet.http.HttpServletRequestWrapper {

        private final Map<String, String[]> modifiedParameters;

        public DefaultGrantTypeRequestWrapper(HttpServletRequest request, String defaultGrantType) {
            super(request);

            // 复制原始请求参数
            Map<String, String[]> parameters = new HashMap<>(request.getParameterMap());

            // 添加默认的grant_type参数
            parameters.put("grant_type", new String[]{defaultGrantType});

            this.modifiedParameters = Collections.unmodifiableMap(parameters);
        }

        @Override
        public String getParameter(String name) {
            String[] values = getParameterValues(name);
            return (values != null && values.length > 0) ? values[0] : null;
        }

        @Override
        public Map<String, String[]> getParameterMap() {
            return modifiedParameters;
        }

        @Override
        public String[] getParameterValues(String name) {
            return modifiedParameters.get(name);
        }
    }
}
