package zd.dms.utils.oss.common;

import org.apache.commons.lang3.StringUtils;
import zd.base.utils.ZDUtils;
import zd.dms.entities.UserTask;
import zd.dms.services.user.UserTaskService;

import java.io.File;
import java.io.InputStream;

public abstract class AbstractOSSUtils {

    public abstract boolean doUploadToOSS(File uploadFile, String objectKey, int count);

    public abstract File downloadFileFromOSS(String objectKey);

    public abstract InputStream downloadInputStreamFromOSS(String objectKey);

    public abstract boolean deleteFileFromOSS(String objectKey);

    public abstract void deleteUserTasksFile();

    public abstract boolean exists(String objectKey);

    public abstract long getLastModifiedEpochMilli(String objectKey);

    public UserTask getUserTask(String objectName) {
        if (StringUtils.isBlank(objectName)) {
            return null;
        }

        String[] dirPaths = objectName.split("/");
        if (dirPaths.length < 7) {
            return null;
        }

        String userTaskId = dirPaths[6];
        return ZDUtils.getBean(UserTaskService.class).getUserTask(userTaskId);
    }
}
