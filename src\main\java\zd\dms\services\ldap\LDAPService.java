package zd.dms.services.ldap;

import org.springframework.transaction.annotation.Transactional;
import zd.dms.entities.Group;
import zd.dms.entities.User;
import zd.dms.exception.UserAmountExceededException;
import zd.dms.services.user.exception.UserDisabledException;
import zd.dms.services.user.exception.UserNotExistsException;
import zd.dms.services.user.exception.WrongPasswordException;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Transactional
public interface LDAPService {

    public static final String DEFAULT_PASSWD = "111222";

    void auth(String username, String password) throws UserNotExistsException, UserDisabledException,
            WrongPasswordException, LDAPException;

    Map<String, Map<String, String>> loadLDAPUsers(String adminUsername, String adminPassword) throws LDAPException;

    void importLDAPUsers(String adminUsername, String adminPassword, String[] usernames) throws LDAPException,
            UserAmountExceededException;

    /**
     * 获取部门集合
     *
     * @param adminUsername
     * @param adminPassword
     * @param groupDn
     * @return
     * @throws LDAPException
     */
    List<Map> loadLDAPGroups(String adminUsername, String adminPassword, String groupDn) throws LDAPException;

    /**
     * 获取部门下所有人员
     *
     * @param adminUsername
     * @param adminPassword
     * @param groupDn
     * @return
     */
    Map<String, Map> loadLDAPGroupAllUsers(String adminUsername, String adminPassword, String groupDn)
            throws LDAPException;

    /**
     * @param adminUsername
     * @param adminPassword
     * @param groupDn
     * @param selectGroup
     * @param includeParentGroup 部门是否导入到子级
     * @param importUsers        是否导入用户
     * @param ignoreSameUsername 已存在用户跳过或更新
     * @param forbidUser         系统中存在但域中不存在的用户是否禁用
     * @throws LDAPException
     * @throws UserAmountExceededException
     */
    void doImportLDAPGroups(String adminUsername, String adminPassword, String groupDn, Group selectGroup,
                            String includeParentGroup, boolean importUsers, boolean ignoreSameUsername, boolean forbidUser,
                            Set<User> ldapUserSet, List<String> ldapGuidlist) throws LDAPException, UserAmountExceededException;

    /**
     * 导入人员，同时创建部门
     *
     * @param adminUsername
     * @param adminPassword
     * @param ldapUsernames
     * @throws UserAmountExceededException
     */
    void importLDAPUsersWithGroups(String adminUsername, String adminPassword, String[] ldapUsernames, String groupDn)
            throws LDAPException, UserAmountExceededException;

    /**
     * 根据计算机名获取用户名
     *
     * @param computerName
     * @throws LDAPException
     */
    String getUsernameWithComputerName(String computerName) throws LDAPException;

    /**
     * ladp同步
     *
     * @param adminUsername
     * @param adminPassword
     * @param immediatelySync 是否手动同步
     * @throws LDAPException
     * @throws UserAmountExceededException
     */
    void syncLDAPGroupsAndUsers(String adminUsername, String adminPassword, boolean immediatelySync)
            throws LDAPException, UserAmountExceededException;
}