package zd.dms.repositories.document;

import zd.base.repositories.BaseRepository;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentVersion;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DocumentVersionRepository extends BaseRepository<DocumentVersion, Long> {

    DocumentVersion getOldestDocumentVersion(Document doc);

    DocumentVersion getNewestDocumentVersion(Document doc);

    int getVersionCount(Document document);

    DocumentVersion getDocumentVersion(Document doc, int versionNumber);

    List<DocumentVersion> getDocumentVersionByDocument(Document doc, boolean withTempVersion, boolean asc);

    void deleteAllVersionsByDocument(Document doc);

    void deleteVersionByDocument(Document doc, int versionNumber);
}
