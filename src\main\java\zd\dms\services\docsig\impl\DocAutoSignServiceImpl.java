package zd.dms.services.docsig.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.dms.entities.DocAutoSign;
import zd.dms.repositories.docsig.DocAutoSignRepository;
import zd.dms.services.docsig.DocAutoSignService;

import java.io.IOException;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class DocAutoSignServiceImpl extends BaseJpaServiceImpl<DocAutoSign, String> implements DocAutoSignService {

    private final DocAutoSignRepository docAutoSignRepository;

    @Override
    public BaseRepository<DocAutoSign, String> getBaseRepository() {
        return docAutoSignRepository;
    }

    @Override
    public DocAutoSign getById(String id) {
        return docAutoSignRepository.get(id);
    }

    @Override
    public List<DocAutoSign> getAllDocAutoSign() {
        return docAutoSignRepository.getAll();
    }

    @Override
    public List<DocAutoSign> getAllSortAutoSign() {
        return docAutoSignRepository.getAllSortAutoSign();
    }

    @Override
    public void delete(String id) throws IOException {
        DocAutoSign docAutoSign = getById(id);

        if (docAutoSign != null) {
            delete(docAutoSign);
        }
    }

    @Override
    public void create(DocAutoSign docAutoSign) {
        docAutoSignRepository.save(docAutoSign);
    }

    @Override
    public void update(DocAutoSign docAutoSign) {
        docAutoSignRepository.update(docAutoSign);
    }

    @Override
    public List<DocAutoSign> getAutoSignByWidthAndHeight(int width, int height) {
        return docAutoSignRepository.getAutoSignByWidthAndHeight(width, height);
    }
}
