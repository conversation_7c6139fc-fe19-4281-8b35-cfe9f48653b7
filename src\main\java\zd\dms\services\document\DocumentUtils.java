package zd.dms.services.document;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.analysus.ja.dict.ModeDefinition;
import zd.base.context.UserContextHolder;
import zd.base.utils.system.PropsUtils;
import zd.base.utils.system.SpringUtils;
import zd.dms.entities.*;
import zd.dms.repositories.document.DocumentRelationRepository;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.security.FolderPermissionUtils;
import zd.dms.services.security.RoleService;
import zd.dms.services.security.RoleUtils;

import java.util.*;

/**
 * Property Utils
 *
 * <AUTHOR>
 */
public class DocumentUtils {

    public static Map<String, String> docIconMap = new HashMap<String, String>();

    public static Map<String, String> bigDocIconMap = new HashMap<String, String>();

    public static Set<String> autoVueExtMap = new HashSet<String>();

    public static final int docPreviewContentNumber = PropsUtils.getIntProps("docPreviewContentNumber", 500);

    private static FolderService folderService = SpringUtils.getBean(FolderService.class);

    static {
        autoVueExtMap.add("iam");
        autoVueExtMap.add("shp");
        autoVueExtMap.add("j2k");
        autoVueExtMap.add("dpf");
        autoVueExtMap.add("gz");
        autoVueExtMap.add("dpl");
        autoVueExtMap.add("hpg");
        autoVueExtMap.add("rgb");
        autoVueExtMap.add("j2c");
        autoVueExtMap.add("sdpc");
        autoVueExtMap.add("ppm");
        autoVueExtMap.add("cmx");
        autoVueExtMap.add("res");
        autoVueExtMap.add("cmp");
        autoVueExtMap.add("mac");
        autoVueExtMap.add("neu");
        autoVueExtMap.add("prt");
        autoVueExtMap.add("asm");
        autoVueExtMap.add("gp4");
        autoVueExtMap.add("max");
        autoVueExtMap.add("sdac");
        autoVueExtMap.add("shx");
        autoVueExtMap.add("ref");
        autoVueExtMap.add("907");
        autoVueExtMap.add("g4");
        autoVueExtMap.add("ipt");
        autoVueExtMap.add("dbk");
        autoVueExtMap.add("cot");
        autoVueExtMap.add("ssm");
        autoVueExtMap.add("dst");
        autoVueExtMap.add("pcx");
        autoVueExtMap.add("sda");
        autoVueExtMap.add("ncd");
        autoVueExtMap.add("fsm");
        autoVueExtMap.add("vda");
        autoVueExtMap.add("psm");
        autoVueExtMap.add("bi");
        autoVueExtMap.add("mcm");
        autoVueExtMap.add("sdp");
        autoVueExtMap.add("dbx");
        autoVueExtMap.add("cpm");
        autoVueExtMap.add("ste");
        autoVueExtMap.add("pci");
        autoVueExtMap.add("dtx");
        autoVueExtMap.add("mdd");
        autoVueExtMap.add("xml");
        autoVueExtMap.add("stp");
        autoVueExtMap.add("stl");
        autoVueExtMap.add("pcl");
        autoVueExtMap.add("dat");
        autoVueExtMap.add("eps");
        autoVueExtMap.add("iff");
        autoVueExtMap.add("pcb");
        autoVueExtMap.add("sch");
        autoVueExtMap.add("osm");
        autoVueExtMap.add("ifc");
        autoVueExtMap.add("pts");
        autoVueExtMap.add("odb");
        autoVueExtMap.add("scm");
        autoVueExtMap.add("oda");
        autoVueExtMap.add("xpr");
        autoVueExtMap.add("gds");
        autoVueExtMap.add("model");
        autoVueExtMap.add("ddb");
        autoVueExtMap.add("edn");
        autoVueExtMap.add("ico");
        autoVueExtMap.add("edf");
        autoVueExtMap.add("rar");
        autoVueExtMap.add("ras");
        autoVueExtMap.add("par");
        autoVueExtMap.add("cab");
        autoVueExtMap.add("drl");
        autoVueExtMap.add("dra");
        autoVueExtMap.add("cal");
        autoVueExtMap.add("nrf");
        autoVueExtMap.add("sdw");
        autoVueExtMap.add("pad");
        autoVueExtMap.add("drw");
        autoVueExtMap.add("idw");
        autoVueExtMap.add("hrf");
        autoVueExtMap.add("svg");
        autoVueExtMap.add("idv");
        autoVueExtMap.add("prt");
        autoVueExtMap.add("lay");
        autoVueExtMap.add("gcm");
        autoVueExtMap.add("paf");
        autoVueExtMap.add("frm");
        autoVueExtMap.add("ai");
        autoVueExtMap.add("lbm");
        autoVueExtMap.add("ide");
        autoVueExtMap.add("dsn");
        autoVueExtMap.add("prn");
        autoVueExtMap.add("prj");
        autoVueExtMap.add("pr");
        autoVueExtMap.add("gcd");
        autoVueExtMap.add("ps");
        autoVueExtMap.add("catpart");
        autoVueExtMap.add("sab");
        autoVueExtMap.add("x_t");
        autoVueExtMap.add("cct");
        autoVueExtMap.add("wmf");
        autoVueExtMap.add("cshp");
        autoVueExtMap.add("pho");
        autoVueExtMap.add("rpm");
        autoVueExtMap.add("gbl");
        autoVueExtMap.add("plmxl");
        autoVueExtMap.add("gbr");
        autoVueExtMap.add("dgn");
        autoVueExtMap.add("arj");
        autoVueExtMap.add("brd");
        autoVueExtMap.add("dgm");
        autoVueExtMap.add("dxf");
        autoVueExtMap.add("edif");
        autoVueExtMap.add("attr");
        autoVueExtMap.add("cdr");
        autoVueExtMap.add("sbk");
        autoVueExtMap.add("svgz");
        autoVueExtMap.add("sldasm");
        autoVueExtMap.add("asc");
        autoVueExtMap.add("bsm");
        autoVueExtMap.add("sat");
        autoVueExtMap.add("session");
        autoVueExtMap.add("jp2");
        autoVueExtMap.add("tar");
        autoVueExtMap.add("emf");
        autoVueExtMap.add("slddrw");
        autoVueExtMap.add("asm");
        autoVueExtMap.add("dxb");
        autoVueExtMap.add("x_b");
        autoVueExtMap.add("bdl");
        autoVueExtMap.add("dwg");
        autoVueExtMap.add("emn");
        autoVueExtMap.add("dwf");
        autoVueExtMap.add("hgl");
        autoVueExtMap.add("p21");
        autoVueExtMap.add("emp");
        autoVueExtMap.add("cg4");
        autoVueExtMap.add("jpc");
        autoVueExtMap.add("deb");
        autoVueExtMap.add("igs");
        autoVueExtMap.add("2x2");
        autoVueExtMap.add("igr");
        autoVueExtMap.add("2x3");
        autoVueExtMap.add("cel");
        autoVueExtMap.add("emz");
        autoVueExtMap.add("gds2");
        autoVueExtMap.add("pdif");
        autoVueExtMap.add("iges");
        autoVueExtMap.add("mi");
        autoVueExtMap.add("7z");
        autoVueExtMap.add("dft");
        autoVueExtMap.add("min");
        autoVueExtMap.add("mil");
        autoVueExtMap.add("jpx");
        autoVueExtMap.add("jpf");
        autoVueExtMap.add("plmxml");
        autoVueExtMap.add("cut");
        autoVueExtMap.add("dproj");
        autoVueExtMap.add("olb");
        autoVueExtMap.add("lzh");
        autoVueExtMap.add("jt");
        autoVueExtMap.add("img");
        autoVueExtMap.add("mlr");
        autoVueExtMap.add("rle");
        autoVueExtMap.add("rlc");
        autoVueExtMap.add("a11");
        autoVueExtMap.add("cgm");
        autoVueExtMap.add("sld");
        autoVueExtMap.add("strm");
        autoVueExtMap.add("slb");
        autoVueExtMap.add("cgr");
        autoVueExtMap.add("plt");
        autoVueExtMap.add("pkg");
        autoVueExtMap.add("catproduct");
        autoVueExtMap.add("exp");
        autoVueExtMap.add("aew");
        autoVueExtMap.add("wrl");
        autoVueExtMap.add("tg4");
        autoVueExtMap.add("xas");
        autoVueExtMap.add("catdrawing");
        autoVueExtMap.add("tga");
        autoVueExtMap.add("cit");
        autoVueExtMap.add("bz2");
        autoVueExtMap.add("wpg");
        autoVueExtMap.add("zip");
        autoVueExtMap.add("pic");
        autoVueExtMap.add("xwd");
        autoVueExtMap.add("lib");
        autoVueExtMap.add("sldprt");
        autoVueExtMap.add("tgz");
        autoVueExtMap.add("step");
        autoVueExtMap.add("mdb");
    }

    static {
        bigDocIconMap.put("doc", "doc");
        bigDocIconMap.put("xls", "xls");
        bigDocIconMap.put("xlsx", "xlsx");
        bigDocIconMap.put("xlsm", "xlsm");
        bigDocIconMap.put("ppt", "ppt");
        bigDocIconMap.put("docx", "docx");
        bigDocIconMap.put("pptx", "pptx");
        bigDocIconMap.put("accdb", "accdb");
        bigDocIconMap.put("mdb", "mdb");
        bigDocIconMap.put("pub", "pub");
        bigDocIconMap.put("vsd", "vsd");
        bigDocIconMap.put("mht", "mht");
        bigDocIconMap.put("chm", "chm");
        bigDocIconMap.put("pdf", "pdf");

        bigDocIconMap.put("wps", "wps");
        bigDocIconMap.put("et", "et");
        bigDocIconMap.put("dps", "dps");

        bigDocIconMap.put("mp3", "mp3");
        bigDocIconMap.put("wav", "mp3");
        bigDocIconMap.put("wma", "mp3");

        bigDocIconMap.put("ai", "ai");
        bigDocIconMap.put("gif", "gif");
        bigDocIconMap.put("jpg", "jpg");
        bigDocIconMap.put("png", "png");
        bigDocIconMap.put("psd", "psd");
        bigDocIconMap.put("tif", "tif");
        bigDocIconMap.put("tiff", "tif");
        bigDocIconMap.put("bmp", "bmp");
        bigDocIconMap.put("cdr", "cdr");
        bigDocIconMap.put("eps", "eps");
        bigDocIconMap.put("max", "max");
        bigDocIconMap.put("ma", "maya");
        bigDocIconMap.put("mb", "maya");
        bigDocIconMap.put("xmind", "xmind");

        bigDocIconMap.put("dwg", "dwg");
        bigDocIconMap.put("dxf", "dwg");
        bigDocIconMap.put("stl", "stl");
        bigDocIconMap.put("sldprt", "solidworks");
        bigDocIconMap.put("slddrw", "solidworks");
        bigDocIconMap.put("sldasm", "solidworks");
        bigDocIconMap.put("prt", "prt");

        bigDocIconMap.put("rar", "rar");
        bigDocIconMap.put("zip", "rar");
        bigDocIconMap.put("exe", "exe");

        bigDocIconMap.put("cs", "cs");
        bigDocIconMap.put("css", "css");
        bigDocIconMap.put("htm", "htm");
        bigDocIconMap.put("html", "htm");
        bigDocIconMap.put("js", "js");
        bigDocIconMap.put("pcd", "pcd");
        bigDocIconMap.put("xml", "xml");
        bigDocIconMap.put("txt", "txt");
        bigDocIconMap.put("as", "as");
        bigDocIconMap.put("asp", "asp");
        bigDocIconMap.put("aspx", "aspx");
        bigDocIconMap.put("css", "css");
        bigDocIconMap.put("java", "java");
        bigDocIconMap.put("h", "h");
        bigDocIconMap.put("eml", "eml");

        bigDocIconMap.put("flv", "flv");
        bigDocIconMap.put("fla", "fla");
        bigDocIconMap.put("rmvb", "rmvb");
        bigDocIconMap.put("mp4", "rmvb");
        bigDocIconMap.put("mpg", "rmvb");
        bigDocIconMap.put("mpeg", "rmvb");
        bigDocIconMap.put("avi", "rmvb");
        bigDocIconMap.put("wmv", "rmvb");
        bigDocIconMap.put("rm", "rmvb");
        bigDocIconMap.put("swf", "swf");
    }

    static {
        docIconMap.put("doc", "doc");
        docIconMap.put("xls", "xls");
        docIconMap.put("ppt", "ppt");
        docIconMap.put("docx", "doc");
        docIconMap.put("xlsx", "xls");
        docIconMap.put("xlsm", "xlsm");
        docIconMap.put("pptx", "ppt");
        docIconMap.put("vsd", "vsd");
        docIconMap.put("wps", "wps");
        docIconMap.put("dps", "dps");
        docIconMap.put("et", "et");

        docIconMap.put("asp", "asp");
        docIconMap.put("aspx", "aspx");
        docIconMap.put("mht", "mht");
        docIconMap.put("xml", "xml");
        docIconMap.put("html", "htm");
        docIconMap.put("htm", "htm");
        docIconMap.put("java", "java");
        docIconMap.put("js", "js");
        docIconMap.put("pdf", "pdf");
        docIconMap.put("php", "php");
        docIconMap.put("rar", "rar");
        docIconMap.put("txt", "txt");
        docIconMap.put("zip", "zip");
        docIconMap.put("eml", "eml");

        docIconMap.put("ai", "ai");
        docIconMap.put("bmp", "bmp");
        docIconMap.put("gif", "gif");
        docIconMap.put("jpg", "jpg");
        docIconMap.put("png", "png");
        docIconMap.put("psd", "psd");
        docIconMap.put("tif", "tif");
        docIconMap.put("tiff", "tif");
        docIconMap.put("cdr", "cdr");
        docIconMap.put("eps", "eps");

        docIconMap.put("dwg", "cad");
        docIconMap.put("dxf", "cad");
        docIconMap.put("pcb", "pcb");
        docIconMap.put("prt", "prt");
        docIconMap.put("sldprt", "solidworks");
        docIconMap.put("slddrw", "solidworks");
        docIconMap.put("stl", "stl");
        docIconMap.put("max", "max");
        docIconMap.put("ma", "maya");
        docIconMap.put("mb", "maya");

        docIconMap.put("swf", "swf");
        docIconMap.put("flv", "flv");
        docIconMap.put("fla", "fla");
        docIconMap.put("mpg", "media");
        docIconMap.put("mp4", "media");
        docIconMap.put("avi", "media");
        docIconMap.put("wmv", "media");
        docIconMap.put("mov", "media");
        docIconMap.put("rmvb", "rmvb");
        docIconMap.put("rm", "rmvb");

        docIconMap.put("mp3", "mp3");
        docIconMap.put("wav", "mp3");
        docIconMap.put("wma", "mp3");

        docIconMap.put("chm", "chm");
        docIconMap.put("dfm", "dfm");
        docIconMap.put("exe", "exe");
    }

    /**
     * 获取目录打印数量限制，包含父目录
     *
     * @param folder
     * @return
     */
    public static long getPrintLimitWithFolder(Folder folder) {
        if (folder == null) {
            return -1;
        }

        if (folder.getPrintLimit() != -1) {
            return folder.getPrintLimit();
        } else {
            Folder parent = folder.getParent();
            if (parent == null) {
                return -1;
            } else {
                return getPrintLimitWithFolder(parent);
            }
        }
    }

    public static String getDocLeftMenuTitle() {
        return StringUtils.defaultIfEmpty(SystemConfigManager.getInstance().getProperty("docLeftMenuTitle"), "公共文档");
    }

    public static boolean isInAutoVueExtMap(String extension) {
        return autoVueExtMap.contains(extension);
    }

    public static String getBigDocIcon(String ext) {
        String result = bigDocIconMap.get(ext);

        if (StringUtils.isBlank(result)) {
            result = "none";
        }

        return result;
    }

    public static String getDocIcon(String ext) {
        String result = docIconMap.get(ext);

        if (StringUtils.isBlank(result)) {
            result = "none";
        }

        return result;
    }

    public static String getShortAltString(String shortString, String fullString) {
        StringBuilder sb = new StringBuilder();
        sb.append("<span");
        sb.append(" alt=\"" + fullString + "\"");
        sb.append(" title=\"" + fullString + "\"");
        sb.append(">");
        sb.append(shortString);
        sb.append("</span>");

        return sb.toString();
    }

    public static String getDocumentShortNoticeUrl(Document document) {
        StringBuilder sb = new StringBuilder();
        if (document != null) {
            sb.append("<a href=\"javascript:void(0)\" onclick=loadFolder(\"");
            sb.append(document.getFolder().getId());
            sb.append("\",null,\"");
            sb.append(document.getId());
            sb.append("\");");
            sb.append(" alt=\"" + document.getFilename() + "\"");
            sb.append(" title=\"" + document.getFilename() + "\"");
            sb.append(">");
            sb.append(document.getSummaryFilename());
            sb.append("</a>");
        }

        return sb.toString();
    }

    public static String getDocumentNoticeUrl(Document document) {
        return getDocumentShortNoticeUrl(document);
		/*
		StringBuilder sb = new StringBuilder();

		if (document != null) {
			sb.append("<a href=\"javascript:void(0)\" onclick=loadFolder(\"");
			sb.append(document.getFolder().getId());
			sb.append("\",null,\"");
			sb.append(document.getId());
			sb.append("\");>");
			sb.append(document.getDisplayFullpath());
			sb.append("</a>");
		}

		return sb.toString();
		*/
    }

    public static String getAreaDisplayDocumentPath(Folder folder, Document document) {
        if (folder == null) {
            return "";
        }

        String result = "/" + folder.getName();

        Folder parent = folder.getParent();

        while (parent != null) {
            result = "/" + parent.getName() + result;
            parent = parent.getParent();
        }

        if (Folder.TYPE_PUBLIC == folder.getFolderType()) {
            FolderAreaService folderAreaService = SpringUtils.getBean(FolderAreaService.class);
            FolderArea area = folderAreaService.getFolderAreaByTreeName(folder.getTreeName());
            if (area != null) {
                result = "/" + area.getAreaName() + result;
            } else {
                result = "/" + DocumentUtils.getDocLeftMenuTitle() + result;
            }
        } else {
            if (document != null) {
                result = "/" + document.getCreatorFullname() + "的文档" + result;
            } else {
                result = "/我的文档" + result;
            }
        }

        return result;
    }

    public static String getDisplayDocumentPathWithSession(Folder folder, Document document) {
        if (folder == null) {
            return "";
        }

        String result = "/" + folder.getName();

        Folder parent = folder.getSessionParent();

        while (parent != null) {
            result = "/" + parent.getName() + result;
            parent = parent.getSessionParent();
        }

        if (Folder.TYPE_PUBLIC == folder.getFolderType()) {
            FolderAreaService folderAreaService = SpringUtils.getBean(FolderAreaService.class);
            FolderArea area = folderAreaService.getFolderAreaByTreeName(folder.getTreeName());
            if (area != null) {
                result = area.getAreaName() + result;
            } else {
                result = DocumentUtils.getDocLeftMenuTitle() + result;
            }
        } else {
            if (document != null) {
                result = "/" + document.getCreatorFullname() + "的文档" + result;
            } else {
                result = "/我的文档" + result;
            }
        }

        return result;
    }

    public static String getDisplayDocumentPath(Folder folder, Document document) {
        if (folder == null) {
            return "";
        }

        String result = "/" + folder.getName();

        Folder parent = folder.getParent();

        while (parent != null) {
            result = "/" + parent.getName() + result;
            parent = parent.getParent();
        }

        if (Folder.TYPE_PUBLIC == folder.getFolderType()) {
            result = "/" + getDocLeftMenuTitle() + result;
        } else {
            if (document != null) {
                result = "/" + document.getCreatorFullname() + "的文档" + result;
            } else {
                result = "/我的文档" + result;
            }
        }

        return result;
    }

    public static String getDisplayDocumentPathWithLink(Folder folder, Document document) {
        if (folder == null) {
            return "";
        }

        String result = "/<a href=\"javascript:void(0)\" onclick=\"loadFolder(" + folder.getId() + ");\">" +
                folder.getName() + "</a>";

        Folder parent = folder.getParent();

        while (parent != null) {
            result = "/" + "<a href=\"javascript:void(0)\" onclick=\"loadFolder(" + parent.getId() + ")\">" +
                    parent.getName() + "</a>" + result;
            parent = parent.getParent();
        }

        if (Folder.TYPE_PUBLIC == folder.getFolderType()) {
            FolderAreaService folderAreaService = SpringUtils.getBean(FolderAreaService.class);
            FolderArea area = folderAreaService.getFolderAreaByTreeName(folder.getTreeName());
            if (area != null) {
                result = "/<a href=\"javascript:void(0)\" onclick=\"loadFolder(-1,null,null,null,\'" +
                        area.getTreeName() + "\');\">" + area.getAreaName() + "</a>" + result;
            } else {
                result = "/<a href=\"javascript:void(0)\" onclick=\"loadFolder(-1);\">" + getDocLeftMenuTitle() +
                        "</a>" + result;
            }
        } else {
            if (document != null) {
                result = "/<a href=\"javascript:void(0)\" onclick=\"loadFolder(-2);\">" +
                        document.getCreatorFullname() + "的文档</a>" + result;
            } else {
                result = "/<a href=\"javascript:void(0)\" onclick=\"loadFolder(-2);\">我的文档</a>" + result;
            }
        }

        return result;
    }

    public static String getDisplayDocumentPathWithLinkAndEscapeChar(Folder folder, Document document) {
        if (folder == null) {
            return "";
        }

        String result = "/<a href=\"javascript:void(0)\" onclick=\"loadFolder(" + folder.getId() + ");\">" +
                folder.getName() + "</a>";

        Folder parent = folder.getParent();

        while (parent != null) {
            result = "/" + "<a href=\"javascript:void(0)\" onclick=\"loadFolder(" + parent.getId() + ")\">" +
                    parent.getName() + "</a>" + result;
            parent = parent.getParent();
        }

        if (Folder.TYPE_PUBLIC == folder.getFolderType()) {
            FolderAreaService folderAreaService = SpringUtils.getBean(FolderAreaService.class);
            FolderArea area = folderAreaService.getFolderAreaByTreeName(folder.getTreeName());
            if (area != null) {
                result = "/<a href=\"javascript:void(0)\" onclick=\"loadFolder(-1,null,null,null,\\'" +
                        area.getTreeName() + "\\');\">" + area.getAreaName() + "</a>" + result;
            } else {
                result = "/<a href=\"javascript:void(0)\" onclick=\"loadFolder(-1);\">" + getDocLeftMenuTitle() +
                        "</a>" + result;
            }
        } else {
            if (document != null) {
                result = "/<a href=\"javascript:void(0)\" onclick=\"loadFolder(-2);\">" +
                        document.getCreatorFullname() + "的文档</a>" + result;
            } else {
                result = "/<a href=\"javascript:void(0)\" onclick=\"loadFolder(-2);\">我的文档</a>" + result;
            }
        }

        return result;
    }

    public static boolean isFolderChild(Folder parent, Folder child) {
        if (parent == null || child == null) {
            return false;
        }

        if (parent.getChildren().size() != 0) {
            if (parent.getChildren().contains(child)) {
                return true;
            }

            for (Folder g : parent.getChildren()) {
                if (isFolderChild(g, child)) {
                    return true;
                }
            }
        }

        return false;
    }

    public static List<Document> getLinkedDocuments(Document originDocument) {
        DocumentRelationRepository documentRelationRepository = SpringUtils.getBean(DocumentRelationRepository.class);

        List<DocumentRelation> documentRelations = documentRelationRepository.getDocumentRelations(originDocument);
        if (CollectionUtils.isEmpty(documentRelations)) {
            return Collections.emptyList();
        }

        List<Document> result = new ArrayList<>();
        for (DocumentRelation documentRelation : documentRelations) {
            Document documentRelated = documentRelation.getDocumentRelated();
            if (!documentRelated.equals(originDocument)) {
                result.add(documentRelated);
            }

            Document document = documentRelation.getDocument();
            if (!document.equals(originDocument)) {
                result.add(document);
            }
        }

        return result;
    }

    public static boolean isPicture(Document document) {
        boolean result = false;
        if (document == null) {
            return result;
        }

        if ("jpg".equalsIgnoreCase(document.getExtension()) || "png".equalsIgnoreCase(document.getExtension())) {
            result = true;
        }

        return result;
    }
}
