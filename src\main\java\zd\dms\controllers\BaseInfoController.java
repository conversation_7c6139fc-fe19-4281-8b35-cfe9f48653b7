package zd.dms.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.dms.utils.admin.SettingsUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "LoginController", description = "登录Controller")
@RequestMapping("/opening")
public class BaseInfoController extends ControllerSupport {
    @Operation(summary = "获取产品信息")
    @RequestMapping(value = "/getProductInfos", method = {RequestMethod.GET, RequestMethod.POST})
    @ZDLog("获取产品信息")
    public JSONResultUtils<Map<String, Object>> getProductInfos() {
        List<String> keys = new ArrayList<>();
        keys.add("productName");
        keys.add("productShortName");
        keys.add("loginPageText");

        return JSONResultUtils.successWithData(SettingsUtils.getSettings(keys));
    }
}
