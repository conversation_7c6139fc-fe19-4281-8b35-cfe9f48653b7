package zd.dms.repositories.id.impl;

import jakarta.persistence.EntityManager;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.DmsId;
import zd.dms.repositories.id.DmsIdRepository;

public class DmsIdRepositoryDaoImpl extends BaseRepositoryDaoImpl<DmsId, String> implements DmsIdRepository {

    public DmsIdRepositoryDaoImpl(Class<DmsId> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }
}
