package zd.dms.services.document;

import org.springframework.transaction.annotation.Transactional;
import zd.dms.entities.User;
import zd.record.entities.RecFolder;

import java.io.File;
import java.io.IOException;
import java.util.Map;

@Transactional
public interface ImportExportService {

    void batchDownloadZipRecordAttachment(File zipFile, String[] docIds, Map<String, String> docsPrefix,
            String encoding, User operator, String ip, RecFolder folder) throws IOException;

    void batchDownloadZipRecordAttachmentIncludesSubRecord(File zipExportFile, String[] docIds,
            Map<String, String> docsPrefix, String string, User operator, String ipAddress);

    void batchDownloadZipDocs(File zipFile, String[] docIds, Map<String, String> docsPrefix,
                              User operator, String ip)
            throws IOException;
}
