package zd.dms.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.context.UserContextHolder;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapTool;
import zd.dms.controllers.sso.LoginUtils;
import zd.dms.entities.InstantMessage;
import zd.dms.entities.User;
import zd.dms.services.im.InstantMessageService;
import zd.dms.services.security.SecurityInfo;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.services.user.UserService;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "LoginController", description = "登录Controller")
@RequestMapping("/")
public class LoginController extends ControllerSupport {

    private final UserService userService;

    private final InstantMessageService instantMessageService;

    @Operation(summary = "用户登录")
    @RequestMapping(value = "/login", method = {RequestMethod.GET, RequestMethod.POST})
    @ZDLog("用户登录")
    public JSONResultUtils<Map<String, Object>> login(@RequestParam(required = false) String username, @RequestParam(required = false) String pwd, @RequestBody(required = false) Map<String, Object> condition,
                                                      HttpServletRequest request, HttpServletResponse response) throws Exception {

        username = MapUtils.getString(condition, "username", username);
        pwd = MapUtils.getString(condition, "pwd", pwd);
        String enpwd = MapUtils.getString(condition, "enpwd", "");

        String token = MapUtils.getString(condition, "token", "");
        boolean checkPassword = true;
        if (StringUtils.isNotBlank(token)) {
            checkPassword = false;
            pwd = "notUsed";

            String tokenUsername = LoginUtils.getDecryptTokenUsername(token);
            if (tokenUsername.startsWith("error:")) {
                return JSONResultUtils.error(tokenUsername.replace("error:", ""));
            }

            username = tokenUsername.replace("success:", "");
        }

        boolean useUsernameAndPassword = false;
        String loginCode = "";
        if (StringUtils.isNotBlank(enpwd)) {
            useUsernameAndPassword = true;
            String passwordAndLoginCode = LoginUtils.decryptWithBase64AndRSA(enpwd);
            if (StringUtils.isNotBlank(passwordAndLoginCode)) {
                int indexOf = passwordAndLoginCode.lastIndexOf("_");
                if (indexOf >= 0) {
                    pwd = passwordAndLoginCode.substring(0, indexOf);
                    loginCode = passwordAndLoginCode.substring(indexOf + 1);
                } else {
                    pwd = passwordAndLoginCode;
                }
            }
        }

        // 账号密码登录
        if (StringUtils.isBlank(username)) {
            return JSONResultUtils.error("账号不能为空");
        }

        if (checkPassword && StringUtils.isBlank(pwd)) {
            return JSONResultUtils.error("密码不能为空");
        }

        if (useUsernameAndPassword) {
            boolean checkLoginCode = LoginUtils.checkLoginCode(loginCode);
            if (!checkLoginCode) {
                return JSONResultUtils.error("登陆页面已过期，请刷新后重试");
            }

            // 用户密码错误三次，则必须填写验证码
            if (SecurityInfo.isReachMaxLoginSecurityCode(username)) {
                String captcha = MapUtils.getString(condition, "captcha", "");
                String validateCaptcha = LoginUtils.validateCaptcha(request, captcha);
                if (!"success".equals(validateCaptcha)) {
                    Map<String, Object> resultMap = new HashMap<>();
                    resultMap.put("showCaptcha", SecurityInfo.isReachMaxLoginSecurityCode(username));
                    return JSONResultUtils.errorWithData(validateCaptcha, resultMap).whole();
                }
            }
        }

        LoginUtils.allowLogin(username);

        User u = userService.getUserByUsername(username);
        if (u == null) {
            SecurityInfo.setLoginErrorIP(getIpAddress());
            return JSONResultUtils.error("错误的用户名或密码");
        }

        LoginUtils.allowAdminLogin(u);

        Date lastLoginDate = u.getLastLoginDate();
        String lastLoginIp = u.getLastIp();

        try {
            LoginUtils.login(username, pwd, false, checkPassword, request, response);
        } catch (Exception e) {
            String message = e.getMessage();
            if (StringUtils.isNotBlank(message)) {
                if (message.contains("错误的用户名或密码")) {
                    Map<String, Object> resultMap = new HashMap<>();
                    resultMap.put("showCaptcha", SecurityInfo.isReachMaxLoginSecurityCode(username));
                    return JSONResultUtils.errorWithData(message, resultMap).whole();
                } else {
                    throw e;
                }
            } else {
                throw e;
            }
        }

        logInfo("用户 " + username + " 登录系统");
        log.debug("用户 {} 登录成功", username);

        Map<String, Object> jsonData = LoginUtils.getUserData(u, request);

        // 发送上次登录信息的提示
        if (lastLoginDate != null && StringUtils.isNotBlank(lastLoginIp)) {
            String msg = "您上次登录的时间是: <b>" + DateFormatUtils.format(lastLoginDate, "yyyy/MM/dd HH:mm") +
                    "</b><br/>IP地址是: <b>" + lastLoginIp + "</b>";

            instantMessageService.sendMessage(InstantMessage.TYPE_LOGIN, UserGroupUtils.ADMIN_USERNAME, "系统管理员", u.getUsername(),
                    u.getFullname(), msg, u);
        }

        return JSONResultUtils.successWithData(jsonData).whole();
    }

    @Operation(summary = "检测用户登陆状态")
    @RequestMapping(value = "/checkLoginStatus", method = {RequestMethod.GET, RequestMethod.POST})
    @ZDLog("检测用户登陆状态")
    public JSONResultUtils<Object> checkLoginStatus() {
        User currentUser = getCurrentUser();

        boolean loginStatus = false;
        if (currentUser != null) {
            loginStatus = true;
        }

        Map<String, Object> loginStatusMap = ZDMapTool.getInstance().put("loginStatus", loginStatus).getMap();
        return JSONResultUtils.successWithData(loginStatusMap);
    }

    @Operation(summary = "获取登陆唯一标识")
    @GetMapping(value = "/getLoginCode")
    @ZDLog("获取登陆唯一标识")
    public JSONResultUtils<Object> getLoginCode() {
        String loginCode = LoginUtils.getLoginCode();

        Map<String, Object> loginStatusMap = ZDMapTool.getInstance().put("loginCode", loginCode).getMap();
        return JSONResultUtils.successWithData(loginStatusMap);
    }
}
