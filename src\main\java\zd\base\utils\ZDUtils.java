package zd.base.utils;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.context.UserContextHolder;
import zd.base.utils.jpa.JPAUtils;
import zd.base.utils.system.PropsUtils;
import zd.base.utils.system.SpringUtils;
import zd.base.utils.system.SystemInitUtils;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.document.FolderAreaService;
import zd.dms.services.document.FolderService;
import zd.dms.services.propdata.DoCreateDefaultRecordsInfoUtils;
import zd.dms.services.propdata.DocAdvPropsDBUtils;
import zd.dms.services.user.UserService;
import zd.dms.utils.db.ZDDBConfigUtils;
import zd.dms.utils.db.ZDJoinTableGroupUserUtils;
import zd.dms.utils.zdmq.ZDMQDBUtils;

import java.io.File;
import java.util.*;

@Slf4j
public class ZDUtils {

    private static Map<String, String> externalLinkMap;

    private static List<String> backGroundImgList;

    private static Map<String, String> configurationMap;

    public static boolean ZD_DEBUG_ENABLED = PropsUtils.getBoolProps("enableZDDebug", false);

    public static boolean ZD_INFO_ENABLED = PropsUtils.getBoolProps("enableZDInfo", false);

    public static void info(String msg) {
        if (StringUtils.isEmpty(msg)) {
            return;
        }

        log.info(msg);
    }

    /**
     * 仅在特殊情况需要打印日志时使用，仅开启log4j.xml中的debug，此日志不会打印
     */
    public static void error(String msg, Throwable t) {
        log.error(msg, t);
    }

    /**
     * 仅在特殊情况需要打印日志时使用，仅开启log4j.xml中的debug，此日志不会打印
     *
     * @param classLog
     * @param msg
     * @param objArr
     */
    public static void error(Logger classLog, String msg, Object[] objArr) {
        if (!ZD_DEBUG_ENABLED || classLog == null) {
            return;
        }

        if (msg == null) {
            msg = "";
        }

        if (objArr == null) {
            classLog.debug(msg);
        } else {
            classLog.debug(msg, objArr);
        }
    }

    public static void info(String msg, Object[] objArr) {
        if (!ZD_INFO_ENABLED) {
            return;
        }

        if (msg == null) {
            msg = "";
        }

        if (objArr == null) {
            log.info(msg);
        } else {
            log.info(msg, objArr);
        }
    }

    /**
     * 仅在特殊情况需要打印日志时使用，仅开启log4j.xml中的info，此日志不会打印
     *
     * @param msg
     * @param objArr
     */
    public static void debug(String msg, Object[] objArr) {
        if (!ZD_INFO_ENABLED) {
            return;
        }

        if (msg == null) {
            msg = "";
        }

        if (objArr == null) {
            log.debug(msg);
        } else {
            log.debug(msg, objArr);
        }
    }

    /**
     * 仅在特殊情况需要打印日志时使用，仅开启log4j.xml中的info，此日志不会打印
     *
     * @param classLog
     * @param msg
     * @param objArr
     */
    public static void info(Logger classLog, String msg, Object[] objArr) {
        if (!ZD_INFO_ENABLED || classLog == null) {
            return;
        }

        if (msg == null) {
            msg = "";
        }

        if (objArr == null) {
            classLog.info(msg);
        } else {
            classLog.info(msg, objArr);
        }
    }

    /**
     * 仅在特殊情况需要打印日志时使用，仅开启log4j.xml中的debug，此日志不会打印
     *
     * @param classLog
     * @param msg
     * @param objArr
     */
    public static void debug(Logger classLog, String msg, Object[] objArr) {
        if (!ZD_DEBUG_ENABLED || classLog == null) {
            return;
        }

        if (msg == null) {
            msg = "";
        }

        if (objArr == null) {
            classLog.debug(msg);
        } else {
            classLog.debug(msg, objArr);
        }
    }

    /**
     * 仅在特殊情况需要打印日志时使用，仅开启log4j.xml中的debug，此日志不会打印 Throwable
     *
     * @param classLog
     * @param msg
     * @param t
     */
    public static void debug(Logger classLog, String msg, Throwable t) {
        if (!ZD_DEBUG_ENABLED || classLog == null) {
            return;
        }

        if (msg == null) {
            msg = "";
        }

        if (t == null) {
            classLog.debug(msg);
        } else {
            classLog.debug(msg, t);
        }
    }

    /**
     * 系统启动刚结束时加载，未读取配置文件前
     *
     * @return
     */
    public static boolean beforeBootstrapListener() {
        try {
            JPAUtils.createSequence();

            // 创建配置信息
            System.out.println("ZDECM - Create default DB Config.");
            ZDDBConfigUtils.createTable();

            // 创建用户及部门中间表
            System.out.println("ZDECM - Create default TDMS_GROUP_TDMS_USER.");
            ZDJoinTableGroupUserUtils.createTable();
        } catch (Throwable t) {
            log.error("postBootstrapListener init error", t);
            return false;
        }

        return true;
    }

    /**
     * 系统启动时加载方法
     *
     * @return
     */
    public static boolean postBootstrapListener() {
        try {
            JPAUtils.createSequence();

            // 创建默认用户和目录
            System.out.println("ZDECM - Create default users and groups.");
            SpringUtils.getBean(UserService.class).createDefaultUserAndGroups();

            System.out.println("ZDECM - Create default folders.");
            FolderService folderService = SpringUtils.getBean(FolderService.class);
            folderService.createDefaultFolders();

            System.out.println("ZDECM - Create docProps tables.");
            DocAdvPropsDBUtils.createDefaultPropsTable();

            System.out.println("ZDECM - Create DefaultRecTablesAndFolders.");
            DoCreateDefaultRecordsInfoUtils.doCreateDefaultRecordsInfo();

            /*System.out.println("ZDECM - Create default record folders.");
            RecFolderService recFolderService = SpringUtils.getBean(RecFolderService.class);
            recFolderService.createDefaultFolders();*/

            System.out.println("ZDECM - Create default folderAreas.");
            FolderAreaService folderAreaService = SpringUtils.getBean(FolderAreaService.class);
            folderAreaService.createDefaultArea();

            System.out.println("ZDECM - Create default mq table.");
            ZDMQDBUtils.createTable();

            /*try {
                System.out.println("ZDECM - Create utilization table.");
                RecordDBUtils.createUtilizationTable();
            } catch (Throwable t) {
                log.debug("createUtilizationTable utilization已创建");
            }*/

            /*System.out.println("ZDECM - Create lawTableAndFolder.");
            PortalUtils.createLawTableAndFolder();*/
        } catch (Throwable t) {
            log.error("postBootstrapListener init error", t);
            return false;
        }

        // TODO ngcopy
        /*try {
            String ladpSyncType = scm.getProperty("ldap.syncType");
            if (LdapUtils.LDAPSYNCTYPE_CRON.equals(ladpSyncType)) {
                System.out.println("ZDECM - Set Ladp Sync Cron.");
                String ladpSyncCron = scm.getProperty("ldap.syncCron");
                LdapUtils.setLadpSyncCron(ladpSyncCron);
            }
        } catch (Throwable t) {
            log.error("postBootstrapListener init setLadpSyncCron error", t);
            return false;
        }*/

        return true;
    }

    /**
     * 生成用户密码
     *
     * @param username
     * @param mobilePhone
     * @param officePhone
     * @param homePhone
     * @return
     */
    public static String generateUserPassword(String username, String mobilePhone, String officePhone, String homePhone) {
        String suffix = "";

        if (StringUtils.isNotBlank(mobilePhone)) {
            suffix = mobilePhone;
        }

        if (StringUtils.isNotBlank(officePhone)) {
            suffix = officePhone;
        }

        if (StringUtils.isNotBlank(homePhone)) {
            suffix = homePhone;
        }

        if (!NumberUtils.isDigits(suffix)) {
            suffix = "111222";
        }

        // 截取后6位
        suffix = StringUtils.substring(suffix, suffix.length() - 6, suffix.length());
        // 不够6位补0
        suffix = StringUtils.leftPad(suffix, 6, "0");
        return suffix;
    }

    // 技术支持服务配置
    public static boolean isShowServiceExpiredAfterDate() {
        Date showServiceExpiredAfterDate = null;
        String showServiceExpiredAfterDateStr = PropsUtils.getProps("showServiceExpiredAfterDate", "");
        if (StringUtils.isNotBlank(showServiceExpiredAfterDateStr)) {
            try {
                showServiceExpiredAfterDate = DateUtils.parseDate(showServiceExpiredAfterDateStr, "yyyy-MM-dd");
            } catch (Exception e) {
                log.error("showServiceExpiredAfterDate parseDate error");
            }
        }

        return showServiceExpiredAfterDate == null || showServiceExpiredAfterDate.getTime() < new Date().getTime();
    }

    public static Map<String, String> getConfigurationMap(HttpServletRequest request, HttpServletResponse response) {
        if (configurationMap == null) {
            synchronized (ZDUtils.class) {
                initConfigurationMap(request, response);
            }
        }

        return configurationMap;
    }

    public static List<String> getBackGroundImgList(File dir) {
        if (backGroundImgList != null) {
            return backGroundImgList;
        }

        backGroundImgList = new ArrayList<String>();
        if (dir == null || !dir.exists() || dir.isFile()) {
            return backGroundImgList;
        }

        File[] listFiles = dir.listFiles();
        for (File file : listFiles) {
            if (file.isFile()) {
                backGroundImgList.add(file.getName());
            }
        }

        return backGroundImgList;
    }

    /**
     * 过滤LFI
     *
     * @param text
     * @return
     */
    public static boolean filterLFI(String text) {
        if (StringUtils.isNotBlank(text) &&
                (text.contains("/.") || text.contains("./") || text.contains("\\.") || text.contains(".\\"))) {

            return false;
        }

        return true;
    }

    /**
     * 获取外部链接配置
     *
     * @return
     */
    public static Map<String, String> getExternalLinkMap() {
        externalLinkMap = new LinkedHashMap<String, String>();
        String externalLink = SystemConfigManager.getInstance().getProperty("externalLink");

        log.debug("ZDUtils externalLink:{}", externalLink);

        if (StringUtils.isNotBlank(externalLink)) {
            String[] linkArray = externalLink.split("\r\n");
            for (String link : linkArray) {
                if (StringUtils.isBlank(link)) {
                    continue;
                }

                String[] singLinkArray = StringUtils.split(link, "=", 2);
                if (singLinkArray == null || singLinkArray.length != 2) {
                    continue;
                }

                externalLinkMap.put(replace(singLinkArray[0]), replace(singLinkArray[1]));
            }
        }

        return externalLinkMap;
    }

    private static void initConfigurationMap(HttpServletRequest request, HttpServletResponse response) {
        if (configurationMap == null) {
            configurationMap = new HashMap<String, String>();

            // 获取协议，默认为http，主要解决服务器配置证书后不能批量下载文件的问题
            String defaultProtocol = "http";
            if (PropsUtils.getBoolProps("sslWholeSiteEnabled", false)) {
                defaultProtocol = "https";
            }

            String protocol = PropsUtils.getProps("protocol", defaultProtocol);
            String serverPort = PropsUtils.getProps("serverPort", request.getServerPort() + "");
            configurationMap.put("protocol", protocol);
            configurationMap.put("serverPort", serverPort);
        }
    }

    public static String getEdition() {
        return PropsUtils.getProps("ngen_edition", "旗舰版");
    }

    private static String replace(String source) {
        source = source.replace("#username#", UserContextHolder.getUsername());
        source = source.replace("#fullname#", UserContextHolder.getFullname());

        return source;
    }

    public static <T> T getBean(Class<T> classes) {
        return SpringUtils.getBean(classes);
    }

    public static File getHomeDir() {
        return SystemInitUtils.getHomeDir();
    }

    public static File getTempDir() {
        return SystemInitUtils.getTempDir();
    }
}
