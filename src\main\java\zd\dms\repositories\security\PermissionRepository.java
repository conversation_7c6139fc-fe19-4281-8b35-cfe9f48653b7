package zd.dms.repositories.security;

import zd.base.repositories.BaseRepository;
import zd.dms.entities.FolderPermission;

import java.util.List;
import java.util.Map;

/**
 * Permission Data Access Object
 *
 * <AUTHOR>
 * @version $Revision$
 */
public interface PermissionRepository extends BaseRepository<FolderPermission, Long> {

    List<FolderPermission> getPermissionsByFolderId(long folderId);

    List<FolderPermission> getPermissionsByFolderIds(List<Long> folderIds);

    List<FolderPermission> getPermissionsByUsername(String username);

    List<FolderPermission> getPermissionsByGroupCode(String groupCode);

    List<FolderPermission> getPermissionsByPosition(String position);

    List<FolderPermission> getUserPermissions(String username, long folderId);

    List<FolderPermission> getGroupPermissions(String groupId, long folderId);

    List<FolderPermission> getPosPermissions(String position, long folderId);

    List<FolderPermission> getUserPermissionsWithPosition(String username, List<String> positions, long folderId);

    List<FolderPermission> getPermissionsForUser(String username, List<String> groupCodes, List<String> positions, long folderId);

    List<FolderPermission> getPermissionsForUser(String username, List<String> groupCodes, List<String> positions, List<String> perms);

    List<Map<String, Object>> getPermissionRecFolderCount(List<Long> folderIds);

    void deletePermissionsByUsername(String username);

    void deletePermissionsByFolderId(long folderId);

    void deletePermissionsByGroupCode(String groupCode);
}
