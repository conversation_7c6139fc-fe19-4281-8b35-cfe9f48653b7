package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.context.UserContextHolder;
import zd.base.entities.AbstractSequenceEntity;

import java.util.Date;


/**
 * DocumentVersion Domain Object
 * 
 * @version $Revision$, $Date$
 * <AUTHOR>
 */
@Entity
@Table(name = "docctrlver")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class DocumentControlledVersion extends AbstractSequenceEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3903411817181652182L;

	/**
	 * 修订版号
	 */
	@Index(name = "i_dcver_rvn")
	private int revisionNumber;

	/**
	 * 发布版号
	 */
	@Index(name = "i_dcver_pvn")
	private String publishVersion;

	/**
	 * 文档ID
	 */
	@Index(name = "i_dcver_docid")
	private String docId;

	/**
	 * 作者名，使用全名
	 */
	@Column(nullable = false)
	private String creatorFullname;

	/**
	 * 状态
	 */
	@Index(name = "i_dcver_status")
	private int status;

	/**
	 * 创建时间
	 */
	@Index(name = "i_dver_cd")
	@Column(nullable = false)
	private Date creationDate;

	@Column(length = Length.LOB_DEFAULT)
	private String headerImage;

	@Column(length = Length.LOB_DEFAULT)
	private String footerImage;

	/**
	 * 默认构造器
	 */
	public DocumentControlledVersion() {
		super();
		creationDate = new Date();
		User currentUser = UserContextHolder.getUser();
		creatorFullname = currentUser != null ? currentUser.getFullname() : null;
	}

	public String getCreatorFullname() {
		return creatorFullname;
	}

	public void setCreatorFullname(String creatorFullname) {
		this.creatorFullname = creatorFullname;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}

	public String getPublishVersion() {
		return publishVersion;
	}

	public void setPublishVersion(String publishVersion) {
		this.publishVersion = publishVersion;
	}

	public String getDocId() {
		return docId;
	}

	public void setDocId(String docId) {
		this.docId = docId;
	}

	public String getHeaderImage() {
		return headerImage;
	}

	public void setHeaderImage(String headerImage) {
		this.headerImage = headerImage;
	}

	public String getFooterImage() {
		return footerImage;
	}

	public void setFooterImage(String footerImage) {
		this.footerImage = footerImage;
	}

	public int getRevisionNumber() {
		return revisionNumber;
	}

	public void setRevisionNumber(int revisionNumber) {
		this.revisionNumber = revisionNumber;
	}
}
