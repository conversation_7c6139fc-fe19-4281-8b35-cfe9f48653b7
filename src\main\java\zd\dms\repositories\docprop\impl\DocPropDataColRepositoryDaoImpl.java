//                    佛祖保佑       永无BUG
//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                           O\  =  /O
//                        ____/`---'\____
//                      .'  \\|     |//  `.
//                     /  \\|||  :  |||//  \
//                    /  _||||| -:- |||||-  \
//                    |   | \\\  -  /// |   |
//                    | \_|  ''\---/''  |   |
//                    \  .-\__  `-`  ___/-. /
//                  ___`. .'  /--.--\  `. . __
//               ."" '<  `.___\_<|>_/___.'  >'"".
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |
//              \  \ `-.   \_ __\ /__ _/   .-` /  /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
package zd.dms.repositories.docprop.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.DocPropDataCol;
import zd.dms.entities.Group;
import zd.dms.repositories.docprop.DocPropDataColRepository;

import java.util.ArrayList;
import java.util.List;

public class DocPropDataColRepositoryDaoImpl extends BaseRepositoryDaoImpl<DocPropDataCol, Long> implements DocPropDataColRepository {

    public DocPropDataColRepositoryDaoImpl(Class<DocPropDataCol> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public int getNumByColName(String colName) {
        return NumberUtils.toInt(findObject("select count(*) from DocPropDataCol where name=?1", colName) + "");
    }

    @Override
    public List<DocPropDataCol> getColumns() {
        Specification<DocPropDataCol> spec = (root, criteriaQuery, criteriaBuilder) -> {
            Order creationDateDesc = criteriaBuilder.desc(root.get("creationDate"));

            return criteriaQuery.orderBy(creationDateDesc).getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public DocPropDataCol getDocPropDataColByName(String colName) {
        Specification<DocPropDataCol> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("name"), colName));

            return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
        };

        List<DocPropDataCol> docPropDataCol = findAll(spec);
        if (CollectionUtils.isNotEmpty(docPropDataCol)) {
            return docPropDataCol.get(0);
        } else {
            return null;
        }
    }
}
