package zd.base.config.oauth2;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;

/**
 * Redis令牌存储配置
 */
@Configuration
public class RedisTokenStoreConfig {

    /**
     * 配置Redis令牌存储服务
     */
    @Bean
    @Primary
    public OAuth2AuthorizationService authorizationService(
            RedisConnectionFactory redisConnectionFactory,
            RegisteredClientRepository registeredClientRepository) {
        
        return new RedisOAuth2AuthorizationService(redisConnectionFactory, registeredClientRepository);
    }
}
