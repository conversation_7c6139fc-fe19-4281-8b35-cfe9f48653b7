package zd.dms.entities;

import jakarta.persistence.*;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Length;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.dms.utils.IMUtils;
import zd.dms.utils.TextUtils;

import java.util.Date;

/**
 * Instant Message Domain Object
 * 
 * @version $Revision$, $Date$
 * <AUTHOR>
 */
@Entity
@Table(name = "instant_message")
@BatchSize(size = 50)
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class InstantMessage extends AbstractSequenceEntity {

	/**
	 * serial
	 */
	private static final long serialVersionUID = -2912649451492406800L;

	public static final String TYPE_DOC = "doc";

	public static final String TYPE_REMIND = "remind";

	public static final String TYPE_MSG = "msg";

	public static final String TYPE_WORKFLOW = "wf";

	public static final String TYPE_LOGIN = "login";

	public static final String TYPE_MAIL = "mail";

	public static final String TYPE_NOTICE = "notice";

	public static final String TYPE_RECORD = "record";

	public static final String TYPE_ISO = "iso";

	/**
	 * 信息
	 */
	@Column(nullable = false,length = Length.LOB_DEFAULT)
	private String msg;

	/**
	 * 信息
	 */
	@Transient
	private String qywxLinkMsg;

	private String title;

	/**
	 * 接收者用户名
	 */
	@Index(name = "i_im_target")
	private String target;

	/**
	 * 接收者全名
	 */
	private String targetFullname;

	/**
	 * 发送者用户名
	 */
	@Index(name = "i_im_sender")
	private String sender;

	/**
	 * 发送者全名
	 * 
	 */
	private String senderFullname;

	/**
	 * 是否已接收
	 */
	@Index(name = "i_im_received")
	private boolean received;

	/**
	 * 信息类别
	 */
	@Index(name = "i_im_type")
	private String type;

	@Transient
	private String dingEmployeeId;

	@Transient
	private String qywxId;

	@Transient
	private String redirectUrl;

	/**
	 * 创建时间
	 */
	@Index(name = "i_im_cd")
	@Column(nullable = false)
	private Date creationDate;

	/**
	 * 默认构造器
	 */
	public InstantMessage() {
		super();
		received = false;
		creationDate = new Date();
	}

	public String getActualMsg() {
		return msg;
	}

	public String getLinkMsg() {
		return IMUtils.replaceQuestionAndAnswer(getEscapeScriptMsg(), true, type);
	}

	public String getHtmlMsg() {
		return TextUtils.toHtml(getLinkMsg());
	}

	public String getMsg() {
		return IMUtils.replaceQuestionAndAnswer(msg, false, type);
	}

	public String getEscapeScriptMsg() {
		if (StringUtils.isBlank(msg)) {
			return msg;
		}

		String lowerCaseMsg = msg.toLowerCase();
		if ((lowerCaseMsg.contains("script") || lowerCaseMsg.contains("javascript")) &&
				!lowerCaseMsg.contains("javascript:void(0)")) {
			return getEscapeXmlMsg();
		}

		return msg;
	}

	public String getEscapeXmlMsg() {
		return TextUtils.escapeXml(msg);
	}

	public String getEscapeXmlTitle() {
		return TextUtils.escapeXml(title);
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public String getTarget() {
		return target;
	}

	public void setTarget(String target) {
		this.target = target;
	}

	public String getSender() {
		return sender;
	}

	public void setSender(String sender) {
		this.sender = sender;
	}

	public boolean isReceived() {
		return received;
	}

	public void setReceived(boolean received) {
		this.received = received;
	}

	public String getTargetFullname() {
		return targetFullname;
	}

	public void setTargetFullname(String targetFullname) {
		this.targetFullname = targetFullname;
	}

	public String getSenderFullname() {
		return senderFullname;
	}

	public void setSenderFullname(String senderFullname) {
		this.senderFullname = senderFullname;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getDingEmployeeId() {
		return dingEmployeeId;
	}

	public void setDingEmployeeId(String dingEmployeeId) {
		this.dingEmployeeId = dingEmployeeId;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}

	public String getQywxId() {
		return qywxId;
	}

	public void setQywxId(String qywxId) {
		this.qywxId = qywxId;
	}

	public String getQywxLinkMsg() {
		return qywxLinkMsg;
	}

	public void setQywxLinkMsg(String qywxLinkMsg) {
		this.qywxLinkMsg = qywxLinkMsg;
	}
	
	public String getRedicrectUrl() {
		return redirectUrl;
	}

	public void setRedicrectUrl(String redirectUrl) {
		this.redirectUrl = redirectUrl;
	}
}
