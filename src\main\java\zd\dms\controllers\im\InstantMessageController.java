package zd.dms.controllers.im;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapUtils;
import zd.base.utils.model.ObjectMapperUtils;
import zd.base.utils.validate.ValidateUtils;
import zd.dms.entities.Document;
import zd.dms.entities.Group;
import zd.dms.entities.InstantMessage;
import zd.dms.entities.User;
import zd.dms.services.document.DocumentService;
import zd.dms.services.im.InstantMessageService;
import zd.dms.services.mail.MailService;
import zd.dms.services.sms.SmsService;
import zd.dms.services.sms.SmsUtils;
import zd.dms.services.user.GroupService;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.services.user.UserService;
import zd.dms.utils.PageableUtils;
import zd.dms.utils.TempFolderUtils;
import zd.dms.utils.TextUtils;

import java.util.*;

@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "InstantMessageController", description = "提示消息Controller")
@RequestMapping("/im")
public class InstantMessageController extends ControllerSupport {

    private final InstantMessageService instantMessageService;

    private final UserService userService;

    private final GroupService groupService;

    private final SmsService smsService;

    private final MailService mailService;

    private final DocumentService documentService;

    @Operation(summary = "获取提示消息列表")
    @GetMapping(value = "/getUnreadMsgs")
    @ZDLog("获取提示消息列表")
    public JSONResultUtils<Object> getUnreadMsgs() {
        List<InstantMessage> messagesUnread = instantMessageService.getMessagesUnread(getCurrentUsername());
        instantMessageService.receiveAllMsg(getCurrentUsername());
        return successData(ObjectMapperUtils.toMapList(messagesUnread));
    }

    @Operation(summary = "获取提示消息列表")
    @PostMapping(value = "/list")
    @ZDLog("获取提示消息列表")
    public JSONResultUtils<Object> list(@RequestBody Map<String, Object> params) {
        String type = MapUtils.getString(params, "type");
        String keyword = MapUtils.getString(params, "keyword");

        long totalSize = 0;
        List<InstantMessage> instantMessages = null;
        if (StringUtils.isNotBlank(keyword)) {
            instantMessages = instantMessageService.searchMessages(keyword, type, getCurrentUsername());
        } else {
            Page messages = instantMessageService.getMessages(type, getCurrentUsername(), PageableUtils.getPageNumber(params), PageableUtils.getPageSize(params));
            if (messages != null) {
                totalSize = messages.getTotalElements();
                instantMessages = messages.getContent();
            }
        }

//        instantMessageService.receiveAllMsg(getCurrentUsername());

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("smsEnabled", isSmsEnabled());
        resultMap.put("totalSize", totalSize);
        resultMap.put("currentUsername", getCurrentUsername());
        resultMap.put("instantMessages", ObjectMapperUtils.toMapList(instantMessages));
        return successData(resultMap);
    }

    @Operation(summary = "发送消息")
    @PostMapping(value = "/doSend")
    @ZDLog("发送消息")
    public JSONResultUtils<Object> doSend(@RequestBody Map<String, Object> params, HttpServletRequest request) throws Exception {
        String message = MapUtils.getString(params, "message", "");
        ValidateUtils.isTrue(StringUtils.isNotBlank(message), "请填写信息内容");

        message = TextUtils.escapeXml(message);

        List<String> fullnames = new ArrayList<>();
        Set<User> sendToList = new HashSet<>();

        String sendSmsOrMsg = MapUtils.getString(params, "sendSmsOrMsg", "");
        if ("sms".equals(sendSmsOrMsg) || "msgAndSms".equals(sendSmsOrMsg)) {
            String blackWord = smsService.hasBlackWords(message);
            if (StringUtils.isNotBlank(blackWord)) {
                throw new Exception("内容包含无法发送短信的关键字：" + blackWord + "，请修改后重发");
            }
        }

        // 将部门里的人加入发送列表
        long groupId = MapUtils.getLongValue(params, "groupId");
        if (groupId > 0) {
            Group group = groupService.getGroupById(groupId);
            if (group != null) {
                sendToList.addAll(UserGroupUtils.getAllUsers(group));
            }
        }

        // 将用户加入发送列表
        String sendToUsername = MapUtils.getString(params, "sendToUsername", "");
        User sendTo = userService.getUserByUsername(sendToUsername);
        log.debug("doSend username: {}, obj: {}", sendToUsername, sendTo);
        if (sendTo != null) {
            sendToList.add(sendTo);
        }
        List<String> selectedUserGroups = ZDMapUtils.getListStringValue(params, "selectedUserGroups");
        sendToList.addAll(userService.getUsersByUGPrefixList(selectedUserGroups.toArray(new String[]{}), true));

        List<String> mobiles = new LinkedList<String>();

        // 发送信息给发送列表
        for (User u : sendToList) {
            if (getCurrentUser().equals(u)) {
                log.debug("不给自己发信息，跳过");
                continue;
            }

            fullnames.add(u.getFullname());

            // 将手机号加入列表
            if (isSmsEnabled() && SmsUtils.isValidMobile(u.getMobilePhone())) {
                mobiles.add(u.getMobilePhone());
            }

            if (!"sms".equals(sendSmsOrMsg)) {
                String imMessage = message;
                boolean useAttachments = MapUtils.getBooleanValue(params, "useAttachments", false);
                if (useAttachments) {
                    imMessage = imMessage + getAttachmentString(request);
                }
                mailService.sendMessageAndMail("信息提醒", imMessage, InstantMessage.TYPE_MSG, getCurrentUser(), u);
            }
        }

        boolean smsSendSuccess = false;
        if ("sms".equals(sendSmsOrMsg) || "msgAndSms".equals(sendSmsOrMsg)) {
            smsSendSuccess = smsService.sendSms(mobiles.toArray(new String[]{}), message, getCurrentUser()
                            .getUsername(), getCurrentUser().getFullname(), getCurrentUser().getGroupsNamesWithComma(),
                    getIpAddress());
        }

        log.debug("{} 发送消息给 {}", getCurrentUser().getUsername(), fullnames);

        String responsePrefix = "即时消息";
        String responseSuffix = "";
        if ("sms".equals(sendSmsOrMsg)) {
            responsePrefix = "短信";
            if (!smsSendSuccess) {
                responseSuffix = ", 短信发送失败";
            }
        } else if ("msgAndSms".equals(sendSmsOrMsg)) {
            responsePrefix = "即时消息及短信";
            if (!smsSendSuccess) {
                responseSuffix = ", 短信发送失败";
            }
        }

        return JSONResultUtils.successWithMsgWhole(
                responsePrefix + "已发送给<b>" + StringUtils.join(fullnames, ",") + responseSuffix + "</b>");
    }

    private String getAttachmentString(HttpServletRequest request) {
        String baseString = "<br/><br/><b>附件: </b>";

        List<String> docIds = TempFolderUtils.getDocList(request);

        if (docIds.size() <= 0) {
            return "";
        }

        List<String> links = new LinkedList<String>();

        for (String docId : docIds) {
            Document d = documentService.getDocumentById(docId);
            if (d != null) {
                StringBuilder sb = new StringBuilder();
                sb.append("<a href=\"javascript:void(0)\" onclick=\"openNoticeDocWin('");
                sb.append(d.getId());
                sb.append("','");
                sb.append(d.getFolderId());
                sb.append("','");
                sb.append(d.getFilename());
                sb.append("');\"");
                sb.append(" alt=\"" + d.getFilename() + "\"");
                sb.append(" title=\"" + d.getFilename() + "\"");
                sb.append(">");
                sb.append(d.getSummaryFilename30());
                sb.append("</a>");
                links.add(sb.toString());
            }
        }

        baseString += StringUtils.join(links, ", ");

        return baseString;
    }

    @Operation(summary = "删除即时消息")
    @PostMapping(value = "/deleteMessages")
    @ZDLog("删除即时消息")
    public JSONResultUtils<Object> deleteMessages(@RequestBody Map<String, Object> params) {
        int deleteMessageDays = MapUtils.getIntValue(params, "deleteMessageDays", 60);
        Date date = DateUtils.addDays(new Date(), -deleteMessageDays);
        instantMessageService.deleteMessagesBeforeDate(date);
        logInfo("删除" + deleteMessageDays + "天前的即时消息");

        return success();
    }
}
