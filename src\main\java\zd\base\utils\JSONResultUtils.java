package zd.base.utils;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import zd.base.utils.constant.ErrorCodeDefine;
import zd.base.utils.constant.ErrorCodeUtils;

import java.io.Serializable;

@Getter
@Setter
public class JSONResultUtils<T> implements Serializable {

    private static final long serialVersionUID = 3637122497350396679L;

    @Schema(
            title = "success",
            description = "响应状态 true: 成功, false: 失败"
    )
    private boolean success;

    @Schema(
            title = "data",
            description = "响应数据"
    )
    private T data;

    @Schema(
            title = "msg",
            description = "响应信息 返回的成功或失败消息",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    private String msg;

    @Schema(
            title = "errorCode",
            description = "响应码 0: 成功, 其他: 失败",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String errorCode;

    private Long timestamp;

    private boolean whole;

    public JSONResultUtils<T> whole() {
        this.setWhole(true);
        return this;
    }

    public JSONResultUtils(boolean success, String errorCode, T data) {
        this.success = success;
        this.data = data;
        this.msg = ZDI18nUtils.getMsg(ErrorCodeUtils.getMsg(errorCode));
        this.errorCode = errorCode;
        this.timestamp = System.currentTimeMillis();
    }

    public JSONResultUtils(boolean success, String errorCode, String msg, T data) {
        super();
        this.success = success;
        this.data = data;
        this.msg = ZDI18nUtils.getMsg(msg);
        this.errorCode = errorCode;
        this.timestamp = System.currentTimeMillis();
    }

    public JSONResultUtils(boolean success, String errorCode, String msg, T data, boolean whole) {
        super();
        this.success = success;
        this.data = data;
        this.msg = ZDI18nUtils.getMsg(msg);
        this.errorCode = errorCode;
        this.whole = whole;
        this.timestamp = System.currentTimeMillis();
    }

    /********* S 返回正确的结果集 *************/

    /**
     * 返回正确结果不带数据
     *
     * @return
     */
    public static <T> JSONResultUtils<T> success() {
        return new JSONResultUtils<T>(true, ErrorCodeDefine.SUCCESS, ErrorCodeUtils.getMsg(ErrorCodeDefine.SUCCESS),
                null);
    }

    /**
     * 返回正确结果不带数据带消息
     *
     * @return
     */
    public static <T> JSONResultUtils<T> successWithMsg(String msg) {
        return new JSONResultUtils<T>(true, ErrorCodeDefine.SUCCESS, msg, null);
    }

    /**
     * 返回正确结果不带数据带消息
     *
     * @return
     */
    public static <T> JSONResultUtils<T> successWithMsgWhole(String msg) {
        return new JSONResultUtils<T>(true, ErrorCodeDefine.SUCCESS, msg, null, true);
    }

    /**
     * 返回
     *
     * @return
     */
    public static <T> JSONResultUtils<T> successWithData(T data) {
        return new JSONResultUtils<T>(true, ErrorCodeDefine.SUCCESS, ErrorCodeUtils.getMsg(ErrorCodeDefine.SUCCESS),
                data);
    }

    /**
     * 返回正确结果不带数据带消息
     *
     * @return
     */
    public static <T> JSONResultUtils<T> successWithMsgAndData(String msg, T data) {
        return new JSONResultUtils<T>(true, ErrorCodeDefine.SUCCESS, msg, data);
    }

    /********* E 返回正确的结果集 *************/

    /********* S 返回错误的结果集 *************/
    /**
     * 返回错误的结果带错误信息
     *
     * @param codeOrMsg 错误码或者错误信息
     * @return
     */
    public static <T> JSONResultUtils<T> error(String codeOrMsg) {
        return errorWithData(codeOrMsg, null);
    }

    public static <T> JSONResultUtils<T> errorWithMsg(String errorCode, String msg) {
        return new JSONResultUtils<T>(false, errorCode, msg, null);
    }

    public static <T> JSONResultUtils<T> errorWithData(String codeOrMsg, T data) {
        if (ErrorCodeUtils.hasDefined(codeOrMsg)) {
            return new JSONResultUtils<T>(false, codeOrMsg, ErrorCodeUtils.getMsg(codeOrMsg), data);
        }

        return new JSONResultUtils<T>(false, ErrorCodeDefine.UNDEFINED_ERROR, codeOrMsg, data);
    }

    public static <T> JSONResultUtils<T> errorWithMsgAndData(String errorCode, String msg, T data) {
        return new JSONResultUtils<T>(false, errorCode, msg, data);
    }
    /********* E 返回错误的结果集 *************/
}