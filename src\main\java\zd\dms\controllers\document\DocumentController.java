package zd.dms.controllers.document;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapUtils;
import zd.base.utils.model.ObjectMapperUtils;
import zd.dms.dto.PageResponse;
import zd.dms.entities.Document;
import zd.dms.entities.Folder;
import zd.dms.entities.FolderPermission;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.document.DocumentService;
import zd.dms.services.document.FolderService;
import zd.dms.services.security.FolderPermissionUtils;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "DocumentController", description = "文档Controller")
@RequestMapping("/document")
public class DocumentController extends ControllerSupport {

    private final DocumentService documentService;
    private final FolderService folderService;

    @Operation(summary = "文档列表")
    @GetMapping("/getDocument/{docId}")
    @ZDLog("文档列表")
    public JSONResultUtils<Object> list(@PathVariable String docId) {
        Document document = documentService.getDocumentById(docId);
        if (document == null) {
            return error("文档不存在");
        }

        return successData(ObjectMapperUtils.toMap(document, "info"));
    }

    @Operation(summary = "文档列表")
    @PostMapping("/list")
    @ZDLog("文档列表")
    public JSONResultUtils<Object> list(@RequestBody Map<String, Object> params) {
        Page<Document> page = documentService.list(params, getCurrentUser());

        return successData(PageResponse.of(page));
    }

    @Operation(summary = "文档回收站列表")
    @PostMapping("/getAllTrash")
    @ZDLog("文档回收站列表")
    public JSONResultUtils<Object> getAllTrash(@RequestBody Map<String, Object> params) {
        int pageNumber = MapUtils.getIntValue(params, "pageNumber", 1);
        int pageSize = MapUtils.getIntValue(params, "pageSize", 10);
        Page page = documentService.getAllTrash(pageNumber, pageSize);

        return successData(PageResponse.of(page));
    }

    @Operation(summary = "永久删除文档")
    @PostMapping("/deleteDocs")
    @ZDLog("永久删除文档")
    public JSONResultUtils<Object> deleteDocs(@RequestBody String[] ids) {
        documentService.deleteDocs(ids, getCurrentUser(), getIpAddress(), true);

        return success();
    }

    @Operation(summary = "批量删除文档")
    @PostMapping("/moveToTrash/{folderId}")
    @ZDLog("批量删除文档")
    public JSONResultUtils<Object> moveToTrash(@RequestBody String[] ids, @PathVariable long folderId) {

        documentService.moveDocsToTrash(folderId, ids, getCurrentUser(), getIpAddress());

        return success();
    }

    @Operation(summary = "还原文档")
    @PostMapping("/restoreDocs")
    @ZDLog("还原文档")
    public JSONResultUtils<Object> restoreDocs(@RequestBody String[] ids) {
        documentService.restoreDocs(ids, getCurrentUser(), getIpAddress());

        return success();
    }

    @Operation(summary = "移动文档")
    @PostMapping("/doMove")
    @ZDLog("移动文档")
    public JSONResultUtils<Object> doMove(@RequestBody Map<String, Object> params) {
        Long targetFolderId = MapUtils.getLongValue(params, "targetFolderId", 0L);
        List<String> docIds = ZDMapUtils.getListStringValue(params, "docIds");

        if (docIds == null || docIds.isEmpty()) {
            return error("请选择要移动的文档");
        }

        Folder targetFolder = folderService.getById(targetFolderId);
        if (targetFolder == null) {
            return error("目标目录不存在");
        }

        if (SystemConfigManager.getInstance().getBooleanProperty("moveToListPerm")) {
            checkPermission(targetFolder, "L");
        } else {
            checkPermission(targetFolder, "C");
        }

        if (targetFolder.isSearchFolder()) {
            return error("无法移动文档至高级搜索目录");
        }

        documentService.moveDocs(targetFolder, docIds.toArray(new String[0]), getCurrentUser(), getIpAddress(), false);

        return success();
    }

    @Operation(summary = "重命名文档")
    @PostMapping("/rename")
    @ZDLog("重命名文档")
    public JSONResultUtils<Object> rename(@RequestBody Map<String, Object> params) {
        String docId = MapUtils.getString(params, "docId", "");
        if (StringUtils.isBlank(docId)) {
            return error("请选择要重命名的文档");
        }

        String baseName = MapUtils.getString(params, "newBaseName", "");
        if (StringUtils.isBlank(baseName)) {
            return error("文件名为空");
        }

        Document document = documentService.getDocumentById(docId);
        if (document == null) {
            return error("文档不存在");
        }

        FolderPermissionUtils.checkPermissionWithThrowError(getCurrentUser(), document.getFolder(), FolderPermission.EDIT);

        document.setFilename(baseName + "." + FilenameUtils.getExtension(document.getFilename()));
        documentService.updateDocument(document, true);

        return success();
    }
}
