package zd.dms.controllers.document;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapUtils;
import zd.base.utils.model.ObjectMapperUtils;
import zd.base.utils.validate.ValidateUtils;
import zd.dms.entities.Folder;
import zd.dms.entities.FolderPermission;
import zd.dms.services.document.FolderService;
import zd.dms.services.security.FolderPermissionUtils;
import zd.dms.utils.folder.FolderTreeUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "FolderController", description = "文档目录Controller")
@RequestMapping("/document/folder")
public class FolderController extends ControllerSupport {

    private final FolderService folderService;

    @Operation(summary = "获取子目录")
    @GetMapping("/list/{parentId}")
    @ZDLog("获取子目录")
    public JSONResultUtils<Object> listGroups(@PathVariable long parentId) {
        List<Folder> resultsFolders = folderService.getChildren(parentId);
        FolderTreeUtils.setChildrenCount(resultsFolders, parentId);
        resultsFolders = FolderPermissionUtils.getFolderChildrenWithPermissions(resultsFolders, FolderPermission.LIST, getCurrentUser());

        // 获取所有子目录数据, 使用一个sql一次性查询，防止出现多个sql
        /*List<Long> childrenIds = resultsFolders.stream().map(AbstractSequenceEntity::getId).toList();
        Map<Long, Long> childCount = folderService.getChildCount(childrenIds);*/

        // 封装每个目录子目录数量
        /*resultsFolders.forEach(f -> {
            f.setParentId(parentId);

            Long id = f.getId();
            Long count = childCount.get(id);
            if (count == null) {
                count = 0L;
            }

            f.setChildCount(count);
        });*/

        return successData(ObjectMapperUtils.toMapList(resultsFolders, "list"));
    }

    @Operation(summary = "根据目录分区获取子目录")
    @GetMapping("/getTopFolderListByFolderAreaId/{folderAreaId}")
    @ZDLog("根据目录分区获取子目录")
    public JSONResultUtils<Object> getTopFolderListByFolderAreaId(@PathVariable long folderAreaId) {
        List<Folder> resultsFolders = folderService.getTopFolderListByFolderAreaId(folderAreaId);
        FolderTreeUtils.setChildrenCount(resultsFolders, 0);
        resultsFolders = FolderPermissionUtils.getFolderChildrenWithPermissions(resultsFolders, FolderPermission.LIST, getCurrentUser());

        // 获取所有子目录数据, 使用一个sql一次性查询，防止出现多个sql
        /*List<Long> childrenIds = resultsFolders.stream().map(AbstractSequenceEntity::getId).toList();
        Map<Long, Long> childCount = folderService.getChildCount(childrenIds);*/

        // 封装每个目录子目录数量
        /*resultsFolders.forEach(f -> {
            Long id = f.getId();
            Long count = childCount.get(id);
            if (count == null) {
                count = 0L;
            }

            f.setChildCount(count);
        });*/

        List<Map<String, Object>> list = ObjectMapperUtils.toMapList(resultsFolders, "list");
        list.forEach(map -> map.put("parentId", -folderAreaId));

        return successData(list);
    }

    @Operation(summary = "创建目录")
    @PostMapping("/create")
    @ZDLog("创建目录")
    public JSONResultUtils<Object> create(@RequestBody Map<String, Object> params) {
        return folderService.create(params, getCurrentUser());
    }

    @Operation(summary = "更新目录")
    @PostMapping("/update")
    @ZDLog("更新目录")
    public JSONResultUtils<Object> update(@RequestBody Map<String, Object> params) {
        return folderService.update(params, getCurrentUser());
    }

    @Operation(summary = "删除目录")
    @PostMapping("/delete/{id}")
    @ZDLog("删除目录")
    public JSONResultUtils<Object> delete(@PathVariable long id) {
        ValidateUtils.isTrue(id > 0, "请选择目录");

        return folderService.delete(id, getCurrentUser());
    }

    @Operation(summary = "获取目录")
    @PostMapping("/get/{id}")
    @ZDLog("获取目录")
    public JSONResultUtils<Object> get(@PathVariable long id) {
        ValidateUtils.isTrue(id > 0, "请选择目录");

        Folder folder = folderService.getById(id);
        if (folder.getParent() != null) {
            folder.setParentId(folder.getParent().getId());
        }

        return successData(ObjectMapperUtils.toMap(folder));
    }

    @Operation(summary = "加载选择文档目录树")
    @PostMapping("/getSelectedFolderTree")
    @ZDLog("加载选择文档目录树")
    public JSONResultUtils<Object> getSelectedFolderTree(@RequestBody Map<String, Object> params) {
        long parentId = MapUtils.getLongValue(params, "parentId", 0L);
        if (parentId > 0) {
            Folder parentFolder = folderService.getById(parentId);
            if (parentFolder == null) {
                return successData(new ArrayList<>());
            }

            List<Folder> resultsFolders = folderService.getChildren(parentId);
            FolderTreeUtils.setChildrenCount(resultsFolders, parentId);
            resultsFolders = FolderPermissionUtils.getFolderChildrenWithPermissions(resultsFolders, FolderPermission.LIST, getCurrentUser());

            // 获取所有子目录数据, 使用一个sql一次性查询，防止出现多个sql
            /*List<Long> childrenIds = resultsFolders.stream().map(AbstractSequenceEntity::getId).toList();
            Map<Long, Long> childCount = folderService.getChildCount(childrenIds);*/

            // 封装每个目录子目录数量
            /*resultsFolders.forEach(f -> {
                Long id = f.getId();
                Long count = childCount.get(id);
                if (count == null) {
                    count = 0L;
                }

                f.setChildCount(count);
            });*/

            List<Map<String, Object>> resultsData = ObjectMapperUtils.toMapList(resultsFolders, "selectedJson");
            return successData(resultsData);
        }

        List<Long> selectedFolderIds = ZDMapUtils.getListLongValue(params, "selectedFolderIds");
        selectedFolderIds.remove(null);
        List<Map<String, Object>> results = FolderTreeUtils.getTreeFoldersWithTopFolder(selectedFolderIds);

        return successData(results);
    }

    @Operation(summary = "搜索选择文档目录树")
    @PostMapping("/searchSelectedFolderTree")
    @ZDLog("搜索选择文档目录树")
    public JSONResultUtils<Object> searchSelectedFolderTree(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> results = new ArrayList<>();

        String keywords = MapUtils.getString(params, "keywords", "");
        if (StringUtils.isBlank(keywords)) {
            return successData(results);
        }

        results = FolderTreeUtils.searchTreeFolders(keywords, FolderPermission.LIST, getCurrentUser());
        return successData(results);
    }
}
