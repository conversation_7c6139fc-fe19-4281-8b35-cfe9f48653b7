package zd.dms.repositories.docsig.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.DocAutoSign;
import zd.dms.repositories.docsig.DocAutoSignRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;

public class DocAutoSignRepositoryDaoImpl extends BaseRepositoryDaoImpl<DocAutoSign, String> implements DocAutoSignRepository {

    public DocAutoSignRepositoryDaoImpl(Class<DocAutoSign> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<DocAutoSign> getAllSortAutoSign() {
        Specification<DocAutoSign> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocAutoSign> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.asc("numberIndex");
            specTools.asc("priorityLevel");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<DocAutoSign> getAutoSignByWidthAndHeight(int width, int height) {
        Specification<DocAutoSign> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocAutoSign> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.le("minWidth", width);
            specTools.ge("maxWidth", width);

            specTools.le("minHeight", height);
            specTools.ge("maxHeight", height);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }
}
