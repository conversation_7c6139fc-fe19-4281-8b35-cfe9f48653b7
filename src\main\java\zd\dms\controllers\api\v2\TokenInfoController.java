package zd.dms.controllers.api.v2;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import zd.base.utils.JSONResultUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 令牌信息控制器
 * 用于查询令牌信息
 */
@RestController
@RequestMapping("/api/v2/token")
@RequiredArgsConstructor
@Slf4j
public class TokenInfoController {

    private final OAuth2AuthorizationService authorizationService;

    /**
     * 查询令牌信息
     */
    @GetMapping("/info")
    public JSONResultUtils<Map<String, Object>> getTokenInfo(@RequestParam("token") String token) {
        log.info("查询令牌信息: {}", token);
        
        OAuth2Authorization authorization = authorizationService.findByToken(token, OAuth2TokenType.ACCESS_TOKEN);
        if (authorization == null) {
            return JSONResultUtils.error("令牌不存在或已过期");
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("id", authorization.getId());
        result.put("principal_name", authorization.getPrincipalName());
        result.put("registered_client_id", authorization.getRegisteredClientId());
        
        if (authorization.getAccessToken() != null) {
            Map<String, Object> accessTokenInfo = new HashMap<>();
            accessTokenInfo.put("token_value", authorization.getAccessToken().getToken().getTokenValue());
            accessTokenInfo.put("issued_at", authorization.getAccessToken().getToken().getIssuedAt());
            accessTokenInfo.put("expires_at", authorization.getAccessToken().getToken().getExpiresAt());
            accessTokenInfo.put("scopes", authorization.getAccessToken().getToken().getScopes());
            result.put("access_token", accessTokenInfo);
        }
        
        if (authorization.getRefreshToken() != null) {
            Map<String, Object> refreshTokenInfo = new HashMap<>();
            refreshTokenInfo.put("token_value", authorization.getRefreshToken().getToken().getTokenValue());
            refreshTokenInfo.put("issued_at", authorization.getRefreshToken().getToken().getIssuedAt());
            refreshTokenInfo.put("expires_at", authorization.getRefreshToken().getToken().getExpiresAt());
            result.put("refresh_token", refreshTokenInfo);
        }
        
        return JSONResultUtils.successWithData(result);
    }
}
