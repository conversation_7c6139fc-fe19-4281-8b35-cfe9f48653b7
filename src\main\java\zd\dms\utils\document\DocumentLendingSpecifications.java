package zd.dms.utils.document;

import jakarta.persistence.criteria.Predicate;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.Nullable;
import zd.base.utils.validate.ValidateUtils;
import zd.dms.entities.DocumentLending;

public class DocumentLendingSpecifications {

    public static Specification<DocumentLending> searchLendFromMe(String currentUsername,
        @Nullable String fileName, @Nullable String borrowerFullName) {
        return Specification.where(creatorEquals(currentUsername))
            .and(fileNameContainsIfNotBlank(fileName))
            .and(fullnamesContainsIfNotBlank(borrowerFullName));
    }

    public static Specification<DocumentLending> searchLendToMe(String currentUsername, @Nullable String fileName,
        @Nullable String lenderFullname) {
        ValidateUtils.notEmpty(currentUsername, "currentUsername参数不能为空");

        return (entity, query, builder) -> {
            Predicate predicate = builder.like(entity.get("usernames"), "%**" + currentUsername + "**%");

            if (StringUtils.isNotBlank(fileName)) {
                predicate = builder.and(builder.like(entity.get("fileName"), "%" + fileName + "%"));
            }

            if (StringUtils.isNotBlank(lenderFullname)) {
                predicate = builder.and(builder.like(entity.get("creatorFullname"), "%" + lenderFullname + "%"));
            }

            return predicate;
        };
    }

    private static Specification<DocumentLending> fileNameContainsIfNotBlank(String fileName) {
        return (entity, query, builder) -> Optional.ofNullable(fileName)
            .filter(StringUtils::isNotBlank)
            .map($ -> builder.like(entity.get("fileName"), "%" + fileName + "%"))
            .orElse(null);
    }

    private static Specification<DocumentLending> creatorEquals(String username) {
        return (entity, query, builder) -> builder.equal(entity.get("creator"), username);
    }

    private static Specification<DocumentLending> fullnamesContainsIfNotBlank(String fullName) {
        return (entity, query, cb) -> Optional.ofNullable(fullName)
            .filter(StringUtils::isNotBlank)
            .map($ -> cb.like(entity.get("fullnames"), "%" + fullName + "%"))
            .orElse(null);
    }
}
