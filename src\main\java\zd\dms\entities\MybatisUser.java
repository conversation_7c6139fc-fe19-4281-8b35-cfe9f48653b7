package zd.dms.entities;

import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.Setter;

/*@Getter
@Setter
@Entity
@TableName("test_user")*/
public class MybatisUser extends BaseEntity {

  /*@Column(name = "username", nullable = false)
  private String username;

  @Column(name = "name", nullable = false)
  private String name;*/
}
