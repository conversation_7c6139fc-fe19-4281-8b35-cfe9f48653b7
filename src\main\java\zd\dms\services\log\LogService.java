package zd.dms.services.log;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.dms.entities.Group;
import zd.dms.entities.LogMessage;
import zd.dms.entities.User;
import zd.dms.exception.EntityAlreadyExistsException;
import zd.dms.exception.UserAmountExceededException;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Transactional
public interface LogService extends BaseJpaService<LogMessage, Long> {

    /**
     * 根据id获取
     *
     * @param id id
     * @return id对应的对象
     */
    @Transactional(readOnly = true)
    LogMessage getById(Long id);

    /**
     * 根据时间获取日志列表
     *
     * @param pageNumber    page number
     * @param pageSize      page size
     * @param level         日志级别
     * @param startDate     开始时间
     * @param endDate       结束时间
     * @param orderProperty 排序字段
     * @param asc           是否为升序
     * @return 日志列表
     */
    @Transactional(readOnly = true)
    Page getLogs(int pageNumber, int pageSize, int level, Date startDate,
                 Date endDate, String orderProperty, boolean asc);

    List<LogMessage> getLogsByDate(Date startDate, Date endDate);

    void clearLogs();

    /**
     * 添加一个日志
     *
     * @param level    日志级别
     * @param operator 操作者
     * @param ip       IP
     * @param url      URL
     * @param msg      信息
     */
    void addMessage(int level, String operator, String ip, String msg);

    void addMessage(int level, String operator, String ip, String msg,
                    Throwable e);

    /**
     * 创建LogMessage
     *
     * @param logMsg 要创建的LogMessage
     */
    void create(LogMessage logMsg);

    /**
     * 删除LogMessage
     *
     * @param logMsg 要删除的LogMessage
     */
    void delete(LogMessage logMsg);
}
