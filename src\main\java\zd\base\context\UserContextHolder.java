//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                           O\  =  /O
//                        ____/`---'\____
//                      .'  \\|     |//  `.
//                     /  \\|||  :  |||//  \
//                    /  _||||| -:- |||||-  \
//                    |   | \\\  -  /// |   |
//                    | \_|  ''\---/''  |   |
//                    \  .-\__  `-`  ___/-. /
//                  ___`. .'  /--.--\  `. . __
//               ."" '<  `.___\_<|>_/___.'  >'"".
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |
//              \  \ `-.   \_ __\ /__ _/   .-` /  /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
//                    佛祖保佑       永无BUG
package zd.base.context;

import zd.dms.entities.User;

public class UserContextHolder {

    private static ThreadLocal<User> userContextHolder = new ThreadLocal<User>();

    /**
     * 设置用户对象
     *
     * @param user 用户对象
     */
    public static void setUser(User user) {
        userContextHolder.set(user);
    }

    /**
     * 获取用户对象
     *
     * @return 用户对象
     */
    public static User getUser() {
        return userContextHolder.get();
    }

    public static String getUsername() {
        User user = userContextHolder.get();
        if (user == null) {
            return "";
        }

        return user.getUsername();
    }

    public static String getFullname() {
        User user = userContextHolder.get();
        if (user == null) {
            return "";
        }

        return user.getFullname();
    }
}
