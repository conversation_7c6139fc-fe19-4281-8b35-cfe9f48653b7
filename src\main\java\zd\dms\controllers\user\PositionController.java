package zd.dms.controllers.user;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.BaseController;
import zd.base.service.BaseJpaService;
import zd.base.utils.JSONResultUtils;
import zd.dms.dto.PageResponse;
import zd.dms.entities.Group;
import zd.dms.entities.Position;
import zd.dms.entities.User;
import zd.dms.services.position.PositionService;
import zd.dms.services.user.UserService;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "PositionController", description = "岗位Controller")
@RequestMapping("/position")
public class PositionController extends BaseController<Position, String> {

    private final PositionService positionService;

    private final UserService userService;

    @Override
    public BaseJpaService<Position, String> getBaseJpaService() {
        return positionService;
    }

    @Operation(summary = "获取分页用户")
    @PostMapping("/getPages")
    @ZDLog("获取分页用户")
    public JSONResultUtils<Object> getPages(@RequestBody Map<String, Object> params) {
        int pageNumber = MapUtils.getIntValue(params, "pageNumber", 1);
        int pageSize = MapUtils.getIntValue(params, "pageSize", 10);
        String userKeywords = MapUtils.getString(params, "userKeywords", "");
        String groupKeywords = MapUtils.getString(params, "groupKeywords", "");
        String posKeywords = MapUtils.getString(params, "posKeywords", "");
        Page page = positionService.getPositionsPage(userKeywords, groupKeywords, posKeywords, pageNumber, pageSize);
        return successData(PageResponse.of(page, ""));
    }

    @Operation(summary = "保存岗位")
    @PostMapping("/savePosition")
    @ZDLog("保存岗位")
    public JSONResultUtils<Object> savePosition(@RequestBody Map<String, Object> params) {
        String id = MapUtils.getString(params, "id", "");
        String username = MapUtils.getString(params, "username", "");
        String groupCode = MapUtils.getString(params, "groupCode", "");
        String position = MapUtils.getString(params, "position", "");
        int numIndex = MapUtils.getIntValue(params, "numIndex", 9999);
        boolean enabled = MapUtils.getBoolean(params, "enabled", true);
        boolean useCurrentGroup = MapUtils.getBoolean(params, "useCurrentGroup", false);
        if (useCurrentGroup) {
            User user = userService.getUserByUsername(username);
            if (user == null) {
                return error("用户不存在");
            }

            List<Group> groups = user.getGroups();
            if (CollectionUtils.isNotEmpty(groups)) {
                groups.forEach(item -> {
                    String code = item.getCode();
                    if (StringUtils.isBlank(code)) {
                        return;
                    }

                    positionService.saveOrUpdatePosition(id, username, code, position, numIndex, enabled);
                });
            }

        } else {
            positionService.saveOrUpdatePosition(id, username, groupCode, position, numIndex, enabled);
        }

        return success();
    }

    @Operation(summary = "更改岗位启用状态")
    @PostMapping("/changeEnabled")
    @ZDLog("更改岗位启用状态")
    public JSONResultUtils<Object> changeEnabled(@RequestBody Map<String, Object> params) {
        String id = MapUtils.getString(params, "id", "");
        if (StringUtils.isBlank(id)) {
            return error("id不能为空");
        }

        boolean enabled = MapUtils.getBoolean(params, "enabled", true);
        positionService.updateEnabled(id, enabled);
        return success();
    }

    @Operation(summary = "删除岗位")
    @PostMapping("/deletePosition/{id}")
    @ZDLog("删除岗位")
    public JSONResultUtils<Object> deletePosition(@PathVariable String id) {
        if (StringUtils.isBlank(id)) {
            return error("id不能为空");
        }

        Position position = positionService.getPositionById(id);
        if (position == null) {
            return error("岗位不存在");
        }

        positionService.deletePosition(position);

        return success();
    }
}
