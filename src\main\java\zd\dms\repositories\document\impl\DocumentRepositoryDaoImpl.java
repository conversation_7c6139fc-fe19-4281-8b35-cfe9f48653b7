package zd.dms.repositories.document.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.support.JdbcUtils;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.base.utils.ZDDateUtils;
import zd.base.utils.ZDMapUtils;
import zd.dms.entities.Document;
import zd.dms.entities.Folder;
import zd.dms.repositories.document.DocumentRepository;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.utils.PageableUtils;
import zd.dms.utils.repositories.QueryParamsUtils;
import zd.dms.utils.repositories.SpecTools;
import zd.record.utils.RecordDBUtils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class DocumentRepositoryDaoImpl extends BaseRepositoryDaoImpl<Document, String> implements DocumentRepository {

    private static final String SAPERATOR = " ";

    public DocumentRepositoryDaoImpl(Class<Document> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<Document> getDocumentsByFolderWithoutDeletedByJDBC(Connection conn, String docSql, Long folderId, int startIndex, int endIndex) throws SQLException {
        SystemConfigManager scm = SystemConfigManager.getInstance();
        log.debug("getDocumentsByFolderWithoutDeletedByJDBC startIndex: {}, endIndex: {}, folderId:{}, sql: {}",
                new Object[]{startIndex, endIndex, folderId, docSql});

        PreparedStatement pstmt = null;
        ResultSet rs = null;
        List<Document> docList = new ArrayList<Document>();
        if (conn != null) {
            try {
                pstmt = conn.prepareStatement(docSql);
                if (RecordDBUtils.isOracle()) {
                    pstmt.setLong(1, folderId);
                    pstmt.setInt(2, 0);
                    pstmt.setLong(3, endIndex);
                    pstmt.setLong(4, startIndex);
                } else if (RecordDBUtils.isMysql()) {
                    pstmt.setLong(1, folderId);
                    pstmt.setInt(2, 0);
                    pstmt.setLong(3, startIndex);
                    pstmt.setLong(4, endIndex - startIndex);
                } else if (RecordDBUtils.isSQLServer()) {
                    pstmt.setLong(1, endIndex - startIndex);
                    pstmt.setLong(2, folderId);
                    pstmt.setInt(3, 0);
                    pstmt.setLong(4, startIndex);
                } else if (RecordDBUtils.isPostgreSQLOrUXOrHighgoOrKingbase()) {
                    pstmt.setLong(1, folderId);
                    pstmt.setInt(2, 0);
                    pstmt.setLong(3, endIndex - startIndex);
                    pstmt.setLong(4, startIndex);
                }

                rs = pstmt.executeQuery();
                while (rs.next()) {

                    Document doc = new Document();
                    doc.setId(rs.getString("id"));
                    doc.setSerialNumber(rs.getString("serialNumber"));
                    doc.setFilename(rs.getString("filename"));
                    doc.setExtension(rs.getString("extension"));
                    doc.setDocVersion(rs.getString("docVersion"));
                    doc.setNewestVersion(rs.getInt("newestVersion"));
                    doc.setFileSize(rs.getLong("fileSize"));
                    doc.setCreatorFullname(rs.getString("creatorFullname"));
                    doc.setCreator(rs.getString("creator"));
                    doc.setCreationDate(rs.getTimestamp("creationDate"));
                    doc.setModifiedDate(rs.getTimestamp("modifiedDate"));
                    doc.setFromScan(rs.getBoolean("fromScan"));
                    doc.setLocked(rs.getBoolean("locked"));
                    doc.setLockDate(rs.getDate("lockDate"));
                    doc.setLockedByFullname(rs.getString("lockedByFullname"));
                    doc.setPiId(rs.getString("piId"));
                    doc.setHash(rs.getString("hash"));
                    doc.setClickCount(rs.getInt("clickCount"));
                    doc.setDocProps(rs.getString("docProps"));
                    doc.setCustomDate1(rs.getTimestamp("customDate1"));
                    doc.setCustomDate2(rs.getTimestamp("customDate2"));

                    docList.add(doc);
                }

            } catch (SQLException e) {
                log.error("getDocumentsByFolderWithoutDeletedByJDBC", e);
                throw e;
            } finally {
                JdbcUtils.closeResultSet(rs);
                JdbcUtils.closeStatement(pstmt);
                JdbcUtils.closeConnection(conn);
            }
        }

        return docList;
    }

    @Override
    public List<Document> getDocumentByFolderAndSql(long folderId, String extraWhere) {
        if (StringUtils.isBlank(extraWhere)) {
            extraWhere = "1=1";
        }
        String hql = "from Document d where folderId = ?1 and " + extraWhere;
        return findAll(hql, folderId);
    }


    @Override
    public List<Document> getAllDocumentsWithoutDeletedByJDBC(Connection conn, String docSql, int startIndex, int endIndex) throws SQLException {
        SystemConfigManager scm = SystemConfigManager.getInstance();
        log.debug("getAllDocumentsWithoutDeletedByJDBC startIndex: {}, endIndex: {}, sql: {}", new Object[]{
                startIndex, endIndex, docSql});

        PreparedStatement pstmt = null;
        ResultSet rs = null;
        List<Document> docList = new ArrayList<Document>();
        if (conn != null) {
            try {
                pstmt = conn.prepareStatement(docSql);
                if (RecordDBUtils.isOracle()) {
                    pstmt.setInt(1, 0);
                    pstmt.setLong(2, endIndex);
                    pstmt.setLong(3, startIndex);
                } else if (RecordDBUtils.isMysql()) {
                    pstmt.setInt(1, 0);
                    pstmt.setLong(2, startIndex);
                    pstmt.setLong(3, endIndex - startIndex);
                } else if (RecordDBUtils.isSQLServer()) {
                    pstmt.setLong(1, endIndex - startIndex);
                    pstmt.setInt(2, 0);
                    pstmt.setLong(3, startIndex);
                } else if (RecordDBUtils.isPostgreSQLOrUXOrHighgoOrKingbase()) {
                    // TODO 未测试
                    pstmt.setInt(1, 0);
                    pstmt.setLong(2, endIndex - startIndex);
                    pstmt.setLong(3, startIndex);
                }

                rs = pstmt.executeQuery();
                while (rs.next()) {

                    Document doc = new Document();
                    doc.setSerialNumber(rs.getString("serialNumber"));
                    doc.setFilename(rs.getString("filename"));
                    doc.setExtension(rs.getString("extension"));
                    doc.setDocVersion(rs.getString("docVersion"));
                    doc.setNewestVersion(rs.getInt("newestVersion"));
                    doc.setFileSize(rs.getLong("fileSize"));
                    doc.setCreatorFullname(rs.getString("creatorFullname"));
                    doc.setCreator(rs.getString("creator"));
                    doc.setCreationDate(rs.getTimestamp("creationDate"));
                    doc.setModifiedDate(rs.getTimestamp("modifiedDate"));
                    doc.setFromScan(rs.getBoolean("fromScan"));
                    doc.setLocked(rs.getBoolean("locked"));
                    doc.setLockDate(rs.getDate("lockDate"));
                    doc.setLockedByFullname(rs.getString("lockedByFullname"));
                    doc.setPiId(rs.getString("piId"));
                    doc.setHash(rs.getString("hash"));
                    doc.setClickCount(rs.getInt("clickCount"));
                    doc.setDocProps(rs.getString("docProps"));
                    doc.setCustomDate1(rs.getTimestamp("customDate1"));
                    doc.setCustomDate2(rs.getTimestamp("customDate2"));
                    doc.setFolderId(rs.getLong("folder_id"));

                    docList.add(doc);
                }

            } catch (SQLException e) {
                log.error("getAllDocumentsWithoutDeletedByJDBC", e);
                throw e;
            } finally {
                JdbcUtils.closeResultSet(rs);
                JdbcUtils.closeStatement(pstmt);
                JdbcUtils.closeConnection(conn);
            }
        }

        return docList;
    }

    @Override
    public Document getDocumentByFolder(String filename, Folder folder) {
        Specification<Document> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(criteriaBuilder.equal(root.get("folderId"), folder.getId()));
            predicates.add(criteriaBuilder.equal(root.get("filename"), filename));

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates);
        };

        List<Document> docs = findAll(spec);
        if (!docs.isEmpty()) {
            return docs.get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<Document> getDocumentsByFolders(List<Long> folders) {
        if (folders == null || folders.size() == 0) {
            return new ArrayList<Document>();
        }

        String hql = "from Document d where d.folder.id in (" + StringUtils.join(folders, ",") + ")";
        return findAll(hql);
    }

    @Override
    public List<Document> getDocumentsByExtension(String extension) {
        Specification<Document> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(criteriaBuilder.equal(root.get("extension"), extension));

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates);
        };

        return findAll(spec);
    }

    @Override
    public Page<Document> getDocumentsWithoutAttach(int pageNumber, int pageSize) {
        Specification<Document> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<Document> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.le("attachRecId", 0);
            specTools.eqOrNull("attachRecTableName", null);

            specTools.asc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }

    @Override
    public List<Document> getMostReadDocuments() {
        Specification<Document> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            Order modifiedDateOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "clickCount", false);

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, modifiedDateOrder);
        };

        Page<Document> page = findAll(spec, PageableUtils.getPageable(1, 10));

        return page.getContent();
    }

    @Override
    public List<Document> getDocumentsByFolderWithoutDeleted(Folder folder, String sortField, boolean asc) {
        Specification<Document> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(criteriaBuilder.equal(root.get("folderId"), folder.getId()));
            predicates.add(criteriaBuilder.equal(root.get("deleted"), false));

            Order modifiedDateOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, sortField, asc, "modifiedDate", false);

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, modifiedDateOrder);
        };

        return findAll(spec);
    }

    @Override
    public Page<Document> getDocumentsByFolder(long folderId, String deleteByUsername, boolean isTrash, String keywords, Map<String, Object> advSearchKeywords, String sortField, boolean asc,
                                               int pageNumber, int pageSize) {
        Specification<Document> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (folderId > 0) {
                predicates.add(criteriaBuilder.equal(root.get("folderId"), folderId));
            }

            if (isTrash) {
                if (folderId <= 0) {
                    predicates.add(criteriaBuilder.equal(root.get("deletedBy"), deleteByUsername));
                }

                predicates.add(criteriaBuilder.equal(root.get("deleted"), true));
            } else {
                predicates.add(criteriaBuilder.equal(root.get("deleted"), false));
            }

            if (MapUtils.isNotEmpty(advSearchKeywords)) {
                addAdvSearchKeywords(criteriaBuilder, root, predicates, advSearchKeywords);
            } else {
                if (StringUtils.isNotBlank(keywords)) {
                    predicates.add(criteriaBuilder.like(root.get("filename"), "%" + keywords + "%"));
                }
            }

            Order modifiedDateOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, sortField, asc, "modifiedDate", false);

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, modifiedDateOrder);
        };

        return findAll(spec, PageableUtils.getPageable(pageNumber, pageSize));
    }

    private void addAdvSearchKeywords(CriteriaBuilder criteriaBuilder, Root<Document> root, List<Predicate> predicates, Map<String, Object> advSearchKeywords) {
        if (MapUtils.isEmpty(advSearchKeywords) || predicates == null) {
            return;
        }

        String filenameKeywords = MapUtils.getString(advSearchKeywords, "filename", "");
        if (StringUtils.isNotBlank(filenameKeywords)) {
            predicates.add(criteriaBuilder.like(root.get("filename"), "%" + filenameKeywords + "%"));
        }

        String creatorKeywords = MapUtils.getString(advSearchKeywords, "creator", "");
        if (StringUtils.isNotBlank(creatorKeywords)) {
            predicates.add(criteriaBuilder.like(root.get("creator"), "%" + creatorKeywords + "%"));
        }

        String creatorFullnameKeywords = MapUtils.getString(advSearchKeywords, "creatorFullname", "");
        if (StringUtils.isNotBlank(creatorFullnameKeywords)) {
            predicates.add(criteriaBuilder.like(root.get("creatorFullname"), "%" + filenameKeywords + "%"));
        }

        String contentKeywords = MapUtils.getString(advSearchKeywords, "content", "");
        if (StringUtils.isNotBlank(contentKeywords)) {
            predicates.add(criteriaBuilder.like(root.get("content"), "%" + contentKeywords + "%"));
        }

        String startCreationDateStr = MapUtils.getString(advSearchKeywords, "startCreationDateStr", "");
        if (StringUtils.isNotBlank(startCreationDateStr)) {
            Date startDate = ZDDateUtils.getStartDateByString(startCreationDateStr);
            if (startDate != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("creationDate"), startDate));
            }
        }

        String endCreationDateStr = MapUtils.getString(advSearchKeywords, "endCreationDateStr", "");
        if (StringUtils.isNotBlank(endCreationDateStr)) {
            Date endDate = ZDDateUtils.getEndDateByString(endCreationDateStr);
            if (endDate != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("creationDate"), endDate));
            }
        }
    }

    @Override
    public List<Document> getDeletedDocumentsByFolder(Folder folder) {
        Specification<Document> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(criteriaBuilder.equal(root.get("folderId"), folder.getId()));
            predicates.add(criteriaBuilder.equal(root.get("deleted"), false));

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates);
        };

        return findAll(spec);
    }

    @Override
    public List<Document> getAllDocumentsByFolder(Folder folder) {
        Specification<Document> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(criteriaBuilder.equal(root.get("folderId"), folder.getId()));

            Order modifiedDateOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "modifiedDate", false);

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, modifiedDateOrder);
        };

        return findAll(spec);
    }

    @Override
    public List<Document> getAllDocumentsByIndex(int startIndex, int pageSize) {
        pageSize = PageableUtils.getPageSize(pageSize);
        int pageNumber = PageableUtils.getPageNumberByStartIndex(startIndex, pageSize);

        Page<Document> page = getPage(pageNumber, pageSize);

        return page.getContent();
    }

    @Override
    public List<Document> getDocumentsInTrash(String username) {
        Specification<Document> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.isNotBlank(username)) {
                predicates.add(criteriaBuilder.equal(root.get("deletedBy"), username));
            }

            predicates.add(criteriaBuilder.equal(root.get("deleted"), true));

            Order modifiedDateOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "deleteDate", false);

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, modifiedDateOrder);
        };

        return findAll(spec);
    }

    @Override
    public List<Document> getDocumentsInTrash(String username, int type) {
        Specification<Document> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.isNotBlank(username)) {
                predicates.add(criteriaBuilder.equal(root.get("deletedBy"), username));
            }

            predicates.add(criteriaBuilder.equal(root.get("deleted"), true));

            // 根据folderType过滤
            if (type > 0) {
                // 创建子查询获取指定folderType的folderId列表
                jakarta.persistence.criteria.Subquery<Long> subquery = criteriaQuery.subquery(Long.class);
                jakarta.persistence.criteria.Root<Folder> folderRoot = subquery.from(Folder.class);
                subquery.select(folderRoot.get("id"));
                subquery.where(criteriaBuilder.equal(folderRoot.get("folderType"), type));

                // 使用IN条件将Document的folderId与子查询结果匹配
                predicates.add(root.get("folderId").in(subquery));
            }

            Order modifiedDateOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "deleteDate", false);

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, modifiedDateOrder);
        };

        return findAll(spec);
    }

    @Override
    public Page getAllTrash(int pageNumber, int pageSize) {
        Specification<Document> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(criteriaBuilder.equal(root.get("deleted"), true));

            Order modifiedDateOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "deleteDate", false);

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, modifiedDateOrder);
        };

        return findAll(spec, pageNumber, pageSize);
    }

    @Override
    public int getDocumentCountByFolder(Folder folder) {
        return NumberUtils.toInt(String
                .valueOf(findObject(
                        "select count(*) from Document as d where d.folderId = ?1 and d.deleted = false",
                        new Object[]{folder.getId()})));
    }

    @Override
    public int getDocumentCountByFolderWithDeleted(Folder folder) {
        return NumberUtils.toInt(String.valueOf(findObject(
                "select count(*) from Document as d where d.folderId = ?1", new Object[]{folder.getId()})));
    }

    @Override
    public boolean isDocumentInFolder(String filename, Folder folder, Document oriDoc) {
        if (oriDoc != null) {
            return NumberUtils.toInt(String.valueOf(findObject(
                    "select count(*) from Document as d where d.folderId = ?1 and d.filename = ?2 and d <> ?3",
                    new Object[]{folder.getId(), filename, oriDoc}))) > 0;
        } else {
            return NumberUtils.toInt(String.valueOf(findObject(
                    "select count(*) from Document as d where d.folderId = ?1 and d.filename = ?2", new Object[]{folder.getId(),
                            filename}))) > 0;
        }
    }

    @Override
    public int getDCWithDeleted() {
        return NumberUtils.toInt(String.valueOf(findObject("select count(*) from Document")));
    }

    @Override
    public int getDCWithoutDeleted() {
        return NumberUtils.toInt(String
                .valueOf(findObject("select count(*) from Document as d where d.deleted = false")));
    }

    @Override
    public void incrementDocumentClickCount(Document doc) {
        String hql = "update Document d set d.clickCount=d.clickCount+1 where d.id = ?1";
        executeUpdate(hql, doc.getId());
    }

    @Override
    public void resetEditingDocument(String username) {
        String hql = "update Document d set d.editing=false where d.editingBy = ?1";
        executeUpdate(hql, username);
    }

    @Override
    public void resetAllEditingDocument() {
        String hql = "update Document d set d.editing=false where d.editing=true";
        executeUpdate(hql);
    }

    @Override
    public List<Document> getDocumentInEditing(String username) {
        Specification<Document> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("editing"), true));
            predicates.add(criteriaBuilder.equal(root.get("editingBy"), username));

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates);
        };

        return findAll(spec);
    }

    @Override
    public List<Document> getDocumentLogByUser(String username, int logType, Date afterDate) {
        return null;
    }

    @Override
    public Document getDocumentBySerial(String serial) {
        Specification<Document> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("serialNumber"), serial));

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates);
        };

        return findSingleObject(spec);

    }

    @Override
    public List<Document> getUploadDocumentLogByUser(String username, Date afterDate) {
        return null;
    }

    @Override
    public Page searchDocsByPage(int pageNumber, int pageSize, String content, String filename, String
            creatorFullname, String sn, Date creationStartDate, Date creationEndDate, Date updateStartDate, Date
                                         updateEndDate, Date customDate1Start, Date customDate1End, Date customDate2Start, Date customDate2End, String
                                         docProps, List<Long> searchFolderIds, boolean withAnd) {
        if (CollectionUtils.isEmpty(searchFolderIds)) {
            return new PageImpl(new ArrayList());
        }

        Specification<Document> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.isNotBlank(content)) {
                predicates.add(criteriaBuilder.like(root.get("content"), "%" + content + "%"));
            }

            if (StringUtils.isNotBlank(filename)) {
                predicates.add(criteriaBuilder.like(root.get("filename"), "%" + filename + "%"));
            }

            if (StringUtils.isNotBlank(creatorFullname)) {
                predicates.add(criteriaBuilder.like(root.get("creatorFullname"), "%" + creatorFullname + "%"));
            }

            if (StringUtils.isNotBlank(sn)) {
                predicates.add(criteriaBuilder.equal(root.get("serialNumber"), sn));
            }

            if (creationStartDate != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("creationDate"), creationStartDate));
            }

            if (creationEndDate != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("creationDate"), creationEndDate));
            }

            if (updateStartDate != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("modifiedDate"), updateStartDate));
            }

            if (updateEndDate != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("modifiedDate"), updateEndDate));
            }

            if (customDate1Start != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("customDate1"), customDate1Start));
            }

            if (customDate1End != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("customDate1"), customDate1End));
            }

            if (customDate2Start != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("customDate2"), customDate2Start));
            }

            if (customDate2End != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("customDate2"), customDate2End));
            }

            predicates.add(criteriaBuilder.equal(root.get("deleted"), false));

            if (StringUtils.isNotBlank(docProps)) {
                String[] split = docProps.split(SAPERATOR);
                for (String prop : split) {
                    predicates.add(criteriaBuilder.like(root.get("docProps"), "%\"" + prop + "\"%"));
                }
            }

            // sqlserver数据库：rpc请求中参数最多为2100，oracle数据库单个in的个数不能超过1000，所以将目录id进行拼接
            predicates.add(root.get("folderId").in(searchFolderIds));

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates);
        };

        return findAll(spec, PageableUtils.getPageable(pageNumber, pageSize));
    }

    @Override
    public List<String> getImageList(long folderId) {
        String hql = "select id from Document d where d.folderId = " + folderId + " and deleted = false" +
                " and d.extension in ('png','jpg','gif','jpeg','bmp') order by d.modifiedDate desc";

        TypedQuery<String> query = this.entityManager.createQuery(hql, String.class);
        return query.getResultList();
    }

    @Override
    public List<Document> getDocumentByDocProp(String sql) {
        String term = "";
        if (StringUtils.isBlank(sql)) {
            term = "1!=1";
        } else {
            term = "1=1";
        }
        String endSql = "select t1.* from tdms_doc  t1 right join (select docId from tdms_doc_prop where " + term +
                " " + sql + ") t2 on t1.id = t2.docId";
        log.debug("endSql:{}", endSql);
        final String QUERY_SQL = endSql;

        Query nativeQuery = this.entityManager.createNativeQuery(endSql, Document.class);
        return nativeQuery.getResultList();
    }
}
