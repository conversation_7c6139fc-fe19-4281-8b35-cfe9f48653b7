package zd.dms.controllers.oauth2;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;
import zd.base.config.oauth2.DefaultGrantTypeFilter;
import zd.base.utils.JSONResultUtils;
import zd.dms.entities.oauth2.OAuth2Client;
import zd.dms.services.oauth2.OAuth2ClientService;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 令牌控制器
 * 用于手动处理OAuth2令牌请求
 */
@RestController
@RequestMapping("/oauth2/manual")
@RequiredArgsConstructor
@Slf4j
public class TokenController {

    private final OAuth2ClientService oAuth2ClientService;

    private final PasswordEncoder passwordEncoder;

    /**
     * 获取访问令牌
     */
    @PostMapping("/token")
    public JSONResultUtils<Object> getToken(
            @RequestParam("grant_type") String grantType,
            @RequestParam("client_id") String clientId,
            @RequestParam("client_secret") String clientSecret,
            @RequestParam(value = "scope", required = false, defaultValue = "api") String scope) {

        log.info("手动令牌端点被调用: grantType={}, clientId={}, scope={}", grantType, clientId, scope);

        // 查找客户端
        OAuth2Client client = oAuth2ClientService.findByClientId(clientId);
        if (client == null) {
            return JSONResultUtils.error("无效的客户端ID");
        }

        // 验证客户端密钥
        // 注意：这里简化了验证逻辑，实际应用中应该使用PasswordEncoder进行加密比较
        if (passwordEncoder.matches(clientSecret, client.getClientSecret())) {
            return JSONResultUtils.error("无效的客户端密钥");
        }

        // 验证授权类型
        if (StringUtils.isBlank(grantType)) {
            grantType = DefaultGrantTypeFilter.DEFAULT_GRANT_TYPE;
        }

        // 生成访问令牌
        // 注意：这里简化了令牌生成逻辑，实际应用中应该使用OAuth2TokenGenerator
        Map<String, Object> result = new HashMap<>();
        result.put("access_token", UUID.randomUUID().toString());
        result.put("token_type", "Bearer");
        result.put("expires_in", 3600);
        result.put("scope", scope);

        return JSONResultUtils.successWithData(result);
    }

    /**
     * 测试令牌端点
     */
    @GetMapping("/test")
    public JSONResultUtils<Object> testToken() {
        log.info("令牌测试端点被调用");

        Map<String, Object> result = new HashMap<>();
        result.put("message", "令牌端点测试成功");
        result.put("manual_token_endpoint", "/oauth2/manual/token");
        result.put("client_id", "demo-client");
        result.put("client_secret", "demo-secret");
        result.put("grant_type", "client_credentials");

        return JSONResultUtils.successWithData(result);
    }
}
