package zd.dms.controllers.oauth2;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.context.AuthorizationServerContextHolder;
import org.springframework.security.oauth2.server.authorization.token.DefaultOAuth2TokenContext;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenContext;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenGenerator;
import org.springframework.web.bind.annotation.*;
import zd.base.utils.JSONResultUtils;
import zd.dms.services.oauth2.OAuth2ClientService;

import java.time.Instant;
import java.util.*;

/**
 * 令牌控制器
 * 用于手动处理OAuth2令牌请求
 */
@RestController
@RequestMapping("/oauth2/manual")
@RequiredArgsConstructor
@Slf4j
public class TokenController {

    private final OAuth2ClientService oAuth2ClientService;
    private final PasswordEncoder passwordEncoder;
    private final OAuth2TokenGenerator<?> tokenGenerator;
    private final OAuth2AuthorizationService authorizationService;
    private final RegisteredClientRepository registeredClientRepository;

    /**
     * 获取访问令牌
     */
    @PostMapping("/token")
    public JSONResultUtils<Object> getToken(
            @RequestParam("client_id") String clientId,
            @RequestParam("client_secret") String clientSecret,
            @RequestParam(value = "scope", required = false, defaultValue = "api") String scope) {

        log.info("手动令牌端点被调用: , clientId={}, clientSecret={}, scope={}", clientId, clientSecret, scope);

        try {
            // 查找注册的客户端
            RegisteredClient registeredClient = registeredClientRepository.findByClientId(clientId);
            if (registeredClient == null) {
                log.warn("无效的客户端ID: {}", clientId);
                return JSONResultUtils.error("无效的客户端ID");
            }

            // 验证客户端密钥 - 修复逻辑错误
            if (!passwordEncoder.matches(clientSecret, registeredClient.getClientSecret())) {
                log.warn("客户端密钥验证失败: clientId={}", clientId);
                return JSONResultUtils.error("无效的客户端密钥");
            }

            // 生成OAuth2令牌
            Map<String, Object> tokenResponse = generateOAuth2Token(registeredClient, scope);

            log.info("成功生成令牌: clientId={}, scope={}", clientId, scope);
            return JSONResultUtils.successWithData(tokenResponse);

        } catch (Exception e) {
            log.error("生成令牌时发生错误: clientId={}, error={}", clientId, e.getMessage(), e);
            return JSONResultUtils.error("令牌生成失败: " + e.getMessage());
        }
    }

    /**
     * 生成OAuth2令牌并存储到Redis
     */
    private Map<String, Object> generateOAuth2Token(RegisteredClient registeredClient, String scope) {
        // 创建授权对象
        OAuth2Authorization.Builder authorizationBuilder = OAuth2Authorization.withRegisteredClient(registeredClient)
                .principalName(registeredClient.getClientId())
                .authorizationGrantType(org.springframework.security.oauth2.core.AuthorizationGrantType.CLIENT_CREDENTIALS);

        // 设置授权范围
        if (StringUtils.isNotBlank(scope)) {
            Set<String> scopes = new HashSet<>(Arrays.asList(scope.split(",")));
            authorizationBuilder.authorizedScopes(scopes);
        }

        OAuth2Authorization authorization = authorizationBuilder.build();

        // 创建认证对象
        Authentication principal = new AnonymousAuthenticationToken(
                "key", registeredClient.getClientId(), AuthorityUtils.createAuthorityList("ROLE_CLIENT"));

        // 生成访问令牌
        DefaultOAuth2TokenContext.Builder tokenContextBuilder = DefaultOAuth2TokenContext.builder()
                .registeredClient(registeredClient)
                .principal(principal)
                .authorizationServerContext(AuthorizationServerContextHolder.getContext())
                .authorization(authorization)
                .tokenType(OAuth2TokenType.ACCESS_TOKEN)
                .authorizationGrantType(org.springframework.security.oauth2.core.AuthorizationGrantType.CLIENT_CREDENTIALS);

        if (StringUtils.isNotBlank(scope)) {
            Set<String> scopes = new HashSet<>(Arrays.asList(scope.split(",")));
            tokenContextBuilder.authorizedScopes(scopes);
        }

        OAuth2TokenContext tokenContext = tokenContextBuilder.build();
        OAuth2AccessToken accessToken = (OAuth2AccessToken) tokenGenerator.generate(tokenContext);

        if (accessToken == null) {
            throw new RuntimeException("无法生成访问令牌");
        }

        // 更新授权对象，添加访问令牌
        authorization = OAuth2Authorization.from(authorization)
                .token(accessToken)
                .build();

        // 保存到Redis
        authorizationService.save(authorization);

        // 构建响应
        Map<String, Object> tokenResponse = new HashMap<>();
        tokenResponse.put("access_token", accessToken.getTokenValue());
        tokenResponse.put("token_type", accessToken.getTokenType().getValue());
        tokenResponse.put("expires_in", getExpiresIn(accessToken));
        tokenResponse.put("scope", scope);

        log.debug("令牌已保存到Redis: tokenValue={}, expiresAt={}",
                accessToken.getTokenValue(), accessToken.getExpiresAt());

        return tokenResponse;
    }

    /**
     * 计算令牌过期时间（秒）
     */
    private long getExpiresIn(OAuth2AccessToken accessToken) {
        if (accessToken.getExpiresAt() != null) {
            return accessToken.getExpiresAt().getEpochSecond() - Instant.now().getEpochSecond();
        }
        return 3600; // 默认1小时
    }

    /**
     * 测试令牌端点
     */
    @GetMapping("/test")
    public JSONResultUtils<Object> testToken() {
        log.info("令牌测试端点被调用");

        Map<String, Object> result = new HashMap<>();
        result.put("message", "令牌端点测试成功");
        result.put("manual_token_endpoint", "/oauth2/manual/token");
        result.put("standard_token_endpoint", "/oauth2/manual/oauth2/token");
        result.put("client_id", "demo-client");
        result.put("client_secret", "demo-secret");
        result.put("grant_type", "client_credentials");

        return JSONResultUtils.successWithData(result);
    }
}
