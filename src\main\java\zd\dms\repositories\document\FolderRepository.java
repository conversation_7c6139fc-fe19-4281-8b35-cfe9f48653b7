package zd.dms.repositories.document;

import zd.base.repositories.BaseRepository;
import zd.dms.entities.Folder;
import zd.dms.entities.FolderArea;
import zd.record.entities.RecFolder;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface FolderRepository extends BaseRepository<Folder, Long> {

    List<Map<String, Object>> getChildCount(List<Long> parentIds);

    List<Folder> getTopFolderListByFolderAreaId(FolderArea folderArea);

    List<Folder> getChildren(long parentId);

    List<Long> getFolderIdsByLimit(int limit);

    List<Long> getSubFolderIds(List<Long> parentIds);

    List<Folder> getFoldersByNameAndParent(Folder parentFolder, String name);

    List<Folder> findFolders(String keywords);

    /**
     * 根据treeName获取指定目录分区下的的顶级公共目录
     *
     * @param treeName
     * @return
     */
    List<Folder> getPubTopFoldersByTreeName(String treeName);

    /**
     * 从父目录中根据子目录的目录名查询一个子目录
     *
     * @param parentFolder    父目录
     * @param childFolderName 子目录名
     * @return
     */
    Folder getChildFolderByName(Folder parentFolder, String childFolderName);

    /**
     * 获取folder:目录回收站
     *
     * @return
     */
    Folder getTrashFolder();

    /**
     * 根据目录名称，模糊查询出相应的目录
     *
     * @param folderNameKeyWordArray
     * @return
     */
    List<Folder> findFoldersByName(String[] folderNameKeyWordArray);

    void updateNullNumIndex(int numIndex);

    List<Long> getTopFolderIdsByType(int type);

    List<Folder> getTopFoldersByType(int type);

    List<Folder> getTopFoldersByType(int type, String username);

    List<Folder> getSubFoldersByFolder(Folder folder);

    int getPublicFolderCount();

    int isFolderNameInFolder(String name, Folder parent, Folder oriFolder, int folderType, String creator);

    List<Folder> searchFoldersByNameAndDescription(String name, String description);

    List<Folder> getFoldersByName(String folderName);

    List<Folder> getAllMyFolders(String username);

    List<Folder> getFoldersByIndex(int startIndex, int pageSize);

    long getSumFileSize(List<Long> folderIds);

    int isFolderNameInFolder(String name, Folder parent, Folder oriFolder);
}
