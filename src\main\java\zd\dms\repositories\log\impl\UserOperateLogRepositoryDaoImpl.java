package zd.dms.repositories.log.impl;

import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.UserOperateLog;
import zd.dms.repositories.log.UserOperateLogRepository;
import zd.dms.utils.PageableUtils;
import zd.dms.utils.repositories.SpecTools;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class UserOperateLogRepositoryDaoImpl extends BaseRepositoryDaoImpl<UserOperateLog, Long> implements UserOperateLogRepository {

    public UserOperateLogRepositoryDaoImpl(Class<UserOperateLog> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<UserOperateLog> getLogs(String username, int type) {
        Specification<UserOperateLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<UserOperateLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("operator", username);
            specTools.eq("operateType", type);
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<UserOperateLog> getLogs(int type, int startIndex, int pageSize) {
        Specification<UserOperateLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<UserOperateLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("operateType", type);
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        int pageNumber = PageableUtils.getPageNumberByStartIndex(startIndex, pageSize);

        Page<UserOperateLog> page = findAll(spec, pageNumber, pageSize);
        return page.getContent();
    }

    @Override
    public void createUserOpLog(String operator, String operatorFullname, int operateType, String filename, String folderPath, String docId, long folderId) {
        UserOperateLog log = new UserOperateLog();
        log.setDocId(docId);
        log.setFilename(filename);
        log.setFullname(operatorFullname);
        log.setOperator(operator);
        log.setOperateType(operateType);
        log.setFolderPath(folderPath);
        log.setFolderId(folderId);
        save(log);
    }

    @Override
    public void deleteOutdatedLogs(String operator, int operateType) {
        Specification<UserOperateLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<UserOperateLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("operator", operator);
            specTools.eq("operateType", operateType);
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        List<UserOperateLog> allLogs = findAll(spec);

        int i = 0;
        List<Long> deleteIds = new ArrayList<Long>();
        for (UserOperateLog l : allLogs) {
            if (l == null) {
                continue;
            }

            if (i > 20) {
                deleteIds.add(l.getId());
            }
            i++;
        }

        log.debug("deleteOutdatedLogs delete size: {}", deleteIds.size());

        if (!deleteIds.isEmpty()) {
            String hql = "delete from UserOperateLog t where t.id in (" +
                    StringUtils.join(deleteIds, ",") + ") and operator='" +
                    operator + "' and operateType = " + operateType;
            executeUpdate(hql);
        }
    }

    @Override
    public void deleteLogByUserAndTypeAndDocId(String operator, int operateType, String docId) {
        String hql = "delete from UserOperateLog where operator='" + operator +
                "' and operateType=" + operateType + " and docId='" + docId +
                "'";
        int result = executeUpdate(hql);
        log.debug("deleteLogByUserAndTypeAndDocId result: {}", result);
    }

    @Override
    public void deleteAllUserOperateLogs(String operator) {
        String hql = "delete from UserOperateLog where operator='" + operator +
                "'";
        executeUpdate(hql);
    }

    @Override
    public void deleteLogByDocId(String docId) {
        String hql = "delete from UserOperateLog where docId='" + docId + "'";
        executeUpdate(hql);
    }
}
