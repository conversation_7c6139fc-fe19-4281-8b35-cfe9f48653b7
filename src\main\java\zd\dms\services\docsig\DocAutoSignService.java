package zd.dms.services.docsig;

import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.dms.entities.DocAutoSign;

import java.io.IOException;
import java.util.List;

@Transactional
public interface DocAutoSignService extends BaseJpaService<DocAutoSign, String> {

    @Transactional(readOnly = true)
    DocAutoSign getById(String id);

    @Transactional(readOnly = true)
    List<DocAutoSign> getAllDocAutoSign();

    @Transactional(readOnly = true)
    List<DocAutoSign> getAllSortAutoSign();

    void delete(DocAutoSign docAutoSign);

    void delete(String id) throws IOException;

    void create(DocAutoSign docAutoSign);

    void update(DocAutoSign docAutoSign);

    List<DocAutoSign> getAutoSignByWidthAndHeight(int width, int height);
}
