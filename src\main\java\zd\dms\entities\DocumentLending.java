package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import zd.base.entities.AbstractSequenceEntity;
import zd.base.utils.ZDDateUtils;
import zd.base.utils.system.SpringUtils;
import zd.dms.services.document.DocumentService;

import java.util.Date;


/**
 * DocumentLending Domain Object
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Entity
@Table(name = "document_lending",
        indexes = {
                @Index(name = "i_dlending_docId", columnList = "docId"),
                @Index(name = "i_dlending_cd", columnList = "creationDate"),
        }
)
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class DocumentLending extends AbstractSequenceEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 借出者全名
     */
    private String creatorFullname;

    /**
     * 阅读者用户名列表 使用**username**分隔
     */
    @Column(length = Length.LOB_DEFAULT)
    private String usernames;

    /**
     * 阅读者全名
     */
    @Column(length = Length.LOB_DEFAULT)
    private String fullnames;

    /**
     * 借阅结束时间
     */
    private Date endDate;

    /**
     * 权限
     */
    private String permission;

    private String docId;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 借阅时的文件名，可能会被修改
     */
    private String fileName;

    /**
     * 默认构造器
     */
    public DocumentLending() {
        super();
        creationDate = new Date();
    }

    public Document getDocument() {
        return SpringUtils.getBean(DocumentService.class).getDocumentById(docId);
    }

    /**
     * 返回小时与时间
     */
    public String getRemainDaysAndHours() {
        int remainDaysAndHours = ZDDateUtils.betweenInHour(endDate, new Date());
        if (remainDaysAndHours < 0) {
            remainDaysAndHours = 0;
        }
        if (remainDaysAndHours <= 24) {
            return remainDaysAndHours + "小时";
        }

        return remainDaysAndHours % 24 == 0 ? remainDaysAndHours / 24 + "天 " : remainDaysAndHours / 24 + "天 " +
                remainDaysAndHours % 24 + "小时";
    }

    public int getRemainHours() {
        return ZDDateUtils.betweenInHour(endDate, new Date());
    }

    public String computeFileName30(){
        if (StringUtils.isBlank(fileName) || fileName.length() < 30) {
            return fileName;
        }

        return fileName.substring(0, 15) + "..." + fileName.substring(fileName.length() - 12);
    }
}
