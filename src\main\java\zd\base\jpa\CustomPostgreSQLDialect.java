package zd.base.jpa;

import org.hibernate.dialect.PostgreSQLDialect;

public class CustomPostgreSQLDialect extends PostgreSQLDialect {

    /*@Override
    protected String columnType(int sqlTypeCode) {
        switch (sqlTypeCode) {
            case -15:
                return this.columnType(1);
            case -9:
                return this.columnType(12);
            case -6:
                return "smallint";
            case -3:
            case -2:
            case 4003:
                return "bytea";
            case 2004:
            case 2011:
                return "oid";
            case 3003:
                return this.columnType(2014);
            case 4001:
            case 2005:
            case 4002:
                return "text";
            default:
                return super.columnType(sqlTypeCode);
        }
    }*/
}
