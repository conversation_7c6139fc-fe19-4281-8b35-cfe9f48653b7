package zd.dms.controllers.workflow;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.model.ObjectMapperUtils;
import zd.dms.workflow.engine.ProcessService;
import zd.dms.workflow.entities.SSProcessDefinition;
import zd.dms.workflow.entities.SSProcessInstance;
import zd.dms.workflow.utils.WorkflowNodeUtils;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "WorkflowUserController", description = "流程Controller")
@RequestMapping("/workflow")
public class WorkflowUserController extends ControllerSupport {

    private final ProcessService processService;

    @Operation(summary = "获取流程定义")
    @GetMapping("/getPi/{id}")
    @ZDLog("获取流程定义")
    public JSONResultUtils<Object> getPi(@PathVariable String id) {
        SSProcessInstance pi = processService.getProcessInstance(id);
        Map<String, Object> map = ObjectMapperUtils.toMap(pi);

        String nodeJson = MapUtils.getString(map, "nodeJson");
        String tasksNodeJson = WorkflowNodeUtils.getTasksNodeJson(pi, nodeJson);
        map.put("nodeJson", tasksNodeJson);

        return JSONResultUtils.successWithData(map);
    }
}
