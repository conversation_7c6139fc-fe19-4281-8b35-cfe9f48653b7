package zd.dms.repositories.workflow;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.User;
import zd.dms.workflow.entities.SSProcessInstance;

import java.util.Date;
import java.util.List;


public interface ProcessInstanceRepository extends BaseRepository<SSProcessInstance, String> {

    Page getProcessInstancesOrderByStatus(String username, String resourceType, String recPiType, int pageNumber,
                                          int pageSize);

    /**
     * 通过数据id：recId,查询该数据的审批记录
     *
     * @param recId
     * @param pageNumber
     * @param pageSize
     * @param includeNoEnded    查询的数据是否包含未完结审批记录，
     *                          true:查询所有审批记录，包含未完结审批记录，false：只查询已完结审批记录
     * @param resourceTableName 数据所在的表名
     * @return
     */
    Page getProcessInstancesByRecId(long recId, int pageNumber, int pageSize, boolean includeNoEnded,
                                    String resourceTableName);

    /**
     * 查询我转办的流程
     *
     * @param username     初始转办人
     * @param includeEnded true:查询所有审批记录，包含已完结审批记录，false：只查询未完结审批记录
     * @return
     */
    List<SSProcessInstance> getMyForwardProcessInstance(String username, String resourceType, boolean includeEnded);

    Page getProcessInstances(String keywords, String username, String resourceType, String pdType, int status, int pageNumber, int pageSize);

    long getProcessInstancesCount(String username, String resourceType, String pdType, int status);

    Page getRecordPis(long recId, String tableName, String username, String resourceType, String pdType, int pageNumber, int pageSize);

    /**
     * 查询文档的审批记录
     *
     * @param resourceId
     * @param pageNumber
     * @param pageSize
     * @param includeNoEnded 查询的数据是否包含未完结审批记录，
     *                       true:查询所有审批记录，包含未完结审批记录，false：只查询已完结审批记录
     * @return
     */
    Page getProcessInstancesByResId(String resourceId, int pageNumber, int pageSize, boolean includeNoEnded);

    List<SSProcessInstance> getProcessInstanceByResourceIdAndType(String resId, String resType);

    List<SSProcessInstance> searchProcessInstance(String keywords, User operator, String resType, String recPiType,
                                                  Date startDate, Date endDate);

    List<SSProcessInstance> getProcessInstanceByCreationDate(Date startDate, Date endDate, String resourceType);

    List<SSProcessInstance> getNeedRemindProcessInstance();

    List<SSProcessInstance> getMyHandlePis(String username, String searchKeyword);

    // void deleteProcessInstanceByResourceIdAndType(String resId, String
// resType);

    /**
     * 获取与记录关联的最新流程实例
     *
     * @param recId 记录ID
     * @param tableName 表名
     * @return 最新的流程实例，如果没有则返回null
     */
    SSProcessInstance getLatestProcessInstanceByRecId(long recId, String tableName);
}
