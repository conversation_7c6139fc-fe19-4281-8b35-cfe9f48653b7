package zd.dms.controllers.document;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapUtils;
import zd.base.utils.model.ObjectMapperUtils;
import zd.dms.entities.FolderPermission;
import zd.dms.services.security.PermissionService;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "FolderPermissionController", description = "数据目录Controller")
@RequestMapping("/document/folder/permission")
public class FolderPermissionController extends ControllerSupport {

    private final PermissionService permissionService;

    @Operation(summary = "获取文档目录权限")
    @GetMapping("/getFolderPerm/{folderPermId}")
    @ZDLog("获取文档目录权限")
    public JSONResultUtils<Object> getFolderPerm(@PathVariable long folderPermId) {
        FolderPermission folderPermission = permissionService.findById(folderPermId);
        return successData(ObjectMapperUtils.toMap(folderPermission));
    }

    @Operation(summary = "删除文档目录权限")
    @PostMapping("/delete")
    @ZDLog("删除文档目录权限")
    public JSONResultUtils<Object> delete(@RequestBody Map<String, Object> params) {
        List<Long> permIds = ZDMapUtils.getListLongValue(params, "permIds");
        if (CollectionUtils.isEmpty(permIds)) {
            return error("请选则要删除的权限");
        }

        String currentUsername = getCurrentUsername();

        permIds.forEach(item -> {
            FolderPermission tempFolderPermission = permissionService.findById(item);
            if (tempFolderPermission == null) {
                return;
            }

            // 禁止删除自己的权限
            String username = tempFolderPermission.getUsername();
            if (currentUsername.equals(username)) {
                return;
            }

            permissionService.deletePermission(tempFolderPermission);
        });

        return success();
    }

    @Operation(summary = "获取文档目录权限列表")
    @GetMapping("/getFolderAllPerms/{folderId}")
    @ZDLog("获取文档目录权限列表")
    public JSONResultUtils<Object> getFolderAllPerms(@PathVariable long folderId) {
        List<FolderPermission> permissions = permissionService.getPermissionsByFolderId(folderId);
        return successData(ObjectMapperUtils.toMapList(permissions));
    }

    @Operation(summary = "文档目录赋权")
    @PostMapping("/grantPermission")
    @ZDLog("文档目录赋权")
    public JSONResultUtils<Object> grantPermission(@RequestBody Map<String, Object> params) {
        return permissionService.grantPermission(params, getCurrentUser());
    }

    @Operation(summary = "文档目录权限更新")
    @PostMapping("/updatePermission")
    @ZDLog("文档目录权限更新")
    public JSONResultUtils<Object> updatePermission(@RequestBody Map<String, Object> params) {
        return permissionService.updatePermission(params, getCurrentUser());
    }
}
