package zd.dms.utils.oss;

import io.minio.*;
import io.minio.messages.Item;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import zd.base.config.oss.MinioConfig;
import zd.base.utils.system.SpringUtils;
import zd.base.utils.system.SystemInitUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;

@Slf4j
public class MinIOUtils {

    private static final MinioConfig minioConfig = SpringUtils.getBean(MinioConfig.class);
    private static final MinioClient minioClient = SpringUtils.getBean(MinioClient.class);

    public static boolean doUploadToOSS(File uploadFile, String objectKey, int count) {
        if (count > 10) {
            log.error("上传文件时，重试次数超过十次 objectKey ：{}", objectKey);
            return false;
        }

        try (FileInputStream fileInputStream = new FileInputStream(uploadFile)) {
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectKey)
                    .stream(fileInputStream, uploadFile.length(), -1)
                    .build();

            minioClient.putObject(args);
        } catch (Throwable e) {
            log.error("doUploadToOSS error", e);
            return false;
        }

        return true;
    }

    public static InputStream downloadInputStreamFromOSS(String objectKey) {
        try {
            return minioClient.getObject(GetObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectKey).build());
        } catch (Throwable e) {
            log.error("downloadFileFromOSS error", e);
        }

        return null;
    }

    public static boolean exists(String objectKey) {
        StatObjectResponse stat = getStat(objectKey);
        if (stat == null) {
            return false;
        }

        return true;
    }

    public static StatObjectResponse getStat(String objectKey) {
        if (StringUtils.isBlank(objectKey)) {
            return null;
        }

        try {
            return minioClient.statObject(StatObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectKey).build());
        } catch (Throwable e) {
            log.error("getStat error", e);
        }

        return null;
    }

    public static File downloadFileFromOSS(String objectKey) {
        File tempFile = new File(SystemInitUtils.getTempDir(), RandomStringUtils.randomAlphabetic(15));
        InputStream input = null;
        FileOutputStream fileOutputStream = null;
        try {
            input = minioClient.getObject(GetObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectKey).build());

            fileOutputStream = new FileOutputStream(tempFile);
            IOUtils.copy(input, fileOutputStream);
        } catch (Throwable e) {
            log.error("downloadFileFromOSS error", e);
        } finally {
            IOUtils.closeQuietly(fileOutputStream);
            IOUtils.closeQuietly(input);
        }

        /*if (!tempFile.exists()) {
            tempFile = new File(BootstrapManager.getInstance().getHomeDir(), objectKey);
            if (tempFile.exists()) {
                boolean doUploadToOSS = doUploadToOSS(tempFile, objectKey, 0);
                if (doUploadToOSS) {
                    File tempFile2 = ZDFileUtils.getTempFile(0);
                    FileInputStream tempFileInputStream = null;
                    FileOutputStream tempFileOutputStream = null;
                    try {
                        tempFileInputStream = new FileInputStream(tempFile);
                        tempFileOutputStream = new FileOutputStream(tempFile2);
                        IOUtils.copy(tempFileInputStream, tempFileOutputStream);
                    } catch (Exception e) {
                        log.error("downloadFileFromOSS", e);
                        return tempFile;
                    } finally {
                        IOUtils.closeQuietly(tempFileOutputStream);
                        IOUtils.closeQuietly(tempFileInputStream);
                    }

                    FileUtils.deleteQuietly(tempFile);
                    tempFile = tempFile2;
                }
            }
        }*/

        return tempFile;
    }

    public static boolean deleteFileFromOSS(String objectKey) {
        try {
            Iterable<Result<Item>> results = listFileFromOSS(objectKey);
            if (results == null) {
                return true;
            }

            results.forEach(itemResult -> {
                try {
                    Item item = itemResult.get();
                    String tempObjectName = item.objectName();
                    if (StringUtils.isBlank(tempObjectName)) {
                        return;
                    }

                    // 单文件删除
                    deleteSingleFileFromOSS(tempObjectName);
                } catch (Exception e) {
                    log.error("deleteFileFromOSS listObjects error e");
                }
            });
        } catch (Throwable e) {
            log.error("deleteFileFromOSS error", e);
            return false;
        }

        return true;
    }

    /**
     * 该结果只能遍历，不能存入任何内容对象中，否则可能导致量大而内存溢出
     */
    public static Iterable<Result<Item>> listFileFromOSS(String objectKey) {
        if (StringUtils.isBlank(objectKey)) {
            return null;
        }

        try {
            return minioClient.listObjects(ListObjectsArgs.builder().bucket(minioConfig.getBucketName()).prefix(objectKey).recursive(true).build());
        } catch (Exception e) {
            log.error("listFileFromOSS error", e);
        }

        return null;
    }

    public static boolean deleteSingleFileFromOSS(String objectKey) {
        if (StringUtils.isBlank(objectKey)) {
            return true;
        }

        try {
            minioClient.removeObject(RemoveObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectKey).build());
            return true;
        } catch (Exception e) {
            log.error("deleteSingleFileFromOSS error", e);
        }

        return false;
    }
}

