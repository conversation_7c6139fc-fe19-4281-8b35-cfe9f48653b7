package zd.dms.services.log.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import zd.dms.entities.*;
import zd.dms.io.ZDIOUtils;
import zd.dms.repositories.document.DocumentLogRepository;
import zd.dms.repositories.log.FolderLogRepository;
import zd.dms.repositories.log.ThreeAdminLogRepository;
import zd.dms.services.log.LogExportService;
import zd.dms.services.log.LogService;
import zd.record.entities.RecFolderLog;
import zd.record.entities.RecordLog;
import zd.record.repositories.folder.RecFolderLogRepository;
import zd.record.repositories.record.RecordLogRepository;
import zd.record.utils.SXSSFExcelExporter;

import java.io.*;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class LogExportServiceImpl implements LogExportService {

    private final ThreeAdminLogRepository threeAdminLogRepository;

    private final LogService logService;

    private final DocumentLogRepository documentLogRepository;

    private final FolderLogRepository folderLogRepository;

    private final RecordLogRepository recordLogRepository;

    private final RecFolderLogRepository recFolderLogRepository;

    @Override
    public InputStream getThreeAdminLogExcel(Date startDate, Date endDate, int type, int targetType, String username) throws FileNotFoundException {
        File tempFile = ZDIOUtils.getTempFile();
        OutputStream fileOut = new FileOutputStream(tempFile);
        putThreeAdminLogData(fileOut, startDate, endDate, type, targetType, username);
        InputStream in = new FileInputStream(tempFile);
        return in;
    }

    private void putThreeAdminLogData(OutputStream os, Date startDate, Date endDate, int type, int targetType,
                                      String username) {
        log.debug("putThreeAdminLogData type: {}, username: {}, targetType:{}", new Object[]{type, username,
                targetType});

        try {
            // EXCEL2007
            SXSSFExcelExporter ee = new SXSSFExcelExporter("安全审计", new String[]{"操作对象", "操作人", "日志", "IP", "时间"});

            List<ThreeAdminLog> logs = threeAdminLogRepository.getLogsByDate(startDate, endDate, type, targetType, username);

            for (int i = 0; i < logs.size(); i++) {
                ThreeAdminLog log = logs.get(i);

                if (log == null) {
                    continue;
                }

                List value = new LinkedList();
                value.add(log.getTarget());
                value.add(log.getOperatorFullname() + "(" + log.getOperator() + ")");
                value.add(log.getMsg());
                value.add(log.getIp());
                value.add(DateFormatUtils.format(log.getCreationDate(), "yyyy/MM/dd HH:mm"));

                ee.createTableRow(value, i + 1);
            }

            ee.exportExcel(os);
        } catch (Exception e) {
            log.error("putFolderLogData ex", e);
        } finally {
            IOUtils.closeQuietly(os);
        }
    }

    @Override
    public InputStream getLogExcel(Date startDate, Date endDate) throws FileNotFoundException {
        File tempFile = ZDIOUtils.getTempFile();
        OutputStream fileOut = new FileOutputStream(tempFile);
        putLogData(fileOut, startDate, endDate);
        InputStream in = new FileInputStream(tempFile);
        return in;
    }

    private void putLogData(OutputStream os, Date startDate, Date endDate) {
        try {
            SXSSFExcelExporter ee = new SXSSFExcelExporter("系统日志", new String[]{"类型", "时间", "操作人", "IP", "信息"});

            List<LogMessage> logs = logService.getLogsByDate(startDate, endDate);
            for (int i = 0; i < logs.size(); i++) {
                LogMessage log = logs.get(i);

                String type = "信息";
                if (log.getLevel() == LogMessage.LEVEL_ERROR) {
                    type = "错误";
                }

                List value = new LinkedList();
                value.add(type);
                value.add(DateFormatUtils.format(log.getCreationDate(), "yyyy/MM/dd HH:mm"));
                value.add(log.getCreator());
                value.add(log.getIp());
                value.add(log.getMsg());

                ee.createTableRow(value, i + 1);
            }

            ee.exportExcel(os);
        } catch (Exception e) {
            log.error("putLogData ex", e);
        } finally {
            IOUtils.closeQuietly(os);
        }
    }

    @Override
    public InputStream getDocumentLogExcel(Date startDate, Date endDate, int type, String username) throws FileNotFoundException {
        File tempFile = ZDIOUtils.getTempFile();
        OutputStream fileOut = new FileOutputStream(tempFile);
        putDocumentLogData(fileOut, startDate, endDate, type, username);
        InputStream in = new FileInputStream(tempFile);
        return in;
    }

    private void putDocumentLogData(OutputStream os, Date startDate, Date endDate, int type, String username) {
        log.debug("putDocumentLogData type: {}, username: {}", type, username);

        try {
            SXSSFExcelExporter ee = new SXSSFExcelExporter("文档审计", new String[]{"文档", "目录", "操作人", "日志", "IP", "时间"});

            List<DocumentLog> logs = documentLogRepository.getLogsByDate(startDate, endDate, type, username);
            for (int i = 0; i < logs.size(); i++) {
                DocumentLog log = logs.get(i);

                if (log == null || log.getDocument() == null) {
                    continue;
                }

                List value = new LinkedList();
                value.add(log.getDocument().getFilename());
                value.add(log.getDocument().getDisplayFolderWithoutLink());
                value.add(log.getFullname() + "(" + log.getOperator() + ")");
                value.add(log.getMsg());
                value.add(log.getIp());
                value.add(DateFormatUtils.format(log.getCreationDate(), "yyyy/MM/dd HH:mm"));

                ee.createTableRow(value, i + 1);
            }

            ee.exportExcel(os);
        } catch (Exception e) {
            log.error("putDocumentLogData ex", e);
        } finally {
            IOUtils.closeQuietly(os);
        }
    }

    @Override
    public InputStream getFolderLogExcel(Date startDate, Date endDate, int type, String username) throws FileNotFoundException {
        File tempFile = ZDIOUtils.getTempFile();
        OutputStream fileOut = new FileOutputStream(tempFile);
        putFolderLogData(fileOut, startDate, endDate, type, username);
        InputStream in = new FileInputStream(tempFile);
        return in;
    }

    private void putFolderLogData(OutputStream os, Date startDate, Date endDate, int type, String username) {
        log.debug("putFolderLogData type: {}, username: {}", type, username);

        try {
            // EXCEL2007
            SXSSFExcelExporter ee = new SXSSFExcelExporter("目录审计", new String[]{"目录", "操作人", "日志", "IP", "时间"});

            List<FolderLog> logs = folderLogRepository.getLogsByDate(startDate, endDate, type, username);

            for (int i = 0; i < logs.size(); i++) {
                FolderLog log = logs.get(i);

                if (log == null) {
                    continue;
                }

                Folder folder = log.getFolder();
                if (folder == null) {
                    continue;
                }

                List value = new LinkedList();
                value.add(folder.getDisplayFolder());
                value.add(log.getOperatorFullname() + "(" + log.getOperator() + ")");
                value.add(log.getMsg());
                value.add(log.getIp());
                value.add(DateFormatUtils.format(log.getCreationDate(), "yyyy/MM/dd HH:mm"));

                ee.createTableRow(value, i + 1);
            }

            ee.exportExcel(os);
        } catch (Exception e) {
            log.error("putFolderLogData ex", e);
        } finally {
            IOUtils.closeQuietly(os);
        }
    }

    @Override
    public InputStream getRecLogExcel(Date startDate, Date endDate, int type, String username, String from) throws FileNotFoundException {
        File tempFile = ZDIOUtils.getTempFile();
        OutputStream fileOut = new FileOutputStream(tempFile);
        putRecLogData(fileOut, startDate, endDate, type, username, from);
        InputStream in = new FileInputStream(tempFile);
        return in;
    }

    private void putRecLogData(OutputStream os, Date startDate, Date endDate, int type, String username, String from) {
        log.debug("putRecLogData type: {}, username: {}", type, username);

        try {
            SXSSFExcelExporter ee = new SXSSFExcelExporter("ECM审计",
                    new String[]{"数据ID", "编号", "主显示", "目录", "操作人", "日志", "IP", "时间"});
            List<RecordLog> logs = recordLogRepository.getLogsByDate(startDate, endDate, type, username);

            for (int i = 0; i < logs.size(); i++) {
                RecordLog log = logs.get(i);

                if (log == null) {
                    continue;
                }

                List value = new LinkedList();
                value.add(log.getRecId());
                value.add(log.getSN());
                value.add(log.getMainDisplay());
                value.add(log.getFolder() != null ? log.getFolder().getDisplayFolder() : "");
                value.add(log.getFullname() + "(" + log.getOperator() + ")");
                value.add(log.getMsg());
                value.add(log.getIp());
                value.add(DateFormatUtils.format(log.getCreationDate(), "yyyy/MM/dd HH:mm"));

                ee.createTableRow(value, i + 1);
            }

            ee.exportExcel(os);
        } catch (Exception e) {
            log.error("putDocumentLogData ex", e);
        } finally {
            IOUtils.closeQuietly(os);
        }
    }

    @Override
    public InputStream getRecFolderLogExcel(Date startDate, Date endDate, int type, String username) throws FileNotFoundException {
        File tempFile = ZDIOUtils.getTempFile();
        OutputStream fileOut = new FileOutputStream(tempFile);
        putRecFolderLogData(fileOut, startDate, endDate, type, username);
        InputStream in = new FileInputStream(tempFile);
        return in;
    }

    private void putRecFolderLogData(OutputStream os, Date startDate, Date endDate, int type, String username) {
        log.debug("putRecFolderLogData type: {}, username: {}", type, username);

        try {
            SXSSFExcelExporter ee = new SXSSFExcelExporter("ECM目录审计", new String[]{"目录", "操作人", "日志", "IP", "时间"});

            List<RecFolderLog> logs = recFolderLogRepository.getLogsByDate(startDate, endDate, type, username);

            for (int i = 0; i < logs.size(); i++) {
                RecFolderLog log = logs.get(i);

                if (log == null || log.getRecFolderId() <= 0) {
                    continue;
                }

                List value = new LinkedList();
                value.add(log.getRecFolder().getDisplayFolder());
                value.add(log.getOperatorFullname() + "(" + log.getOperator() + ")");
                value.add(log.getMsg());
                value.add(log.getIp());
                value.add(DateFormatUtils.format(log.getCreationDate(), "yyyy/MM/dd HH:mm"));

                ee.createTableRow(value, i + 1);
            }

            ee.exportExcel(os);
        } catch (Exception e) {
            log.error("putRecFolderLogData ex", e);
        } finally {
            IOUtils.closeQuietly(os);
        }
    }
}
