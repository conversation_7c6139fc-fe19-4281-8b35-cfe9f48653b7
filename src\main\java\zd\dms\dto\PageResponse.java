package zd.dms.dto;

import org.springframework.data.domain.Page;
import zd.base.utils.model.ObjectMapperUtils;
import zd.dms.utils.PageableUtils;

import java.util.List;


public class PageResponse {
    private long totalCount;
    private int totalPage;
    private int pageNumber;
    private int pageSize;
    private List<? extends Object> results;

    public long getTotalCount() {
        return totalCount;
    }

    public PageResponse setTotalCount(long totalCount) {
        this.totalCount = totalCount;
        return this;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public PageResponse setTotalPage(int totalPage) {
        this.totalPage = totalPage;
        return this;
    }

    public int getPageNumber() {
        return pageNumber;
    }

    public PageResponse setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
        return this;
    }

    public List<? extends Object> getResults() {
        return results;
    }

    public PageResponse setResults(List<? extends Object> results) {
        this.results = results;
        return this;
    }

    public int getPageSize() {
        return pageSize;
    }

    public PageResponse setPageSize(int pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    public static PageResponse of(Page page) {
        return of(page, null);
    }

    public static PageResponse ofMap(Page page) {
        if (page == null) {
            page = PageableUtils.genPage(null, 1, 30, 0);
        }

        PageResponse pageResponse = new PageResponse().setPageNumber(page.getNumber() + 1).setPageSize(page.getSize())
                .setTotalCount(page.getTotalElements()).setTotalPage(page.getTotalPages());

        pageResponse.setResults(page.getContent());
        return pageResponse;
    }

    public static PageResponse of(Page page, String actionName) {
        if (page == null) {
            page = PageableUtils.genPage(null, 1, 30, 0);
        }

        PageResponse pageResponse = new PageResponse().setPageNumber(page.getNumber() + 1).setPageSize(page.getSize())
                .setTotalCount(page.getTotalElements()).setTotalPage(page.getTotalPages());

        pageResponse.setResults(ObjectMapperUtils.toMapList(page.getContent(), actionName));
        return pageResponse;
    }

    public static PageResponse of(int pageNumber, int pageSize, long totalCount, List content, String actionName) {
        int totalPage = pageSize == 0 ? 1 : (int) Math.ceil((double) totalCount / (double) pageSize);

        PageResponse pageResponse = new PageResponse().setPageNumber(pageNumber).setPageSize(pageSize)
                .setTotalCount(totalCount).setTotalPage(totalPage);

        pageResponse.setResults(ObjectMapperUtils.toMapList(content, actionName));
        return pageResponse;
    }
}
