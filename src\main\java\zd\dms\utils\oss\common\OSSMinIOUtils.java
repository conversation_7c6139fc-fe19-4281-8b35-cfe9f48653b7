package zd.dms.utils.oss.common;

import io.minio.Result;
import io.minio.StatObjectResponse;
import io.minio.messages.Item;
import zd.dms.entities.UserTask;
import zd.dms.utils.oss.MinIOUtils;
import zd.dms.utils.usertask.UserTaskFileUtils;

import java.io.File;
import java.io.InputStream;
import java.time.ZonedDateTime;

public class OSSMinIOUtils extends AbstractOSSUtils {

    public boolean doUploadToOSS(File uploadFile, String objectKey, int count) {
        return MinIOUtils.doUploadToOSS(uploadFile, objectKey, count);
    }

    public File downloadFileFromOSS(String objectKey) {
        return MinIOUtils.downloadFileFromOSS(objectKey);
    }

    public InputStream downloadInputStreamFromOSS(String objectKey) {
        return MinIOUtils.downloadInputStreamFromOSS(objectKey);
    }

    public boolean deleteFileFromOSS(String objectKey) {
        return MinIOUtils.deleteFileFromOSS(objectKey);
    }

    public void deleteUserTasksFile() {
        Iterable<Result<Item>> results = MinIOUtils.listFileFromOSS(UserTaskFileUtils.ossRootPath);
        if (results == null) {
            return;
        }

        results.forEach(itemResult -> {
            try {
                Item item = itemResult.get();
                String tempObjectName = item.objectName();
                UserTask userTask = getUserTask(tempObjectName);
                if (userTask == null) {
                    MinIOUtils.deleteSingleFileFromOSS(tempObjectName);
                }
            } catch (Exception e) {
            }
        });
    }

    public boolean exists(String objectKey) {
        return MinIOUtils.exists(objectKey);
    }

    public long getLastModifiedEpochMilli(String objectKey) {
        StatObjectResponse stat = MinIOUtils.getStat(objectKey);
        if (stat != null) {
            ZonedDateTime zonedDateTime = stat.lastModified();
            return zonedDateTime.toInstant().toEpochMilli();
        }

        return 0;
    }
}
