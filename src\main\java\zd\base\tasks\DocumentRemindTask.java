package zd.base.tasks;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zd.dms.services.document.OutLinkService;
import zd.dms.services.documentremind.DocAutoRemindService;
import zd.dms.services.documentremind.DocumentRemindService;
import zd.dms.utils.usertask.UserTaskUtils;
import zd.record.service.record.RecRemindService;

@Component
@Slf4j
public class DocumentRemindTask extends AbstractTask {

    @Autowired
    private DocumentRemindService documentRemindManager;

    @Autowired
    private RecRemindService recRemindService;

    @Autowired
    private OutLinkService outLinkService;

    @Autowired
    private DocAutoRemindService docAutoRemindService;

    @Scheduled(cron = "0 0 9 * * ?")
    @Scheduled(initialDelay = 60000)
    @Override
    public void work() {
        log.debug("执行文档提醒任务");
        documentRemindManager.remind();
        documentRemindManager.remindRelatives();
        recRemindService.remind();
        outLinkService.deleteUseless();
        recRemindService.doAutoReminds();
        docAutoRemindService.doAutoReminds();
        UserTaskUtils.deleteUserTasks();
        UserTaskUtils.deleteUserTasksFile();
    }
}
