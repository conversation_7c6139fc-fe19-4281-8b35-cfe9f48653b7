package zd.base.tasks;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zd.dms.utils.ZDMailUtils;

@Component
@Slf4j
public class SendMailTask extends AbstractTask {

    @Scheduled(cron = "0 * * * * ?")
    @Scheduled(initialDelay = 60000)
    @Override
    public void work() {
        ZDMailUtils.sendMailQueue();
    }
}
