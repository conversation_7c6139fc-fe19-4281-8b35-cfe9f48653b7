package zd.dms.repositories.position.impl;

import io.micrometer.common.util.StringUtils;
import jakarta.persistence.EntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.Position;
import zd.dms.repositories.position.PositionRepository;
import zd.dms.utils.repositories.PredicateUtils;
import zd.dms.utils.repositories.SpecTools;

import java.util.ArrayList;
import java.util.List;

public class PositionRepositoryDaoImpl extends BaseRepositoryDaoImpl<Position, String> implements PositionRepository {

    public PositionRepositoryDaoImpl(Class<Position> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<Position> getPositions(String username, String groupCode, String position) {
        Specification<Position> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<Position> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            if (StringUtils.isNotBlank(username)) {
                specTools.eq("username", username);
            }

            if (StringUtils.isNotBlank(groupCode)) {
                specTools.eq("groupCode", groupCode);
            }

            if (StringUtils.isNotBlank(position)) {
                specTools.eq("position", position);
            }

            specTools.asc("numIndex");
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<Position> getPositionsByPosition(String position) {
        Specification<Position> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<Position> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("position", position);
            specTools.eq("enabled", true);

            specTools.asc("numIndex");
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<Position> getPositionsByUsername(String username) {
        Specification<Position> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<Position> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("username", username);
            specTools.eq("enabled", true);

            specTools.asc("numIndex");
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public Page getPositionsPage(String userKeywords, String groupKeywords, String posKeywords, int pageNumber, int pageSize) {
        Specification<Position> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<Position> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            if (StringUtils.isNotBlank(userKeywords)) {
                specTools.or(PredicateUtils.like(root, criteriaBuilder, "username", userKeywords),
                        PredicateUtils.like(root, criteriaBuilder, "userFullname", userKeywords));
            }

            if (StringUtils.isNotBlank(groupKeywords)) {
                specTools.or(PredicateUtils.like(root, criteriaBuilder, "groupCode", groupKeywords),
                        PredicateUtils.like(root, criteriaBuilder, "groupName", groupKeywords));
            }

            if (StringUtils.isNotBlank(posKeywords)) {
                specTools.like("position", posKeywords);
            }

            specTools.asc("numIndex");
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }

    @Override
    public List<Position> getPositionsByUserAndGroup(String username, String groupCode) {
        Specification<Position> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<Position> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("username", username);
            specTools.eq("groupCode", groupCode);
            specTools.eq("enabled", true);

            specTools.asc("numIndex");
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<Position> getPositionsByPositionAndGroupCode(String position, String groupCode) {
        Specification<Position> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<Position> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            List<String> groupCodes = new ArrayList<>();
            groupCodes.add("all");
            if (!"all".equals(groupCode)) {
                groupCodes.add(groupCode);
            }

            specTools.in("groupCode", groupCodes);
            specTools.eq("position", position);
            specTools.eq("enabled", true);

            specTools.asc("numIndex");
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }
}
