package zd.dms.controllers.sso;

import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.codec.Base64Encoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.analysus.ja.dict.TaskDictionary;
import zd.base.context.UserContextHolder;
import zd.base.controllers.security.CaptchaController;
import zd.base.exception.IllegalArgumentException;
import zd.base.utils.redis.ZDRedisUtils;
import zd.base.utils.system.SpringUtils;
import zd.dms.entities.InstantMessage;
import zd.dms.entities.LogMessage;
import zd.dms.entities.Role;
import zd.dms.entities.User;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.log.LogService;
import zd.dms.services.mail.MailService;
import zd.dms.services.security.AuthenticationService;
import zd.dms.services.security.RoleUtils;
import zd.dms.services.security.SecurityInfo;
import zd.dms.services.user.UserService;
import zd.dms.services.user.exception.UserNotExistsException;
import zd.dms.services.user.exception.WrongPasswordException;
import zd.dms.utils.JSONUtils;
import zd.dms.utils.RSAUtils;
import zd.dms.utils.SessionUtils;
import zd.dms.utils.WebUtils;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class LoginUtils {

    private static final LogService logService = SpringUtils.getBean(LogService.class);

    private static final AuthenticationService authenticationService = SpringUtils.getBean(AuthenticationService.class);

    private static final UserService userService = SpringUtils.getBean(UserService.class);

    private static final MailService mailService = SpringUtils.getBean(MailService.class);

    public static String getToken(String username) {
        Map<String, Object> tokenMap = new HashMap<>();
        tokenMap.put("username", username);
        tokenMap.put("exp", System.currentTimeMillis() + 5 * 60 * 1000);

        String encrypt = RSAUtils.encrypt(JSONUtils.toJSONString(tokenMap, ""));
        String encode = Base64Encoder.encode(encrypt);

        System.out.println(encode);

        return encode;
    }

    public static String getDecryptTokenUsername(String token) {
        String decode = new String(Base64Decoder.decode(token));

        String decrypt = RSAUtils.decrypt(decode);
        if (StringUtils.isBlank(decrypt)) {
            return "error:无效的令牌";
        }

        Map<String, Object> map = JSONUtils.parseObject(decrypt, Map.class, new HashMap<>());
        if (MapUtils.isEmpty(map)) {
            return "error:令牌转换失败";
        }

        String username = MapUtils.getString(map, "username");
        if (StringUtils.isBlank(username)) {
            return "error:令牌未知用户";
        }

        long exp = MapUtils.getLongValue(map, "exp", 0L);
        if (exp <= 0) {
            return "error:令牌过期";
        }

        // token过期时间在5分钟内
        long currentTime = System.currentTimeMillis();
        if (exp < currentTime) {
            return "error:令牌已过期";
        }

        // token过期时间只能在3天内
        if (exp > (currentTime + 3 * 24 * 60 * 60 * 1000)) {
            return "error:令牌超期时间错误";
        }

        return "success:" + username;
    }

    public static boolean isMobile(String userAgent) {
        if (StringUtils.containsIgnoreCase(userAgent, "iPhone") ||
                StringUtils.containsIgnoreCase(userAgent, "Android")) {
            return true;
        }

        return false;
    }

    public static boolean isHasMobileAccess(User user) {
        // 是否可以使用移动端登录：开启移动模块、允许移动设备登录
        boolean canLoginWithMobile = true;
        if (!SystemConfigManager.getInstance().getBooleanProperty("mobbileAndroidEnabled")) {
            canLoginWithMobile = false;
        }

        if (TaskDictionary.getMainDefinition().mcl() <= 0) {
            canLoginWithMobile = false;
        }

        if (!user.isHasMobileAccess()) {
            canLoginWithMobile = false;
        }

        return canLoginWithMobile;
    }

    public static Map<String, Object> getUserData(User user, HttpServletRequest request) {
        Map<String, Object> jsonData = new HashMap<String, Object>();
        if (user == null) {
            return jsonData;
        }

        jsonData.put("username", user.getUsername());
        jsonData.put("roleList", user.getRoleList());
        jsonData.put("token", SessionUtils.genToken(request.getSession()));

        return jsonData;
    }

    public static void login(String username, String password, boolean autoLogin,
                             boolean checkPassword, HttpServletRequest request, HttpServletResponse response) {
        String errorMsg = "";
        try {
            authenticationService.login(username, password, autoLogin, checkPassword, request, response);
        } catch (UserNotExistsException e) {
            logError("尝试用不存在的用户名 " + username + " 登录系统");
            SecurityInfo.setLoginErrorIP(WebUtils.getIPAddress(request));
            errorMsg = e.getMessage();
        } catch (WrongPasswordException e) {
            logError("用户 " + username + " 尝试用错误的密码登录系统");
            // 判断是否超过错误密码限制
            SecurityInfo.setLoginError(username);
            errorMsg = e.getMessage();
        } catch (Exception e) {
            errorMsg = e.getMessage();
        }

        if (StringUtils.isNotBlank(errorMsg)) {
            throw new IllegalArgumentException(errorMsg);
        }

        // 重置登录错误状态
        SecurityInfo.removeLoginError(username, WebUtils.getIPAddress(request));
    }

    public static void allowAdminLogin(User user) {
        if (user == null) {
            return;
        }

        // 判定是否为允许管理员登录的时段
        int adminLoginStartHour = SystemConfigManager.getInstance().getIntProperty("sec_alsh");
        int adminLoginEndHour = SystemConfigManager.getInstance().getIntProperty("sec_aleh");
        if (adminLoginStartHour > 0 && adminLoginEndHour > 0) {
            log.debug("已开启管理员登录时段限制，{}至{}", adminLoginStartHour, adminLoginEndHour);
            if (adminLoginStartHour > adminLoginEndHour) {
                log.error("设置了错误的管理员登录时段限制, {}至{}", adminLoginStartHour, adminLoginEndHour);
            } else {
                // 只处理管理员
                if (RoleUtils.isUserGroupInRole(user, Role.SYSTEM_ADMIN) ||
                        RoleUtils.isUserGroupInRole(user, Role.LIMITED_ADMIN)) {
                    int currentHour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
                    if (currentHour < adminLoginStartHour || currentHour > adminLoginEndHour) {
                        log.debug("当前时段：{}点，无法登录管理员角色", currentHour);

                        throw new IllegalArgumentException("当前时段无法登录管理员角色");
                    } else {
                        log.debug("当前时段：{}点，允许登录管理员角色", currentHour);
                    }
                }
            }
        }
    }

    public static void allowLogin(String username) {
        if (StringUtils.isBlank(username)) {
            return;
        }

        String ipAddress = getIPAddress();
        if (SecurityInfo.isReachMaxLoginBlockIP(ipAddress)) {
            log.debug("IP:{} 已达到错误次数，禁止登录", ipAddress);
            SecurityInfo.setLoginErrorIP(ipAddress);
            throw new IllegalArgumentException("禁止登陆，请稍后重试");
        }


        // 验证是否需要禁止帐号登录
        if (SecurityInfo.isReachMaxLoginBlock(username)) {
            log.debug("帐号:{} 已达到错误次数，禁止登录", username);

            if (SystemConfigManager.getInstance().getBooleanProperty("sec_sls")) {
                // 只有在5次错误的时候发送短信
                if (SecurityInfo.getLoginErrorTime(username) == SecurityInfo.MAX_LOGIN_BLOCK) {
                    User u = userService.getUserByUsername(username);

                    if (u != null) {
                        String alertSmsContent = "尊敬的" + u.getFullname() + "，您的E6帐号:" + u.getUsername() +
                                " 已经连续输入5次错误密码，可能存在安全问题，请尽快与系统管理员联络";
                        mailService.sendMessageAndMail("连续密码错误提醒", alertSmsContent, InstantMessage.TYPE_LOGIN, u, u);
                    }
                }
            }

            SecurityInfo.setLoginError(username);
            throw new IllegalArgumentException("已达到密码错误限制，请3分钟后再尝试登录");
        }
    }

    protected static void logError(String msg) {
        doLog(LogMessage.LEVEL_ERROR, msg);
    }

    private static void doLog(int level, String msg) {
        String username = "";
        User user = UserContextHolder.getUser();
        if (user != null) {
            username = user.getUsername();
        } else {
            username = "system";
        }

        log.debug("doLog username {}", username);

        String ip = getIPAddress();
        logService.addMessage(level, username, ip, msg);
    }

    private static String getIPAddress() {
        return WebUtils.getIPAddress();
    }

    public static String decryptWithBase64AndRSA(String encryptedText) throws Exception {
        try {
            if (StringUtils.isBlank(encryptedText)) {
                log.debug("加密文本为空，无法解密");
                return "";
            }

            log.debug("开始解密文本: {}", encryptedText);

            // 1. 解码Base64编码的加密文本（使用传入的encryptedText参数）
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedText);

            // 2. 解码Base64编码的私钥并生成PrivateKey对象
            byte[] keyBytes = Base64.getDecoder().decode(RSAUtils.getCurrentPrivateKey());
            PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privKey = keyFactory.generatePrivate(spec);

            // 3. 初始化Cipher用于解密
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.DECRYPT_MODE, privKey);

            // 4. 获取密钥大小（字节）
            int keySize = ((RSAPrivateKey) privKey).getModulus().bitLength() / 8;
            int maxBlockSize = keySize; // RSA解密时的最大块大小

            // 5. 分块解密
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            int offSet = 0;
            byte[] cache;
            int i = 0;

            // 对数据分块解密
            while (encryptedBytes.length - offSet > 0) {
                if (encryptedBytes.length - offSet > maxBlockSize) {
                    cache = cipher.doFinal(encryptedBytes, offSet, maxBlockSize);
                } else {
                    cache = cipher.doFinal(encryptedBytes, offSet, encryptedBytes.length - offSet);
                }
                outputStream.write(cache, 0, cache.length);
                i++;
                offSet = i * maxBlockSize;
            }

            byte[] decryptedBytes = outputStream.toByteArray();
            outputStream.close();

            // 6. 将解密后的字节转换为字符串
            String result = new String(decryptedBytes, "UTF-8");
            log.debug("解密成功，结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("解密失败", e);
            throw e;
        }
    }

    public static String validateCaptcha(HttpServletRequest request, String captcha) {
        // 验证码校验
        if (StringUtils.isBlank(captcha)) {
            return "验证码不能为空";
        }

        // 从session中获取验证码
        String sessionCaptcha = (String) request.getSession().getAttribute(CaptchaController.CAPTCHA_SESSION_KEY);
        if (sessionCaptcha == null) {
            return "验证码已过期，请刷新验证码";
        }

        // 验证码使用后立即清除，防止重复使用
        request.getSession().removeAttribute(CaptchaController.CAPTCHA_SESSION_KEY);

        // 验证码校验（不区分大小写）
        if (!sessionCaptcha.equalsIgnoreCase(captcha)) {
            return "验证码错误";
        }

        return "success";
    }

    public static long setUserErrorCount(String username) {
        return ZDRedisUtils.incr(getUserErrorCountKey(username), 1, 3 * 60);
    }

    public static long getUserErrorCount(String username) {
        return ZDRedisUtils.getIncr(getUserErrorCountKey(username));
    }

    public static void removeUserErrorCount(String username) {
        ZDRedisUtils.delIncr(getUserErrorCountKey(username));
    }

    private static String getUserErrorCountKey(String username) {
        if (StringUtils.isBlank(username)) {
            return username;
        }

        return "user:error:count:" + username;
    }

    public static long setIPErrorCount(String ip) {
        return ZDRedisUtils.incr(getIpErrorCountKey(ip), 1, 3 * 60);
    }

    public static long getIPErrorCount(String ip) {
        return ZDRedisUtils.getIncr(getIpErrorCountKey(ip));
    }

    public static void removeIPErrorCount(String ip) {
        ZDRedisUtils.delIncr(getIpErrorCountKey(ip));
    }

    private static String getIpErrorCountKey(String ip) {
        if (ip == null) {
            ip = "";
        }

        String replaceIp = ip.replace(":", ".");
        return "ip:error:count:" + replaceIp;
    }

    public static String getLoginCode() {
        String loginCode = RandomStringUtils.randomAlphanumeric(10);
        ZDRedisUtils.hset(getLoginCodeKey(), loginCode, "", 24 * 60 * 60);
        return loginCode;
    }

    public static boolean checkLoginCode(String loginCode) {
        if (StringUtils.isBlank(loginCode)) {
            return false;
        }

        boolean hasKey = ZDRedisUtils.hHasKey(getLoginCodeKey(), loginCode);
        removeLoginCode(loginCode);

        return hasKey;
    }

    public static void removeLoginCode(String loginCode) {
        if (StringUtils.isBlank(loginCode)) {
            return;
        }

        ZDRedisUtils.hdel(getLoginCodeKey(), loginCode);
    }

    public static String getLoginCodeKey() {
        return "user_login_code";
    }
}
