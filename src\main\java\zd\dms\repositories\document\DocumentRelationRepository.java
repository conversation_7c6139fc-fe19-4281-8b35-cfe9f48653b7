package zd.dms.repositories.document;

import zd.base.repositories.BaseRepository;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentRelation;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DocumentRelationRepository extends BaseRepository<DocumentRelation, Long> {

    void deleteDocumentRelation(Document document);

    void deleteRelationById(long id);

    void deleteRelation(Document doc, Document docRelated);

    List<DocumentRelation> getDocumentRelations(Document document);
}
