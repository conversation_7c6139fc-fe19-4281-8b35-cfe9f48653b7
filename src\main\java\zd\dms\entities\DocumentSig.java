package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.entities.AbstractEntity;

import java.util.Date;


/**
 * 记录文档内签章的对象
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "sigindoc")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class DocumentSig extends AbstractEntity {

	private static final Logger log = LoggerFactory.getLogger(DocumentSig.class);

	/**
	 * serial
	 */
	private static final long serialVersionUID = 1L;

	@Index(name = "i_dsig_docid")
	private String docId;

	private String markname;

	private String markguid;

	private String creatorFullname;

	private String dateTime;

	private String ip;

	/**
	 * 创建时间
	 */
	@Index(name = "i_dsig_cd")
	@Column(nullable = false)
	private Date creationDate;

	public DocumentSig() {
		super();
		creationDate = new Date();
	}

	public String getDocId() {
		return docId;
	}

	public void setDocId(String filename) {
		this.docId = filename;
	}

	public String getMarkguid() {
		return markguid;
	}

	public void setMarkguid(String creatorFullname) {
		this.markguid = creatorFullname;
	}

	public String getDateTime() {
		return dateTime;
	}

	public void setDateTime(String permissions) {
		this.dateTime = permissions;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String permissionsDisplay) {
		this.ip = permissionsDisplay;
	}

	public String getMarkname() {
		return markname;
	}

	public void setMarkname(String extension) {
		this.markname = extension;
	}

	public String getCreatorFullname() {
		return creatorFullname;
	}

	public void setCreatorFullname(String creatorFullname) {
		this.creatorFullname = creatorFullname;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}
}
