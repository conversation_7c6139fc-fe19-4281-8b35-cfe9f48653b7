package zd.dms.repositories.log.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.LogMessage;
import zd.dms.repositories.log.LogRepository;
import zd.dms.utils.PageableUtils;
import zd.dms.utils.repositories.QueryParamsUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LogRepositoryDaoImpl extends BaseRepositoryDaoImpl<LogMessage, Long> implements LogRepository {

    public LogRepositoryDaoImpl(Class<LogMessage> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public Page getLogs(int pageNumber, int pageSize, int level, Date startDate, Date endDate, String orderProperty, boolean asc) {
        Specification<LogMessage> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (startDate != null) {
                predicates.add(criteriaBuilder.greaterThan(root.get("creationDate"), startDate));
            }
            if (endDate != null) {
                predicates.add(criteriaBuilder.lessThan(root.get("creationDate"), endDate));
            }
            if (level > 0) {
                predicates.add(criteriaBuilder.equal(root.get("level"), level));
            }

            Order orderPropertyOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, orderProperty, asc, "id", false);

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, orderPropertyOrder);
        };

        return findAll(spec, PageableUtils.getPageable(pageNumber, pageSize));
    }

    @Override
    public List<LogMessage> getLogsByDate(Date startDate, Date endDate) {
        Specification<LogMessage> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (startDate != null) {
                predicates.add(criteriaBuilder.greaterThan(root.get("creationDate"), startDate));
            }
            if (endDate != null) {
                predicates.add(criteriaBuilder.lessThan(root.get("creationDate"), endDate));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
        };

        return findAll(spec);
    }

    @Override
    public void clearLogs() {
        executeUpdate("delete from LogMessage");
    }
}
