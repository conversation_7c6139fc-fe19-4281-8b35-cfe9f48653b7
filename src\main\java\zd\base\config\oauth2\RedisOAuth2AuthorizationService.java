package zd.base.config.oauth2;

import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.core.OAuth2RefreshToken;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.util.Assert;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Redis OAuth2授权服务实现
 */
@Slf4j
public class RedisOAuth2AuthorizationService implements OAuth2AuthorizationService {

    private static final String AUTHORIZATION_PREFIX = "oauth2:authorization:";
    private static final String ACCESS_TOKEN_PREFIX = "oauth2:access_token:";
    private static final String REFRESH_TOKEN_PREFIX = "oauth2:refresh_token:";

    private final RedisTemplate<String, OAuth2Authorization> redisTemplate;

    public RedisOAuth2AuthorizationService(RedisConnectionFactory connectionFactory,
                                          RegisteredClientRepository registeredClientRepository) {
        Assert.notNull(connectionFactory, "RedisConnectionFactory cannot be null");
        Assert.notNull(registeredClientRepository, "RegisteredClientRepository cannot be null");

        // 配置RedisTemplate
        this.redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(connectionFactory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());

        // 创建支持Java 8日期/时间类型的ObjectMapper
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        // 启用默认类型信息
        objectMapper.activateDefaultTyping(
            LaissezFaireSubTypeValidator.instance,
            ObjectMapper.DefaultTyping.NON_FINAL,
            JsonTypeInfo.As.PROPERTY
        );

        // 使用GenericJackson2JsonRedisSerializer，它会自动添加类型信息
        GenericJackson2JsonRedisSerializer serializer = new GenericJackson2JsonRedisSerializer(objectMapper);

        redisTemplate.setValueSerializer(serializer);
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(serializer);
        redisTemplate.afterPropertiesSet();
    }

    @Override
    public void save(OAuth2Authorization authorization) {
        Assert.notNull(authorization, "Authorization cannot be null");

        String authorizationId = authorization.getId();
        String authorizationKey = AUTHORIZATION_PREFIX + authorizationId;

        // 保存授权对象
        redisTemplate.opsForValue().set(authorizationKey, authorization);

        // 设置过期时间（如果有）
        setExpiryForAuthorization(authorizationKey, authorization);

        // 保存访问令牌索引
        OAuth2AccessToken accessToken = authorization.getAccessToken() != null ?
                authorization.getAccessToken().getToken() : null;
        if (accessToken != null) {
            String accessTokenKey = ACCESS_TOKEN_PREFIX + accessToken.getTokenValue();
            redisTemplate.opsForValue().set(accessTokenKey, authorization);

            // 设置访问令牌过期时间
            if (accessToken.getExpiresAt() != null) {
                Duration duration = Duration.between(Instant.now(), accessToken.getExpiresAt());
                if (!duration.isNegative()) {
                    redisTemplate.expire(accessTokenKey, duration.getSeconds(), TimeUnit.SECONDS);
                }
            }
        }

        // 保存刷新令牌索引
        OAuth2RefreshToken refreshToken = authorization.getRefreshToken() != null ?
                authorization.getRefreshToken().getToken() : null;
        if (refreshToken != null) {
            String refreshTokenKey = REFRESH_TOKEN_PREFIX + refreshToken.getTokenValue();
            redisTemplate.opsForValue().set(refreshTokenKey, authorization);

            // 设置刷新令牌过期时间
            if (refreshToken.getExpiresAt() != null) {
                Duration duration = Duration.between(Instant.now(), refreshToken.getExpiresAt());
                if (!duration.isNegative()) {
                    redisTemplate.expire(refreshTokenKey, duration.getSeconds(), TimeUnit.SECONDS);
                }
            }
        }

        log.debug("Saved authorization: {}", authorizationId);
    }

    @Override
    public void remove(OAuth2Authorization authorization) {
        Assert.notNull(authorization, "Authorization cannot be null");

        List<String> keysToRemove = new ArrayList<>();

        // 添加授权ID键
        keysToRemove.add(AUTHORIZATION_PREFIX + authorization.getId());

        // 添加访问令牌键
        OAuth2AccessToken accessToken = authorization.getAccessToken() != null ?
                authorization.getAccessToken().getToken() : null;
        if (accessToken != null) {
            keysToRemove.add(ACCESS_TOKEN_PREFIX + accessToken.getTokenValue());
        }

        // 添加刷新令牌键
        OAuth2RefreshToken refreshToken = authorization.getRefreshToken() != null ?
                authorization.getRefreshToken().getToken() : null;
        if (refreshToken != null) {
            keysToRemove.add(REFRESH_TOKEN_PREFIX + refreshToken.getTokenValue());
        }

        // 删除所有键
        redisTemplate.delete(keysToRemove);

        log.debug("Removed authorization: {}", authorization.getId());
    }

    @Override
    public OAuth2Authorization findById(String id) {
        Assert.hasText(id, "ID cannot be empty");
        return redisTemplate.opsForValue().get(AUTHORIZATION_PREFIX + id);
    }

    @Override
    public OAuth2Authorization findByToken(String token, OAuth2TokenType tokenType) {
        Assert.hasText(token, "Token cannot be empty");

        String prefix;
        if (tokenType == null) {
            // 尝试所有类型的令牌
            OAuth2Authorization authorization = redisTemplate.opsForValue().get(ACCESS_TOKEN_PREFIX + token);
            if (authorization != null) {
                return authorization;
            }
            return redisTemplate.opsForValue().get(REFRESH_TOKEN_PREFIX + token);
        } else if (OAuth2TokenType.ACCESS_TOKEN.equals(tokenType)) {
            prefix = ACCESS_TOKEN_PREFIX;
        } else if (OAuth2TokenType.REFRESH_TOKEN.equals(tokenType)) {
            prefix = REFRESH_TOKEN_PREFIX;
        } else {
            // 不支持的令牌类型
            return null;
        }

        return redisTemplate.opsForValue().get(prefix + token);
    }

    private void setExpiryForAuthorization(String key, OAuth2Authorization authorization) {
        // 查找最长的过期时间
        Instant maxExpiresAt = null;

        // 检查访问令牌过期时间
        OAuth2Authorization.Token<OAuth2AccessToken> accessToken = authorization.getToken(OAuth2AccessToken.class);
        if (accessToken != null && accessToken.getToken().getExpiresAt() != null) {
            maxExpiresAt = accessToken.getToken().getExpiresAt();
        }

        // 检查刷新令牌过期时间
        OAuth2Authorization.Token<OAuth2RefreshToken> refreshToken = authorization.getToken(OAuth2RefreshToken.class);
        if (refreshToken != null && refreshToken.getToken().getExpiresAt() != null) {
            if (maxExpiresAt == null || refreshToken.getToken().getExpiresAt().isAfter(maxExpiresAt)) {
                maxExpiresAt = refreshToken.getToken().getExpiresAt();
            }
        }

        // 设置过期时间
        if (maxExpiresAt != null) {
            Duration duration = Duration.between(Instant.now(), maxExpiresAt);
            if (!duration.isNegative()) {
                redisTemplate.expire(key, duration.getSeconds(), TimeUnit.SECONDS);
            }
        }
    }
}
