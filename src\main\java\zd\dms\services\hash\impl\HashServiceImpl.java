package zd.dms.services.hash.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentVersion;
import zd.dms.io.ZDIOUtils;
import zd.dms.repositories.document.DocumentRepository;
import zd.dms.repositories.document.DocumentVersionRepository;
import zd.dms.services.hash.HashService;
import zd.dms.utils.HashUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class HashServiceImpl implements HashService {

    private final DocumentRepository documentRepository;

    private final DocumentVersionRepository documentVersionRepository;

    @Override
    public void hashAllDocs() throws IOException {
// List<Document> docs = documentDao.getAll();
        Page page = documentRepository.getPage(1, 100);
        for (int i = 1; i <= page.getTotalPages(); i++) {
            page = documentRepository.getPage(i, 100);
            List<Document> docList = page.getContent();
            hashDocList(docList);
        }
    }

    private void hashDocList(List<Document> docs) throws IOException {
        for (Document doc : docs) {
            if (doc == null) {
                continue;
            }

            log.debug("开始更新hash: {}", doc.getDisplayFullpath());

            String hash = getHash(doc, doc.getNewestVersion());

            if (StringUtils.isNotBlank(hash)) {
                // 更新文档hash
                doc.setHash(hash);
                documentRepository.update(doc);

                // 更新最新版本hash
                DocumentVersion dv = documentVersionRepository.getDocumentVersion(doc,
                        doc.getNewestVersion());
                dv.setHash(hash);
                documentVersionRepository.update(dv);
            }

            // 更新其他版本hash
            List<DocumentVersion> dvs = documentVersionRepository
                    .getDocumentVersionByDocument(doc, false, false);
            for (DocumentVersion dv : dvs) {
                if (dv == null) {
                    continue;
                }

                if (StringUtils.isNotBlank(dv.getHash())) {
                    log.debug("文档 {} 的版本 {} 已有hash，跳过", doc.getFilename(),
                            dv.getVersionNumber());
                    continue;
                }

                String versionHash = getHash(doc, dv.getVersionNumber());
                if (StringUtils.isNotBlank(versionHash)) {
                    dv.setHash(versionHash);
                    documentVersionRepository.update(dv);
                }
            }
        }
    }

    private String getHash(Document doc, int version) throws IOException {
        if (doc == null) {
            return null;
        }

        if (StringUtils.isNotBlank(doc.getHash())) {
            log.debug("已有hash，跳过");
            return null;
        }

        File docFile = ZDIOUtils.getDocFile(doc, version, false);
        if (docFile != null && docFile.exists() && docFile.canRead()) {
            String hash = null;
            if (!ZDIOUtils.FE()) {
                log.debug("未加密，直接获取hash");
                hash = HashUtils.getHashFile(docFile);
            } else {
                File decTemp = null;
                try {
                    decTemp = ZDIOUtils.df(docFile, true);
                    log.debug("已加密，先复制临时文件：{}", decTemp.getAbsolutePath());
                    hash = HashUtils.getHashFile(docFile);
                } catch (IOException e) {
                    log.error("hashAllDocs docFile " +
                            doc.getDisplayFullpath() + " error", e);
                    throw e;
                } finally {
                    FileUtils.deleteQuietly(decTemp);
                }
            }

            return hash;
        } else {
            log.error("docFile {} null or not exists or can't read",
                    doc.getDisplayFullpath());

            return null;
        }
    }
}
