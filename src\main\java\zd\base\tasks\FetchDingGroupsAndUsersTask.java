package zd.base.tasks;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zd.base.utils.system.PropsUtils;
import zd.dms.services.user.GroupAndUserService;
import zd.dms.utils.FetchDataUtils;

import java.util.Set;

@Component
@Slf4j
public class FetchDingGroupsAndUsersTask extends AbstractTask {

    @Autowired
    private GroupAndUserService groupAndUserService;

    @Scheduled(cron = "0 0 0 * * ?")
    @Scheduled(initialDelay = 60000)
    @Override
    public void work() {
        if (!PropsUtils.getBoolProps("DingSyncEnabled", false)) {
            return;
        }

        log.info("start sync groups and users job!");

        String accessToken = FetchDataUtils.getAccessToken();
        if (accessToken == null) {
            log.error("accessToken is null!");
            return;
        }

        String groupStr = FetchDataUtils.getGroupStr(accessToken);
        if (StringUtils.isBlank(groupStr)) {
            log.error("groupStr is null!");
            return;
        }

        Set<String> dingGroupIds = FetchDataUtils.getGroupIds(groupStr);

        groupAndUserService.batchDisposeGroups(groupStr, dingGroupIds);

        groupAndUserService.fetchAndDisposeUsers(accessToken, dingGroupIds);
    }
}
