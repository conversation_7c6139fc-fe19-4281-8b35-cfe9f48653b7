package zd.dms.services.ldap;

/**
 * <AUTHOR>
 */
public class LDAPException extends Exception {

	/**
	 * serial
	 */
	private static final long serialVersionUID = -2727243141813368785L;

	/**
	 * 构造器
	 */
	public LDAPException() {
		super();
		// TODO Auto-generated constructor stub
	}

	/**
	 * 构造器
	 * 
	 * @param arg0
	 * @param arg1
	 */
	public LDAPException(String arg0, Throwable arg1) {
		super(arg0, arg1);
		// TODO Auto-generated constructor stub
	}

	/**
	 * 构造器
	 * 
	 * @param arg0
	 */
	public LDAPException(String arg0) {
		super(arg0);
		// TODO Auto-generated constructor stub
	}

	/**
	 * 构造器
	 * 
	 * @param arg0
	 */
	public LDAPException(Throwable arg0) {
		super(arg0);
		// TODO Auto-generated constructor stub
	}
}