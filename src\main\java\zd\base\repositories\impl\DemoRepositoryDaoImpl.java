package zd.base.repositories.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import zd.base.repositories.DemoRepository;
import zd.dms.entities.User;

import java.util.List;


public class DemoRepositoryDaoImpl extends BaseRepositoryDaoImpl<User, String> implements DemoRepository {

    public DemoRepositoryDaoImpl(Class<User> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<User> testSelect() {
        /*Specification spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            Predicate countryPredicate = criteriaBuilder.equal(root.get("username"), "ancc");
            predicates.add(countryPredicate);
            return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
        };

        List<User> list = findAll(spec);
        return list;*/

        Query query = this.entityManager.createQuery("from User u where username = 'ancc'");
        List<User> resultList = query.getResultList();
        return resultList;

        /*Query nativeQuery = this.entityManager.createNativeQuery("select id,username,password from tdms_user u where username = 'ancc'");
        nativeQuery.unwrap(NativeQueryImpl.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<User> resultList = nativeQuery.getResultList();
        return resultList;*/
    }

}
