package zd.dms.services.document.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.analysus.ja.dict.ConcurrentDictionary;
import org.apache.lucene.analysus.ja.dict.ModeDefinition;
import org.apache.lucene.analysus.ja.dict.TaskDictionary;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import zd.base.context.UserContextHolder;
import zd.base.exception.IllegalArgumentException;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapUtils;
import zd.base.utils.ZDUtils;
import zd.base.utils.es.ESQueueUtils;
import zd.base.utils.zdmq.ZDDocMQUtils;
import zd.dms.dto.document.DocumentUploadDto;
import zd.dms.entities.*;
import zd.dms.io.ZDIOUtils;
import zd.dms.repositories.docsig.DocumentSigRepository;
import zd.dms.repositories.document.*;
import zd.dms.repositories.log.FolderLogRepository;
import zd.dms.repositories.workflow.ProcessInstanceRepository;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.document.DocumentLinkService;
import zd.dms.services.document.DocumentService;
import zd.dms.services.document.DocumentUtils;
import zd.dms.services.document.FolderService;
import zd.dms.services.document.exception.*;
import zd.dms.services.documentremind.DocumentRemindService;
import zd.dms.services.im.InstantMessageService;
import zd.dms.services.log.LogService;
import zd.dms.services.log.UserOpLogUtils;
import zd.dms.services.log.UserOperateLogService;
import zd.dms.services.propdata.DocAdvPropsDBUtils;
import zd.dms.services.rule.FolderRuleService;
import zd.dms.services.security.FolderPermissionUtils;
import zd.dms.services.security.UnauthorizedException;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.utils.*;
import zd.dms.workflow.engine.ProcessService;
import zd.dms.workflow.entities.SSProcessInstance;
import zd.record.dto.record.AttachmentUploadDto;
import zd.record.entities.RecFolder;
import zd.record.entities.RecFolderPermission;
import zd.record.entities.RecordLog;
import zd.record.service.folder.RecFolderService;
import zd.record.service.record.AttachmentUtils;
import zd.record.service.record.RecordService;
import zd.record.service.security.RecFolderPermissionUtils;
import zd.record.utils.RecordAttachmentsSettingUtils;
import zd.record.utils.RecordDBUtils;

import java.io.*;
import java.util.*;

@RequiredArgsConstructor
@Service
@Slf4j
public class DocumentServiceImpl extends BaseJpaServiceImpl<Document, String> implements DocumentService {

    private final DocumentRepository documentRepository;

    @Lazy
    @Autowired
    private ProcessService processService;

    @Lazy
    @Autowired
    private FolderService folderService;

    @Lazy
    @Autowired
    private RecFolderService recFolderService;

    @Lazy
    @Autowired
    private RecordService recordService;

    private final ProcessInstanceRepository processInstanceRepository;

    private final DocumentVersionRepository documentVersionRepository;

    private final DocumentRelationRepository documentRelationRepository;

    @Lazy
    @Autowired
    private DocumentLinkService documentLinkService;

    private final DocumentNoteRepository documentNoteRepository;

    private final DocumentLogRepository documentLogRepository;

    private final DocumentLendingRepository documentLendingRepository;

    @Lazy
    @Autowired
    private DocumentRemindService documentRemindService;

    private final DocumentSigRepository documentSigRepository;

    private final FolderSubscribeRepository folderSubscribeRepository;

    private final UserOperateLogService userOperateLogService;

    @Lazy
    @Autowired
    private InstantMessageService instantMessageService;

    private final FolderRepository folderRepository;

    private final LogService logService;

    private final FolderRuleService folderRuleService;

    private final FolderLogRepository folderLogRepository;

    @Override
    public BaseRepository<Document, String> getBaseRepository() {
        return documentRepository;
    }

    @Override
    public JSONResultUtils<Object> saveUploadDocument(MultipartFile uploadFile, DocumentUploadDto documentUploadDto, User user) {
        // 检查文件是否存在
        if (uploadFile == null || uploadFile.isEmpty()) {
            return JSONResultUtils.error("文件不存在");
        }

        if (user == null) {
            return JSONResultUtils.error("用户不存在");
        }

        long folderId = documentUploadDto.getFolderId();
        Folder folder = folderService.getById(folderId);
        if (folder == null) {
            return JSONResultUtils.error("目录不存在");
        }

        if (!FolderPermissionUtils.checkPermission(user, folder, FolderPermission.CREATE)) {
            throw new IllegalArgumentException("无权进行此操作");
        }

        try {
            String originalFilename = uploadFile.getOriginalFilename();
            if (StringUtils.isBlank(originalFilename)) {
                log.debug("文件名为空: {}，跳过", originalFilename);
                return JSONResultUtils.error("文件名为空");
            }

            long size = uploadFile.getSize();
            if (size <= 0) {
                log.debug("空文档: {}，跳过", originalFilename);
                return JSONResultUtils.error("空文档");
            }

            // 保存文件到临时目录
            File tempFile = ZDFileUtils.getTempFile(0);
            uploadFile.transferTo(tempFile);

            Document document = new Document();

            String filename = FilenameUtils.getBaseName(originalFilename);
            String extension = StringUtils.lowerCase(FilenameUtils.getExtension(originalFilename));
            if (StringUtils.isNotBlank(extension)) {
                filename = filename + "." + extension;
            }

            document.setFilename(filename);
            document.setExtension(extension);
            document.setCreatorFullname(user.getFullname());
            document.setUpdaterFullname(user.getFullname());
            document.setFolderId(folder.getId());

            saveNewDoc(document, tempFile);

            addLog(document, DocumentLog.TYPE_CREATE, "上传文档: " + document.getFilename(),
                    user.getUsername(), user.getFullname(), WebUtils.getIPAddress());

            // 发布提示信息
            instantMessageService.sendFolderSubcribeMessage(user, document,
                    " 上传文档：<b>" + DocumentUtils.getDocumentNoticeUrl(document) + "</b>");

            // 执行规则
            folderRuleService.executeRule(FolderRule.ACTION_UPLOAD, document, user, WebUtils.getIPAddress());

            // 执行提取文字线程
            ZDDocMQUtils.addDocExtractMq(document.getId(), document.getNewestVersion(), user.getUsername());

            return JSONResultUtils.success();  // 返回成功并包含文档信息

        } catch (DocumentAmountExceededException dae) {
            log.debug("达到文档数上限");
            throw new IllegalArgumentException("达到文档数上限");
        } catch (ForbidExtException dae) {
            log.debug("被禁止的后缀");
            throw new IllegalArgumentException("被禁止的后缀");
        } catch (Throwable e) {
            log.error("保存文档错误: ", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public JSONResultUtils<Object> saveUploadAttachment(List<MultipartFile> uploadFiles, AttachmentUploadDto attachmentUploadDto, User user) {
        if (CollectionUtils.isEmpty(uploadFiles)) {
            return JSONResultUtils.error("文件不存在");
        }

        if (user == null) {
            return JSONResultUtils.error("用户不存在");
        }

        long attachmentFolderId = attachmentUploadDto.getAttachmentFolderId();
        Folder attachmentFolder = folderService.getById(attachmentFolderId);

        long recFolderId = attachmentUploadDto.getRecFolderId();
        RecFolder recFolder = recFolderService.getById(recFolderId);
        if (recFolder == null) {
            return JSONResultUtils.error("数据目录不存在");
        }

        long recId = attachmentUploadDto.getRecId();
        Map<String, Object> recordData = recordService.getRecordData(recId, null, recFolder);
        if (recordData == null || recordData.size() == 0) {
            return JSONResultUtils.error("数据不存在");
        }

        // 验证分区的目录设置是否正确
        log.debug("attachmentAreaCode -> {}", attachmentUploadDto.getAttachmentAreaCode());
        if (!RecordAttachmentsSettingUtils.validateAttachmentLocation(recFolder, recordData, attachmentUploadDto.getAttachmentAreaCode())) {
            return JSONResultUtils.error("附件区域附件目录设置有误,已自动跳过");
        }

        // 重置附件目录
        String attachmentAreaCode = attachmentUploadDto.getAttachmentAreaCode();
        attachmentFolder = RecordAttachmentsSettingUtils.createOrFindFolder(recFolder, recordData, attachmentFolder,
                attachmentAreaCode);
        if (attachmentFolder == null) {
            return JSONResultUtils.error("附件目录不存在");
        }

        if (!WorkflowUtils.isRecordInTask(recordData, attachmentUploadDto.getTaskId(), user)) {
            RecFolderPermissionUtils.checkPermission(user, recFolder, RecFolderPermission.CREATE);
        }

        if (BooleanUtils.toBoolean(String.valueOf(recordData.get("isLocked")))) {
            return JSONResultUtils.error("本数据已锁定，禁止编辑");
        }

        for (int i = 0; i < uploadFiles.size(); i++) {
            MultipartFile item = uploadFiles.get(i);
            try {
                String originalFilename = item.getOriginalFilename();
                if (StringUtils.isBlank(originalFilename)) {
                    log.debug("文件名为空: {}，跳过", originalFilename);
                    continue;
                }

                long size = item.getSize();
                if (size <= 0) {
                    log.debug("空文档: {}，跳过", originalFilename);
                    continue;
                }

                File uploadFile = ZDFileUtils.getTempFile(0);
                item.transferTo(uploadFile);

                String oldJson = "";
                if (recordData.get("附件") != null) {
                    oldJson = (String) recordData.get("附件");
                }

                // 检查同名附件是否已存在
                boolean recNoSameNameAttachment = SystemConfigManager.getInstance().getBooleanProperty(
                        "recNoSameNameAttachment");
                if (recNoSameNameAttachment && AttachmentUtils.getAttachment(oldJson, originalFilename) != null) {
                    log.debug("同名附件已存在，忽略");
                    continue;
                }

                log.debug("上传ECM附件: {}", originalFilename);

                // 上传附件
                Document document = new Document();
                String filename = FilenameUtils.getBaseName(originalFilename);
                String extension = StringUtils.lowerCase(FilenameUtils.getExtension(originalFilename));
                if (StringUtils.isNotBlank(extension)) {
                    filename = filename + "." + extension;
                }
                document.setFilename(filename);
                document.setExtension(StringUtils.lowerCase(FilenameUtils.getExtension(originalFilename)));
                document.setCreatorFullname(user.getFullname());
                document.setFolderId(attachmentFolder.getId());

                // record
                List<String> idAndTableList = document.getRecordIdAndTableList();
                idAndTableList.add(recId + "," + recFolder.getTableName());
                document.setRecordIdAndTable(StringUtils.join(idAndTableList, ":"));

                saveNewDoc(document, uploadFile);
                addLog(document, DocumentLog.TYPE_CREATE, "上传文档: " + document.getFilename(),
                        user.getUsername(), user.getFullname(), WebUtils.getIPAddress());

                // 发布提示信息
                instantMessageService.sendFolderSubcribeMessage(user, document, " 上传文档：<b>" +
                        DocumentUtils.getDocumentNoticeUrl(document) + "</b>");

                // 执行规则
                folderRuleService.executeRule(FolderRule.ACTION_UPLOAD, document, user, WebUtils.getIPAddress());

                // 执行提取文字线程
                ZDDocMQUtils.addDocExtractMq(document.getId(), document.getNewestVersion(), user.getUsername());

                AttachmentUtils.addAttachmentSync(recId, recFolder, recFolder.getTableName(), document, attachmentAreaCode,
                        user);

                recordService.addLog(recId, recFolder.getTableName(), RecordLog.TYPE_DOWNLOAD,
                        "上传附件: " + originalFilename,
                        user.getUsername(), user.getFullname(), WebUtils.getIPAddress());
            } catch (DocumentAmountExceededException dae) {
                log.debug("达到文档数上限");
                throw new IllegalArgumentException("达到文档数上限");
            } catch (ForbidExtException dae) {
                log.debug("被禁止的后缀");

                throw new IllegalArgumentException("被禁止的后缀");
            } catch (Throwable e) {
                log.error("保存文档错误: ", e);
                throw new RuntimeException(e);
            }
        }

        return JSONResultUtils.success();
    }

    @Override
    public JSONResultUtils<Object> saveUpdateDocument(MultipartFile uploadFile, String docId, String filename, String comment, User user) {
        if (uploadFile == null) {
            return JSONResultUtils.error("请上传文件");
        }

        long size = uploadFile.getSize();
        if (size <= 0) {
            log.debug("空文档: {}，跳过", filename);
            return JSONResultUtils.error("空文档, 无法更新");
        }

        if (user == null) {
            return JSONResultUtils.error("用户不存在");
        }

        Document document = documentRepository.get(docId);
        if (document == null) {
            return JSONResultUtils.error("文档不存在");
        }

        Folder folder = document.getFolder();
        if (!FolderPermissionUtils.checkPermission(user, folder, FolderPermission.CREATE)) {
            throw new IllegalArgumentException("无权进行此操作");
        }

        if (!document.isHasWorkflowEditPermission(user.getUsername())) {
            return JSONResultUtils.error("文档正在审批中，无法编辑");
        }

        try {
            File tempFile = ZDFileUtils.getTempFile(0);
            uploadFile.transferTo(tempFile);

            document.setFileSize(tempFile.length());

            // 设置新文件名
            if (StringUtils.isNotBlank(filename)) {
                String extension = document.getExtension();
                if (StringUtils.isNotBlank(extension)) {
                    filename = filename + "." + extension;
                }

                // 过滤文件名
                filename = TextUtils.filterFilename(filename);

                // 检查是否有重名文档和文件名长度
                if (filename.length() <= 255 && !isDocumentInFolder(filename, folder, document)) {
                    document.setFilename(filename);
                }
            }

            saveDoc(document, tempFile, user.getFullname(), comment, true);

            String ipAddress = WebUtils.getIPAddress();

            addLog(document, DocumentLog.TYPE_UPDATE, "更新文档: " + document.getFilename(),
                    user.getUsername(), user.getFullname(), ipAddress);

            // 执行规则
            folderRuleService.executeRule(FolderRule.ACTION_UPDATE, document, user, ipAddress);

            // 执行提取文字线程
            ZDDocMQUtils.addDocExtractMq(document.getId(), document.getNewestVersion(), user.getUsername());
        } catch (Exception e) {
            log.error("保存文档错误: ", e);
            return JSONResultUtils.error("保存文档错误");
        }

        return JSONResultUtils.success();
    }

    @Override
    public Page<Document> getDocumentsWithoutAttach(int pageNumber, int pageSize) {
        return documentRepository.getDocumentsWithoutAttach(pageNumber, pageSize);
    }

    @Override
    public Page<Document> list(Map<String, Object> params, User user) {
        long folderId = MapUtils.getLongValue(params, "folderId", 0L);
        boolean isTrash = MapUtils.getBooleanValue(params, "isTrash", false);
        String sortField = MapUtils.getString(params, "sortField", "");
        boolean asc = MapUtils.getBoolean(params, "asc", false);
        int pageNumber = MapUtils.getIntValue(params, "pageNumber", 0);
        int pageSize = MapUtils.getIntValue(params, "pageSize", 0);
        String keywords = MapUtils.getString(params, "keywords", "");
        Map<String, Object> advSearchKeywords = ZDMapUtils.getMapVlaue(params, "advSearchKeywords");

        return list(folderId, isTrash, keywords, advSearchKeywords, sortField, asc, user, pageNumber, pageSize);
    }


    @Override
    public Page<Document> list(long folderId, boolean isTrash, String keywords, Map<String, Object> advSearchKeywords, String sortField, boolean asc, User user, int pageNumber, int pageSize) {
        Folder folder = folderRepository.get(folderId);
        if (folder != null) {
            if (!FolderPermissionUtils.checkPermission(UserContextHolder.getUser(), folder, FolderPermission.LIST)) {
                throw new IllegalArgumentException("无权进行此操作");
            }
        } else {
            if (!isTrash) {
                throw new IllegalArgumentException("未找到目录");
            }
        }

        return documentRepository.getDocumentsByFolder(folderId, user.getUsername(), isTrash, keywords, advSearchKeywords, sortField, asc, pageNumber, pageSize);
    }

    @Override
    public void copyVersionToOtherDoc(String oriDocId, int oriDocVersion, String targetDocSn, String versionComment, String creatorFullname, boolean copyWorkflow, String ip, boolean checkPerm, boolean addLog) throws DocumentNotFoundException, DifferentExtensionException, InWorkflowException {
        Document oriDoc = getDocumentById(oriDocId);
        if (oriDoc == null) {
            throw new DocumentNotFoundException();
        }

        if (StringUtils.isBlank(targetDocSn)) {
            targetDocSn = oriDoc.getProperty("mergeToDocSn");
        }

        if (StringUtils.isBlank(targetDocSn)) {
            throw new DocumentNotFoundException();
        }

        targetDocSn = targetDocSn.trim();

        Document targetDoc = getDocumentBySerial(targetDocSn);
        if (targetDoc == null) {
            throw new DocumentNotFoundException();
        }

        if (!targetDoc.getExtension().equals(oriDoc.getExtension())) {
            throw new DifferentExtensionException();
        }

        if (checkPerm &&
                (!FolderPermissionUtils.checkPermission(UserContextHolder.getUser(), targetDoc.getFolder(), FolderPermission.EDIT) ||
                        !FolderPermissionUtils
                                .checkPermission(UserContextHolder.getUser(), oriDoc.getFolder(), FolderPermission.EDIT))) {
            throw new UnauthorizedException();
        }

        if (oriDocVersion == -1) {
            oriDocVersion = oriDoc.getNewestVersion();
        }

        // 处理流程
        if (copyWorkflow && StringUtils.isNotBlank(oriDoc.getPiId())) {
            SSProcessInstance pi = processService.getProcessInstance(oriDoc.getPiId());
            if (pi != null) {
                if (!pi.isEnded()) {
                    throw new InWorkflowException();
                }

                targetDoc.setPiId(oriDoc.getPiId());
                /*pi.setResourceId(targetDoc.getId());*/
                pi.setResourceName(targetDoc.getFilename());
                processInstanceRepository.update(pi);

                // 清除之前文档的审批状态
                oriDoc.setPiId(null);
            }
        }

        // 默认合并至之前的文档
        oriDoc.setProperty("mergeToDocSn", targetDocSn);
        documentRepository.update(oriDoc);

        // 检查缩略图
        if (oriDoc.isHasThumbnail()) {
            log.debug("copyVersionToOtherDoc原文档有缩略图");
            try {
                File oriTnFile = ZDIOUtils.getThumbnailFile(oriDoc, oriDoc.getNewestVersion());
                File targetTnFile = ZDIOUtils.getThumbnailFile(targetDoc, targetDoc.getNewestVersion() + 1);

                if (oriTnFile != null && oriTnFile.exists() && oriTnFile.canRead()) {
                    FileUtils.copyFile(oriTnFile, targetTnFile);
                    targetDoc.setHasThumbnail(true);
                }
            } catch (Throwable e) {
                log.error("copyVersionToOtherDoc复制缩略图时出错", e);
            }
        }

        // 更新document对象
        targetDoc.setNewestVersion(targetDoc.getNewestVersion() + 1);
        targetDoc.setModifiedDate(new Date());
        targetDoc.setFileSize(oriDoc.getFileSize());
        targetDoc.setContent(oriDoc.getContent());
        targetDoc.setHash(oriDoc.getHash());

        // 修改目标文档的文件名
        if (!oriDoc.getFilename().equals(targetDoc.getFilename())) {
            String newFilename = getUniqueFilename(oriDoc.getFilename(), targetDoc.getFolder());
            targetDoc.setFilename(newFilename);
        }

        documentRepository.update(targetDoc);

        // 如果修订版说明为空，去获取原文档的修订版说明
        if (StringUtils.isBlank(versionComment)) {
            DocumentVersion oriNewestVersion = documentVersionRepository.getNewestDocumentVersion(oriDoc);
            if (oriNewestVersion != null) {
                versionComment = oriNewestVersion.getComment();
                log.debug("copyVersionToOtherDoc 修订版说明为空，获取原文档说明: {}, {}", versionComment,
                        oriNewestVersion.getVersionNumber());
            }
        }

        // 增加新的DocumentVersion
        DocumentVersion newVer = new DocumentVersion();
        newVer.setComment(TextUtils.escapeXml(versionComment));
        newVer.setVersionNumber(targetDoc.getNewestVersion());
        newVer.setCreatorFullname(creatorFullname);
        newVer.setDocId(targetDoc.getId());
        newVer.setContent(oriDoc.getContent());
        newVer.setHash(oriDoc.getHash());
        documentVersionRepository.save(newVer);

        // 复制文件
        try {
            File oriDocFile = ZDIOUtils.getDocFile(oriDoc, oriDoc.getNewestVersion(), false);
            File targetDocFile = ZDIOUtils.getDocFile(targetDoc, targetDoc.getNewestVersion(), false);
            FileUtils.copyFile(oriDocFile, targetDocFile);
        } catch (Throwable t) {
            log.error("copyVersionToOtherDoc复制文件异常", t);
        }

        // 审计
        if (addLog) {
            User u = UserContextHolder.getUser();
            if (u == null) {
                u = UserGroupUtils.getDummyAdminUser();
            }
            addLog(oriDoc, DocumentLog.TYPE_UPDATE, "将文档: " + oriDoc.getFilename() + "  合并至：" +
                    targetDoc.getFilename() + "，目标文档编号：" + targetDocSn, u.getUsername(), u.getFullname(), ip);
            addLog(targetDoc, DocumentLog.TYPE_UPDATE, "文档：" + oriDoc.getFilename() + "，编号：" +
                    oriDoc.getSerialNumber() + " 合并至本文档", u.getUsername(), u.getFullname(), ip);
        }
    }

    @Override
    public void addRecordIdAndTable(Document document, long recId, String table) {
        if (document == null || recId <= 0 || StringUtils.isBlank(table)) {
            return;
        }

        List<String> idAndTableList = document.getRecordIdAndTableList();
        if (!idAndTableList.contains(recId + "," + table)) {
            idAndTableList.add(recId + "," + table);
            document.setRecordIdAndTable(StringUtils.join(idAndTableList, ":"));
            updateDocument(document, false);
        }
    }

    @Override
    public String getUniqueFilename(String docUploadFileName, Folder folder) {
        String extension = StringUtils.lowerCase(FilenameUtils.getExtension(docUploadFileName));
        String filename = docUploadFileName;

        // 检查是否有重名文档
        // 在文件名后面增加括号和数字
        if (isDocumentInFolder(docUploadFileName, folder, null)) {
            int i = 1;
            filename = FilenameUtils.getBaseName(docUploadFileName) + "(" + i + ")";
            if (StringUtils.isNotBlank(extension)) {
                filename += "." + extension;
            }
            if (filename.length() > 255) {
                filename = filename.substring(filename.length() - 255);
            }

            while (isDocumentInFolder(filename, folder, null)) {
                i++;
                filename = FilenameUtils.getBaseName(docUploadFileName) + "(" + i + ")";
                if (StringUtils.isNotBlank(extension)) {
                    filename += "." + extension;
                }
                if (filename.length() > 255) {
                    filename = filename.substring(filename.length() - 255);
                }
            }

            log.debug("文件名已存在，自动更名为：{}", filename);
        }

        return filename;
    }

    @Override
    public void clearPi(Document document, User operator, String ip) {
        if (document == null) {
            return;
        }

        if (document.getProcessInstance() == null) {
            return;
        }

        if (!document.getProcessInstance().isEnded()) {
            return;
        }

        String username = UserGroupUtils.ADMIN_USERNAME;
        String fullname = "系统管理员";

        if (operator != null) {
            username = operator.getUsername();
            fullname = operator.getFullname();
        }

        document.setPiId(null);
        document.setLocked(false);
        updateDocument(document, false);

        addLog(document, DocumentLog.TYPE_WORKFLOW,
                "将文档: " + document.getFilename() + "  审批归档并解锁", username, fullname,
                ip);
    }

    @Override
    public void createDocumentRelation(String docId, String docSerialNumber, User operator, String ipAddress) {
        Document doc = getDocumentById(docId);

        if (doc != null) {
            Document docRelated = documentRepository.getDocumentBySerial(docSerialNumber);
            if (docRelated != null) {
                // 不可以关联自身
                if (docId.equals(docRelated.getId())) {
                    return;
                }

                DocumentRelation dr = new DocumentRelation();
                dr.setDocId(doc.getId());
                dr.setRelatedDocId(docRelated.getId());
                if (operator != null) {
                    dr.setCreatorFullname(operator.getFullname());

                    if (StringUtils.isBlank(dr.getCreator())) {
                        dr.setCreator(operator.getCreator());
                    }
                }
                documentRelationRepository.save(dr);

                addLog(docRelated, DocumentLog.TYPE_RELATION,
                        "将文档: " + docRelated.getFilename() + " 关联至： " + doc.getFilename(), operator.getUsername(),
                        operator.getFullname(), ipAddress);
            }
        }
    }

    @Override
    public List<DocumentRelation> getDocumentRelations(Document doc) {
        return documentRelationRepository.getDocumentRelations(doc);
    }

    @Override
    public List<Map> getRelatedDocuments(Document doc) {
        List<Map> result = new LinkedList<>();

        // 处理关联文档
        List<DocumentRelation> tempRelations = getDocumentRelations(doc);
        Set<String> documentRelations = new LinkedHashSet<>();
        for (DocumentRelation dr : tempRelations) {
            Map docMap = new HashMap();

            if (documentRelations.contains(dr.getDocument().getId()) ||
                    documentRelations.contains(dr.getDocumentRelated().getId())) {
                continue;
            }

            if (!dr.getDocument().isDeleted() && !dr.getDocument().equals(doc)) {
                documentRelations.add(dr.getDocument().getId());
                docMap.put("document", dr.getDocument());
            }

            if (!dr.getDocumentRelated().isDeleted() && !dr.getDocumentRelated().equals(doc)) {
                documentRelations.add(dr.getDocumentRelated().getId());
                docMap.put("document", dr.getDocumentRelated());
            }

            docMap.put("drCreator", dr.getCreator());
            docMap.put("drCreatorFullname", dr.getCreatorFullname());
            docMap.put("drCreationDate", dr.getCreationDate());
            if (docMap.containsKey("document")) {
                result.add(docMap);
            }
        }

        return result;
    }

    @Override
    public Document getDocumentBySerial(String serial) {
        return documentRepository.getDocumentBySerial(serial);
    }

    @Override
    public List<Document> getDocumentsByExtension(String extension) {
        return documentRepository.getDocumentsByExtension(extension);
    }

    @Override
    public void deleteRelation(Document doc, Document docRelated) {
        documentRelationRepository.deleteRelation(doc, docRelated);
    }

    @Override
    public void deleteDocument(Document doc) {
        if (doc != null) {
            log.debug("删除文档：{}", doc.getFilename());

            // 清除磁盘空间缓存
            //            defaultCacheManager.clearProperty("systemInfo.allDocumentSize");

            // 删除所有链接
            documentLinkService.delete(doc);

            // 删除版本
            deleteAllVersion(doc);

            // 删除评论
            documentNoteRepository.deleteAllNotesByDocument(doc);

            // 删除审计日志
            documentLogRepository.deleteAllLogsByDocument(doc);

            // 删除文档关联
            documentRelationRepository.deleteDocumentRelation(doc);

            // 删除文档借阅
            documentLendingRepository.deleteByDocument(doc);

            // 删除文档提醒
            documentRemindService.deleteRemindByDocument(doc);

            // 删除文档对应签章
            documentSigRepository.deleteSigsByDocId(doc.getId());

            // 删除订阅
            folderSubscribeRepository.deleteAllSubscribesByDocId(doc.getId());

            // 删除审批流程
            // processService.deleteProcessInstanceByResourceIdAndType(doc.getId(),
            // SSProcessInstance.RES_TYPE_DOC);

            // 删除操作记录
            userOperateLogService.deleteLogByDocId(doc.getId());

            // 删除文档数据库记录
            documentRepository.delete(doc);

            // 删除文档索引
            if (!doc.isAttachDoc()) {
                ESQueueUtils.deleteDocument(doc);
            } else {
                // 修改附件信息
            }

            // 删除与文档关联的属性记录
            DocAdvPropsDBUtils.removeDocPropertyByDocId(doc.getId());
        }
    }

    private void deleteAllVersion(Document document) {
        documentVersionRepository.deleteAllVersionsByDocument(document);
        ZDIOUtils.deleteDocDir(document);
    }

    @Override
    public List<String> moveDocsToTrash(long folderId, String[] ids, User user, String ip) {
        List<String> movedSuccess = new LinkedList<String>();

        if (ids != null) {
            for (String aId : ids) {
                if (aId.startsWith("link_")) {
                    Folder folder = folderRepository.get(folderId);
                    Document document = getDocumentById(aId.substring(5));

                    if (folder != null && document != null) {
                        log.debug("removeLink: {}, {}", folder.getName(), document.getFilename());
                        documentLinkService.removeLinkFromFolder(folder, document);
                        movedSuccess.add(aId);
                    }

                    continue;
                }

                Document document = getDocumentById(aId);

                if (document == null) {
                    continue;
                }

                if (folderId != document.getFolderId()) {
                    log.debug("moveDocsToTrash 传递的目录ID与文档目录ID不符，尝试删除链接");
                    Folder folder = folderRepository.get(folderId);

                    if (folder != null && document != null) {
                        log.debug("moveDocsToTrash removeLink: {}, {}", folder.getName(), document.getFilename());
                        documentLinkService.removeLinkFromFolder(folder, document);
                        movedSuccess.add(aId);
                    }

                    continue;
                }

                if (document.isDeleted()) {
                    continue;
                }

                if (document.isEditing()) {
                    log.debug("moveDocsToTrash 文档{}正在编辑，跳过", document.getFilename());
                    continue;
                }

                if (document.isLocked()) {
                    log.debug("moveDocsToTrash 文档{}已锁定，跳过", document.getFilename());
                    continue;
                }

                if (document.isOnWorkflow()) {
                    log.debug("moveDocsToTrash 文档{}在审批中，跳过", document.getFilename());
                    continue;
                }

                folderId = document.getFolder().getId();

                if (!FolderPermissionUtils.checkPermission(user, document, FolderPermission.DELETE)) {
                    log.debug("moveDocsToTrash 文档{}无删除权限，跳过", document.getFilename());
                    continue;
                }

                document.setDeleted(true);
                document.setDeletedBy(user.getUsername());
                document.setDeletedByFullname(user.getFullname());
                document.setDeleteDate(new Date());

                // 文件名增加已删除后缀
                String newFilename = document.getBaseName() + "[回收站]";
                if (StringUtils.isNotBlank(document.getExtension())) {
                    newFilename += "." + document.getExtension();
                }

                // 防止加入回收站字样后文件名重复
                newFilename = getUniqueFilename(newFilename, document.getFolder());
                document.setFilename(getUniqueFilename(newFilename, document.getFolder()));

                updateDocument(document);

                movedSuccess.add(aId);

                addLog(document, DocumentLog.TYPE_TRASH, "将文档: " + document.getFilename() + "  移动至回收站",
                        user.getUsername(), user.getFullname(), ip);

                // 发布提示信息
                instantMessageService.sendFolderSubcribeMessage(
                        user,
                        document,
                        "将文档：<b>" +
                                DocumentUtils.getShortAltString(document.getSummaryFilename30(),
                                        document.getDisplayFullpath()) + "</b>移动至回收站");

                // 删除文档借阅
                documentLendingRepository.deleteByDocument(document);
            }
        }

        return movedSuccess;
    }

    @Override
    public void restoreDocs(String[] ids, User user, String ip) {
        if (ids != null) {
            for (String aId : ids) {
                Document document = getDocumentById(aId);

                if (document == null) {
                    continue;
                }

                if (!document.isDeleted()) {
                    continue;
                }

                if (FolderPermissionUtils.checkPermission(user, document, FolderPermission.DELETE)) {
                    String restoreFilename = document.getFilename().replace("[回收站]", "");
                    document.setDeleted(false);
                    document.setDeletedBy(null);
                    document.setDeletedByFullname(null);
                    document.setDeleteDate(null);
                    document.setFilename(restoreFilename);
                    updateDocument(document);

                    addLog(document, DocumentLog.TYPE_RESTORE,
                            "还原文档: " + document.getFilename(), user.getUsername(),
                            user.getFullname(), ip);

                    // 提示信息
                    instantMessageService.sendFolderSubcribeMessage(user, document,
                            " 还原文档：<b>" + DocumentUtils.getDocumentNoticeUrl(document) + "</b>");
                }
            }
        }
    }

    @Override
    public void deleteDocs(String[] ids, User user, String ip, boolean byUser) {
        boolean userCanNotDeleteTrash = SystemConfigManager.getInstance().getBooleanProperty("ucndt");

        if (ids != null) {
            for (String aId : ids) {
                Document document = getDocumentById(aId);

                if (document == null) {
                    continue;
                }

                /*if (!document.isDeleted()) {
                    continue;
                }*/

                if (byUser && userCanNotDeleteTrash && document.getFolder().getFolderType() == Folder.TYPE_PUBLIC) {
                    continue;
                }

                if (FolderPermissionUtils.checkPermission(user, document.getFolder(), FolderPermission.DELETE)) {
                    deleteDocument(document);

                    logService.addMessage(LogMessage.LEVEL_INFO, user.getUsername(), ip, user.getFullname() + "(" +
                            user.getUsername() + ")" + " 永久删除文档：" + document.getFilename());
                }
            }
        }
    }

    @Override
    public List<String> moveDocs(Folder folder, String[] ids, User user, String ip, boolean fromRuleManager) {
        List<String> movedSuccess = new LinkedList<String>();

        if (folder == null) {
            log.debug("moveDocs null dest folder");
            return movedSuccess;
        }

        if (ids != null) {
            for (String aId : ids) {
                if (StringUtils.startsWith(aId, "folder_")) {
                    continue;
                }

                Document document = getDocumentById(aId);

                if (document == null) {
                    continue;
                }

                // 检测是否为同一目录
                Folder oriFolder = document.getFolder();
                if (folder.getId() == oriFolder.getId()) {
                    log.debug("moveDocs same source and dest folder");
                    continue;
                }

                if (document.isEditing()) {
                    log.debug("文档{}正在编辑，跳过", document.getFilename());
                    continue;
                }

                if (document.isLocked()) {
                    log.debug("文档{}已锁定，跳过", document.getFilename());
                    continue;
                }

                if (document.isOnWorkflow()) {
                    log.debug("文档{}正在审批中，跳过", document.getFilename());
                    continue;
                }

                if (isDocumentInFolder(document.getFilename(), folder, null)) {
                    log.debug("重名文档{}，自动改名", document.getFilename());
                    document.setFilename(getUniqueFilename(document.getFilename(), folder));
                }

                if (!ZDIOUtils.moveDoc(folder, document)) {
                    log.debug("文档 {} 移动失败，跳过", document.getFilename());
                    continue;
                }

                document.setFolderId(folder.getId());
                updateDocument(document);

                // 移动文档同时，修改文档属性
                DocAdvPropsDBUtils.setDocPropFolderId(aId, folder.getId());

                movedSuccess.add(aId);

                addLog(document,
                        DocumentLog.TYPE_MOVE,
                        "移动文档: " + document.getFilename() + " 从 " + oriFolder.getDisplayFolder() + " 至 " +
                                folder.getDisplayFolder(), user.getUsername(), user.getFullname(), ip);

                // 执行规则
                if (!fromRuleManager) {
                    folderRuleService.executeRule(FolderRule.ACTION_MOVE, document, user, ip);
                }
            }
        }

        return movedSuccess;
    }

    @Override
    public Document copyDoc(Document oriDoc, String newFilename, long folderId) throws IOException, DocumentAmountExceededException, ForbidExtException {
        if (oriDoc == null) {
            log.debug("null ori document");
            throw new IOException();
        }

        if (oriDoc.isLocked()) {
            return null;
        }

        if (oriDoc.isEditing()) {
            return null;
        }

        if (oriDoc.isOnWorkflow()) {
            return null;
        }

        // 处理新文件名
        String finalFilename = newFilename;
        if (StringUtils.isBlank(finalFilename)) {
            finalFilename = oriDoc.getBaseName();
        }
        finalFilename = TextUtils.filterFilename(finalFilename);
        if (StringUtils.isBlank(finalFilename)) {
            log.debug("null filename");
            throw new IOException();
        }

        Document newDoc = new Document();
        String filename = finalFilename;
        String extension = oriDoc.getExtension();
        if (StringUtils.isNotBlank(extension)) {
            filename = filename + "." + extension;
        }
        newDoc.setFilename(filename);
        newDoc.setExtension(extension);
        newDoc.setCreatorFullname(UserContextHolder.getUser().getFullname());
        newDoc.setContent(oriDoc.getContent());
        newDoc.setProperties(oriDoc.getProperties());

        if (StringUtils.isBlank(oriDoc.getProperty("mergeToDocSn"))) {
            newDoc.setProperty("mergeToDocSn", oriDoc.getSerialNumber());
        }

        Folder targetFolder = oriDoc.getFolder();
        if (folderId > 0) {
            Folder tmpFolder = folderRepository.get(folderId);
            if (tmpFolder != null) {
                targetFolder = tmpFolder;
            }
        }
        newDoc.setFolderId(targetFolder.getId());

        // 获取原文件
        File tempFile = ZDIOUtils.getTempFile();
        InputStream in = ZDIOUtils.getDocInputStream(oriDoc, oriDoc.getNewestVersion());
        OutputStream fileOut = new FileOutputStream(tempFile);
        IOUtils.copy(in, fileOut);

        try {
            saveNewDoc(newDoc, tempFile);
            return newDoc;
        } finally {
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(fileOut);
            FileUtils.deleteQuietly(tempFile);
        }
    }

    @Override
    public void saveNewDoc(Document document, File uploadFile) throws IOException, DocumentAmountExceededException, ForbidExtException {
        if (document == null || uploadFile == null) {
            log.debug("null document or null uploadFile");
            throw new IOException();
        }

        // 检查单文档尺寸限制
        int singleDocLimit = SystemConfigManager.getInstance().getIntProperty("singleDocLimit");
        if (singleDocLimit > 0) {
            long limit = singleDocLimit * 1024L * 1024L;
            if (uploadFile.length() > limit) {
                throw new DocumentAmountExceededException();
            }
        }

        // 如果后缀被禁止，就抛出异常
        if (ZDFilenameUtils.isInForbidExt(document.getFilename())) {
            throw new ForbidExtException();
        }

        // 如果是体验版或者硬件信息不匹配，文档数限制为1100
        // 最新版改为文档数限制1100
        if (!TaskDictionary.getMainDefinition().dv()) {
            int docCount = RecordDBUtils.checkJdbcTemplate().queryForObject("select count(*) from tdms_doc", Integer.class);
            if (docCount > 1100) {
                throw new DocumentAmountExceededException();
            }
        }

        // 如果是软加密，则文档数限制为1200
        if (ModeDefinition.sk()) {
            int docCount = RecordDBUtils.checkJdbcTemplate().queryForObject("select count(*) from tdms_doc", Integer.class);
            if (docCount > 1100) {
                throw new DocumentAmountExceededException();
            }
        }

        // 检查OfficeUtils和InstrumentationUpdatingAgent的60分钟检测进程是否正常运行
        // 如果已经超过2小时没运行，退出系统
        Date now = new Date();
        if ((now.getTime() - OfficeUtils.lastAccessDate.getTime()) > 7600000) {
            ConcurrentDictionary.lout(0);
            ZDUtils.info("ZDSD: 001");
            System.exit(0);
        }
        if ((now.getTime() - OfficeUtils.lastAccessDate2.getTime()) > 7600000) {
            ConcurrentDictionary.lout(0);
            ZDUtils.info("ZDSD: 002");
            System.exit(0);
        }

        // 检查个人文档空间
        if (document.getFolder().getFolderType() == Folder.TYPE_MYDOCUMENT) {
            document.setStorage("0");
            if (isMyDocLimitEnabled()) {
                log.debug("个人文档，开始检查剩余空间");
                long freeSpace = getMyDocFreeSpace(document.getCreator());
                if (freeSpace < uploadFile.length()) {
                    log.debug("空间不足，剩余：{}，需要：{}", freeSpace, uploadFile.length());
                    throw new DocumentAmountExceededException();
                }
            }
        }

        // 提取txt encoding
        if (OfficeUtils.isTxtFile(document.getExtension())) {
            String encoding = OfficeUtils.detectTextFileCharset(uploadFile);
            log.debug("txt file: {}, encoding: {}", document.getFilename(), encoding);
            document.setEncoding(encoding);
        }

        String extractedText = "";
		/*
		// 提取文档摘要
		String extractedText = OfficeUtils.extractText(document, uploadFile,
				document.getExtension());

		if (StringUtils.isNotBlank(extractedText)) {
			document.setContent(extractedText);
		}
		*/

        // 生成流水号
        if (StringUtils.isBlank(document.getSerialNumber())) {
            generateDocumentSerial(document);
        }

        // hash
        String hash = null;
		/*
		if (ZDIOUtils.FH()) {
			hash = HashUtils.getHashFile(uploadFile);
			if (StringUtils.isNotBlank(hash)) {
				document.setHash(hash);
			}
		}
		*/

        // 生成唯一的文件名
        document.setFilename(getUniqueFilename(document.getFilename(), document.getFolder()));
        log.debug("已获取唯一文件名：{}", document.getFilename());

        document.setFileSize(uploadFile.length());
        document.setModifiedDate(new Date());
        documentRepository.save(document);
        log.debug("已保存document对象：{}", document.getFilename());

        // 版本
        DocumentVersion ver1 = new DocumentVersion();
        ver1.setComment("");
        ver1.setContent(extractedText);
        ver1.setHash(hash);
        ver1.setVersionNumber(SystemConfigManager.getInstance().getDefaultDocRevision());
        ver1.setCreatorFullname(document.getCreatorFullname());
        ver1.setDocId(document.getId());
        ver1.setDocVersion(document.getDocVersion());
        documentVersionRepository.save(ver1);
        log.debug("已保存documentVersion对象：{}", document.getFilename());

        // 索引文档
        if (!document.isAttachDoc()) {
            ESQueueUtils.createDocument(document);
        }

        log.debug("已索引文档：{}", document.getFilename());

        // 保存文件
        try {
            ZDIOUtils.saveNewDoc(document, uploadFile);
            log.debug("已保存文件：{}", document.getFilename());
        } catch (Throwable t) {
            log.error("saveNewDoc复制文件异常", t);
            if (!document.isAttachDoc()) {
                ESQueueUtils.deleteDocument(document);
            }
            throw new IOException("saveNewDoc复制文件异常", t);
        }

        // 创建用户操作记录
        UserOpLogUtils.createUserOpLog(document.getCreator(), document.getCreatorFullname(),
                UserOperateLog.TYPE_CREATE, document.getFilename(), document.getDisplayFolderWithoutLink(),
                document.getId(), document.getFolderId());
        log.debug("已创建用户操作记录：{}", document.getFilename());
    }

    private boolean isMyDocLimitEnabled() {
        int myDocLimit = SystemConfigManager.getInstance().getIntProperty(SystemConfigManager.MYDOC_LIMIT_KEY);
        log.debug("isMyDocLimitEnabled: {} MB", myDocLimit);
        return myDocLimit > 0;
    }

    private long getMyDocFreeSpace(String username) {
        long used = ZDIOUtils.getMyDocStorageUsed(username);
        long myDocLimit = SystemConfigManager.getInstance().getIntProperty(SystemConfigManager.MYDOC_LIMIT_KEY);
        long result = (myDocLimit * 1024L * 1024L) - used;
        log.debug("getMyDocFreeSpace: {}", username);
        return result;
    }

    /**
     * 生成文档流水号
     *
     * @param doc
     */
    private void generateDocumentSerial(Document doc) {
        while (true) {
            String serial = RandomStringUtils.random(10, false, true);
            if (serial.startsWith("0")) {
                continue;
            }

            if (documentRepository.getDocumentBySerial(serial) != null) {
                continue;
            }

            log.debug("生成流水号：{}", serial);
            doc.setSerialNumber(serial);
            break;
        }
    }

    @Override
    public void saveNewDoc(Document document, File file, Folder uploadedFolder) throws IOException, DocumentAmountExceededException, ForbidExtException {
        saveNewDoc(document, file);
    }

    @Override
    public void saveDoc(Document document, File uploadFile, String creatorFullname, String versionComment, boolean addNewVersion) throws IOException {
        log.debug("saveDoc versionComment: {}", versionComment);

        String extractedText = "";
        String hash = null;


        if (!addNewVersion) {
            document.setModifiedDate(new Date());
            document.setFileSize(uploadFile.length());
            document.setUpdaterFullname(creatorFullname);
            documentRepository.update(document);

            // 保存文件
            try {
                ZDIOUtils.saveDoc(document, document.getNewestVersion(), uploadFile);
            } catch (Throwable t) {
                log.error("saveDoc复制文件异常", t);
            }

            // 设置现有版本的更新者
            DocumentVersion dv = documentVersionRepository.getNewestDocumentVersion(document);
            dv.setCreatorFullname(creatorFullname);
            dv.setComment(TextUtils.escapeXml(versionComment));
            dv.setContent(extractedText);
            dv.setCreationDate(new Date());
            dv.setHash(hash);
            dv.setDocVersion(document.getDocVersion());
            documentVersionRepository.update(dv);
        } else {
            // 更新document对象
            document.setNewestVersion(document.getNewestVersion() + 1);
            document.setModifiedDate(new Date());
            document.setFileSize(uploadFile.length());
            document.setUpdaterFullname(creatorFullname);
            documentRepository.update(document);

            // 增加新的DocumentVersion
            DocumentVersion newVer = new DocumentVersion();
            newVer.setComment(TextUtils.escapeXml(versionComment));
            newVer.setVersionNumber(document.getNewestVersion());
            newVer.setCreatorFullname(creatorFullname);
            newVer.setDocId(document.getId());
            newVer.setContent(extractedText);
            newVer.setHash(hash);
            newVer.setDocVersion(document.getDocVersion());
            documentVersionRepository.save(newVer);

            // 保存文件
            try {
                ZDIOUtils.saveDoc(document, document.getNewestVersion(), uploadFile);
                log.debug("saveDoc, documentVersion version: {}, comment: {}", newVer.getVersionNumber(),
                        newVer.getComment());
            } catch (Throwable t) {
                log.error("saveDoc复制文件异常", t);
                if (!document.isAttachDoc()) {
                    ESQueueUtils.deleteDocument(document);
                }
                throw new IOException("saveDoc复制文件异常", t);
            }

            // 根据需要删除旧版本
            deleteOldestVersion(document);
        }

        if (!document.isAttachDoc()) {
            ESQueueUtils.createDocument(document);
        }

        // 创建用户操作记录
        User currentUser = UserContextHolder.getUser();
        if (currentUser != null) {
            UserOpLogUtils.createUserOpLog(currentUser.getUsername(), currentUser.getFullname(),
                    UserOperateLog.TYPE_UPDATE, document.getFilename(), document.getDisplayFolderWithoutLink(),
                    document.getId(), document.getFolderId());
        }
    }

    private void deleteOldestVersion(Document document) {
        // 文档修改版的最大数量应该为：高级设置中的版本数+对应发布版本的修订版数
        int maxVersion = SystemConfigManager.getInstance().getIntProperty("maxVersion");

        int versions = documentVersionRepository.getVersionCount(document);

        log.debug("versionSize: {}", versions);

        if (versions > maxVersion) {
            // 得到该文档最早且非发布版本的修订版
            DocumentVersion oldestVersion = documentVersionRepository.getOldestDocumentVersion(document);
            int versionNumber = oldestVersion.getVersionNumber();
            log.debug("超出限制：{}, 自动删除版本：{}", maxVersion, versionNumber);
            documentVersionRepository.delete(oldestVersion);
            ZDIOUtils.deleteDocVersion(document, versionNumber);

            // 如果是视频格式则删除对应版本预览视频
            if (document.isVideoFile()) {
                ZDIOUtils.deleteDocPreviewFile(document, versionNumber);
            }

            ZDIOUtils.deleteDocConvertPdfFile(document, versionNumber);
            ZDIOUtils.deleteDocTypeFile(document, versionNumber, ZDFileUtils.ZDFILE_TYPE_OFFICE07);
        }
    }

    @Override
    public void saveDocForTemp(Document document, File uploadFile, String creatorFullname, int versionNumber, String versionComment) throws IOException {
        // 清除磁盘空间缓存
        //        defaultCacheManager.clearProperty("systemInfo.allDocumentSize");

        DocumentVersion dv = documentVersionRepository.getDocumentVersion(document, versionNumber);

        // pptx在保存后会存为2003格式
		/*
		if (document.getFilename().endsWith(".pptx")) {
			document.setFilename(document.getBaseName() + ".ppt");
			document.setExtension("ppt");
			documentDao.update(document);
		}
		*/

        if (dv == null) {
            log.debug("SaveNewVer: {}", versionNumber);
            dv = new DocumentVersion();
            dv.setComment(versionComment);
            dv.setVersionNumber(versionNumber);
            dv.setCreatorFullname(creatorFullname);
            dv.setDocId(document.getId());
            documentVersionRepository.save(dv);
        } else {
            log.debug("SaveVer: {}", versionNumber);
            dv.setCreatorFullname(creatorFullname);
            dv.setComment(versionComment);
            documentVersionRepository.update(dv);
        }

        // 保存文档
        try {
            ZDIOUtils.saveDoc(document, versionNumber, uploadFile);
        } catch (Throwable t) {
            log.error("saveDocVersion复制文件异常", t);
        }

        // 保存临时文档，无需索引
        // searchManager.indexDoc(document);
    }

    @Override
    public void rollbackVersion(Document document, String creator, String creatorFullname, int version, String comment) throws IOException {
        // 清除磁盘空间缓存
        //        defaultCacheManager.clearProperty("systemInfo.allDocumentSize");

        File selectedVersionFile = ZDIOUtils.getDocFile(document, version, false);
        DocumentVersion selectedVersion = documentVersionRepository.getDocumentVersion(document, version);

        if (!selectedVersionFile.exists() || selectedVersion == null) {
            return;
        }

        // 更新document对象
        // 解出新的内容文字
        // 先从版本对象里获取，如果没有，就解析文件
        // updated 2012-9-21 因为必须要在线程中提取文字，所以本位置不再提取文字，直接从版本中获取
        String extractedText = selectedVersion.getContent();

		/*
		if (StringUtils.isBlank(extractedText)) {
			log.debug("文档 {} 的回退使用新提取的文字", document.getFilename());
			if (OfficeUtils.isExtractable(document.getFilename())) {
				if (!ZDIOUtils.FE()) {
					log.debug("未加密，直接解出文字");
					extractedText = OfficeUtils.extractText(document,
							selectedVersionFile, document.getExtension());
				} else {
					File decTemp = ZDIOUtils.df(selectedVersionFile, true);
					log.debug("已加密，先复制临时文件：{}", decTemp.getAbsolutePath());
					extractedText = OfficeUtils.extractText(document, decTemp,
							document.getExtension());
					FileUtils.deleteQuietly(decTemp);
				}
			}
		} else {
			log.debug("文档 {} 的回退使用版本内的提取文字", document.getFilename());
		}
		*/

        if (StringUtils.isNotBlank(extractedText)) {
            document.setContent(extractedText);
        }

        document.setNewestVersion(document.getNewestVersion() + 1);
        document.setModifiedDate(new Date());
        document.setFileSize(selectedVersionFile.length());
        document.setHash(selectedVersion.getHash());
        document.setDocVersion(selectedVersion.getDocVersion());
        updateDocument(document);

        // 增加新的DocumentVersion
        DocumentVersion newVer = new DocumentVersion();

        if (StringUtils.isNotBlank(comment)) {
            newVer.setComment(comment);
        } else {
            newVer.setComment("回退至版本: " + version);
        }

        newVer.setVersionNumber(document.getNewestVersion());
        newVer.setCreatorFullname(creatorFullname);
        newVer.setCreator(creator);
        newVer.setDocId(document.getId());
        // 设置版本
        newVer.setDocVersion(selectedVersion.getDocVersion());
        if (StringUtils.isNotBlank(extractedText)) {
            newVer.setContent(extractedText);
        }
        newVer.setHash(selectedVersion.getHash());
        documentVersionRepository.save(newVer);

        // 复制文件
        File docFileObj = ZDIOUtils.getNewestDocFile(document);
        try {
            ZDIOUtils.rollBackVersion(document, selectedVersionFile, docFileObj, version);
        } catch (Throwable t) {
            log.error("rollback复制文件异常", t);
        }

        // 根据需要删除旧版本
        deleteOldestVersion(document);
    }

    @Override
    public int getVersionCount(Document document) {
        return documentVersionRepository.getVersionCount(document);
    }

    @Override
    public List<DocumentVersion> getVersions(Document document, boolean withTempVersion, boolean asc) {
        return documentVersionRepository.getDocumentVersionByDocument(document, withTempVersion, asc);
    }

    @Override
    public DocumentVersion getVersion(Document document, int version) {
        return documentVersionRepository.getDocumentVersion(document, version);
    }

    @Override
    public List<Document> getDocumentsByFolder(Folder folder, String sortField, boolean asc) {
        return documentRepository.getDocumentsByFolderWithoutDeleted(folder, sortField, asc);
    }

    @Override
    public List<Document> getDocumentsByFolder(Folder folder) {
        return documentRepository.getDocumentsByFolderWithoutDeleted(folder, null, false);
    }

    @Override
    public List<Document> getDocumentsInTrash(String username, int type) {
        return documentRepository.getDocumentsInTrash(username, type);
    }

    @Override
    public Page getAllTrash(int pageNumber, int pageSize) {
        return documentRepository.getAllTrash(pageNumber, pageSize);
    }

    @Override
    public int getDocumentCountByFolder(Folder folder) {
        return documentRepository.getDocumentCountByFolder(folder);
    }

    public int getDocumentCountByFolderWithDeleted(Folder folder) {
        return documentRepository.getDocumentCountByFolderWithDeleted(folder);
    }

    @Override
    public boolean isDocumentInFolder(String filename, Folder folder, Document oriDoc) {
        return documentRepository.isDocumentInFolder(filename, folder, oriDoc);
    }

    @Override
    public Document getDocumentById(String id) {
        return documentRepository.get(id);
    }

    @Override
    public void deleteVersion(Document document, int version) {
        documentVersionRepository.deleteVersionByDocument(document, version);
        File versionFile = ZDIOUtils.getDocFile(document, version, false);
        FileUtils.deleteQuietly(versionFile);
        File versionGGFile = ZDIOUtils.getGGDocFile(document, version);
        FileUtils.deleteQuietly(versionGGFile);
        log.debug("删除文件:{}的版本:{}", document.getFilename(), version);
    }

    @Override
    public List<DocumentNote> getDocumentNotes(Document doc) {
        return documentNoteRepository.getDocumentNoteByDocument(doc);
    }

    @Override
    public void createDocumentNote(DocumentNote note) {
        documentNoteRepository.save(note);
    }

    @Override
    public void deleteDocumentNote(DocumentNote note) {
        documentNoteRepository.delete(note);
    }

    @Override
    public void deleteDocumentNote(long id) {
        documentNoteRepository.deleteNoteById(id);
    }

    @Override
    public DocumentNote getDocumentNote(Long id) {
        return documentNoteRepository.get(id);
    }

    @Override
    public DocumentLog addLog(Document doc, int type, String msg, String operator, String fullname, String ipAddress) {
        if (doc == null) {
            log.debug("addLog null doc");
            return null;
        }

        if (doc.getFolder().getFolderType() == Folder.TYPE_MYDOCUMENT) {
            log.debug("addLog 个人文档，不增加日志");
            return null;
        }

        DocumentLog log = new DocumentLog();
        log.setDocId(doc.getId());
        log.setOperateType(type);
        log.setMsg(msg);
        log.setOperator(operator);
        log.setFullname(fullname);
        log.setIp(ipAddress);

        documentLogRepository.save(log);

        return log;
    }

    @Override
    public void addLogFromAdmin(Document doc, int type, String msg) {
        addLog(doc, type, msg, UserGroupUtils.ADMIN_USERNAME, "系统管理员", "127.0.0.1");
    }

    @Override
    public List<DocumentLog> getLogs(Document doc, int type) {
        return documentLogRepository.getDocumentLogByDocument(doc, type);
    }

    @Override
    public List<DocumentLog> getTop100Logs(Document doc) {
        return documentLogRepository.getTop100DocumentLogsByDocument(doc);
    }

    @Override
    public void deleteLogsBeforeDays(int days) {
        documentLogRepository.deleteLogsBeforeDays(days);
        folderLogRepository.deleteLogsBeforeDays(days);
    }

    @Override
    public Page getDocumentLog(int pageNumber, int pageSize, int type, String username, Date startDate, Date endDate) {
        return documentLogRepository.getDocumentLog(pageNumber, pageSize, type, username, startDate, endDate);
    }

    @Override
    public DocumentVersion getNewestDocumentVersion(Document doc) {
        return documentVersionRepository.getNewestDocumentVersion(doc);
    }

    @Override
    public void updateVersion(DocumentVersion version) {
        documentVersionRepository.update(version);
    }

    @Override
    public void updateDocument(Document doc) {
        updateDocument(doc, false);
    }

    @Override
    public void updateDocument(Document doc, boolean updateModifiedDate) {
        PropertyUtils.updateProperties(doc);

        if (updateModifiedDate) {
            doc.setModifiedDate(new Date());
        }
        documentRepository.update(doc);

        if (!doc.isAttachDoc()) {
            ESQueueUtils.createDocument(doc);
        }
    }

    @Override
    public void incrementDocumentClickCount(Document doc) {
        documentRepository.incrementDocumentClickCount(doc);
    }

    @Override
    public void resetEditingDocument(String username, String fullname) {
        try {
            // 保存版本0，并删除
            List<Document> documentsInEditing = documentRepository.getDocumentInEditing(username);
            for (Document d : documentsInEditing) {
                if (getVersion(d, 0) != null) {
                    log.debug("恢复自动保存版本");
                    try {
                        rollbackVersion(d, username, fullname, 0, "恢复自动保存版本");
                    } catch (IOException e) {
                        log.error("error in rollback", e);
                    }

                    deleteVersion(d, 0);
                }
            }

            documentRepository.resetEditingDocument(username);
            log.debug("Edit tag of {}'s documents is reseted", username);
        } catch (Throwable t) {
            log.error("resetEditingDocument ex", t);
        }
    }

    @Override
    public void resetAllEditingDocument() {
        try {
            documentRepository.resetAllEditingDocument();
            log.debug("All Edit tag documents is reseted");
        } catch (Throwable t) {
            log.error("resetAllEditingDocument ex", t);
        }
    }

    @Override
    public List<Document> getDocumentLogByUser(String username, int logType, Date afterDate) {
        return documentRepository.getDocumentLogByUser(username, logType, afterDate);
    }

    @Override
    public List<Document> getUploadDocumentLogByUser(String username, Date afterDate) {
        return documentRepository.getUploadDocumentLogByUser(username, afterDate);
    }

    @Override
    public void copyDocProps(User operator, String oriSn, Document targetDoc) {
        Document oriDoc = getDocumentBySerial(oriSn);
        if (oriDoc == null || targetDoc == null) {
            return;
        }

        if (targetDoc.isLocked()) {
            return;
        }

        if (targetDoc.isEditing()) {
            return;
        }

        if (FolderPermissionUtils.checkPermission(operator, oriDoc.getFolder(), FolderPermission.READ)) {

            targetDoc.setDocProps(oriDoc.getDocProps());
            updateDocument(targetDoc, false);
        }
    }
}
