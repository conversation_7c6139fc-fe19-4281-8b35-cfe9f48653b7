package zd.dms.controllers.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.connection.DataType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapUtils;
import zd.base.utils.redis.ZDRedisUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "RedisController", description = "缓存Controller")
@RequestMapping("/admin/redis")
@Slf4j
public class RedisController extends ControllerSupport {

    @Operation(summary = "获取缓存列表")
    @PostMapping("/list")
    @ZDLog("获取缓存列表")
    public JSONResultUtils<Object> list(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> results = new ArrayList<>();

        List<String> keys = ZDRedisUtils.scan("*");
        for (String key : keys) {
            DataType keyType = ZDRedisUtils.getKeyType(key);

            String code = keyType.code();
            if ("string".equals(code)) {
                Object o = null;
                try {
                    o = ZDRedisUtils.get(key);
                } catch (Exception e) {
                    log.debug("error", e);
                }

                Map<String, Object> map = new HashMap<>();
                map.put("id", key);
                map.put("key", key);
                map.put("value", o);
                map.put("type", code);

                map.put("expire", ZDRedisUtils.getExpire(key));
                results.add(map);
            } else if ("hash".equals(code)) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", key);
                map.put("key", key);
                map.put("type", code);
                map.put("value", "");

                map.put("hasChildren", true);

                map.put("expire", ZDRedisUtils.getExpire(key));
                results.add(map);
            } else if ("list".equals(code)) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", key);
                map.put("key", key);
                map.put("type", code);
                map.put("value", "");

                map.put("hasChildren", true);

                map.put("expire", ZDRedisUtils.getExpire(key));
                results.add(map);
            } else if ("zset".equals(code)) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", key);
                map.put("key", key);
                map.put("type", code);
                map.put("value", "");

                map.put("hasChildren", true);

                map.put("expire", ZDRedisUtils.getExpire(key));
                results.add(map);
            }
        }

        return successData(results);
    }

    @Operation(summary = "获取子缓存列表")
    @PostMapping("/loadChildKeys")
    @ZDLog("获取子缓存列表")
    public JSONResultUtils<Object> loadChildKeys(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> results = new ArrayList<>();

        String key = MapUtils.getString(params, "key", "");
        String type = MapUtils.getString(params, "type", "");
        if (StringUtils.isBlank(type) || StringUtils.isBlank(key)) {
            return successData(results);
        }

        if ("hash".equals(type)) {
            long expire = ZDRedisUtils.getExpire(key);
            Map<String, Object> hmget = ZDRedisUtils.hmget(key);
            if (MapUtils.isNotEmpty(hmget)) {
                for (Map.Entry<String, Object> entry : hmget.entrySet()) {
                    Map<String, Object> map = new HashMap<>();
                    String tempKey = entry.getKey();
                    map.put("id", key + "---" + tempKey);
                    map.put("key", tempKey);
                    map.put("type", "");
                    map.put("value", entry.getValue());

                    map.put("expire", expire);
                    results.add(map);
                }
            }
        } else if ("list".equals(type)) {
            long expire = ZDRedisUtils.getExpire(key);
            List<Object> lrange = ZDRedisUtils.lrange(key, 0, 99);
            if (CollectionUtils.isNotEmpty(lrange)) {
                for (Object obj : lrange) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", "");
                    map.put("key", "");
                    map.put("type", "");
                    map.put("value", obj);
                    map.put("isUnDeletable", true);

                    map.put("expire", expire);
                    results.add(map);
                }
            }
        } else if ("zset".equals(type)) {
            long expire = ZDRedisUtils.getExpire(key);
            Map<String, Double> value = ZDRedisUtils.zreverseRangeWithScores(key, 0, 99);
            if (MapUtils.isNotEmpty(value)) {
                for (Map.Entry<String, Double> entry : value.entrySet()) {
                    Map<String, Object> map = new HashMap<>();
                    String tempKey = entry.getKey();
                    map.put("id", key + "---" + tempKey);
                    map.put("key", tempKey);
                    map.put("type", "");
                    map.put("value", entry.getValue());
                    map.put("isUnDeletable", true);

                    map.put("expire", expire);
                    results.add(map);
                }
            }
        }

        return successData(results);
    }

    @Operation(summary = "删除缓存")
    @PostMapping("/deleteCaches")
    @ZDLog("删除缓存")
    public JSONResultUtils<Object> deleteCaches(@RequestBody Map<String, Object> params) {
        List<String> ids = ZDMapUtils.getListStringValue(params, "ids");

        List<String> keys = new ArrayList<>();
        Map<String, List<String>> hashKeys = new HashMap<>();
        for (String id : ids) {
            if (id.contains("---")) {
                int index = id.lastIndexOf("---");
                if (index <= 0) {
                    continue;
                }

                String key = id.substring(0, index);
                String item = id.substring(index + 3);

                List<String> items = hashKeys.computeIfAbsent(key, k -> new ArrayList<>());
                items.add(item);
            } else {
                keys.add(id);
            }
        }

        ZDRedisUtils.delete(keys.toArray(new String[]{}));

        if (MapUtils.isNotEmpty(hashKeys)) {
            for (Map.Entry<String, List<String>> entry : hashKeys.entrySet()) {
                String key = entry.getKey();
                List<String> items = entry.getValue();

                if (StringUtils.isBlank(key) || CollectionUtils.isEmpty(items)) {
                    continue;
                }
                ZDRedisUtils.hdel(key, items.toArray(new String[]{}));
            }
        }


        return success();
    }
}
