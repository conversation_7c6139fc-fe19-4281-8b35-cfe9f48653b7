package zd.dms.repositories.docsig.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.DocumentSig;
import zd.dms.repositories.docsig.DocumentSigRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;

public class DocumentSigRepositoryDaoImpl extends BaseRepositoryDaoImpl<DocumentSig, String> implements DocumentSigRepository {

    public DocumentSigRepositoryDaoImpl(Class<DocumentSig> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<DocumentSig> getSigsByDocId(String docId) {
        Specification<DocumentSig> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentSig> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("docId", docId);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void deleteSigsByDocId(String docId) {
        String hql = "delete from DocumentSig where docId = ?1";
        executeUpdate(hql, docId);
    }
}
