package zd.base.tasks.zdmq;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.analysus.ja.dict.TaskDictionary;
import org.redisson.api.RLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zd.base.tasks.AbstractTask;
import zd.base.utils.es.ESQueueUtils;
import zd.base.utils.es.ESUtils;
import zd.base.utils.hlp.ZDHlpUtils;
import zd.base.utils.redis.ZDRedisLockUtils;
import zd.base.utils.system.PropsUtils;
import zd.base.utils.system.SpringUtils;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentVersion;
import zd.dms.entities.User;
import zd.dms.services.document.DocumentService;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.services.user.UserService;
import zd.dms.utils.JSONUtils;
import zd.dms.utils.OfficeUtils;
import zd.dms.utils.ZDFileUtils;
import zd.dms.utils.abbyy.AbbyyOcrUtils;
import zd.dms.utils.ocr.OcrUtils;
import zd.dms.utils.zdmq.ZDMQConfigUtils;
import zd.dms.utils.zdmq.ZDMQDBUtils;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * mq任务
 */
@RequiredArgsConstructor
@Component
@Slf4j
public class DocExtractTask extends AbstractTask {

    @Scheduled(cron = "20 */1 * * * ?")
    @Scheduled(initialDelay = 60000)
    @Override
    public void work() {
        String batchNo = ZDMQConfigUtils.getBatchNo();
        int batchSize = 1;
        while (true) {
            RLock lock = ZDRedisLockUtils.getLock("ZDMQDocExtractTask");
            boolean lockResult = ZDRedisLockUtils.tryLock(lock, 10 * 60);
            if (!lockResult) {
                break;
            }

            try {
                List<Map<String, Object>> nextMsgs = ZDMQDBUtils.getNextMsgs(ZDMQConfigUtils.TYPE_DOC_EXTRACT, batchNo, batchSize);
                if (CollectionUtils.isEmpty(nextMsgs)) {
                    break;
                }

                nextMsgs.forEach(item -> {
                    String id = MapUtils.getString(item, "ID", "");
                    String msg = MapUtils.getString(item, "MSG", "");
                    if (StringUtils.isBlank(msg)) {
                        ZDMQDBUtils.removeMsg(id);
                        return;
                    }

                    Map<String, Object> msgMap = JSONUtils.parseObject(msg, Map.class);
                    doExtractText(msgMap);
                    ZDMQDBUtils.removeMsg(id);
                });

                if (nextMsgs.size() < batchSize) {
                    break;
                }
            } catch (Exception e) {
                log.debug("error", e);
            } finally {
                ZDRedisLockUtils.unlock(lock);
            }
        }
    }

    private void doExtractText(Map<String, Object> msgMap) {
        try {
            String extractedText = "";

            String docId = MapUtils.getString(msgMap, "docId", "");
            if (StringUtils.isBlank(docId)) {
                return;
            }

            DocumentService documentService = SpringUtils.getBean(DocumentService.class);
            Document document = documentService.getDocumentById(docId);
            if (document == null) {
                return;
            }

            String docFilename = document.getFilename();
            log.debug("doExtractText: {}", docFilename);
            boolean extractable = OfficeUtils.isExtractable(docFilename);
            boolean needOcr = OcrUtils.isNeedOcr(docFilename);

            if (!extractable && !needOcr) {
                return;
            }

            String extension = document.getExtension();

            int versionNumber = MapUtils.getIntValue(msgMap, "version", -1);
            if (versionNumber <= -1) {
                versionNumber = document.getNewestVersion();
            }
            File tempFile = ZDFileUtils.getTempFile(document, null);
            // 提取文档摘要
            if (extractable && !AbbyyOcrUtils.isMustAbbyyOcr(docFilename)) {
                extractedText = OfficeUtils.extractText(tempFile, extension, document.getEncoding());
            }

            UserService userService = SpringUtils.getBean(UserService.class);
            String username = MapUtils.getString(msgMap, "username", UserGroupUtils.ADMIN_USERNAME);
            User user = UserGroupUtils.getUserOrAdminUser(username);

            // 当开启ocr模块、文档摘要为空且后缀符合时，进行abbyyocr解析
            if (TaskDictionary.getMainDefinition().hm("ocrEngine") && StringUtils.isBlank(extractedText) &&
                    OcrUtils.isNeedOcr(docFilename)) {
                String ocrServerAddress = PropsUtils.getProps("ocrServerAddress");
                OcrUtils.ocrDocByAbbyyOrTesseract(ocrServerAddress, docId, docFilename, tempFile, versionNumber, user,
                        true);
            }

            log.debug("doExtractText filename: {} extracted", docFilename);

            if (StringUtils.isNotBlank(extractedText)) {
                // 更新文档对象
                document.setContent(extractedText);
                documentService.updateDocument(document);
                log.debug("ExtractTextThread filename: {} success", docFilename);

                // 更新版本对象
                DocumentVersion newestDV = documentService.getVersion(document, versionNumber);
                if (newestDV == null) {
                    log.debug("ExtractTextThread filename: {} version: {} not exists, return", docFilename, versionNumber);
                    return;
                }
                newestDV.setContent(extractedText);
                documentService.updateVersion(newestDV);
                log.debug("ExtractTextThread filename: {} version success", docFilename);
            }

            long attachRecId = document.getAttachRecId();
            String attachRecTableName = document.getAttachRecTableName();
            if (attachRecId > 0 && StringUtils.isNotBlank(attachRecTableName)) {
                boolean hlp = true;
                if (StringUtils.isNotBlank(extractedText)) {
                    hlp = ZDHlpUtils.updateRecordAndEs(extractedText, attachRecId, attachRecTableName);
                }

                boolean result = ESUtils.updateRecDocContent(attachRecId, attachRecTableName, extractedText);
                ESQueueUtils.addQueue(result && hlp, ESUtils.getIndexId(attachRecTableName, attachRecId), ESQueueUtils.TYPE_REC_INSERT);
            } else {
                boolean result = ESUtils.updateDocContent(document, extractedText);
                ESQueueUtils.addQueue(result, ESUtils.getIndexId(document), ESQueueUtils.TYPE_DOC_INSERT);
            }
        } catch (Exception e) {

        }
    }
}
