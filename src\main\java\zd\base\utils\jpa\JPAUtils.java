package zd.base.utils.jpa;

import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import zd.record.utils.RecordDBUtils;

@Slf4j
public class JPAUtils {

    public static void createSequence() {
        if (RecordDBUtils.isOracle()) {
            try {
                String sql = "CREATE SEQUENCE  HIBERNATE_SEQUENCE MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 START WITH 1 CACHE 20 NOORDER  NOCYCLE";
                JdbcTemplate jt = RecordDBUtils.checkJdbcTemplate();
                jt.execute(sql);
            } catch (Exception e) {
            }
        }
    }
}
