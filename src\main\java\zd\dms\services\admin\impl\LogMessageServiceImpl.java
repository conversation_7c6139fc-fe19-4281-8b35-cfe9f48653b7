package zd.dms.services.admin.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.dms.entities.LogMessage;
import zd.dms.repositories.admin.LogMessageRepository;
import zd.dms.services.admin.LogMessageService;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class LogMessageServiceImpl extends BaseJpaServiceImpl<LogMessage, Long> implements LogMessageService {

    private final LogMessageRepository logMessageRepository;

    public LogMessageServiceImpl(LogMessageRepository logMessageRepository) {
        this.logMessageRepository = logMessageRepository;
    }

    @Override
    public BaseRepository<LogMessage, Long> getBaseRepository() {
        return logMessageRepository;
    }


    @Override
    public LogMessage getLogMessageById(Long id) {
        return logMessageRepository.get(id);
    }

    @Override
    public List<LogMessage> getAllLogMessage() {
        return logMessageRepository.getAll();
    }

    @Override
    public void saveLogMessage(LogMessage logMessage) {
         logMessageRepository.save(logMessage);
    }

    @Override
    public void deleteLogMessage(LogMessage logMessage) {
        logMessageRepository.delete(logMessage);
    }

    @Override
    public void updateLogMessage(LogMessage logMessage) {
        logMessageRepository.update(logMessage);
    }

    @Override
    public List<LogMessage> getLogMessageByOperator(String operator) {
        return logMessageRepository.getLogMessageByOperator(operator);
    }

    @Override
    public List<LogMessage> getLogMessageByLevel(int level) {
        return logMessageRepository.getLogMessageByLevel(level);
    }

    @Override
    public List<LogMessage> getLogsByDate(Date startDate, Date endDate) {
        return logMessageRepository.getLogsByDate(startDate,endDate);
    }

    @Override
    public void clearLogMessage() {

    }
}
