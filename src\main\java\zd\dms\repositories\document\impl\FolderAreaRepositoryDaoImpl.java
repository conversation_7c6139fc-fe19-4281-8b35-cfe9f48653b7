//                    佛祖保佑       永无BUG
//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                           O\  =  /O
//                        ____/`---'\____
//                      .'  \\|     |//  `.
//                     /  \\|||  :  |||//  \
//                    /  _||||| -:- |||||-  \
//                    |   | \\\  -  /// |   |
//                    | \_|  ''\---/''  |   |
//                    \  .-\__  `-`  ___/-. /
//                  ___`. .'  /--.--\  `. . __
//               ."" '<  `.___\_<|>_/___.'  >'"".
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |
//              \  \ `-.   \_ __\ /__ _/   .-` /  /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
package zd.dms.repositories.document.impl;

import jakarta.persistence.EntityManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.FolderArea;
import zd.dms.repositories.document.FolderAreaRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;

public class FolderAreaRepositoryDaoImpl extends BaseRepositoryDaoImpl<FolderArea, Long> implements FolderAreaRepository {

    public FolderAreaRepositoryDaoImpl(Class<FolderArea> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<FolderArea> getDefaultAreas() {
        Specification<FolderArea> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderArea> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("defaultArea", true);
            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<FolderArea> getFolderAreasByType(String areaType, Boolean enabled, boolean asc) {
        Specification<FolderArea> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderArea> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            if (StringUtils.isNotBlank(areaType)) {
                specTools.eq("areaType", areaType);
            }

            if (enabled != null) {
                specTools.eq("enabled", enabled);
            }

            if (asc) {
                specTools.asc("numIndex");
            } else {
                specTools.desc("numIndex");
            }

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<FolderArea> getFolderAreasByTypeAndIsEnabledAndIsoArea(String areaType, boolean enabled, boolean isoArea) {
        Specification<FolderArea> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderArea> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            if (StringUtils.isNotBlank(areaType)) {
                specTools.eq("areaType", areaType);
            }

            specTools.eq("enabled", enabled);

            specTools.eq("isoArea", isoArea);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public FolderArea getFolderAreaByTreeName(String treeName) {
        Specification<FolderArea> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderArea> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("treeName", treeName);

            return specTools.getRestriction();
        };

        return findSingleObject(spec);
    }

    @Override
    public Page getAllFolderAreaPage(int pageNumber, int pageSize) {
        Specification<FolderArea> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderArea> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.asc("creationDate");

            return specTools.getRestriction();
        };
        return findAll(spec,pageNumber,pageSize);
    }
}
