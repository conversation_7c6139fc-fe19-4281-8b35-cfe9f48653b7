package zd.dms.services.log;

import org.springframework.transaction.annotation.Transactional;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.Date;

@Transactional
public interface LogExportService {

	InputStream getThreeAdminLogExcel(Date startDate, Date endDate, int type, int targetType, String username)
			throws FileNotFoundException;

	InputStream getLogExcel(Date startDate, Date endDate) throws FileNotFoundException;

	InputStream getDocumentLogExcel(Date startDate, Date endDate, int type, String username)
			throws FileNotFoundException;

	InputStream getFolderLogExcel(Date startDate, Date endDate, int type, String username) throws FileNotFoundException;

	InputStream getRecLogExcel(Date startDate, Date endDate, int type, String username, String from)
			throws FileNotFoundException;

	InputStream getRecFolderLogExcel(Date startDate, Date endDate, int type, String username)
			throws FileNotFoundException;
}
