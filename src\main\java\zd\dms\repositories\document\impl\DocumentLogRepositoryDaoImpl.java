package zd.dms.repositories.document.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentLog;
import zd.dms.entities.User;
import zd.dms.repositories.document.DocumentLogRepository;
import zd.dms.utils.TimeLineUtils;
import zd.dms.utils.repositories.PredicateUtils;
import zd.dms.utils.repositories.SpecTools;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class DocumentLogRepositoryDaoImpl extends BaseRepositoryDaoImpl<DocumentLog, Long> implements DocumentLogRepository {

    public DocumentLogRepositoryDaoImpl(Class<DocumentLog> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<DocumentLog> getDocumentLogByDocument(Document doc, int type) {
        Specification<DocumentLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("docId", doc.getId());

            if (type > 0) {
                specTools.eq("operateType", type);
            }

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<DocumentLog> getTop100DocumentLogsByDocument(Document doc) {
        Specification<DocumentLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("docId", doc.getId());
            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void deleteAllLogsByDocument(Document doc) {
        String hql = "delete from DocumentLog where docId = ?1";
        executeUpdate(hql, doc.getId());
    }

    @Override
    public void deleteLogsBeforeDays(int days) {
        String hql = "delete from DocumentLog where creationDate < ?1";

        Date d = DateUtils.addDays(new Date(), -days);
        executeUpdate(hql, d);
    }

    @Override
    public Page getDocumentLog(int pageNumber, int pageSize, int type, String username, Date startDate, Date endDate) {
        Specification<DocumentLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.desc("id");

            if (startDate != null) {
                specTools.ge("creationDate", startDate);
            }
            if (endDate != null) {
                specTools.le("creationDate", endDate);
            }

            if (type > 0) {
                specTools.eq("operateType", type);
            }

            if (StringUtils.isNotBlank(username)) {
                specTools.eq("operator", username);
            }

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }

    @Override
    public List<DocumentLog> getLogsByDate(Date startDate, Date endDate, int type, String username) {
        Specification<DocumentLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.desc("id");

            if (startDate != null) {
                specTools.ge("creationDate", startDate);
            }
            if (endDate != null) {
                specTools.le("creationDate", endDate);
            }

            if (type > 0) {
                specTools.eq("operateType", type);
            }

            if (StringUtils.isNotBlank(username)) {
                specTools.eq("operator", username);
            }

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public int getCount() {
        return NumberUtils.toInt(String.valueOf(findObject("select count(*) from DocumentLog")));

    }

    @Override
    public int getUserDocLogCountByTypeAndDate(User user, int type, Date startDate, Date endDate) {
        if (user == null) {
            return 0;
        }

        Specification<DocumentLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("operateType", type);
            specTools.eq("creator", user.getUsername());

            if (startDate != null) {
                specTools.ge("creationDate", startDate);
            }

            if (endDate != null) {
                specTools.le("creationDate", endDate);
            }

            return specTools.getRestriction();
        };

        return (int) count(spec);
    }

    @Override
    public Page getDocumentTimeline(int pageNumber, int pageSize, Document doc, User user) {
        Specification<DocumentLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            List<String> documentTimelineQueryTypes = null;
            String documentTimelineQueryTypesStr = user.getProperty("documentTimelineQueryTypes");
            if (StringUtils.isBlank(documentTimelineQueryTypesStr)) {
                documentTimelineQueryTypes = TimeLineUtils.getDefaultDocumentTimelineQueryTypeList();
            } else {
                documentTimelineQueryTypes = Arrays.asList(documentTimelineQueryTypesStr.split(","));
            }

            List<Predicate> predicates = new ArrayList<>();
            for (String operateType : documentTimelineQueryTypes) {
                predicates.add(PredicateUtils.equal(root, criteriaBuilder, "operateType", NumberUtils.toInt(operateType)));
            }

            specTools.or(predicates);
            specTools.eq("docId", doc.getId());
            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }
}
