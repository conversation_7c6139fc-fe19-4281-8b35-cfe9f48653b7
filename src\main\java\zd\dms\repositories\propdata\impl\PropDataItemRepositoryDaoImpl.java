package zd.dms.repositories.propdata.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.PropDataItem;
import zd.dms.repositories.propdata.PropDataItemRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;

public class PropDataItemRepositoryDaoImpl extends BaseRepositoryDaoImpl<PropDataItem, Long> implements PropDataItemRepository {

    public PropDataItemRepositoryDaoImpl(Class<PropDataItem> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public Page getItems(int pageNumber, int pageSize) {
        Specification<PropDataItem> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<PropDataItem> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }

    @Override
    public PropDataItem getItemByName(String name) {
        Specification<PropDataItem> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<PropDataItem> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("keyName", name);

            return specTools.getRestriction();
        };

        return findSingleObject(spec);
    }

    @Override
    public List<PropDataItem> getAllItems() {
        Specification<PropDataItem> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<PropDataItem> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }
}
