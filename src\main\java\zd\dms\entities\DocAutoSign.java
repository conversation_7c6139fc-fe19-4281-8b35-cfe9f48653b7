package zd.dms.entities;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.entities.AbstractEntity;
import zd.dms.utils.TextUtils;

/**
 * 文档签章
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "docautosign")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class DocAutoSign extends AbstractEntity {

	private static final long serialVersionUID = 2864197979198085395L;

	private static final Logger log = LoggerFactory.getLogger(DocAutoSign.class);

	@Index(name = "i_docatuosign_minw")
	private int minWidth;

	@Index(name = "i_docatuosign_maxw")
	private int maxWidth;

	@Index(name = "i_docatuosign_minh")
	private int minHeight;

	@Index(name = "i_docatuosign_minh")
	private int maxHeight;

	private String docFilenames;

	private String taskNodenames;

	private int addType;

	private String pageString;

	private float pellucidity;

	private float xPos;

	private float yPos;

	private int numberIndex;

	private int priorityLevel;

	public int getMinWidth() {
		return minWidth;
	}

	public void setMinWidth(int minWidth) {
		this.minWidth = minWidth;
	}

	public int getMaxWidth() {
		return maxWidth;
	}

	public void setMaxWidth(int maxWidth) {
		this.maxWidth = maxWidth;
	}

	public int getMinHeight() {
		return minHeight;
	}

	public void setMinHeight(int minHeight) {
		this.minHeight = minHeight;
	}

	public int getMaxHeight() {
		return maxHeight;
	}

	public void setMaxHeight(int maxHeight) {
		this.maxHeight = maxHeight;
	}

	public String getDocFilenames() {
		return TextUtils.escapeXml(docFilenames);
	}

	public void setDocFilenames(String docFilenames) {
		this.docFilenames = docFilenames;
	}

	public String getTaskNodenames() {
		return TextUtils.escapeXml(taskNodenames);
	}

	public void setTaskNodenames(String taskNodenames) {
		this.taskNodenames = taskNodenames;
	}

	public int getAddType() {
		return addType;
	}

	public void setAddType(int addType) {
		this.addType = addType;
	}

	public String getPageString() {
		return TextUtils.escapeXml(pageString);
	}

	public void setPageString(String pageString) {
		this.pageString = pageString;
	}

	public float getPellucidity() {
		return pellucidity;
	}

	public void setPellucidity(float pellucidity) {
		this.pellucidity = pellucidity;
	}

	public float getxPos() {
		return xPos;
	}

	public void setxPos(float xPos) {
		this.xPos = xPos;
	}

	public float getyPos() {
		return yPos;
	}

	public void setyPos(float yPos) {
		this.yPos = yPos;
	}

	public int getNumberIndex() {
		return numberIndex;
	}

	public void setNumberIndex(int numberIndex) {
		this.numberIndex = numberIndex;
	}

	public int getPriorityLevel() {
		return priorityLevel;
	}

	public void setPriorityLevel(int priorityLevel) {
		this.priorityLevel = priorityLevel;
	}
}
