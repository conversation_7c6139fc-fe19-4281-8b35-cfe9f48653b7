package zd.dms.services.document.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.dms.entities.*;
import zd.dms.repositories.document.DocumentLinkRepository;
import zd.dms.repositories.document.DocumentRelationRepository;
import zd.dms.services.document.DocumentLinkService;
import zd.dms.services.document.DocumentService;
import zd.dms.services.document.FolderAreaService;
import zd.dms.services.document.FolderService;
import zd.record.utils.APIUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RequiredArgsConstructor
@Service
@Slf4j
public class DocumentLinkServiceImpl extends BaseJpaServiceImpl<DocumentLink, Long> implements DocumentLinkService {

    private final DocumentLinkRepository documentLinkRepository;

    @Lazy
    @Autowired
    private DocumentService documentService;

    @Lazy
    @Autowired
    private FolderService folderService;

    private final FolderAreaService folderAreaService;

    private final DocumentRelationRepository documentRelationRepository;

    @Override
    public BaseRepository<DocumentLink, Long> getBaseRepository() {
        return documentLinkRepository;
    }

    @Override
    public void delete(User user) {
        documentLinkRepository.deleteDocumentLink(user);
    }

    @Override
    public void delete(Folder folder) {
        documentLinkRepository.deleteDocumentLink(folder);
    }

    @Override
    public void delete(Document document) {
        documentLinkRepository.deleteDocumentLink(document);
    }

    @Override
    public void deleteById(long id) {
        documentLinkRepository.deleteLinkById(id);
    }

    @Override
    public DocumentLink getDocumentLinkById(long id) {
        return documentLinkRepository.get(id);
    }

    @Override
    public List<DocumentLink> getDocumentLinks(int type, Folder folder) {
        return documentLinkRepository.getDocumentLinks(type, folder);
    }

    @Override
    public List<DocumentLink> getDocumentLinks(int type, Document document) {
        return documentLinkRepository.getDocumentLinks(type, document);
    }

    @Override
    public List<DocumentLink> getDocumentLinks(int type, User user) {
        return documentLinkRepository.getDocumentLinks(type, user);
    }

    @Override
    public void publishToMyCommon(Document doc, User user) {
        DocumentLink link = new DocumentLink();
        link.setDocId(doc.getId());
        link.setUsername(user.getUsername());
        link.setLinkType(DocumentLink.TYPE_MY_COMMON);

        documentLinkRepository.save(link);
    }

    @Override
    public void publishToFolder(Document doc, Folder folder, User publisher) {
        DocumentLink link = new DocumentLink();
        link.setDocId(doc.getId());
        link.setFolderId(folder.getId());
        link.setLinkType(DocumentLink.TYPE_FOLDER_LINK);
        link.setCreatorFullname(publisher.getFullname());

        documentLinkRepository.save(link);
    }

    @Override
    public void removeLinkFromMyCommon(User user, Document doc) {
        List<DocumentLink> links = getDocumentLinks(DocumentLink.TYPE_MY_COMMON, user);

        // 删除链接
        for (DocumentLink l : links) {
            if (l.getDocId().equals(doc.getId())) {
                delete(l);
            }
        }
    }

    @Override
    public void removeLinkFromFolder(Folder folder, Document doc) {
        List<DocumentLink> links = getDocumentLinks(DocumentLink.TYPE_FOLDER_LINK, folder);

        // 删除链接
        for (DocumentLink l : links) {
            if (l.getDocId().equals(doc.getId())) {
                delete(l);
            }
        }
    }

    @Override
    public List<DocumentLink> searchDocumentLinks(int typeMyCommon, String username, String fileNames, String advUpdateStartDate, String advUpdateEndDate) {
        return documentLinkRepository.searchDocumentLinks(typeMyCommon, username, fileNames, advUpdateStartDate,
                advUpdateEndDate);
    }

    @Override
    public void linkDocumentRule(Document document, User user) {
        log.debug("linkDocumentRule");

        if (document == null) {
            return;
        }

        Folder currentFolder = document.getFolder();
        if (currentFolder == null) {
            return;
        }

        String content = document.getContent();
        if (StringUtils.isBlank(content)) {
            return;
        }

        String linkFolders = currentFolder.getProperty("linkFolders");
        if (StringUtils.isBlank(linkFolders)) {
            return;
        }

        String linkRegex = StringUtils.isBlank(currentFolder.getProperty("linkRegex")) ? "《(.*?)》" : currentFolder
                .getProperty("linkRegex");

        log.debug("linkDocumentRule linkFolders : {}, linkRegex : {}", linkFolders, linkRegex);

        Set<Folder> allfolder = new HashSet<Folder>();
        Set<Folder> allFolderSet = null;

        List<String> docNameList = getDocNameList(content, linkRegex);

        List<DocumentRelation> list = documentRelationRepository.getDocumentRelations(document);
        for (DocumentRelation documentRelation : list) {
            Iterator<String> iterable = docNameList.iterator();
            while (iterable.hasNext()) {
                String docName = iterable.next();

                String documentName = documentRelation.getDocument().getFilename() // 关联的原文件名
                        .substring(0, documentRelation.getDocument().getFilename().lastIndexOf("."));
                String documentRelatedName = documentRelation.getDocumentRelated().getFilename() // 关联文件名
                        .substring(0, documentRelation.getDocumentRelated().getFilename().lastIndexOf("."));

                // 此关联已存在,则把docName从集合中除去
                if (StringUtils.equals(documentName, docName) || StringUtils.equals(documentRelatedName, docName)) {
                    iterable.remove();
                }
            }
        }

        log.debug("linkDocumentRule docNameList : {}", docNameList);

        if (docNameList == null || docNameList.size() == 0) {
            return;
        }

        String[] ruleArr = StringUtils.split(linkFolders, "||");
        for (String row : ruleArr) {
            String[] folderArr = row.split("//");
            String ruleStr = folderArr[0].trim();
            if ("所有目录".equals(ruleStr)) {
                allFolderSet = new HashSet<Folder>();
                // 匹配所有目录

                List<Folder> pubTopFolders = folderService.getPubTopFoldersByTreeName(null);
                for (Folder folder : pubTopFolders) {
                    this.getChildrenFolder(folder, allFolderSet, true);
                }

                List<FolderArea> folderAreas = folderAreaService.getFolderAreasByTypeAndIsEnabledAndIsoArea("doc",
                        true, false);
                for (FolderArea folderArea : folderAreas) {
                    List<Folder> fList = folderService.getPubTopFoldersByTreeName(folderArea.getTreeName());
                    for (Folder folder : fList) {
                        this.getChildrenFolder(folder, allFolderSet, true);
                    }
                }

                break;
            }

            if ("本目录".equals(ruleStr)) {
                // 匹配本目录
                allfolder.add(currentFolder);
                continue;
            }

            if ("本目录及子目录".equals(ruleStr)) {
                // 匹配本目录及子目录
                this.getChildrenFolder(currentFolder, allfolder, false);
                continue;
            }

            if ("本目录及所有子目录".equals(ruleStr)) {
                // 匹配本目录及所有子目录
                this.getChildrenFolder(currentFolder, allfolder, true);
                continue;
            }

            if ("父目录".equals(ruleStr)) {
                // 匹配父目录
                Folder parentFolder = currentFolder.getParent();
                if (parentFolder == null) {
                    continue;
                }

                allfolder.add(parentFolder);
                continue;
            }

            if ("父目录及子目录".equals(ruleStr)) {
                // 匹配本目录
                Folder parentFolder = currentFolder.getParent();
                if (parentFolder == null) {
                    continue;
                }

                this.getChildrenFolder(parentFolder, allfolder, false);
                continue;
            }

            if ("父目录及所有子目录".equals(ruleStr)) {
                // 匹配本目录
                Folder parentFolder = currentFolder.getParent();
                if (parentFolder == null) {
                    continue;
                }

                this.getChildrenFolder(parentFolder, allfolder, true);
                continue;
            }

            String[] str = ruleStr.split(":");
            if (str.length != 2) {
                continue;
            }

            if ("指定目录".equals(str[0].trim())) {
                Folder f = APIUtils.getOrCreateFolder(user, str[1].trim(), folderService, false);
                if (f == null) {
                    continue;
                }

                allfolder.add(f);
                continue;
            }

            if ("指定目录及子目录".equals(str[0].trim())) {
                Folder f = APIUtils.getOrCreateFolder(user, str[1].trim(), folderService, false);
                if (f == null) {
                    continue;
                }

                this.getChildrenFolder(f, allfolder, false);
                continue;
            }

            if ("指定目录及所有子目录".equals(str[0].trim())) {
                Folder f = APIUtils.getOrCreateFolder(user, str[1].trim(), folderService, false);
                if (f == null) {
                    continue;
                }

                this.getChildrenFolder(f, allfolder, true);
                continue;
            }
        }

        if (allFolderSet != null && allFolderSet.size() > 0) {
            allfolder = allFolderSet;
        }

        if (allfolder != null && allfolder.size() > 0) {
            List<Document> documents = new ArrayList<Document>();
            for (Folder f : allfolder) {
                documents.addAll(documentService.getDocumentsByFolder(f));
            }

            for (String docName : docNameList) {
                for (Document doc : documents) {
                    if (StringUtils.equals(doc.getFilename().substring(0, doc.getFilename().lastIndexOf(".")), docName)) {
                        Document docToRelation = documentService.getDocumentById(doc.getId());
                        if (docToRelation != null) {
                            documentService.createDocumentRelation(document.getId(), docToRelation.getSerialNumber(),
                                    user, user.getLastIp());

                            log.debug("linkDocumentRule {} : link {}", document.getFilename(),
                                    docToRelation.getFilename());
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取文档中的文件名并放入集合中
     *
     * @param extractedText
     * @return
     */
    private static List<String> getDocNameList(String extractedText, String regex) {
        if (StringUtils.isBlank(extractedText)) {
            return null;
        }

        if (StringUtils.isBlank(regex)) {
            regex = "《(.*?)》";
        }

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(extractedText);
        List<String> docNameList = new ArrayList<String>();

        while (matcher.find()) {
            if (StringUtils.isNotBlank(matcher.group(1))) {
                docNameList.add(matcher.group(1));
            }
        }

        return docNameList;
    }

    /**
     * 获得本目录、子目录或者所有子目录
     *
     * @param folder
     * @param allChild
     * @param allSub
     * @return
     */
    private Set<Folder> getChildrenFolder(Folder folder, Set<Folder> allChild, boolean allSub) {
        // 先将当前目录放入集合
        allChild.add(folder);

        List<Folder> children = folder.getChildren();
        allChild.addAll(children);

        if (children != null && allSub) {
            for (Folder child : children) {
                getChildrenFolder(child, allChild, allSub);
            }
        }

        return allChild;
    }
}
