package zd.dms.ecm.test;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import zd.dms.utils.HttpUtils;
import zd.dms.utils.JSONUtils;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class OAuth2Test {

    public static void main(String[] args) {
        String token = getToken();
        if (token == null) {
            System.out.println("token is null");
            return;
        }

        getApi(token);

        postApi(token);
    }

    private static void getApi(String accessToken) {
        System.out.println("=========get start========");
        HashMap<String, Object> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + accessToken);
        String responseJson = HttpUtils.get("http://localhost:8081/api/v2/test/testGet/123?value=asda", headerMap);

        System.out.println(responseJson);
        System.out.println("=========get end========");
    }

    private static void postApi(String accessToken) {
        System.out.println("=========post start========");
        HashMap<String, Object> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + accessToken);

        Map<String, Object> params = new HashMap<>();
        params.put("value", "asda");

        String responseJson = HttpUtils.post("http://localhost:8081/api/v2/test/testPost", params, headerMap);

        System.out.println(responseJson);
        System.out.println("=========post end========");
    }

    private static String getToken() {
        HashMap<String, Object> headerMap = new HashMap<>();
        String clientId = "aaa";
        String clientSecret = "ccc";

        String encode = Base64.getEncoder().encodeToString((clientId + ":" + clientSecret).getBytes());
        headerMap.put("Authorization", "Basic " + encode);
        headerMap.put("Content-Type", "application/x-www-form-urlencoded");

        String responseJson = HttpUtils.post("http://localhost:8081/oauth2/token", null, headerMap);

        if (StringUtils.isBlank(responseJson)) {
            return "";
        }

        Map map = JSONUtils.parseObject(responseJson, Map.class, new HashMap<>());

        return MapUtils.getString(map, "access_token", "");
    }
}
