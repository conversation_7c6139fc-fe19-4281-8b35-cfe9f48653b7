package zd.dms.repositories.rule.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.FolderRuleCondition;
import zd.dms.repositories.rule.FolderRuleConditionRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;

public class FolderRuleConditionRepositoryDaoImpl extends BaseRepositoryDaoImpl<FolderRuleCondition, String> implements FolderRuleConditionRepository {

    public FolderRuleConditionRepositoryDaoImpl(Class<FolderRuleCondition> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<FolderRuleCondition> getConditionsByRule(String ruleId) {
        Specification<FolderRuleCondition> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderRuleCondition> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("ruleId", ruleId);
            specTools.asc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void deleteConditionsByRule(String ruleId) {
        String hql = "delete from FolderRuleCondition where ruleId = ?1";
        executeUpdate(hql, ruleId);
    }

    @Override
    public void deleteConditionsByFolder(long folderId) {
        String hql = "delete from FolderRuleCondition where folderId = ?1";
        executeUpdate(hql, folderId);
    }
}
