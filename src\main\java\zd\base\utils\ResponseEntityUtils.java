package zd.base.utils;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.File;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

public class ResponseEntityUtils {
    public static ResponseEntity<byte[]> nullResult() {
        ResponseEntity.BodyBuilder builder = ResponseEntity.status(411);
        return builder.body(new byte[]{});
    }

    public static ResponseEntity<byte[]> fileResult(String fileName, File file) throws Exception {
        ResponseEntity.BodyBuilder builder = ResponseEntity.ok();
        // 内容长度
        builder.contentLength(file.length());
        // application/octet-stream 二进制数据流（最常见的文件下载）
        builder.contentType(MediaType.APPLICATION_OCTET_STREAM);
        // 使用URLEncoding.decode对文件名进行解码
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        fileName = fileName.replace("+", "%20");
        builder.header("Content-Disposition", "attachment; filename=" + fileName);
        builder.header("Access-Control-Expose-Headers", "Content-Disposition");

        return builder.body(FileUtils.readFileToByteArray(file));
    }

    public static ResponseEntity<byte[]> fileResult(String fileName, InputStream in) throws Exception {
        ResponseEntity.BodyBuilder builder = ResponseEntity.ok();

        byte[] byteArray = IOUtils.toByteArray(in);

        // 内容长度
        builder.contentLength(byteArray.length);

        // application/octet-stream 二进制数据流（最常见的文件下载）
        builder.contentType(MediaType.APPLICATION_OCTET_STREAM);
        // 使用URLEncoding.decode对文件名进行解码
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        fileName = fileName.replace("+", "%20");
        // 根据浏览器类型，决定处理方式
        builder.header("Content-Disposition", "attachment; filename=" + fileName);
        builder.header("Access-Control-Expose-Headers", "Content-Disposition");

        return builder.body(byteArray);
    }
}
