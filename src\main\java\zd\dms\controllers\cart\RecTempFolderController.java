package zd.dms.controllers.cart;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapUtils;
import zd.base.utils.model.ObjectMapperUtils;
import zd.dms.entities.Document;
import zd.dms.entities.Folder;
import zd.dms.services.document.DocumentService;
import zd.dms.services.document.FolderService;
import zd.record.entities.RecFolder;
import zd.record.entities.RecTempFolder;
import zd.record.service.folder.RecFolderService;
import zd.record.service.record.RecTempFolderService;
import zd.record.utils.ECMUtils;
import zd.record.utils.RecordDBUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "RecTempFolderController", description = "借阅车Controller")
@RequestMapping("/cart")
public class RecTempFolderController extends ControllerSupport {

    private final RecTempFolderService recTempFolderService;

    private final RecFolderService recFolderService;

    private final DocumentService documentService;

    private final FolderService folderService;

    @Operation(summary = "借阅车列表")
    @PostMapping("/getCartList")
    @ZDLog("借阅车列表")
    public JSONResultUtils<Object> getCartList(@RequestBody Map<String, Object> params) {
        String type = MapUtils.getString(params, "type", "");
        List<RecTempFolder> recTempFolders = recTempFolderService.getRecTempFoldersByUsername(getCurrentUsername(), type);

        return successData(ObjectMapperUtils.toMapList(recTempFolders, "list"));
    }

    @Operation(summary = "清空借阅车")
    @GetMapping("/clearCart")
    @ZDLog("清空借阅车")
    public JSONResultUtils<Object> clearCart() {
        recTempFolderService.clearRecTempFolders(getCurrentUsername(), "");
        return success();
    }

    @Operation(summary = "发送档案至借阅车")
    @PostMapping("/addRecordsToRecTempFolder")
    @ZDLog("发送档案至借阅车")
    public JSONResultUtils<Object> addRecordsToRecTempFolder(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> selectRecords = ZDMapUtils.getListMapValue(params, "selectRecords");
        if (CollectionUtils.isEmpty(selectRecords)) {
            return JSONResultUtils.successWithData(new ArrayList<>());
        }

        Map<Long, String> recFolderIdAndTableName = new HashMap<>();
        Map<Long, String> recFolderIdAndDisplayFolder = new HashMap<>();

        selectRecords.forEach(item -> {
            long tempRecId = MapUtils.getLongValue(item, "recId", 0L);
            long tempRecFolderId = MapUtils.getLongValue(item, "recFolderId", 0L);
            String tempTableName = MapUtils.getString(item, "tableName", "");

            if (tempRecId <= 0) {
                return;
            }

            if (tempRecFolderId <= 0 && StringUtils.isBlank(tempTableName)) {
                return;
            }

            Map<String, Object> recordData = null;
            if (StringUtils.isNotBlank(tempTableName)) {
                recordData = RecordDBUtils.getRecordByTableName(tempRecId, null, tempTableName);
                if (MapUtils.isEmpty(recordData)) {
                    return;
                }

                if (tempRecFolderId <= 0) {
                    tempRecFolderId = MapUtils.getLongValue(recordData, "目录ID", 0L);
                    if (tempRecFolderId <= 0) {
                        return;
                    }
                }
            } else {
                if (StringUtils.isBlank(tempTableName)) {
                    tempTableName = recFolderIdAndTableName.get(tempRecFolderId);
                    if (StringUtils.isBlank(tempTableName)) {
                        RecFolder recFolder = recFolderService.getById(tempRecFolderId);
                        if (recFolder == null) {
                            return;
                        }

                        tempTableName = recFolder.getTableName();
                        recFolderIdAndTableName.put(tempRecFolderId, tempTableName);

                        if (recFolderIdAndDisplayFolder.containsKey(tempRecFolderId)) {
                            recFolderIdAndDisplayFolder.put(tempRecFolderId, recFolder.getDisplayFolder());
                        }
                    }

                    if (StringUtils.isBlank(tempTableName)) {
                        return;
                    }
                }

                recordData = RecordDBUtils.getRecordByTableName(tempRecId, null, tempTableName);
                if (MapUtils.isEmpty(recordData)) {
                    return;
                }
            }

            String dataId = ECMUtils.getStringFromRecordMap(recordData.get("ID"));
            String fileTitle = ECMUtils.getStringFromRecordMap(recordData.get("题名"));
            String fileNumber = ECMUtils.getStringFromRecordMap(recordData.get("档号"));
            String filesNumber = ECMUtils.getStringFromRecordMap(recordData.get("案卷号"));

            String areaDisplayFolder = recFolderIdAndDisplayFolder.get(tempRecFolderId);
            if (StringUtils.isBlank(areaDisplayFolder)) {
                RecFolder recFolder = recFolderService.getById(tempRecFolderId);
                if (recFolder == null) {
                    return;
                }

                areaDisplayFolder = recFolder.getAreaDisplayFolder();
                recFolderIdAndDisplayFolder.put(tempRecFolderId, areaDisplayFolder);

                if (!recFolderIdAndTableName.containsKey(tempRecFolderId)) {
                    recFolderIdAndTableName.put(tempRecFolderId, recFolder.getTableName());
                }
            }

            RecTempFolder recTempFolder = new RecTempFolder(areaDisplayFolder, fileTitle, dataId, fileNumber, filesNumber, tempRecFolderId,
                    tempTableName, RecTempFolder.TYPE_REC);

            recTempFolderService.addRecTempFolder(recTempFolder, RecTempFolder.TYPE_REC);
        });

        return successMsg("已放入借阅车");
    }

    @Operation(summary = "发送文档至借阅车")
    @PostMapping("/addDocumentsToRecTempFolder")
    @ZDLog("发送文档至借阅车")
    public JSONResultUtils<Object> addDocumentsToRecTempFolder(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> selectDocuments = ZDMapUtils.getListMapValue(params, "selectedDocuments");
        if (CollectionUtils.isEmpty(selectDocuments)) {
            return JSONResultUtils.successWithData(new ArrayList<>());
        }

        selectDocuments.forEach(item -> {
            String docId = MapUtils.getString(item, "docId", "");
            if (StringUtils.isBlank(docId)) {
                return;
            }

            Document document = documentService.getDocumentById(docId);
            if (document == null) {
                return;
            }


            String dataId = ECMUtils.getStringFromRecordMap(document.getId());
            String fileTitle = ECMUtils.getStringFromRecordMap(document.getFilename());
            String fileNumber = ECMUtils.getStringFromRecordMap(document.getSerialNumber());

            Folder folder = document.getFolder();
            String areaDisplayFolder = folder.getAreaDisplayFolder();

            RecTempFolder recTempFolder = new RecTempFolder(areaDisplayFolder, fileTitle, dataId, fileNumber, fileNumber, folder.getId(),
                    "document", RecTempFolder.TYPE_DOC);

            recTempFolderService.addRecTempFolder(recTempFolder, RecTempFolder.TYPE_DOC);

        });

        return successMsg("已放入借阅车");
    }
}
