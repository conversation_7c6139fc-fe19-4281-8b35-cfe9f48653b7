package zd.base.service;

import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import zd.base.repositories.BaseRepository;

public interface BaseJpaService<T, ID> {

    BaseRepository<T, ID> getBaseRepository();

    Page<T> getBasePage(int pageNumber, int pageSize);

    void delete(T t);

    void deleteById(ID id);

    void deleteAllById(Iterable<? extends ID> ids);

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    T findById(ID id);
}
