package zd.dms.services.im.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.dms.Constants;
import zd.dms.entities.*;
import zd.dms.repositories.im.InstantMessageRepository;
import zd.dms.repositories.user.UserRepository;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.document.DocumentLinkService;
import zd.dms.services.document.FolderService;
import zd.dms.services.im.InstantMessageService;
import zd.dms.services.mail.MailService;
import zd.dms.services.mail.exception.NoMailServerException;
import zd.dms.utils.DingUtils;
import zd.dms.utils.TextUtils;
import zd.dms.utils.WXUtils;
import zd.record.entities.RecFolder;
import zd.record.entities.RecFolderSubscribe;
import zd.record.service.folder.RecFolderService;
import zd.record.service.security.RecFolderPermissionUtils;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@RequiredArgsConstructor
@Service
@Slf4j
public class InstantMessageServiceImpl extends BaseJpaServiceImpl<InstantMessage, Long> implements InstantMessageService {

    private final InstantMessageRepository instantMessageRepository;

    private final UserRepository userRepository;

    private final FolderService folderService;

    private final DocumentLinkService documentLinkService;

    private final MailService mailService;

    private final RecFolderService recFolderService;

    private static final ExecutorService pushTaskPool = Executors.newFixedThreadPool(5);

    private final SystemConfigManager scm = SystemConfigManager.getInstance();

    @Override
    public BaseRepository<InstantMessage, Long> getBaseRepository() {
        return instantMessageRepository;
    }

    @Override
    public void sendMessage(String type, String sender, String senderFullname, String target, String targetFullname, String title, String msg, User sendTo, String qywxLinkMsg, String redirectUrl) {
        log.debug("sendMessage: {}", msg);

        try {
            InstantMessage im = new InstantMessage();
            im.setTitle(title);
            im.setMsg(msg);
            im.setType(type);
            im.setTarget(target);
            im.setTargetFullname(targetFullname);
            im.setSender(sender);
            im.setSenderFullname(senderFullname);
            im.setDingEmployeeId(sendTo.getDingEmployeeId());
            im.setQywxId(sendTo.getQywxId());
            im.setQywxLinkMsg(qywxLinkMsg);
            im.setRedicrectUrl(redirectUrl);
            instantMessageRepository.save(im);

            // push msg
            pushMsg(im);
        } catch (Throwable t) {
            log.error("sendMessage ex", t);
        }
    }

    private void pushMsg(final InstantMessage im) {
        if (im == null) {
            return;
        }

        if (StringUtils.isBlank(im.getMsg())) {
            return;
        }

        if (im.getMsg().startsWith("您上次登录的时间是")) {
            return;
        }

        Runnable r = new Runnable() {
            public void run() {
                // 推送钉钉消息
                pushDing(im);

                // TODO ngcopy mppush
                /*boolean enablePushNotice = scm.getBooleanProperty("enablePushNotice", false);
                if (enablePushNotice) {
                    User targetUser = userRepository.getUser(im.getTarget());
                    String mobileRecieveNoticeTypeStr = targetUser.getProperty("mobileRecieveNoticeTypes");
                    if (StringUtils.isNotBlank(mobileRecieveNoticeTypeStr)) {
                        String[] mobileRecieveNoticeTypes = mobileRecieveNoticeTypeStr.split(",");
                        String noticeType = im.getType();
                        if (ArrayUtils.contains(mobileRecieveNoticeTypes, noticeType)) {
                            if (InstantMessage.TYPE_WORKFLOW.equals(noticeType)) {
                                noticeType = MPushUtils.getNoticeType(im.getMsg());
                            }

                            try {
                                MPushUtils.pushNotice(targetUser, TextUtils.removeHtmlTags(im.getMsg()), noticeType);
                            } catch (Exception e) {
                                log.error("移动端推送通知失败！", e);
                            }
                        }
                    }
                }*/

                // 发送企业微信消息: 1、已经设置了企业微信 2、用户具有企业微信账号 3、开启了企业微信消息推送
                if (WXUtils.checkRequiredElement() && StringUtils.isNotBlank(im.getQywxId()) &&
                        !scm.getBooleanProperty("qywxDisablePushMsg")) {
                    try {
                        String msg = TextUtils.removeHtmlTags(im.getMsg());
                        if (StringUtils.isNotBlank(im.getQywxLinkMsg())) {
                            msg = im.getQywxLinkMsg();
                        }
                        log.debug("pushMsg send_qywxMsg qywxId:{} msg:{}", im.getQywxId(), msg);
                        WXUtils.send_msg(im.getQywxId(), "", "", "text",
                                NumberUtils.toInt(scm.getProperty("qywxAgentId")), msg, 0);
                    } catch (Throwable t) {
                        log.error("pushMsg send_qywxMsg", t);
                    }
                }
            }
        };

        pushTaskPool.execute(r);
    }

    private void pushDing(InstantMessage im) {
        pushDing(im.getType(), im.getSenderFullname(), im.getDingEmployeeId(), im.getTargetFullname(), im.getTitle(), im.getMsg(), im.getRedicrectUrl());
    }

    @Override
    public List<InstantMessage> searchMessages(String keyword, String type, String username) {
        return instantMessageRepository.searchMessages(keyword, type, username);
    }

    @Override
    public void receiveAllMsg(String username) {
        instantMessageRepository.receiveAllMsg(username);
    }

    @Override
    public void sendMessage(String type, String sender, String senderFullname, String target, String targetFullname, String msg, User sendTo) {
        sendMessage(type, sender, senderFullname, target, targetFullname, null, msg, sendTo);
    }

    @Override
    public void sendMessage(String type, String sender, String senderFullname, String target, String targetFullname, String title, String msg, User sendTo) {
        sendMessage(type, sender, senderFullname, target, targetFullname, title, msg, sendTo, null, null);
    }

    @Override
    public void pushDing(String type, String sendUserFullname, String targetDingEmployeeId, String targetUserFullname, String title, String msg, String redirectUrl) {
        if (StringUtils.isNotBlank(scm.getProperty("dingUrl")) && StringUtils.isNotBlank(targetDingEmployeeId) &&
                !scm.getBooleanProperty("dingDisablePushMsg")) {
            log.debug("pushDing: {}", targetUserFullname);
            try {
                String userIdString = targetDingEmployeeId;
                String content = "";
                if (Objects.equals(type, InstantMessage.TYPE_WORKFLOW)) {
                    DingUtils.sendLink2User(userIdString, TextUtils.removeHtmlTags(msg), redirectUrl, "1", title);
                } else {
                    if (StringUtils.isNotBlank(title)) {
                        content = "[通知公告: " + title + "] " + TextUtils.removeHtmlTags(msg);
                    } else {
                        content = "[" + sendUserFullname + "]: " + TextUtils.removeHtmlTags(msg);
                    }
                    log.debug("ding content: {}", content);

                    DingUtils.sendText2User(userIdString, content);
                }
            } catch (Throwable t) {
                log.error("pushDing ex", t);
            }
        }
    }

    @Override
    public void sendFolderSubcribeMessage(User sender, Document document, String msg) {
        Folder folder = document.getFolder();
        List<FolderSubscribe> subscribes = folderService.getSubscribes(folder);
        List<FolderSubscribe> subscribesDoc = folderService.getSubscribes(document.getId());
        log.debug("subscribe folder count: {}, doc count: {}", subscribes.size(), subscribesDoc.size());

        Set<User> sendToUsers = new HashSet<User>();

        // 将订阅用户都加入发送列表
        for (FolderSubscribe fs : subscribes) {
            if (sender.getUsername().equals(fs.getUser().getUsername())) {
                log.debug("不需要发送给自己，跳过");
                continue;
            } else {
                sendToUsers.add(fs.getUser());
            }
        }
        for (FolderSubscribe fs : subscribesDoc) {
            if (sender.getUsername().equals(fs.getUser().getUsername())) {
                log.debug("不需要发送给自己，跳过");
                continue;
            } else {
                sendToUsers.add(fs.getUser());
            }
        }

        // 加入我的文档的用户
        List<DocumentLink> inMyCommonUsers = documentLinkService
                .getDocumentLinks(DocumentLink.TYPE_MY_COMMON, document);
        for (DocumentLink dl : inMyCommonUsers) {
            if (sender.getUsername().equals(dl.getUsername())) {
                log.debug("不需要发送给自己，跳过");
                continue;
            } else {
                sendToUsers.add(dl.getUser());
            }
        }

        for (User sendTo : sendToUsers) {
            sendMessage(InstantMessage.TYPE_DOC, sender.getUsername(), sender.getFullname(), sendTo.getUsername(),
                    sendTo.getFullname(), msg, sendTo);

            // 发送提醒邮件
            if (sendTo.getAcceptDocMailNotice() && StringUtils.isNotBlank(sendTo.getEmail())) {
                log.debug("remove html: {}", TextUtils.removeHtmlTags(msg));
                String mailMsg = sender.getFullnameAndUsername() + " " + TextUtils.removeHtmlTags(msg) + "<br/><br/>" +
                        "本邮件由" + Constants.getProductShortName() + "系统自动发送，请勿回复。<br/>如需关闭邮件提醒，请在" +
                        Constants.getProductShortName() + "“个人设置”中修改“是否接收提醒邮件”为否。";

                try {
                    mailService.queueMail(sendTo.getEmail(), sender.getEmail(), Constants.getProductShortName() +
                                    "订阅提醒：" + document.getSummaryFilename() + " 已更新", mailMsg, true, null,
                            sender.getFullname(), false);
                } catch (NoMailServerException e) {
                    log.debug("No Mail Server! Ignore");
                } catch (Throwable t) {
                    log.error("sendFolderSubcribeMessage queueMail ex", t);
                }
            }
        }
    }

    @Override
    public void deleteMessage(InstantMessage im) {
        instantMessageRepository.delete(im);
    }

    @Override
    public void deleteMessage(long id) {
        instantMessageRepository.deleteMsgById(id);
    }

    @Override
    public void deleteMessagesByUsername(String username) {
        instantMessageRepository.deleteMessagesByUsername(username);
    }

    @Override
    public void deleteMessagesBeforeDate(Date date) {
        instantMessageRepository.deleteMessagesBeforeDate(date);
    }

    @Override
    public List<InstantMessage> getMessagesUnread(String username) {
        return instantMessageRepository.getMessagesUnread(username);
    }

    @Override
    public void setReceived(InstantMessage im) {
        instantMessageRepository.setRecevied(im);
    }

    @Override
    public Page getMessages(String type, String username, int pageNumber, int pageSize) {
        return instantMessageRepository.getMessages(type, username, pageNumber, pageSize);
    }

    @Override
    public Page getReceviedMessages(String type, String username, int pageNumber, int pageSize) {
        return instantMessageRepository.getReceviedMessages(type, username, pageNumber, pageSize);
    }

    @Override
    public void sendRecSubcribeMessage(User sender, long recId, RecFolder recFolder, Map<String, Object> recordData, String mainDisplayColValue, String msg) {
        List<RecFolderSubscribe> subscribes = recFolderService.getSubscribes(recId, recFolder.getTableName());
        List<RecFolderSubscribe> folderSubscribes = recFolderService.getSubscribes(recFolder);

        Set<User> sendToUsers = new HashSet<User>();

        // 将订阅用户都加入发送列表
        for (RecFolderSubscribe fs : folderSubscribes) {
            if (sender.getUsername().equals(fs.getUser().getUsername())) {
                log.debug("不需要发送给自己，跳过");
                continue;
            } else {
                sendToUsers.add(fs.getUser());
            }
        }

        for (RecFolderSubscribe fs : subscribes) {
            if (sender.getUsername().equals(fs.getUser().getUsername())) {
                log.debug("不需要发送给自己，跳过");
                continue;
            } else {
                sendToUsers.add(fs.getUser());
            }
        }

        for (User sendTo : sendToUsers) {
            if (sendTo == null) {
                continue;
            }

            if (!RecFolderPermissionUtils.checkMijiAndBelongTo(recordData, sendTo, recFolder)) {
                log.debug("No MijiBelongTo perms: {}, props:{}", sendTo.getUsername(), recordData);
                continue;
            }

            sendMessage(InstantMessage.TYPE_RECORD, sender.getUsername(), sender.getFullname(), sendTo.getUsername(),
                    sendTo.getFullname(), msg, sendTo);

            // 发送提醒邮件
            if (sendTo.getAcceptDocMailNotice() && StringUtils.isNotBlank(sendTo.getEmail())) {
                String mailMsg = sender.getFullnameAndUsername() + " " + TextUtils.removeHtmlTags(msg) + "<br/><br/>" +
                        "本邮件由" + Constants.getProductShortName() + "系统自动发送，请勿回复。<br/>如需关闭邮件提醒，请在" +
                        Constants.getProductShortName() + "“个人设置”中修改“是否接收提醒邮件”为否。";

                try {
                    mailService.queueMail(sendTo.getEmail(), sender.getEmail(), Constants.getProductShortName() +
                            "数据更新提醒：" + mainDisplayColValue + " 已更新", mailMsg, true, null, sender.getFullname(), false);
                } catch (NoMailServerException e) {
                    log.debug("No Mail Server! Ignore");
                } catch (Throwable t) {
                    log.error("sendRecSubcribeMessage queueMail ex", t);
                }
            }
        }
    }

    @Override
    public void sendRecSubcribeMessage(User sender, RecFolder recFolder, Map<String, Object> recordData, String msg) {
        List<RecFolderSubscribe> subscribes = recFolderService.getSubscribes(recFolder);
        log.debug("rec subscribe folder count: {}", subscribes.size());

        Set<User> sendToUsers = new HashSet<User>();

        // 将订阅用户都加入发送列表
        for (RecFolderSubscribe fs : subscribes) {
            if (sender.getUsername().equals(fs.getUser().getUsername())) {
                log.debug("不需要发送给自己，跳过");
                continue;
            } else {
                sendToUsers.add(fs.getUser());
            }
        }

        for (User sendTo : sendToUsers) {
            if (sendTo == null) {
                continue;
            }

            if (!RecFolderPermissionUtils.checkMijiAndBelongTo(recordData, sendTo, recFolder)) {
                continue;
            }

            sendMessage(InstantMessage.TYPE_RECORD, sender.getUsername(), sender.getFullname(), sendTo.getUsername(),
                    sendTo.getFullname(), msg, sendTo);

            // 发送提醒邮件
            if (sendTo.getAcceptDocMailNotice() && StringUtils.isNotBlank(sendTo.getEmail())) {
                String mailMsg = sender.getFullnameAndUsername() + " " + TextUtils.removeHtmlTags(msg) + "<br/><br/>" +
                        "本邮件由" + Constants.getProductShortName() + "系统自动发送，请勿回复。<br/>如需关闭邮件提醒，请在" +
                        Constants.getProductShortName() + "“个人设置”中修改“是否接收提醒邮件”为否。";

                try {
                    mailService.queueMail(sendTo.getEmail(), sender.getEmail(), Constants.getProductShortName() +
                                    "数据目录更新提醒：" + recFolder.getName() + " 已更新", mailMsg, true, null, sender.getFullname(),
                            false);
                } catch (NoMailServerException e) {
                    log.debug("No Mail Server! Ignore");
                } catch (Throwable t) {
                    log.error("sendRecSubcribeMessage queueMail ex", t);
                }
            }
        }
    }
}
