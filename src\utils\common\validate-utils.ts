import { toRaw, unref, isRef, isReactive } from 'vue'

export const Validator = {
    /**
     * 检查值是否为空
     * @param value - 要检查的值
     * @returns {boolean} - 如果值为空返回 true，否则返回 false
     * @throws {Error} 当输入值为不支持的数据类型时抛出异常
     */
    isEmpty(value: any): boolean {
        // 先解包响应式对象
        const unpackedValue = this.unpack(value);

        // 处理 null 和 undefined
        if (unpackedValue == null) {
            return true;
        }

        // 处理字符串和数组
        if (typeof unpackedValue === 'string' || Array.isArray(unpackedValue)) {
            return unpackedValue.length === 0;
        }

        // 处理 Set和Map
        if (unpackedValue instanceof Set || unpackedValue instanceof Map) {
            return unpackedValue.size === 0;
        }

        // 处理普通对象
        if (unpackedValue instanceof Object && !Array.isArray(unpackedValue)) {
            return Object.keys(unpackedValue).length === 0;
        }

        // 处理其他不支持的类型
        throw new Error('不支持该数据类型');
    },
    /**
     * 检查值是否不为空，为空抛出异常
     * @param value - 要检查的值
     * @param errorMessage - 错误信息
     * @throws {Error} 当输入值为空时抛出异常
     */
    notEmpty(value: any, errorMessage: string): void {
        if (this.isEmpty(value)) {
            throw new Error(errorMessage);
        }
    },

    /**
     * 解包响应式对象，获取原始值
     * @param value - 要解包的值（可能是ref、reactive或普通值）
     * @returns {any} - 解包后的原始值
     */
    unpack(value: any): any {
        // 如果是 ref，使用 unref 解包
        if (isRef(value)) {
            return unref(value);
        }

        // 如果是 reactive 对象，使用 toRaw 获取原始对象
        if (isReactive(value)) {
            return toRaw(value);
        }

        // 如果是普通值，直接返回
        return value;
    }
}
