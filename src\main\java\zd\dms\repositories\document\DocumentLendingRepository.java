package zd.dms.repositories.document;

import zd.base.repositories.BaseRepository;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentLending;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DocumentLendingRepository extends BaseRepository<DocumentLending, Long> {

    void deleteByDocument(Document document);

    List<DocumentLending> getAllDocumentLendings();

    List<DocumentLending> getDocumentLendings(String username);

    List<DocumentLending> getDocumentLendingsByCreator(String creator);

    List<DocumentLending> getDocumentLendings(Document document);

    void deleteById(long id);

    void deleteExpiredLendings();

    List<DocumentLending> getExpiredLendings();

    boolean isDocumentLended(Document document, String username);

    /**
     * 根据文件名，借出人搜索借给我的文档
     *
     * @param fileName
     * @param lendBy
     * @param currentUser
     * @return
     */
    List<DocumentLending> searchMylendingsDoc(String fileName, String lendBy, String currentUser);

    /**
     * 根据文件名，借给人搜索我借出的文档
     *
     * @param fileName
     * @param lendTo
     * @param currentUser
     * @return
     */
    List<DocumentLending> searchMylendsDoc(String fileName, String lendTo, String currentUser);
}
