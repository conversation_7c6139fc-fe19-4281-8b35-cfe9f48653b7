package zd.dms.controllers.api.sso;

import zd.base.utils.ZDDateUtils;

import java.util.Date;

public class SSOTicket {

    private String ticketId;

    private String username;

    private String password;

    private Date genDate;

    private int validMin;

    public SSOTicket(String username, String password, int validMin) {
        this.username = username;
        this.password = password;
        this.validMin = validMin;
        this.genDate = new Date();
    }

    public boolean isExpired() {
        if (ZDDateUtils.betweenInMinute(new Date(), genDate) > validMin) {
            return true;
        }

        return false;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Date getGenDate() {
        return genDate;
    }

    public void setGenDate(Date genDate) {
        this.genDate = genDate;
    }

    public int getValidMin() {
        return validMin;
    }

    public void setValidMin(int validMin) {
        this.validMin = validMin;
    }
}
