package zd.base.utils.model;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

/**
 * 对象转map
 */
public class ObjectMapper {

    private Object object;
    private Map<String, Supplier<Object>> supplierMap = new HashMap<>();
    private String actionName;
    private Map<String, Object> props;

    public ObjectMapper(Object object, String actionName) {
        this.object = object;
        this.actionName = actionName;
    }

    public ObjectMapper(Object object, String actionName, Map<String, Object> props) {
        this.object = object;
        this.actionName = actionName;
        this.props = props;
    }

    public Map<String, Object> getProps() {
        return props;
    }

    public String getActionName() {
        return actionName;
    }

    public Object getObject() {
        return object;
    }

    public Map<String, Supplier<Object>> getSupplierMap() {
        return supplierMap;
    }

    /**
     * 添加字段
     *
     * @param fieldName
     * @param supplier
     * @return
     */
    public ObjectMapper addField(String fieldName, Supplier<Object> supplier) {
        supplierMap.put(fieldName, supplier);
        return this;
    }

    public ObjectMapper removeField(String fieldName) {
        if (supplierMap.containsKey(fieldName)) {
            supplierMap.remove(fieldName);
        }

        return this;
    }

    /**
     * 最终转map
     *
     * @return
     */
    public Map<String, Object> toMap() {
        Map<String, Object> resultMap = new HashMap<>();
        if (MapUtils.isEmpty(supplierMap)) {
            Object currentObject = getObject();
            if (currentObject == null) {
                return new HashMap<>();
            }

            if (currentObject instanceof Map) {
                return (Map) currentObject;
            }

            return BeanUtils.beanToMap(getObject(), true);
        }

        for (String field : supplierMap.keySet()) {
            Object tempObject = null;
            try {
                tempObject = supplierMap.get(field).get();
            } catch (Exception e) {
                e.printStackTrace();
            }

            resultMap.put(field, tempObject);
        }

        return resultMap;
    }
}
