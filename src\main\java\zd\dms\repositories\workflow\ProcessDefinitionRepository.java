package zd.dms.repositories.workflow;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.workflow.entities.SSProcessDefinition;

import java.util.List;

public interface ProcessDefinitionRepository extends BaseRepository<SSProcessDefinition, String> {

    /**
     * 根据流程分类和、流程分类、流程标签获取流程定义集合
     *
     * @param pdType
     * @param secondType
     * @param tagName
     * @return 流程定义集合
     */
    List<SSProcessDefinition> listByPdTypeAndSecondType(String pdType, String[] secondType, String tagName);

    /**
     * 根据流程分类和流程标签获取流程定义集合
     *
     * @param tag    流程标签
     * @param pdType 流程分类
     * @return 流程定义集合
     */
    List<SSProcessDefinition> listByTagAndPdType(String tag, String pdType);

    List<SSProcessDefinition> getProcessDefinitionsByType(String... pdType);

    Page getProcessDefinitionsPage(int pageNumber, int pageSize, String pdType);

    List<SSProcessDefinition> list();

    List<SSProcessDefinition> listByTag(String tag);

    List<String> getAllTags();
}
