import { api } from 'utils/axios';
import { DOCUMENT_LENDING_PATH } from 'config/resources';
import {
    DocumentLendRequest,
    DocumentLendingDTO,
    SearchLendToMeRequest,
    SearchLendFromMeRequest
} from '@/types/document/document-lending-api-types';
import { Page } from '@/types/common/pagination-types';

export const documentLendingApi = {
    /**
     * 借出文档
     */
    lend: (data: DocumentLendRequest): Promise<null> => api.post(`${DOCUMENT_LENDING_PATH}/lend`, data),

    /**
     * 搜索我借阅的文档
     */
    searchLendToMe: (params: SearchLendToMeRequest): Promise<Page<DocumentLendingDTO>> =>
        api.get(`${DOCUMENT_LENDING_PATH}/searchLendToMe`, { params }),

    /**
     * 搜索我借出的文档
     */
    searchLendFromMe: (params: SearchLendFromMeRequest): Promise<Page<DocumentLendingDTO>> =>
        api.get(`${DOCUMENT_LENDING_PATH}/searchLendFromMe`, { params }),

    /**
     * 收回我借出的文档
     */
    recallDocument: (ids: number[]): Promise<void> =>
        api.post(`${DOCUMENT_LENDING_PATH}/recallDocument`, null, { params: { ids } })
};

