package zd.base.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.repositories.DemoRepository;
import zd.base.service.DemoService;
import zd.dms.entities.User;

import java.util.List;

@RequiredArgsConstructor
@Service
public class DemoServiceImpl extends BaseJpaServiceImpl<User, String> implements DemoService {

    private final DemoRepository demoRepository;

    /**
     * 返回当前类所属得 baseRepository
     *
     * @return baseRepository
     */
    @Override
    public BaseRepository<User, String> getBaseRepository() {
        return demoRepository;
    }


    @Override
    public List<User> testSelect() {
        return demoRepository.testSelect();
    }

}
