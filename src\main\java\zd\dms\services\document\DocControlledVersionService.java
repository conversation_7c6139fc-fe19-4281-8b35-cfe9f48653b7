package zd.dms.services.document;

import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.dms.entities.DocumentControlledVersion;

@Transactional
public interface DocControlledVersionService extends BaseJpaService<DocumentControlledVersion, Long> {

    void save(DocumentControlledVersion documentControlledVersion);

    DocumentControlledVersion getDocControlledVersion(String docId, int revisionNumber, String publishVersion);
}
