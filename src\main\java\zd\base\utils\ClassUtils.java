package zd.base.utils;

import java.lang.reflect.Constructor;

public class ClassUtils {

    public static Object newInstance(String className) throws Exception {
        Class<?> aClass = Class.forName(className);
        Constructor<?> declaredConstructor = aClass.getDeclaredConstructor();
        declaredConstructor.setAccessible(true);
        return declaredConstructor.newInstance();
    }

    public static Object newInstance(String className, Class<?>... params) throws Exception {
        Class<?> aClass = Class.forName(className);
        Constructor<?> declaredConstructor = aClass.getDeclaredConstructor(params);
        declaredConstructor.setAccessible(true);
        return declaredConstructor.newInstance(params);
    }

    public static <T> T newInstance(Class<T> clazz, Class<?>... params) throws Exception {
        Constructor<?> declaredConstructor = clazz.getDeclaredConstructor(params);
        declaredConstructor.setAccessible(true);
        return (T) declaredConstructor.newInstance(params);
    }
}
