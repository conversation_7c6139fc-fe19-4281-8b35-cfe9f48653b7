package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.context.UserContextHolder;
import zd.base.entities.AbstractSequenceEntity;
import zd.base.entities.PropertyAware;
import zd.base.utils.system.SpringUtils;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.document.DocumentUtils;
import zd.dms.services.document.FolderService;
import zd.dms.services.document.FolderUtils;
import zd.dms.services.security.FolderPermissionUtils;
import zd.dms.utils.HibernateSessionUtils;
import zd.dms.utils.PropertyUtils;
import zd.dms.utils.TextUtils;
import zd.record.entities.RecFolderPermission;
import zd.record.service.security.RecFolderPermissionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "folder")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Folder extends AbstractSequenceEntity implements PropertyAware {

    /**
     * serial
     */
    private static final long serialVersionUID = 5059949321187842788L;

    public static final int TYPE_PUBLIC = 1;

    public static final int TYPE_MYDOCUMENT = 2;

    public static final String TRASHFOLDERNAME = "目录回收站";

    public static final String TYPE_ISOFOLDER = "isoArea";

    public static final String TYPE_ORDINARYFOLDER = "ordinaryArea";

    /**
     * 名称
     */
    @Column(nullable = false)
    @Index(name = "i_folder_name")
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 属性
     */
    private String docProperty;

    /**
     * 允许访问的IP
     */
    private String enabledIp;

    /**
     * 禁止访问的IP
     */
    private String disabledIp;

    /**
     * 分类类型
     */
    @Index(name = "i_folder_type")
    private int folderType;

    /**
     * 目录列表查找方式
     */
    private String isoFolderListType;

    /**
     * 排序号
     */
    private Integer numIndex;

    /**
     * 创建者全名
     */
    private String creatorFullname;

    /**
     * 列表显示字段
     */
    private String displayCols;

    /**
     * 默认排序字段
     */
    private String defaultSortField;

    /**
     * 默认排序类型
     */
    private String defaultSortType;

    /**
     * 打印编码规则
     */
    private String printNumberRule;

    /**
     * 发布计划版本规则
     */
    private String publishPlanVersionRule;

    /**
     * 目录所属tree的名称
     */
    private String treeName;

    /**
     * 目录是否允许发起审批
     */
    private String diabledStartWorkflow;

    // 设置回顾月份
    private Integer publishReviewTime;

    private String publishReviewTimeUnit;

    @Transient
    private Map<String, String> propertiesMap;

    @Column(length = Length.LOB_DEFAULT)
    private String properties;

    @Index(name = "i_folder_parentId")
    private Long parentId;

    /**
     * 子目录是否继承当前目录权限
     */
    private Boolean inheritPermission;

    /**
     * 创建时间
     */
    @Index(name = "i_folder_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * folder的权限：u-用户-权限,p-岗位-权限,g-部门::职位-权限，拼接成字符串
     */
    @Column(length = Length.LOB_DEFAULT)
    private String oldFolderPermissionString;

    @Column(length = Length.LOB_DEFAULT)
    private String pdIds;

    /**
     * 目录中docx和xlsx文件的打印限制
     */
    private Long printLimit;

    /**
     * 当前目录权限
     */
    @Transient
    @Setter
    private String myPermissions;

    /**
     * 当前目录的父目录权限
     */
    @Transient
    @Getter
    @Setter
    private String parentPermissions;

    @Setter
    @Getter
    @Transient
    private long childCount;

    @Transient
    @Setter
    private Boolean hasPermissionChildren;

    @Transient
    @Setter
    private String checkPerms;

    /**
     * 默认构造器
     */
    public Folder() {
        super();
        folderType = TYPE_MYDOCUMENT;
        numIndex = 9999;
        creationDate = new Date();
    }

    public Folder getParent() {
        if (parentId == null) {
            return null;
        }

        return SpringUtils.getBean(FolderService.class).getById(parentId);
    }

    public List<Folder> getChildren() {
        return SpringUtils.getBean(FolderService.class).getChildren(id);
    }

    public boolean isCurrentFolderDiabledStartWorkflow() {
        return FolderUtils.isCurrentFolderDiabledStartWorkflow(this);
    }

    public String getDiabledStartWorkflowWithParent() {
        return FolderUtils.getDiabledStartWorkflowWithParent(this);
    }

    public String getPdIdsWithParent() {
        return FolderUtils.getPdIdsWithParent(this);
    }

    /**
     * 获取该目录的一级父目录：不包括"公共文档"
     *
     * @return 一级父目录
     */
    public Folder getTopFolder() {
        Folder result = this;
        Folder parentFolder = getParent();
        while (parentFolder != null) {
            result = parentFolder;
            parentFolder = parentFolder.getParent();
        }

        return result;
    }

    public String getSimilarDocsType() {
        String similarDocsType = getProperty("similarDocsType");
        if (StringUtils.isBlank(similarDocsType)) {
            similarDocsType = SystemConfigManager.getInstance().getProperty("defaultFolderSimilarDocsType");
        }
        if (StringUtils.isBlank(similarDocsType)) {
            similarDocsType = "filename";
        }

        return similarDocsType;
    }

    public void setSimilarDocsType(String type) {
        setProperty("similarDocsType", type);
    }

    public List<String> getDisplayColListIncludeParents() {
        if (StringUtils.isNotBlank(displayCols)) {
            return getDisplayColList();
        }

        Folder parentFolder = getParent();
        while (parentFolder != null && StringUtils.isBlank(parentFolder.getDisplayCols())) {
            parentFolder = parentFolder.getParent();
        }

        if (parentFolder != null) {
            return parentFolder.getDisplayColList();
        } else {
            return FolderUtils.getFolderDefaultCols();
        }
    }

    public String getDefaultSortFieldNameIncludeParents() {
        if (StringUtils.isNotBlank(defaultSortField)) {
            return getDefaultSortFieldName();
        }

        Folder parentFolder = getParent();
        while (parentFolder != null && StringUtils.isBlank(parentFolder.getDisplayCols())) {
            parentFolder = parentFolder.getParent();
        }

        if (parentFolder != null) {
            return parentFolder.getDefaultSortFieldName();
        } else {
            return null;
        }
    }

    public String getDefaultSortFieldIncludeParents() {
        if (StringUtils.isNotBlank(defaultSortField)) {
            return getDefaultSortField();
        }

        Folder parentFolder = getParent();
        while (parentFolder != null && StringUtils.isBlank(parentFolder.getDisplayCols())) {
            parentFolder = parentFolder.getParent();
        }

        if (parentFolder != null) {
            return parentFolder.getDefaultSortField();
        } else {
            return null;
        }
    }

    public String getDefaultSortTypeIncludeParents() {
        if (StringUtils.isNotBlank(defaultSortType)) {
            return getDefaultSortType();
        }

        Folder parentFolder = getParent();
        while (parentFolder != null && StringUtils.isBlank(parentFolder.getDisplayCols())) {
            parentFolder = parentFolder.getParent();
        }

        if (parentFolder != null) {
            return parentFolder.getDefaultSortType();
        } else {
            return null;
        }
    }

    public String getDefaultSortFieldName() {
        if (StringUtils.isBlank(defaultSortField)) {
            return null;
        }

        return Document.DEFAULT_COL_MAP.get(defaultSortField);
    }

    public List<String> getDisplayColList() {
        if (StringUtils.isNotBlank(displayCols)) {
            List<String> results = new LinkedList<String>();
            String[] arr = StringUtils.split(displayCols, ",");
            for (String s : arr) {
                if (StringUtils.isNotBlank(s)) {
                    results.add(s.trim());
                }
            }

            return results;
        } else {
            return FolderUtils.getFolderDefaultCols();
        }
    }

    public String getCustomDate1Label() {
        return StringUtils.isNotBlank(getProperty("customDate1Label")) ? getProperty("customDate1Label") : "";
    }

    public String getCustomDate2Label() {
        return StringUtils.isNotBlank(getProperty("customDate2Label")) ? getProperty("customDate2Label") : "";
    }

    public boolean isSearchFolder() {
        return "true".equalsIgnoreCase(getProperty("isSearchFolder"));
    }

    /**
     * 构造器
     *
     * @param name        组名
     * @param description 组描述
     */
    public Folder(String name, String description) {
        this();
        this.name = name;
        this.description = description;
    }

    public boolean isHasPermissionChildren() {
        if (hasPermissionChildren == null) {
            if (checkPerms == null) {
                checkPerms = RecFolderPermission.LIST;
            } else {
                if (StringUtils.isBlank(checkPerms)) {
                    return hasPermissionChildren;
                }
            }

            hasPermissionChildren = FolderPermissionUtils.hasPermissionChildrenFolders(this, checkPerms, UserContextHolder.getUser());
        }

        return hasPermissionChildren;
    }

    public String getMyPermissionsWithoutInit() {
        return myPermissions;
    }

    public String getMyPermissionsWithInit() {
        if (StringUtils.isBlank(myPermissions)) {
            myPermissions = FolderPermissionUtils.getFolderPermissionsWithRedis(this, UserContextHolder.getUser(), parentPermissions);
        }

        return myPermissions;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getDisplayFolder() {
        return DocumentUtils.getDisplayDocumentPath(this, null);
    }

    public String getAreaDisplayFolder() {
        return DocumentUtils.getAreaDisplayDocumentPath(this, null);
    }

    public String getDisplayFolderWithLink() {
        return DocumentUtils.getDisplayDocumentPathWithLink(this, null);
    }


    /**
     * 带有转义字符'/'的目录路径链接，页面中获取目录路径处有单引号包裹，返回的路径中单引号需要加入转义字符
     *
     * @return
     */
    public String getDisplayFolderWithLinkAndEscapeChar() {
        return DocumentUtils.getDisplayDocumentPathWithLinkAndEscapeChar(this, null);
    }

    /**
     * 设置属性
     *
     * @param name  名称
     * @param value 值
     */
    public void setProperty(String name, String value) {
        initPropertiesMap();
        propertiesMap.put(name, value);
        PropertyUtils.updateProperties(this);
    }

    /**
     * 移除属性
     *
     * @param name 要移除的属性名称
     */
    public void removeProperty(String name) {
        initPropertiesMap();
        propertiesMap.remove(name);
    }

    /**
     * 获取属性
     *
     * @param name 名称
     * @return 值
     */
    public String getProperty(String name) {
        initPropertiesMap();
        return propertiesMap.get(name);
    }


    /**
     * @return Returns the description.
     */
    public String getDescription() {
        return TextUtils.escapeXml(description);
    }

    /**
     * @param description The description to set.
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * @return Returns the name.
     */
    public String getName() {
        return TextUtils.escapeXml(name).replaceAll("&amp;", "&");
    }

    /**
     * @param name The name to set.
     */
    public void setName(String name) {
        this.name = name;
    }

    public Folder getSessionParent() {
        return HibernateSessionUtils.getSessionParentByFolder(this);
    }

    /**
     * @see Object#equals(Object)
     */
    @Override
    public boolean equals(Object o) {
        if (!(o instanceof Folder)) {
            return false;
        }

        final Folder g = (Folder) o;

        return new EqualsBuilder().appendSuper(super.equals(g)).append(name, g.getName()).isEquals();
    }

    /**
     * @see Object#hashCode()
     */
    @Override
    public int hashCode() {
        return new HashCodeBuilder().appendSuper(super.hashCode()).append(name).toHashCode();
    }

    public Map<String, String> getPropertiesMap() {
        initPropertiesMap();
        return propertiesMap;
    }

    public void setPropertiesMap(Map<String, String> propertiesMap) {
        this.propertiesMap = propertiesMap;
    }

    public void initPropertiesMap() {
        if (this.propertiesMap == null) {
            this.propertiesMap = new HashMap<>();

            PropertyUtils.updatePropertiesMap(this);
        }
    }

    public String getProperties() {
        return properties;
    }

    public void setProperties(String properties) {
        this.properties = properties;
    }

    public String getCreatorFullname() {
        return creatorFullname;
    }

    public void setCreatorFullname(String creatorFullname) {
        this.creatorFullname = creatorFullname;
    }

    public int getFolderType() {
        return folderType;
    }

    public void setFolderType(int folderType) {
        this.folderType = folderType;
    }

    public Integer getNumIndex() {
        if (numIndex == null) {
            return 9999;
        }

        return numIndex;
    }

    public void setNumIndex(Integer numIndex) {
        this.numIndex = numIndex;
    }

    public Boolean getInheritPermission() {
        if (inheritPermission == null) {
            return true;
        }

        return inheritPermission;
    }

    public void setInheritPermission(Boolean inheritPermission) {
        this.inheritPermission = inheritPermission;
    }

    public String getDisplayCols() {
        return displayCols;
    }

    public void setDisplayCols(String displayCols) {
        this.displayCols = displayCols;
    }

    public String getDefaultSortField() {
        return defaultSortField;
    }

    public void setDefaultSortField(String defaultSortField) {
        this.defaultSortField = defaultSortField;
    }

    public String getDefaultSortType() {
        return defaultSortType;
    }

    public void setDefaultSortType(String defaultSortType) {
        this.defaultSortType = defaultSortType;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getPrintNumberRule() {
        return printNumberRule;
    }

    public void setPrintNumberRule(String printNumberRule) {
        this.printNumberRule = printNumberRule;
    }

    public String getOldFolderPermissionString() {
        return oldFolderPermissionString;
    }

    public void setOldFolderPermissionString(String oldFolderPermissionString) {
        this.oldFolderPermissionString = oldFolderPermissionString;
    }

    public String getTreeNameWithParent() {
        return FolderUtils.getTreeNameWithParent(this);
    }

    public String getTreeName() {
        return treeName;
    }

    public void setTreeName(String treeName) {
        this.treeName = treeName;
    }

    public String getPublishPlanVersionRule() {
        return publishPlanVersionRule;
    }

    public void setPublishPlanVersionRule(String publishPlanVersionRule) {
        this.publishPlanVersionRule = publishPlanVersionRule;
    }

    // TODO ngcopy
    /*public boolean isIsoArea() {
        return DccUtils.isIsoArea(id);
    }*/

    public String getDocProperty() {
        return docProperty;
    }

    public void setDocProperty(String docProperty) {
        this.docProperty = docProperty;
    }

    public String getEnabledIp() {
        return enabledIp;
    }

    public void setEnabledIp(String enabledIp) {
        this.enabledIp = enabledIp;
    }

    public String getDisabledIp() {
        return disabledIp;
    }

    public void setDisabledIp(String disabledIp) {
        this.disabledIp = disabledIp;
    }

    public String getIsoFolderListType() {
        return isoFolderListType;
    }

    public void setIsoFolderListType(String isoFolderListType) {
        this.isoFolderListType = isoFolderListType;
    }

    public String getPdIds() {
        return pdIds;
    }

    public void setPdIds(String pdIds) {
        this.pdIds = pdIds;
    }

    public Long getPrintLimit() {
        if (printLimit == null) {
            return -1L;
        }

        return printLimit;
    }

    public void setPrintLimit(Long printLimit) {
        this.printLimit = printLimit;
    }

    public String getDiabledStartWorkflow() {
        if (StringUtils.isBlank(diabledStartWorkflow)) {
            return "default";
        }

        return diabledStartWorkflow;
    }

    public void setDiabledStartWorkflow(String diabledStartWorkflow) {
        this.diabledStartWorkflow = diabledStartWorkflow;
    }

    public Integer getPublishReviewTime() {
        if (publishReviewTime == null) {
            publishReviewTime = -1;
        }

        return publishReviewTime;
    }

    public void setPublishReviewTime(Integer publishReviewTime) {
        this.publishReviewTime = publishReviewTime;
    }

    public String getPublishReviewTimeUnit() {
        return publishReviewTimeUnit;
    }

    public void setPublishReviewTimeUnit(String publishReviewTimeUnit) {
        this.publishReviewTimeUnit = publishReviewTimeUnit;
    }
}
