package zd.dms.services.mail;

import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import zd.dms.entities.Document;
import zd.dms.entities.MailQueueItem;
import zd.dms.entities.MailServerInfo;
import zd.dms.entities.User;
import zd.dms.services.mail.exception.NoMailServerException;

import java.util.List;

@Transactional
public interface MailService {

    void sendMessageAndMailForDocAutoReminds(String title, List<String> recDisplay, String msg, String imType,
                                             User sender, User sendTo);

    int resetAllMail();

    void sendMessageAndMail(String mailTitle, String msg, String imType, User sender, User sendTo, String qywxLinkMsg, String redirectUrl);

    void sendMessageAndMail(String mailTitle, String msg, String imType, User sender, User sendTo);

    void sendMessageAndMailForAutoReminds(String title, List<String> recDisplay, String msg, String imType,
                                          User sender, User sendTo);

    boolean doSendMail(MailServerInfo server, MailQueueItem item);

    boolean doSendMail(MailServerInfo server, String sendTo, String replyTo, String subject, String content,
                       boolean isHtml, List<Document> attachments);

    /**
     * 统一发送邮件
     *
     * @param server
     * @param sendTo
     * @param replyTo
     * @param html
     * @param itemList
     * @return
     */
    boolean doSendMailUnified(MailServerInfo server, String sendTo, String replyTo, boolean html,
                              List<MailQueueItem> itemList);

    public abstract void addServer(MailServerInfo info);

    public abstract void updateServer(MailServerInfo info);

    public abstract void deleteServer(MailServerInfo info);

    public abstract void deleteServer(String id);

    public abstract void deleteServers(List<String> ids);

    @Transactional(readOnly = true)
    public abstract MailServerInfo getServerById(String id);

    @Transactional(readOnly = true)
    public abstract List<MailServerInfo> getMailServers();

    @Transactional(readOnly = true)
    public abstract List<MailQueueItem> getMailQueue();

    @Transactional(readOnly = true)
    Page getPageMailQueue(int pageNumber, int pageSize);

    MailQueueItem getMail(String id);

    void deleteMailQueueItem(String id);

    void deleteMailQueueItems(List<String> ids);

    void deleteAllMailQueueItem();

    void updateMailQueueItem(MailQueueItem item);

    void addMailQueueItem(MailQueueItem item);

    void queueMail(String sendTo, String replyTo, String subject, String content, boolean html, String attachments,
                   String creatorFullname, boolean noticeSender) throws NoMailServerException;

    void clearQueue();

    void remindFailedMails();
}