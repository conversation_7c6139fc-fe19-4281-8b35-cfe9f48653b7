package zd.dms.services.log;

import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.dms.entities.UserOperateLog;

import java.util.List;

@Transactional
public interface UserOperateLogService extends BaseJpaService<UserOperateLog, Long> {

    @Transactional(readOnly = true)
    List<UserOperateLog> getLogs(String username, int type);

    @Transactional(readOnly = true)
    List<UserOperateLog> getLogs(int type, int startIndex, int pageSize);

    void createOpLog(String operator, String operatorFullname, int operateType,
                     String filename, String folderPath, String docId, long folderId);

    void deleteOutdatedLogs(String operator, int operateType);

    void deleteAllUserOperateLogs(String operator);

    void deleteLogByUserAndTypeAndDocId(String operator, int operateType,
                                        String docId);

    void deleteLogByDocId(String docId);
}
