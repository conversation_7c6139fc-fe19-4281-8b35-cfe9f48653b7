package zd.dms.services.log;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.utils.system.SpringUtils;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class UserOpLogUtils {

	private static final Logger log = LoggerFactory
			.getLogger(UserOpLogUtils.class);

	private static final ExecutorService createLogExecutor = Executors
			.newFixedThreadPool(5);

	public static void createUserOpLog(final String operator,
			final String operatorFullname, final int operateType,
			final String filename, final String folderPath, final String docId,
			final long folderId) {

		final UserOperateLogService opService = SpringUtils.getBean(UserOperateLogService.class);

		if (opService != null) {
			Runnable r = new Runnable() {
				public void run() {
					try {
						opService.deleteLogByUserAndTypeAndDocId(operator,
								operateType, docId);
						opService.createOpLog(operator, operatorFullname,
								operateType, filename, folderPath, docId,
								folderId);
						opService.deleteOutdatedLogs(operator, operateType);
					} catch (Throwable t) {
						log.error("createUserOpLog ex", t);
					}
				}
			};

			createLogExecutor.execute(r);
		} else {
			log.error("null opManager");
		}
	}
}
