package zd.dms.controllers.document;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.exception.IllegalArgumentException;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.validate.ValidateUtils;
import zd.dms.dto.document.DocumentUploadDto;
import zd.dms.entities.Document;
import zd.dms.entities.Folder;
import zd.dms.entities.FolderPermission;
import zd.dms.services.document.DocumentService;
import zd.dms.services.security.FolderPermissionUtils;
import zd.dms.utils.ZDAuthUtils;
import zd.dms.utils.ZDFileUtils;
import zd.record.entities.RecFolder;
import zd.record.entities.RecFolderPermission;
import zd.record.service.folder.RecFolderService;
import zd.record.service.security.RecFolderPermissionUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;

@RequiredArgsConstructor
@RestController
@Tag(name = "UploadController", description = "文档上传Controller")
@RequestMapping("/document")
public class UploadController extends ControllerSupport {

    private final DocumentService documentService;

    private final RecFolderService recFolderService;

    @Operation(summary = "保存文档")
    @RequestMapping(value = "/saveDoc", method = {RequestMethod.POST, RequestMethod.PUT})
    @ZDLog("保存文档")
    public JSONResultUtils<Object> saveDoc(@RequestParam("uploadFile") MultipartFile uploadFile, @RequestParam String docId,
                                           @RequestParam(required = false) String from, @RequestParam(required = false) String info,
                                           @RequestParam(required = false) String token, HttpServletRequest request, HttpServletResponse response) throws IOException {

        long expMils = 3 * 24 * 60 * 60 * 1000;
        boolean auth = ZDAuthUtils.isAuth(request, response, token, expMils);
        if (!auth) {
            return JSONResultUtils.error("登陆已超时");
        }

        Document document = documentService.getDocumentById(docId);
        if (document == null) {
            return JSONResultUtils.error("没找到对应文档");
        }

        if ("rec".equals(from)) {
            Map<String, Object> infoMap = getInfoMap(info);
            long recFolderId = MapUtils.getLongValue(infoMap, "recFolderId", 0L);

            RecFolder recFolder = recFolderService.getById(recFolderId);
            if (!RecFolderPermissionUtils.checkPermission(getCurrentUser(), recFolder, RecFolderPermission.EDIT)) {
                throw new IllegalArgumentException("无权进行此操作");
            }
        } else {
            Folder folder = document.getFolder();
            if (!FolderPermissionUtils.checkPermission(getCurrentUser(), folder, FolderPermission.EDIT)) {
                throw new IllegalArgumentException("无权进行此操作");
            }
        }

        File docTempFile = ZDFileUtils.getTempFile(0);
        uploadFile.transferTo(docTempFile);
        if (!docTempFile.exists() || docTempFile.length() == 0) {
            return JSONResultUtils.error("文件上传失败");
        }

        documentService.saveDoc(document, docTempFile, getCurrentFullname(), "", true);

        return JSONResultUtils.success();
    }

    private Map<String, Object> getInfoMap(String info) {
        if (StringUtils.isBlank(info) || "info".equals(info)) {
            return new HashMap<>();
        }

        Map<String, Object> results = new HashMap<>();
        String[] split = info.split("--");
        for (String str : split) {
            if (StringUtils.isBlank(str)) {
                continue;
            }

            String[] split1 = str.split("=");
            if (split1.length != 2) {
                continue;
            }

            results.put(split1[0], split1[1]);
        }

        return results;
    }

    @Operation(summary = "上传文档")
    @RequestMapping(value = "/upload", method = {RequestMethod.POST, RequestMethod.PUT})
    @ZDLog("上传文档")
    public JSONResultUtils<Object> uploadDocument(@RequestParam("uploadFile") MultipartFile uploadFile, long folderId) throws IOException {
        DocumentUploadDto documentUploadDto = new DocumentUploadDto();
        documentUploadDto.setFolderId(folderId);
        
        return documentService.saveUploadDocument(uploadFile, documentUploadDto, getCurrentUser());
    }

    @Operation(summary = "更新文档")
    @RequestMapping(value = "/update", method = {RequestMethod.POST, RequestMethod.PUT})
    @ZDLog("更新文档")
    public JSONResultUtils<Object> update(@RequestParam("uploadFile") MultipartFile uploadFile, @RequestParam String docId, @RequestParam String filename, @RequestParam String comment) throws IOException {
        ValidateUtils.isTrue(uploadFile != null, "请选择文件");
        ValidateUtils.isTrue(StringUtils.isBlank(docId), "请选择文档");
        ValidateUtils.isTrue(StringUtils.isNotBlank(filename), "请选择文档");
        return documentService.saveUpdateDocument(uploadFile, docId, filename, comment, getCurrentUser());
    }
}
