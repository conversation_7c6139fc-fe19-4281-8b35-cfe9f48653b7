package zd.base.jpa;

import jakarta.persistence.EntityManager;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.support.JpaRepositoryFactory;
import org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.data.repository.core.RepositoryInformation;
import org.springframework.data.repository.core.RepositoryMetadata;
import org.springframework.data.repository.core.support.RepositoryFactorySupport;
import org.springframework.data.util.TypeInformation;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;

import java.io.Serializable;
import java.lang.reflect.Constructor;

public class BaseRepositoryFactoryBean<R extends JpaRepository<T, Serializable>, T> extends JpaRepositoryFactoryBean<R, T, Serializable> {

    public BaseRepositoryFactoryBean(Class<? extends R> repositoryInterface) {
        super(repositoryInterface);
    }

    @Override
    protected RepositoryFactorySupport createRepositoryFactory(final EntityManager entityManager) {
        return new JpaRepositoryFactory(entityManager) {

            protected SimpleJpaRepository<T, Serializable> getTargetRepository(
                    RepositoryInformation information, EntityManager entityManager) {
                Class<T> domainClass = (Class<T>) information.getDomainType();

                TypeInformation<?> domainTypeInformation = information.getDomainTypeInformation();
                Class<?> repositoryInterface = information.getRepositoryInterface();
                String className = repositoryInterface.getName();
                int i = className.lastIndexOf(".");
                className = className.substring(0, i) + ".impl" + className.substring(i) + "DaoImpl";

                try {

                    Class<?> aClass = Class.forName(className);
                    Constructor<?> declaredConstructor = aClass.getDeclaredConstructor(Class.class, EntityManager.class);
                    declaredConstructor.trySetAccessible();
                    return (BaseRepositoryDaoImpl<T, Serializable>) declaredConstructor.newInstance(domainClass, entityManager);
                } catch (Exception e) {
                }

                return new BaseRepositoryDaoImpl<>(domainClass, entityManager);
            }

            @Override
            protected Class<?> getRepositoryBaseClass(RepositoryMetadata metadata) {
                Class<?> repositoryInterface = metadata.getRepositoryInterface();
                String className = repositoryInterface.getName();
                int i = className.lastIndexOf(".");
                className = className.substring(0, i) + ".impl" + className.substring(i) + "DaoImpl";

                try {
                    return Class.forName(className);
                } catch (ClassNotFoundException e) {
                }

                return BaseRepositoryDaoImpl.class;
            }
        };
    }
}
