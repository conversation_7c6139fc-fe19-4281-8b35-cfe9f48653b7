package zd.base.repositories.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.Query;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.provider.PersistenceProvider;
import org.springframework.data.jpa.repository.query.EscapeCharacter;
import org.springframework.data.jpa.repository.support.CrudMethodMetadata;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.JpaEntityInformationSupport;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.lang.Nullable;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import zd.base.repositories.BaseRepository;
import zd.dms.utils.PageableUtils;
import zd.dms.utils.repositories.QueryParamsUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class BaseRepositoryDaoImpl<T, ID> extends SimpleJpaRepository<T, ID> implements BaseRepository<T, ID> {

    protected final JpaEntityInformation<T, ?> entityInformation;
    protected final EntityManager entityManager;
    protected final PersistenceProvider provider;
    @Nullable
    protected CrudMethodMetadata metadata;
    protected EscapeCharacter escapeCharacter;

    public BaseRepositoryDaoImpl(Class<T> domainClass, EntityManager entityManager) {
        this(JpaEntityInformationSupport.getEntityInformation(domainClass, entityManager), entityManager);
    }

    public BaseRepositoryDaoImpl(JpaEntityInformation<T, ?> entityInformation, EntityManager entityManager) {
        super(entityInformation, entityManager);
        this.escapeCharacter = EscapeCharacter.DEFAULT;
        Assert.notNull(entityInformation, "JpaEntityInformation must not be null");
        Assert.notNull(entityManager, "EntityManager must not be null");
        this.entityInformation = entityInformation;
        this.entityManager = entityManager;
        this.provider = PersistenceProvider.fromEntityManager(entityManager);
    }

    @Override
    public void setRepositoryMethodMetadata(CrudMethodMetadata crudMethodMetadata) {
        this.metadata = crudMethodMetadata;
    }

    @Override
    public void setEscapeCharacter(EscapeCharacter escapeCharacter) {
        this.escapeCharacter = escapeCharacter;
    }

    @Override
    public Page<T> getPage(int pageNumber, int pageSize) {
        return findAll(PageableUtils.getPageable(pageNumber, pageSize));
    }

    @Override
    public Page<T> getPage(int pageNumber, int pageSize, Sort sort) {
        return findAll(PageableUtils.getPageable(pageNumber, pageSize, sort));
    }

    @Override
    public Page<T> getPage(int pageNumber, int pageSize, String orderProperty, boolean asc) {
        Sort.Direction sortDirection = Sort.Direction.ASC;
        if (!asc) {
            sortDirection = Sort.Direction.DESC;
        }

        return getPage(pageNumber, pageSize, Sort.by(sortDirection, orderProperty));
    }

    @Override
    public Page<T> findAll(Specification<T> spec, int pageNumber, int pageSize) {
        return findAll(spec, PageableUtils.getPageable(pageNumber, pageSize));
    }

    @Transactional
    public <S extends T> S update(S entity) {
        Assert.notNull(entity, "Entity must not be null");
        return this.entityManager.merge(entity);
    }

    @Override
    public List<T> getAll() {
        return findAll();
    }

    @Override
    public T get(ID id) {
        return findObjectById(id);
    }

    @Override
    public T findObjectById(ID id) {
        Optional<T> optional = findById(id);
        return optional.orElse(null);
    }

    @Override
    public T findSingleObject(String hql) {
        Query query = this.entityManager.createQuery(hql);
        return findSingleObject(query);
    }

    @Override
    public T findSingleObject(String hql, Object... params) {
        Query query = this.entityManager.createQuery(hql);
        QueryParamsUtils.setQueryParams(query, params);

        return findSingleObject(query);
    }

    @Override
    public T findSingleObject(String hql, List<Object> params) {
        Query query = this.entityManager.createQuery(hql);
        QueryParamsUtils.setQueryParams(query, params);

        return findSingleObject(query);
    }

    @Override
    public T findSingleObject(String hql, Map<String, Object> params) {
        Query query = this.entityManager.createQuery(hql);
        QueryParamsUtils.setQueryParams(query, params);

        return findSingleObject(query);
    }

    private T findSingleObject(Query query) {
        try {
            Object singleResult = query.getSingleResult();
            if (singleResult == null) {
                return null;
            }

            return (T) singleResult;
        } catch (Throwable t) {
            if (!(t instanceof NoResultException)) {
                log.error("findSingleObject error", t);
            } else {
                log.debug("findSingleObject no result");
            }
        }

        return null;
    }

    @Override
    public List<T> findAll(String hql) {
        Query query = this.entityManager.createQuery(hql);
        return query.getResultList();
    }

    @Override
    public List<T> findAll(String hql, Object... params) {
        Query query = this.entityManager.createQuery(hql);
        QueryParamsUtils.setQueryParams(query, params);

        return query.getResultList();
    }

    @Override
    public List<T> findAll(String hql, List<Object> params) {
        Query query = this.entityManager.createQuery(hql);
        QueryParamsUtils.setQueryParams(query, params);

        return query.getResultList();
    }

    @Override
    public List<T> findAll(String hql, Map<String, Object> params) {
        Query query = this.entityManager.createQuery(hql);
        QueryParamsUtils.setQueryParams(query, params);

        return query.getResultList();
    }

    @Override
    public List<T> findAll(String hql, int pageNumber, int pageSize) {
        Query query = this.entityManager.createQuery(hql);

        int startIndex = PageableUtils.getStartIndex(pageNumber, pageSize);
        query.setFirstResult(startIndex).setMaxResults(pageSize);
        return query.getResultList();
    }

    @Override
    public T findSingleObject(Specification<T> spec) {
        Optional<T> optional = findOne(spec);
        return optional.orElse(null);
    }

    @Override
    public int executeUpdate(String hql) {
        Query query = this.entityManager.createQuery(hql);
        QueryParamsUtils.setQueryParams(query);
        return query.executeUpdate();
    }

    @Override
    public int executeUpdate(String hql, Object... params) {
        Query query = this.entityManager.createQuery(hql);
        QueryParamsUtils.setQueryParams(query, params);
        return query.executeUpdate();
    }

    @Override
    public int executeUpdate(String hql, List<Object> params) {
        Query query = this.entityManager.createQuery(hql);
        QueryParamsUtils.setQueryParams(query, params);
        return query.executeUpdate();
    }

    @Override
    public int executeUpdate(String hql, Map<String, Object> params) {
        Query query = this.entityManager.createQuery(hql);
        QueryParamsUtils.setQueryParams(query, params);
        return query.executeUpdate();
    }

    @Override
    public Object findObject(String hql) {
        Query query = this.entityManager.createQuery(hql);
        return findObject(query);
    }

    @Override
    public Object findObject(String hql, Object... params) {
        Query query = this.entityManager.createQuery(hql);
        QueryParamsUtils.setQueryParams(query, params);

        return findObject(query);
    }

    @Override
    public Object findObject(String hql, List<Object> params) {
        Query query = this.entityManager.createQuery(hql);
        QueryParamsUtils.setQueryParams(query, params);

        return findObject(query);
    }

    @Override
    public Object findObject(String hql, Map<String, Object> params) {
        Query query = this.entityManager.createQuery(hql);
        QueryParamsUtils.setQueryParams(query, params);

        return findObject(query);
    }

    private Object findObject(Query query) {
        try {
            Object singleResult = query.getSingleResult();
            if (singleResult == null) {
                return null;
            }

            return singleResult;
        } catch (Throwable t) {
            if (!(t instanceof NoResultException)) {
                log.error("findObject error", t);
            } else {
                log.debug("findObject no result");
            }
        }

        return null;
    }
}
