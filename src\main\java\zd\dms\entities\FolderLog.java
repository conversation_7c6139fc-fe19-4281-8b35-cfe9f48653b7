package zd.dms.entities;

import jakarta.persistence.*;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.base.utils.system.SpringUtils;
import zd.dms.services.document.FolderService;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Entity
@Table(name = "folderlog")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class FolderLog extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = 8417104449109409644L;

    public static final int TYPE_CREATE = 1;

    public static final int TYPE_UPDATE = 2;

    public static final int TYPE_DELETE = 3;

    public static final int TYPE_PERM = 4;

    public static final int TYPE_SUBSCRIPTION = 5;

    public static final int TYPE_EXPORT = 6;

    public static final int TYPE_OUTLINK = 7;

    /**
     * 操作者用户名
     */
    @Column(nullable = false)
    @Index(name = "i_flog_operator")
    private String operator;

    /**
     * 操作类型
     */
    @Index(name = "i_flog_operatetype")
    private int operateType;

    /**
     * 操作者全名
     */
    private String operatorFullname;

    /**
     * 内容
     */
    @Column(length = Length.LOB_DEFAULT)
    private String msg;

    /**
     * IP地址
     */
    private String ip;

    @Index(name = "i_flog_folderId")
    private Long folderId;

    /**
     * 创建时间
     */
    @Index(name = "i_flog_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 默认构造器
     */
    public FolderLog() {
        super();
        creationDate = new Date();
    }

    /**
     * @see Object#equals(Object)
     */
    @Override
    public boolean equals(Object o) {
        if (!(o instanceof FolderLog)) {
            return false;
        }

        final FolderLog dn = (FolderLog) o;

        return new EqualsBuilder().appendSuper(super.equals(dn)).append(operator, dn.getOperator())
                .append(msg, dn.getMsg()).append(folderId, dn.getFolderId()).isEquals();
    }

    /**
     * @see Object#hashCode()
     */
    @Override
    public int hashCode() {
        return new HashCodeBuilder().appendSuper(super.hashCode()).append(operator).append(msg).append(folderId)
                .toHashCode();
    }

    public Folder getFolder() {
        return SpringUtils.getBean(FolderService.class).getById(this.folderId);
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getOperatorFullname() {
        return operatorFullname;
    }

    public void setOperatorFullname(String fullname) {
        this.operatorFullname = fullname;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public int getOperateType() {
        return operateType;
    }

    public void setOperateType(int operateType) {
        this.operateType = operateType;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Long getFolderId() {
        return folderId;
    }

    public void setFolderId(Long folderId) {
        this.folderId = folderId;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
