package zd.dms.repositories.document;

import zd.base.repositories.BaseRepository;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentRemind;
import zd.dms.entities.User;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DocumentRemindRepository extends BaseRepository<DocumentRemind, Long> {

    List<DocumentRemind> getAllRelativeReminds();

    void deleteByDocument(Document document);

    List<DocumentRemind> getAllReminds();

    List<DocumentRemind> getRemindsByUser(User user);

    DocumentRemind getRemindByDocument(Document document);

    List<DocumentRemind> getRemindsByDocument(Document document);

    void deleteById(long id);

    List<DocumentRemind> getExpiredReminds();

    /**
     * 根据文件名，提醒日期搜索我的提醒文档
     *
     * @param currentUser
     * @param fileNames
     * @return
     */
    List<DocumentRemind> searchMyReminds(User currentUser, String fileNames, String advRemindsStartDate,
                                         String advRemindsEndDate);
}
