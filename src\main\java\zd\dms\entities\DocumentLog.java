package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.base.utils.system.SpringUtils;
import zd.dms.services.document.DocumentService;
import zd.dms.utils.WebUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * DocumentLog Domain Object
 *
 * <AUTHOR>
 * @version $Revision$, $Date$
 */
@Entity
@Table(name = "tdms_doclog")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class DocumentLog extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = 8417104449109409644L;

    public static final int TYPE_CREATE = 1;

    public static final int TYPE_UPDATE = 2;

    public static final int TYPE_READ = 3;

    public static final int TYPE_DELETE = 4;

    public static final int TYPE_PRINT = 5;

    public static final int TYPE_DOWNLOAD = 6;

    public static final int TYPE_SUBSCRIPTION = 7;

    public static final int TYPE_DELETE_NOTE = 8;

    public static final int TYPE_ADD_NOTE = 9;

    public static final int TYPE_TRASH = 10;

    public static final int TYPE_RESTORE = 11;

    public static final int TYPE_RENAME = 12;

    public static final int TYPE_ROLLBACK = 13;

    public static final int TYPE_DELETE_DOC_LINK = 14;

    public static final int TYPE_PUBLISH_DOC_LINK = 15;

    public static final int TYPE_LOCK_UNLOCK = 16;

    public static final int TYPE_MOVE = 17;

    public static final int TYPE_SEND_MAIL = 18;

    public static final int TYPE_LENDING = 19;

    public static final int TYPE_WORKFLOW = 20;

    public static final int TYPE_RELATION = 21;

    public static final int TYPE_REMIND = 22;

    public static final int TYPE_VERSION = 23;

    public static final int TYPE_PERM = 24;

    public static final int TYPE_DCC = 25;

    public static final int TYPE_OUTLINK = 26;

    public static final Map<Integer, String> LOG_TYPE_DISPLAY_MAP = new HashMap<Integer, String>();

    static {
        LOG_TYPE_DISPLAY_MAP.put(0, "全部");
        LOG_TYPE_DISPLAY_MAP.put(1, "创建/上传");
        LOG_TYPE_DISPLAY_MAP.put(2, "更新/编辑");
        LOG_TYPE_DISPLAY_MAP.put(3, "阅读/预览");
        LOG_TYPE_DISPLAY_MAP.put(5, "打印");
        LOG_TYPE_DISPLAY_MAP.put(6, "下载");
        LOG_TYPE_DISPLAY_MAP.put(10, "放入回收站");
        LOG_TYPE_DISPLAY_MAP.put(11, "还原");
        LOG_TYPE_DISPLAY_MAP.put(12, "重命名");
        LOG_TYPE_DISPLAY_MAP.put(13, "修订版回退");
        LOG_TYPE_DISPLAY_MAP.put(15, "发送文档链接");
        LOG_TYPE_DISPLAY_MAP.put(16, "锁定/解锁");
        LOG_TYPE_DISPLAY_MAP.put(17, "移动");
        LOG_TYPE_DISPLAY_MAP.put(18, "发送邮件");
        LOG_TYPE_DISPLAY_MAP.put(19, "借阅");
        LOG_TYPE_DISPLAY_MAP.put(20, "流程");
        LOG_TYPE_DISPLAY_MAP.put(21, "关联");
        LOG_TYPE_DISPLAY_MAP.put(22, "提醒");
        LOG_TYPE_DISPLAY_MAP.put(23, "版本");
        LOG_TYPE_DISPLAY_MAP.put(24, "权限");
        LOG_TYPE_DISPLAY_MAP.put(25, "文控");
        LOG_TYPE_DISPLAY_MAP.put(26, "外发");
    }

    /**
     * 操作者用户名
     */
    @Column(nullable = false)
    @Index(name = "i_dlog_operator")
    private String operator;

    /**
     * 操作类型
     */
    @Index(name = "i_dlog_operatetype")
    private int operateType;

    /**
     * 操作者全名
     */
    private String fullname;

    /**
     * 内容
     */
    @Column(length = Length.LOB_DEFAULT)
    private String msg;

    /**
     * IP地址
     */
    private String ip;

    @Index(name = "i_dlog_docId")
    private String docId;

    /**
     * 创建时间
     */
    @Index(name = "i_dlog_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 默认构造器
     */
    public DocumentLog() {
        super();
        creationDate = new Date();
    }

    public String getDocVersion() {
        if (StringUtils.isNotBlank(msg) && msg.length() > 7) {
            int posXiuGaiWei = msg.indexOf("修改为<b>");
            int posComma = msg.indexOf("</b>,");

            if (posXiuGaiWei > -1) {
                posXiuGaiWei += 6;

                if (posComma > -1) {
                    return msg.substring(posXiuGaiWei, posComma);
                }
            }
        }

        return "";
    }

    public String getDocVersionEncoded() {
        try {
            return URLEncoder.encode(getDocVersion(), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * @see Object#equals(Object)
     */
    @Override
    public boolean equals(Object o) {
        if (!(o instanceof DocumentLog)) {
            return false;
        }

        final DocumentLog dn = (DocumentLog) o;

        return new EqualsBuilder().appendSuper(super.equals(dn)).append(operator, dn.getOperator())
                .append(msg, dn.getMsg()).append(docId, dn.getDocument()).isEquals();
    }

    /**
     * @see Object#hashCode()
     */
    @Override
    public int hashCode() {
        return new HashCodeBuilder().appendSuper(super.hashCode()).append(operator).append(msg).append(docId)
                .toHashCode();
    }

    public Document getDocument() {
        return SpringUtils.getBean(DocumentService.class).getDocumentById(docId);
    }

    public String getDocumentFilename() {
        Document document = getDocument();
        if (document == null) {
            return "";
        }
        return document.getFilename();
    }

    public String getDocumentDisplayFolder() {
        Document document = getDocument();
        if (document == null) {
            return "";
        }
        return document.getDisplayFolder();
    }

    public String getDocumentDisplayFolderWithoutLink() {
        Document document = getDocument();
        if (document == null) {
            return "";
        }
        return document.getDisplayFolderWithoutLink();
    }

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public int getOperateType() {
        return operateType;
    }

    public void setOperateType(int operateType) {
        this.operateType = operateType;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public static DocumentLog of(User operator, int type, String docId, String message) {
        return of(operator, type, docId, message, WebUtils.getIPAddress());
    }

    public static DocumentLog of(User operator, int type, String docId, String message, String ipAddress) {
        DocumentLog documentLog = new DocumentLog();
        documentLog.setOperator(operator.getUsername());
        documentLog.setOperateType(type);
        documentLog.setDocId(docId);
        documentLog.setFullname(operator.getFullname());
        documentLog.setMsg(message);
        documentLog.setIp(WebUtils.getIPAddress());

        return documentLog;
    }
}
