package zd.base.utils.hlp;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.text.similarity.LevenshteinDistance;
import zd.dms.utils.OfficeUtils;

import java.io.File;

public class StringSimilarityUtils {

    public static void test() {
        String leftFile = OfficeUtils.extractText(new File("D:\\test\\hanlp\\二开需求(1).docx"), "docx", "UTF-8");
        String rightFile = OfficeUtils.extractText(new File("D:\\test\\hanlp\\二开需求(2).docx"), "docx", "UTF-8");

        System.out.println("==================");
        System.out.println(getSimilarity(leftFile, rightFile));
    }

    public static double getSimilarity(File leftFile, File rightFile) {
        String left = OfficeUtils.extractText(leftFile, FilenameUtils.getExtension(leftFile.getName()), "UTF-8");
        String right = OfficeUtils.extractText(leftFile, FilenameUtils.getExtension(rightFile.getName()), "UTF-8");

        return getSimilarity(left, right);
    }

    public static double getSimilarity(CharSequence left, CharSequence right) {
        if (left == null) {
            left = "";
        }

        if (right == null) {
            right = "";
        }

        Integer distance = getDistance(left, right);
        return 1 - (double) distance / Math.max(left.length(), right.length());
    }

    public static Integer getDistance(CharSequence left, CharSequence right) {
        return LevenshteinDistance.getDefaultInstance().apply(left, right);
    }
}
