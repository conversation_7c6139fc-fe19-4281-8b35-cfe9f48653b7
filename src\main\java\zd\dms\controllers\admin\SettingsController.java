package zd.dms.controllers.admin;

import io.micrometer.common.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapUtils;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.utils.admin.SettingsUtils;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "SettingsController", description = "系统设置Controller")
@RequestMapping("/admin/settings")
public class SettingsController extends ControllerSupport {

    private SystemConfigManager scm = SystemConfigManager.getInstance();

    @Operation(summary = "获取系统设置")
    @GetMapping("/get/{key}")
    @ZDLog("获取系统设置")
    public JSONResultUtils<Object> get(@PathVariable String key) {
        Object property = SettingsUtils.getPropertyValue(key);
        if (property != null) {
            return JSONResultUtils.successWithData(property);
        }

        return JSONResultUtils.success();
    }

    @Operation(summary = "保存系统设置")
    @PostMapping("/saveSettings")
    @ZDLog("保存系统设置")
    public JSONResultUtils<Object> saveSettings(@RequestBody Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            return error("参数为空");
        }

        SettingsUtils.vaildSettings(params);
        return SettingsUtils.saveSettings(params);
    }

    @Operation(summary = "获取系统设置")
    @PostMapping("/listSettings")
    @ZDLog("获取系统设置")
    public JSONResultUtils<Object> listSettings(@RequestBody Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            return error("参数为空");
        }

        List<String> keys = ZDMapUtils.getListStringValue(params, "keys");
        return JSONResultUtils.successWithData(SettingsUtils.getSettings(keys));
    }
}
