package zd.dms.services.docsig.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.dms.entities.DocumentSig;
import zd.dms.entities.DocumentSignature;
import zd.dms.entities.Group;
import zd.dms.entities.User;
import zd.dms.io.ZDIOUtils;
import zd.dms.repositories.docsig.DocumentSigRepository;
import zd.dms.repositories.docsig.DocumentSignatureRepository;
import zd.dms.services.docsig.DocumentSignatureService;
import zd.dms.services.security.PasswordUtils;
import zd.dms.services.user.GroupService;

import java.io.File;
import java.io.IOException;
import java.util.LinkedList;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class DocumentSignatureServiceImpl extends BaseJpaServiceImpl<DocumentSignature, String> implements DocumentSignatureService {

    private final DocumentSignatureRepository documentSignatureRepository;

    private final GroupService groupService;

    private final DocumentSigRepository documentSigRepository;

    @Override
    public BaseRepository<DocumentSignature, String> getBaseRepository() {
        return documentSignatureRepository;
    }

    @Override
    public DocumentSignature getDocSigsByBelongUsername(String username) {
        if (StringUtils.isBlank(username)) {
            return null;
        }

        return documentSignatureRepository.getDocSigsByBelongUsername(username);
    }

    @Override
    public List<DocumentSignature> getAllDocSigsByExt(String extension) {
        List<DocumentSignature> allSigs = getAllDocSigs();
        List<DocumentSignature> result = new LinkedList<DocumentSignature>();

        for (DocumentSignature sig : allSigs) {
            if (sig.getExtension().equals(extension)) {
                result.add(sig);
            }
        }

        return result;
    }

    @Override
    public void createDocSig(DocumentSignature docSig, File uploadFile) throws IOException {
        documentSignatureRepository.save(docSig);
        ZDIOUtils.saveDocSig(docSig, uploadFile);
    }

    @Override
    public DocumentSignature getSigByName(String name) {
        return documentSignatureRepository.getSignatureByName(name);
    }

    @Override
    public DocumentSignature getSigById(String id) {
        return documentSignatureRepository.get(id);
    }

    @Override
    public List<DocumentSignature> getAllDocSigs() {
        return documentSignatureRepository.getAll();
    }

    @Override
    public void updateDocSig(DocumentSignature docSig, File uploadFile) throws IOException {
        documentSignatureRepository.update(docSig);

        if (uploadFile != null) {
            ZDIOUtils.saveDocSig(docSig, uploadFile);
        }
    }

    @Override
    public void deleteDocSig(DocumentSignature docSig) throws IOException {
        if (docSig == null) {
            return;
        }

        ZDIOUtils.deleteDocSig(docSig);
        documentSignatureRepository.delete(docSig);
    }

    @Override
    public void deleteDocSigById(String id) throws IOException {
        DocumentSignature sig = getSigById(id);

        if (sig != null) {
            deleteDocSig(sig);
        }
    }

    @Override
    public List<DocumentSignature> getPermitedSigs(User user) {
        List<DocumentSignature> allSigs = getAllDocSigs();
        List<DocumentSignature> result = new LinkedList<DocumentSignature>();

        for (DocumentSignature sig : allSigs) {
            if (hasSigPermission(user, sig)) {
                result.add(sig);
            }
        }

        return result;
    }

    private boolean hasSigPermission(User user, DocumentSignature sig) {
        if (user == null) {
            return false;
        }

        if (sig == null) {
            return false;
        }

        if (sig.getPermissions().indexOf(user.getId()) > -1) {
            return true;
        }

        String[] permIds = sig.getPermissions().split(",");
        for (String permId : permIds) {
            if (permId.startsWith("g-")) {
                Group g = groupService.getGroupById(NumberUtils.toLong(permId.substring(2)));
                if (groupService.isUserInGroup(user, g)) {
                    return true;
                }
            }
        }

        return false;
    }

    @Override
    public List<DocumentSignature> getPermitedSigs(User user, String extension) {
        List<DocumentSignature> allSigs = getAllDocSigs();
        List<DocumentSignature> result = new LinkedList<DocumentSignature>();

        for (DocumentSignature sig : allSigs) {
            if (hasSigPermission(user, sig) && sig.getExtension().equals(extension)) {
                result.add(sig);
            }
        }

        return result;
    }

    @Override
    public boolean isValidSig(String filename) {
        if (StringUtils.isBlank(filename)) {
            return false;
        }

        String extension = FilenameUtils.getExtension(filename);
        return "esp".equalsIgnoreCase(extension) || "jpg".equalsIgnoreCase(extension) ||
                "bmp".equalsIgnoreCase(extension) || "png".equalsIgnoreCase(extension) ||
                "gif".equalsIgnoreCase(extension);
    }

    @Override
    public boolean checkPassword(DocumentSignature sig, String password) {
        return PasswordUtils.checkPassword(password, sig.getPassword());
    }

    @Override
    public List<DocumentSig> getSigsByDocId(String docId) {
        return documentSigRepository.getSigsByDocId(docId);
    }

    @Override
    public void deleteSigsByDocId(String docId) {
        documentSigRepository.getSigsByDocId(docId);
    }

    @Override
    public void createDocSig(DocumentSig sig) {
        documentSigRepository.save(sig);
    }
}
