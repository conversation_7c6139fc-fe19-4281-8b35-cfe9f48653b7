package zd.base.config.oss;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;

import java.net.URI;

@Slf4j
@Data
@Configuration
public class AWSS3Config {
    /**
     * 服务地址
     */
    @Value("${zd.oss.config.url}")
    private String url;

    /**
     * 用户名
     */
    @Value("${zd.oss.config.accessKey}")
    private String accessKey;

    /**
     * 密码
     */
    @Value("${zd.oss.config.secretKey}")
    private String secretKey;

    /**
     * 存储桶名称
     */
    @Value("${zd.oss.config.bucketName}")
    private String bucketName;

    @Bean
    public S3Client getS3Client() {
        if (StringUtils.isBlank(url)) {
            log.error("getS3Client url null");
        }

        // 创建AWS凭证
        AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(accessKey, secretKey);

        // 创建S3客户端构建器
        S3ClientBuilder s3ClientBuilder = S3Client.builder()
                .credentialsProvider(StaticCredentialsProvider.create(awsCredentials));

        // 如果是自定义端点（如MinIO服务器），则设置端点URI
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            s3ClientBuilder.endpointOverride(URI.create("http://" + url));
        } else {
            s3ClientBuilder.endpointOverride(URI.create(url));
        }

        // 设置为路径样式访问（MinIO兼容模式）
        s3ClientBuilder.region(Region.US_EAST_1); // 默认区域，实际上对于MinIO不重要
        s3ClientBuilder.forcePathStyle(true);

        return s3ClientBuilder.build();
    }
}
