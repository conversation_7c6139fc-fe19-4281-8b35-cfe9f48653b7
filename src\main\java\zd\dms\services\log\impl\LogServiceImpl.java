package zd.dms.services.log.impl;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.dms.Constants;
import zd.dms.entities.Group;
import zd.dms.entities.LogMessage;
import zd.dms.entities.Role;
import zd.dms.entities.User;
import zd.dms.exception.EntityAlreadyExistsException;
import zd.dms.exception.UserAmountExceededException;
import zd.dms.repositories.log.LogRepository;
import zd.dms.repositories.user.GroupRepository;
import zd.dms.repositories.user.UserRepository;
import zd.dms.repositories.user.UserRepositoryApi;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.document.FolderUtils;
import zd.dms.services.log.LogService;
import zd.dms.services.security.PasswordUtils;
import zd.dms.services.user.GroupService;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.services.user.UserService;
import zd.dms.utils.PropertyUtils;
import zd.dms.utils.TextUtils;

import java.util.*;

@RequiredArgsConstructor
@Service
@Slf4j
public class LogServiceImpl extends BaseJpaServiceImpl<LogMessage, Long> implements LogService {

    private final LogRepository logRepository;

    @Override
    public BaseRepository<LogMessage, Long> getBaseRepository() {
        return logRepository;
    }

    @Override
    public LogMessage getById(Long id) {
        return logRepository.findObjectById(id);
    }

    @Override
    public Page getLogs(int pageNumber, int pageSize, int level, Date startDate, Date endDate, String orderProperty, boolean asc) {
        return logRepository.getLogs(pageNumber, pageSize, level, startDate, endDate, orderProperty, asc);
    }

    @Override
    public List<LogMessage> getLogsByDate(Date startDate, Date endDate) {
        return logRepository.getLogsByDate(startDate, endDate);
    }

    @Override
    public void clearLogs() {
        logRepository.clearLogs();
    }

    @Override
    public void addMessage(int level, String operator, String ip, String msg) {
        LogMessage log = new LogMessage();
        log.setLevel(level);
        log.setOperator(operator);
        log.setIp(ip);
        log.setMsg(msg);

        logRepository.save(log);
    }

    @Override
    public void addMessage(int level, String operator, String ip, String msg, Throwable e) {
        LogMessage log = new LogMessage();
        log.setLevel(level);
        log.setOperator(operator);
        log.setIp(ip);

        String exMsg = ExceptionUtils.getStackTrace(e);
        exMsg = exMsg.replaceAll("\\n", "<br/>");
        exMsg = exMsg.replaceAll(" ", "&nbsp;");
        exMsg = exMsg.replaceAll("\\t", "&nbsp;");
        exMsg = StringUtils.deleteWhitespace(exMsg);

        log.setMsg(msg + ": " + exMsg);

        logRepository.save(log);
    }

    @Override
    public void create(LogMessage logMsg) {
        logRepository.save(logMsg);
    }

    @Override
    public void delete(LogMessage logMsg) {
        logRepository.delete(logMsg);
    }
}
