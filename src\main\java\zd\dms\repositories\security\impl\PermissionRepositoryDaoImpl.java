package zd.dms.repositories.security.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.JdbcTemplate;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.FolderPermission;
import zd.dms.repositories.security.PermissionRepository;
import zd.dms.utils.repositories.PredicateUtils;
import zd.dms.utils.repositories.SpecTools;
import zd.record.utils.RecordDBUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class PermissionRepositoryDaoImpl extends BaseRepositoryDaoImpl<FolderPermission, Long> implements PermissionRepository {

    public PermissionRepositoryDaoImpl(Class<FolderPermission> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<FolderPermission> getPermissionsByFolderId(long folderId) {
        Specification<FolderPermission> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderPermission> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("folderId", folderId);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<FolderPermission> getPermissionsByFolderIds(List<Long> folderIds) {
        if (CollectionUtils.isEmpty(folderIds)) {
            return new ArrayList<>();
        }

        Specification<FolderPermission> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderPermission> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.in("folderId", folderIds);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<FolderPermission> getPermissionsByUsername(String username) {
        Specification<FolderPermission> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderPermission> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("username", username);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<FolderPermission> getPermissionsByGroupCode(String groupCode) {
        Specification<FolderPermission> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderPermission> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("groupCode", groupCode);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<FolderPermission> getPermissionsByPosition(String position) {
        Specification<FolderPermission> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderPermission> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("position", position);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<FolderPermission> getUserPermissions(String username, long folderId) {
        Specification<FolderPermission> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderPermission> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("username", username);
            specTools.eq("folderId", folderId);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<FolderPermission> getGroupPermissions(String groupCode, long folderId) {
        Specification<FolderPermission> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderPermission> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("groupCode", groupCode);
            specTools.eq("folderId", folderId);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<FolderPermission> getPosPermissions(String position, long folderId) {
        Specification<FolderPermission> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderPermission> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("position", position);
            specTools.eq("folderId", folderId);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<FolderPermission> getUserPermissionsWithPosition(String username, List<String> positions, long folderId) {
        Specification<FolderPermission> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderPermission> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.or(PredicateUtils.equal(root, criteriaBuilder, "username", username),
                    PredicateUtils.in(root, criteriaBuilder, "position", positions));
            specTools.eq("folderId", folderId);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<FolderPermission> getPermissionsForUser(String username, List<String> groupCodes, List<String> positions, long folderId) {
        Specification<FolderPermission> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderPermission> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.or(PredicateUtils.equal(root, criteriaBuilder, "username", username),
                    PredicateUtils.in(root, criteriaBuilder, "groupCode", groupCodes),
                    PredicateUtils.in(root, criteriaBuilder, "position", positions)
            );

            if (folderId > 0) {
                specTools.eq("folderId", folderId);
            }

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<FolderPermission> getPermissionsForUser(String username, List<String> groupCodes, List<String> positions, List<String> perms) {
        Specification<FolderPermission> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderPermission> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.or(PredicateUtils.equal(root, criteriaBuilder, "username", username),
                    PredicateUtils.in(root, criteriaBuilder, "groupCode", groupCodes),
                    PredicateUtils.in(root, criteriaBuilder, "position", positions)
            );

            if (CollectionUtils.isNotEmpty(perms)) {
                List<Predicate> likePredicates = new ArrayList<>();
                for (String permission : perms) {
                    likePredicates.add(PredicateUtils.like(root, criteriaBuilder, "permission", permission));
                }

                specTools.or(likePredicates);
            }

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<Map<String, Object>> getPermissionRecFolderCount(List<Long> folderIds) {
        if (CollectionUtils.isEmpty(folderIds)) {
            return new ArrayList<>();
        }

        JdbcTemplate jt = RecordDBUtils.checkJdbcTemplate();
        String sql = String.format("select count(*),folderId from rfolder_permission where folderId in (%s) group by folderId", StringUtils.join(folderIds, ","));

        return jt.queryForList(sql);
    }

    @Override
    public void deletePermissionsByUsername(String username) {
        executeUpdate("delete from FolderPermission where username=?1 ", username);
    }

    @Override
    public void deletePermissionsByFolderId(long folderId) {
        executeUpdate("delete from FolderPermission where folderId=?1 ", folderId);
    }

    @Override
    public void deletePermissionsByGroupCode(String groupCode) {
        executeUpdate("delete from FolderPermission where groupCode=?1 ", groupCode);
    }
}
