package zd.base.utils.model;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import zd.base.utils.ZDDateUtils;
import zd.base.utils.ZDUtils;
import zd.dms.entities.*;
import zd.dms.entities.oauth2.OAuth2Client;
import zd.dms.services.user.UserService;
import zd.dms.utils.JSONUtils;
import zd.dms.utils.TextUtils;
import zd.dms.utils.usertask.UserTaskUtils;
import zd.dms.workflow.entities.SSCarbonCopy;
import zd.dms.workflow.entities.SSProcessDefinition;
import zd.dms.workflow.entities.SSProcessInstance;
import zd.dms.workflow.entities.SSTask;
import zd.record.entities.*;
import zd.record.utils.record.DataStructDtoUtils;
import zd.record.utils.record.PrintRecordsUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

/**
 * 对象转map
 */
public enum ObjectMapperEnum {

    zddmsentitiesUser {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            User user = (User) object;

            if ("selectedJson".equals(actionName) || "selectedJson-highlight".equals(actionName)) {
                supplierMap.put("value", () -> "u-" + user.getUsername());
                supplierMap.put("label", user::getFullnameAndUsername);
                supplierMap.put("leaf", () -> true);
                supplierMap.put("icon", () -> "person");

                if ("selectedJson-highlight".equals(actionName)) {
                    supplierMap.put("highlight", () -> true);
                }

                return;
            }

            // 列表展示字段
            supplierMap.put("id", user::getId);
            supplierMap.put("username", user::getUsername);
            supplierMap.put("fullname", user::getFullname);
            supplierMap.put("creationDate",
                    () -> ZDDateUtils.format(user.getCreationDate(), ObjectMapperUtils.dateFormatStr));
            supplierMap.put("enabled", user::getEnabled);
            supplierMap.put("roleList", user::getRoleList);
            supplierMap.put("email", user::getEmail);
            supplierMap.put("mobilePhone", user::getMobilePhone);
            supplierMap.put("officePhone", user::getOfficePhone);
            supplierMap.put("homePhone", user::getHomePhone);
            supplierMap.put("dingEmployeeId", user::getDingEmployeeId);
            supplierMap.put("ipRestrict", user::getIpRestrict);

            if ("list".equals(actionName)) {
                supplierMap.put("groups", user::getGroupMaps);
                supplierMap.put("positions", user::getPositionsList);
            } else if ("info".equals(actionName)) {
                String property = user.getProperty("web-theme");
                System.out.println(property);
                supplierMap.put("web-theme", () -> property);

                supplierMap.put("getGroupCodesList", user::getGroupCodesList);
            }
        }
    },
    zddmsentitiesGroup {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            Group group = (Group) object;

            if ("selectedJson".equals(actionName)) {
                boolean selectGroupEnabled = MapUtils.getBooleanValue(props, "selectGroupEnabled", false);

                supplierMap.put("id", group::getId);
                supplierMap.put("value", () -> "g-" + group.getCode());
                supplierMap.put("code", group::getCode);
                supplierMap.put("label", group::getDisplayFullname);
                supplierMap.put("icon", () -> "mdi-account-group");
                if (!selectGroupEnabled) {
                    supplierMap.put("disabled", () -> true);
                }

                long childCount = group.getChildCount();
                if (childCount <= 0) {
                    supplierMap.put("leaf", () -> true);
                } else {
                    supplierMap.put("leaf", () -> false);
                }

                return;
            }

            // 列表展示字段
            supplierMap.put("id", group::getId);
            supplierMap.put("code", group::getCode);
            supplierMap.put("name", group::getName);
            supplierMap.put("displayFullname", group::getDisplayFullname);
            supplierMap.put("parentCode", group::getParentCode);

            if ("list".equals(actionName)) {
                long childCount = group.getChildCount();
                if (childCount > 0) {
                    supplierMap.put("hasChildren", () -> true);
                } else {
                    supplierMap.put("hasChildren", () -> false);
                }

                supplierMap.put("parentId", group::getParentId);
            } else if ("get".equals(actionName)) {
                supplierMap.put("code", group::getCode);
                supplierMap.put("numIndex", group::getNumIndex);

                Group parent = group.getParent();
                long parentId;
                String parentDisplayFullname;
                if (parent != null) {
                    parentId = parent.getId();
                    parentDisplayFullname = parent.getDisplayFullname();
                } else {
                    parentId = 0L;
                    parentDisplayFullname = "";
                }
                supplierMap.put("parentId", () -> parentId);
                supplierMap.put("parentDisplayFullname", () -> parentDisplayFullname);
            }
        }
    },

    zddmsentitiesPosition {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            Position position = (Position) object;

            // 列表展示字段
            supplierMap.put("id", position::getId);
            supplierMap.put("username", position::getUsername);
            supplierMap.put("userFullname", position::getUserFullname);
            supplierMap.put("groupCode", position::getGroupCode);
            supplierMap.put("groupName", position::getGroupName);
            supplierMap.put("position", position::getPosition);
            supplierMap.put("enabled", position::isEnabled);
            supplierMap.put("numIndex", position::getNumIndex);
            supplierMap.put("creationDate",
                    () -> ZDDateUtils.format(position.getCreationDate(), ObjectMapperUtils.dateFormatStr));
        }
    },

    zdrecordentitiesRecFolder {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            RecFolder recFolder = (RecFolder) object;

            if ("selectedJson".equals(actionName)) {
                supplierMap.put("id", recFolder::getId);
                supplierMap.put("value", recFolder::getId);
                supplierMap.put("label", recFolder::getName);
                if (recFolder.getFolderType() == 101) {
                    if (recFolder.getAttachType()) {
                        supplierMap.put("icon", () -> "mdi-folder-file-outline");
                    } else {
                        supplierMap.put("icon", () -> "mdi-folder-table-outline");
                    }
                } else {
                    supplierMap.put("icon", () -> "mdi-folder-outline");
                }

                long childCount = recFolder.getChildCount();
                if (childCount <= 0) {
                    supplierMap.put("leaf", () -> true);
                    supplierMap.put("hasPermissionChildren", () -> false);
                } else {
                    supplierMap.put("leaf", () -> false);
                    supplierMap.put("hasPermissionChildren", recFolder::isHasPermissionChildren);
                }

                return;
            }

            supplierMap.put("id", recFolder::getId);
            supplierMap.put("parentId", recFolder::getParentId);

            RecFolder parent = recFolder.getParent();
            if (parent != null) {
                if (parent.isDataFolder()) {
                    supplierMap.put("parentType", () -> 101);
                } else {
                    supplierMap.put("parentType", () -> 100);
                }
            }

            supplierMap.put("name", recFolder::getName);
            supplierMap.put("folderType", recFolder::getFolderType);
            supplierMap.put("numIndex", recFolder::getNumIndex);
            supplierMap.put("attachmentFolderId", recFolder::getAttachmentFolderId);
            supplierMap.put("tableName", recFolder::getTableName);
            supplierMap.put("attachType", recFolder::getAttachType);
            supplierMap.put("displayFolder", recFolder::getDisplayFolder);
            supplierMap.put("myPermissions", recFolder::getMyPermissionsWithInit);

            if (recFolder.getFolderType() == 101) {
                if (recFolder.getAttachType()) {
                    supplierMap.put("icon", () -> "mdi-folder-file-outline");
                } else {
                    supplierMap.put("icon", () -> "mdi-folder-table-outline");
                }
            } else {
                supplierMap.put("icon", () -> "mdi-folder-outline");
            }

            if ("list".equals(actionName)) {
                long childCount = recFolder.getChildCount();
                if (childCount > 0) {
                    supplierMap.put("leaf", () -> false);
                    supplierMap.put("hasChildren", () -> true);
                    supplierMap.put("hasPermissionChildren", () -> true);
                } else {
                    if (recFolder.isHasPermissionChildren()) {
                        supplierMap.put("leaf", () -> false);
                        supplierMap.put("hasChildren", () -> true);
                        supplierMap.put("hasPermissionChildren", () -> true);
                    } else {
                        supplierMap.put("leaf", () -> true);
                        supplierMap.put("hasChildren", () -> false);
                        supplierMap.put("hasPermissionChildren", () -> false);
                    }
                }
            }
        }
    },
    zdrecordentitiesRecFolderPermission {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            RecFolderPermission recFolderPermission = (RecFolderPermission) object;

            // 列表展示字段
            supplierMap.put("id", recFolderPermission::getId);
            supplierMap.put("permission", recFolderPermission::getPermission);
            supplierMap.put("permissionList", recFolderPermission::getPermissionList);
            supplierMap.put("displayName", recFolderPermission::getDisplayName);
        }
    },
    zddmsentitiesFolderPermission {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            FolderPermission folderPermission = (FolderPermission) object;

            // 列表展示字段
            supplierMap.put("id", folderPermission::getId);
            supplierMap.put("permission", folderPermission::getPermission);
            supplierMap.put("permissionList", folderPermission::getPermissionList);
            supplierMap.put("displayName", folderPermission::getDisplayName);
        }
    },
    zdrecordentitiesRecFolderRule {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            RecFolderRule recFolderRule = (RecFolderRule) object;

            // 列表展示字段
            supplierMap.put("id", recFolderRule::getId);
            supplierMap.put("name", recFolderRule::getName);
            supplierMap.put("enabled", recFolderRule::isEnabled);
            supplierMap.put("actionType", recFolderRule::getActionType);
            supplierMap.put("ruleConditionList", () -> ObjectMapperUtils.toMapList(recFolderRule.getFolderRuleConditions()));
            supplierMap.put("ruleActionList", () -> ObjectMapperUtils.toMapList(recFolderRule.getFolderRuleActions()));
        }
    },
    zdrecordentitiesRecFolderRuleCondition {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            RecFolderRuleCondition condition = (RecFolderRuleCondition) object;

            // 列表展示字段
            supplierMap.put("id", condition::getId);
            supplierMap.put("conditionMap", condition::getPropertiesMap);
            supplierMap.put("conditionType", condition::getConditionType);
            supplierMap.put("display", condition::getDescription);
        }
    },
    zdrecordentitiesRecFolderRuleAction {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            RecFolderRuleAction action = (RecFolderRuleAction) object;

            // 列表展示字段
            supplierMap.put("id", action::getId);
            supplierMap.put("actionMap", action::getPropertiesMap);
            supplierMap.put("actionType", action::getActionType);
            supplierMap.put("display", action::getDescription);
        }
    },
    zddmsentitiesFolder {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            Folder folder = (Folder) object;

            if ("selectedJson".equals(actionName)) {
                supplierMap.put("id", folder::getId);
                supplierMap.put("value", folder::getId);
                supplierMap.put("label", folder::getName);
                supplierMap.put("icon", () -> "mdi-folder-outline");

                long childCount = folder.getChildCount();
                if (childCount <= 0) {
                    supplierMap.put("leaf", () -> true);
                    supplierMap.put("hasPermissionChildren", () -> false);
                } else {
                    supplierMap.put("leaf", () -> false);
                    supplierMap.put("hasPermissionChildren", folder::isHasPermissionChildren);
                }

                return;
            }

            // 列表展示字段
            supplierMap.put("id", folder::getId);
            supplierMap.put("name", folder::getName);
            supplierMap.put("numIndex", folder::getNumIndex);
            supplierMap.put("parentId", folder::getParentId);
            supplierMap.put("myPermissions", folder::getMyPermissionsWithInit);

            if ("list".equals(actionName)) {
                long childCount = folder.getChildCount();
                if (childCount > 0) {
                    supplierMap.put("hasChildren", () -> true);
                } else {
                    supplierMap.put("hasChildren", () -> false);
                }
            }
        }
    },
    zddmsentitiesDocument {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            Document document = (Document) object;

            // 列表展示字段
            supplierMap.put("id", document::getId);
            supplierMap.put("filename", document::getFilename);
            supplierMap.put("baseName", () -> FilenameUtils.getBaseName(document.getFilename()));

            supplierMap.put("creator", document::getCreator);
            supplierMap.put("creatorFullname", document::getCreatorFullname);
            supplierMap.put("creationDate", () -> ZDDateUtils.format(document.getCreationDate(), ObjectMapperUtils.dateFormatStr));
            supplierMap.put("extension", document::getExtension);
            supplierMap.put("folder", document::getDisplayFolder);
            supplierMap.put("modifiedDate", () -> ZDDateUtils.format(document.getModifiedDate(), ObjectMapperUtils.dateFormatStr));
            supplierMap.put("deleteBy", document::getDeletedBy);
            supplierMap.put("deleteDate", document::getDeleteDate);
            if ("info".equals(actionName)) {
                supplierMap.put("myPermissions", document::getMyPermissions);
            }
        }
    },
    zdrecordentitiesDataListCols {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            DataListCols dataListCols = (DataListCols) object;

            // 列表展示字段
            supplierMap.put("cols", dataListCols::getColsList);
            supplierMap.put("fixedLeftCols", dataListCols::getFixedLeftColsList);
            supplierMap.put("fixedRightCols", dataListCols::getFixedRightColsList);
            supplierMap.put("sortCols", dataListCols::getSortColsList);
            supplierMap.put("orderCols", dataListCols::getOrderColsList);

            supplierMap.put("searchCols", dataListCols::getSearchColsList);
            supplierMap.put("sortSearchCols", dataListCols::getSortSearchColsList);
        }
    },
    zdrecordentitiesDataCol {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            DataCol dataCol = (DataCol) object;

            supplierMap.put("name", dataCol::getName);
            supplierMap.put("type", dataCol::getColType);
            if ("displayCols".equals(actionName)) {
                supplierMap.put("label", dataCol::getDisplayName);
                supplierMap.put("field", dataCol::getName);
                supplierMap.put("align", () -> "left");
            }
        }
    },
    zdrecordentitiesDataStruct {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            DataStruct dataStruct = (DataStruct) object;

            supplierMap.put("id", dataStruct::getId);
            supplierMap.put("tableName", dataStruct::getTableName);
            supplierMap.put("displayName", dataStruct::getDisplayName);
            supplierMap.put("creationDate", () -> ZDDateUtils.format(dataStruct.getCreationDate(), ObjectMapperUtils.dateFormatStr));
            supplierMap.put("mainDisplayColumn", dataStruct::getMainDisplayColumn);
            if ("info".equals(actionName)) {
                supplierMap.put("dataCols", () -> DataStructDtoUtils.getDataColsList(dataStruct));
                supplierMap.put("defaultDataCols", () -> DataStructDtoUtils.getDefaultDataColsList());
            } else if ("printSettings".equals(actionName)) {
                supplierMap.put("bjPrintSettingsMap", () -> PrintRecordsUtils.initPrintSettingsMap(PrintRecordsUtils.TYPE_BJ, JSONUtils.parseObject(dataStruct.getBjPrintSettings(), Map.class, null)));
                supplierMap.put("fpPrintSettingsMap", () -> PrintRecordsUtils.initPrintSettingsMap(PrintRecordsUtils.TYPE_FP, JSONUtils.parseObject(dataStruct.getFpPrintSettings(), Map.class, null)));
                supplierMap.put("bkbPrintSettingsMap", () -> PrintRecordsUtils.initPrintSettingsMap(PrintRecordsUtils.TYPE_BKB, JSONUtils.parseObject(dataStruct.getBkbPrintSettings(), Map.class, null)));
                supplierMap.put("listPrintSettingsMap", () -> PrintRecordsUtils.initPrintSettingsMap(PrintRecordsUtils.TYPE_LIST, JSONUtils.parseObject(dataStruct.getListPrintSettings(), Map.class, null)));
            }
        }
    },
    zddmsentitiesMailServerInfo {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            MailServerInfo mailServerInfo = (MailServerInfo) object;

            supplierMap.put("id", mailServerInfo::getId);
            supplierMap.put("host", mailServerInfo::getHost);
            supplierMap.put("port", mailServerInfo::getPort);
            supplierMap.put("username", mailServerInfo::getUsername);
            supplierMap.put("password", mailServerInfo::getPassword);
            supplierMap.put("fromAddr", mailServerInfo::getFromAddr);
            supplierMap.put("maxSendPerMin", mailServerInfo::getMaxSendPerMin);
            supplierMap.put("useSsl", mailServerInfo::isUseSsl);
            supplierMap.put("useTls", mailServerInfo::getUseTls);
            supplierMap.put("enabled", mailServerInfo::isEnabled);
        }
    },

    zddmsentitiesMailQueueItem {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            MailQueueItem mailQueueItem = (MailQueueItem) object;

            supplierMap.put("id", mailQueueItem::getId);
            supplierMap.put("creator", mailQueueItem::getCreator);
            supplierMap.put("sendTo", mailQueueItem::getSendTo);
            supplierMap.put("subject", mailQueueItem::getSubject);
            supplierMap.put("status", mailQueueItem::getStatus);
            supplierMap.put("failReason", mailQueueItem::getFailReason);
        }
    },
    zddmsworkflowentitiesSSProcessDefinition {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            SSProcessDefinition pd = (SSProcessDefinition) object;

            supplierMap.put("id", pd::getId);
            supplierMap.put("name", pd::getName);
            supplierMap.put("nodeJson", pd::getNodeJson);
            supplierMap.put("pdType", pd::getPdType);

            supplierMap.put("creator", pd::getCreator);
            supplierMap.put("creationDate", () -> ZDDateUtils.format(pd.getCreationDate(), ObjectMapperUtils.dateFormatStr));

            supplierMap.put("relationTableName", pd::getRelationTableName);
        }
    },

    zddmsworkflowentitiesSSProcessInstance {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            SSProcessInstance pi = (SSProcessInstance) object;

            supplierMap.put("id", pi::getId);
            supplierMap.put("name", pi::getName);
            supplierMap.put("nodeJson", pi::getNodeJson);
            supplierMap.put("resourceData", pi::getResourceDataList);

            supplierMap.put("resourceName", pi::getResourceName);
            supplierMap.put("pdType", pi::getPdType);
            supplierMap.put("status", pi::getStatus);
            supplierMap.put("endDate", () -> ZDDateUtils.format(pi.getEndDate(), ObjectMapperUtils.dateFormatStrWithHHmm));

            supplierMap.put("creator", pi::getCreator);
            supplierMap.put("creatorFullname", pi::getCreatorFullname);
            supplierMap.put("creationDate", () -> ZDDateUtils.format(pi.getCreationDate(), ObjectMapperUtils.dateFormatStrWithHHmm));

            supplierMap.put("relationTableName", pi::getRelationTableName);
            supplierMap.put("relationRecId", pi::getRelationRecId);

            supplierMap.put("fromType", pi::getFromType);

            supplierMap.put("lendTime", pi::getLendTime);
            supplierMap.put("lendTimeUnit", pi::getLendTimeUnit);
            supplierMap.put("lendPermissionsList", () -> {
                String lendPermissions = pi.getLendPermissions();
                if (StringUtils.isBlank(lendPermissions)) {
                    return new ArrayList<>();
                }

                List<String> results = new ArrayList<>();
                char[] charArray = lendPermissions.toCharArray();
                for (int i = 0; i < charArray.length; i++) {
                    results.add(charArray[i] + "");
                }

                return results;
            });

            supplierMap.put("approvalLendTime", pi::getApprovalLendTime);
            supplierMap.put("approvalLendTimeUnit", pi::getApprovalLendTimeUnit);
            supplierMap.put("approvalLendPermissionsList", () -> {
                String approvalLendPermissions = pi.getApprovalLendPermissions();
                if (StringUtils.isBlank(approvalLendPermissions)) {
                    return new ArrayList<>();
                }

                List<String> results = new ArrayList<>();
                char[] charArray = approvalLendPermissions.toCharArray();
                for (int i = 0; i < charArray.length; i++) {
                    results.add(charArray[i] + "");
                }

                return results;
            });

            supplierMap.put("renewCanDownloadAttachment", pi::getRenewCanDownloadAttachment);
            supplierMap.put("renewCanReadAttachment", pi::getRenewCanReadAttachment);

            supplierMap.put("approvalCanDownloadAttachment", pi::getApprovalCanDownloadAttachment);
            supplierMap.put("approvalCanReadAttachment", pi::getApprovalCanReadAttachment);

            supplierMap.put("comment", pi::getComment);
            supplierMap.put("commentList", () -> {
                String comment = pi.getComment();
                if (StringUtils.isBlank(comment)) {
                    return new ArrayList<>();
                }

                return comment.replace("\r\n", "#zd-zd#").replace("\n", "#zd-zd#").split("#zd-zd#");
            });
        }
    },

    zddmsworkflowentitiesSSTask {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            SSTask task = (SSTask) object;

            supplierMap.put("id", task::getId);
            supplierMap.put("assignee", task::getAssignee);
            supplierMap.put("assigneeFullname", task::getAssigneeFullname);
            supplierMap.put("status", task::getStatus);
            supplierMap.put("nodeId", task::getNodeId);
            supplierMap.put("nodeName", task::getNodeName);
            supplierMap.put("piId", task::getPiId);
            supplierMap.put("piCreator", task::getPiCreator);
            supplierMap.put("piCreatorFullname", task::getPiCreatorFullname);
            supplierMap.put("piName", task::getPiName);
            supplierMap.put("resourceName", task::getResourceName);
            supplierMap.put("pdType", task::getPdType);
            supplierMap.put("endDate", () -> ZDDateUtils.format(task.getEndDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
            supplierMap.put("comment", task::getComment);
            supplierMap.put("commentList", () -> {
                String comment = task.getComment();
                if (StringUtils.isBlank(comment)) {
                    return new ArrayList<>();
                }

                return comment.replace("\r\n", "#zd-zd#").replace("\n", "#zd-zd#").split("#zd-zd#");
            });

            supplierMap.put("creator", task::getCreator);
            supplierMap.put("creationDate", () -> ZDDateUtils.format(task.getCreationDate(), ObjectMapperUtils.dateFormatStrWithHHmm));

            supplierMap.put("sourceUsername", task::getSourceUsername);
            supplierMap.put("sourceUserFullname", task::getSourceUserFullname);
        }
    },

    zddmsworkflowentitiesSSCarbonCopy {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName,
                 Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            SSCarbonCopy carbonCopy = (SSCarbonCopy) object;

            supplierMap.put("id", carbonCopy::getId);
            supplierMap.put("assignee", carbonCopy::getAssignee);
            supplierMap.put("assigneeFullname", carbonCopy::getAssigneeFullname);
            supplierMap.put("status", carbonCopy::getStatus);
            supplierMap.put("nodeId", carbonCopy::getNodeId);
            supplierMap.put("nodeName", carbonCopy::getNodeName);
            supplierMap.put("piId", carbonCopy::getPiId);
            supplierMap.put("piCreator", carbonCopy::getPiCreator);
            supplierMap.put("piCreatorFullname", carbonCopy::getPiCreatorFullname);
            supplierMap.put("piName", carbonCopy::getPiName);
            supplierMap.put("resourceName", carbonCopy::getResourceName);
            supplierMap.put("pdType", carbonCopy::getPdType);
            supplierMap.put("endDate", () -> ZDDateUtils.format(carbonCopy.getEndDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
            supplierMap.put("comment", carbonCopy::getComment);
            supplierMap.put("commentList", () -> {
                String comment = carbonCopy.getComment();
                if (StringUtils.isBlank(comment)) {
                    return new ArrayList<>();
                }

                return comment.replace("\r\n", "#zd-zd#").replace("\n", "#zd-zd#").split("#zd-zd#");
            });

            supplierMap.put("creator", carbonCopy::getCreator);
            supplierMap.put("creationDate", () -> ZDDateUtils.format(carbonCopy.getCreationDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
        }
    },

    zddmsentitiesInstantMessage {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName, Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            InstantMessage instantMessage = (InstantMessage) object;

            supplierMap.put("id", instantMessage::getId);
            supplierMap.put("creator", instantMessage::getCreator);
            supplierMap.put("msg", instantMessage::getMsg);
            supplierMap.put("title", () -> TextUtils.filterContent(instantMessage.getTitle()));
            supplierMap.put("target", instantMessage::getTarget);
            supplierMap.put("targetFullname", instantMessage::getTargetFullname);
            supplierMap.put("sender", instantMessage::getSender);
            supplierMap.put("senderFullname", instantMessage::getSenderFullname);
            supplierMap.put("received", instantMessage::isReceived);
            supplierMap.put("type", instantMessage::getType);
            supplierMap.put("creationDate", instantMessage::getCreationDate);
        }
    },

    zddmsentitiesDocumentLog {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName, Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            DocumentLog documentLog = (DocumentLog) object;

            supplierMap.put("id", documentLog::getId);
            supplierMap.put("operator", documentLog::getOperator);
            supplierMap.put("document", documentLog::getDocumentFilename);
            supplierMap.put("folder", documentLog::getDocumentDisplayFolderWithoutLink);
            supplierMap.put("ip", documentLog::getIp);
            supplierMap.put("creationDate", () -> ZDDateUtils.format(documentLog.getCreationDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
            supplierMap.put("msg", documentLog::getMsg);
        }
    },

    zddmsentitiesFolderLog {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName, Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            FolderLog folderLog = (FolderLog) object;

            supplierMap.put("id", folderLog::getId);
            supplierMap.put("operator", () -> folderLog.getOperatorFullname() + " (" + folderLog.getOperator() + ")");
            supplierMap.put("folder", () -> TextUtils.filterContent(folderLog.getFolder().getDisplayFolder()));
            supplierMap.put("ip", folderLog::getIp);
            supplierMap.put("creationDate", () -> ZDDateUtils.format(folderLog.getCreationDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
            supplierMap.put("msg", folderLog::getMsg);
        }
    },

    zdrecordentitiesRecordLog {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName, Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            RecordLog recordLog = (RecordLog) object;

            supplierMap.put("id", recordLog::getId);
            supplierMap.put("operator", recordLog::getOperator);
            supplierMap.put("fullname", recordLog::getFullname);
            supplierMap.put("mainDisplay", recordLog::getMainDisplay);
            supplierMap.put("sn", recordLog::getSN);
            supplierMap.put("folder", () -> TextUtils.filterContent(recordLog.getFolder().getDisplayFolder()));
            supplierMap.put("ip", recordLog::getIp);
            supplierMap.put("creationDate", () -> ZDDateUtils.format(recordLog.getCreationDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
            supplierMap.put("msg", recordLog::getMsg);
        }
    },

    zdrecordentitiesRecFolderLog {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName, Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            RecFolderLog recFolderLog = (RecFolderLog) object;

            supplierMap.put("id", recFolderLog::getId);
            supplierMap.put("operator", recFolderLog::getOperator);
            supplierMap.put("folder", () -> TextUtils.filterContent(recFolderLog.getRecFolder().getDisplayFolder()));
            supplierMap.put("ip", recFolderLog::getIp);
            supplierMap.put("creationDate", () -> ZDDateUtils.format(recFolderLog.getCreationDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
            supplierMap.put("msg", recFolderLog::getMsg);
        }
    },

    zddmsentitiesLogMessage {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName, Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            LogMessage logMessage = (LogMessage) object;

            supplierMap.put("id", logMessage::getId);
            supplierMap.put("operator", logMessage::getOperator);
            supplierMap.put("displayLevel", logMessage::getDisplayLevel);
            supplierMap.put("ip", logMessage::getIp);
            supplierMap.put("creationDate", () -> ZDDateUtils.format(logMessage.getCreationDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
            supplierMap.put("msg", logMessage::getMsg);
        }
    },

    zdrecordentitiesRecTempFolder {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName, Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            RecTempFolder recTempFolder = (RecTempFolder) object;

            supplierMap.put("folderId", recTempFolder::getFolderId);
            supplierMap.put("title", recTempFolder::getFileTitle);
            supplierMap.put("folder", recTempFolder::getPath);
            supplierMap.put("fileSn", recTempFolder::getFileNumber);
            supplierMap.put("dataId", recTempFolder::getDataId);
            supplierMap.put("tableName", recTempFolder::getTableName);
            supplierMap.put("type", recTempFolder::getType);
        }
    },

    zdrecordentitiesRecLending {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName, Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            RecLending recLending = (RecLending) object;

            supplierMap.put("id", recLending::getId);
            supplierMap.put("title", recLending::getDisplayRecord);
            supplierMap.put("creator", recLending::getCreatorFullname);
            supplierMap.put("receiver", () -> TextUtils.filterContent(recLending.getLendUser().getFullnameAndUsername()));
            supplierMap.put("creationDate", () -> ZDDateUtils.format(recLending.getCreationDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
            supplierMap.put("permission", recLending::getPermission);
            supplierMap.put("displayPerms", recLending::getDisplayPerms);
            supplierMap.put("type", recLending::getType);
            supplierMap.put("displayRecord", recLending::getDisplayRecord);
            supplierMap.put("disPlayType", recLending::getDisPlayType);
            supplierMap.put("status", () -> TextUtils.filterContent(recLending.getDisplayStatus(recLending.getStatus())));
            supplierMap.put("comment", recLending::getComment);
            supplierMap.put("receiveDate", () -> ZDDateUtils.format(recLending.getReceiveDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
            supplierMap.put("lendDate", () -> ZDDateUtils.format(recLending.getLendDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
            supplierMap.put("returnDate", () -> ZDDateUtils.format(recLending.getReturnDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
            supplierMap.put("renewCount", recLending::getRenewCount);
            supplierMap.put("remainHours", recLending::getRemainDaysAndHours);
            supplierMap.put("recId", recLending::getRecId);
            supplierMap.put("recFolderId", recLending::getRecFolderId);
            supplierMap.put("tableName", recLending::getTableName);
        }
    },

    zdrecordentitiesRecFolderLinkTable {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName, Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            RecFolderLinkTable recFolderLinkTable = (RecFolderLinkTable) object;

            supplierMap.put("id", recFolderLinkTable::getId);
            supplierMap.put("name", recFolderLinkTable::getName);
            supplierMap.put("creator", recFolderLinkTable::getCreator);
            supplierMap.put("creatorFullname", recFolderLinkTable::getCreatorFullname);
            supplierMap.put("permissions", recFolderLinkTable::getPermissions);
            supplierMap.put("creationDate", () -> ZDDateUtils.format(recFolderLinkTable.getCreationDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
            supplierMap.put("recFolderId", recFolderLinkTable::getRecFolderId);
            supplierMap.put("tableName", recFolderLinkTable::getTableName);

            supplierMap.put("advSearchParams", recFolderLinkTable::getAdvSearchParams);
            supplierMap.put("defaultSortField", recFolderLinkTable::getDefaultSortField);
            supplierMap.put("defaultSortType", recFolderLinkTable::getDefaultSortType);
            supplierMap.put("displayCols", recFolderLinkTable::getDisplayCols);
        }
    },

    zdrecordentitiesPrintTpl {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName, Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            PrintTpl printTpl = (PrintTpl) object;

            supplierMap.put("id", printTpl::getId);
            supplierMap.put("name", printTpl::getName);
            supplierMap.put("creator", printTpl::getCreator);
            supplierMap.put("creatorFullname", printTpl::getCreatorFullname);
            supplierMap.put("creationDate", () -> ZDDateUtils.format(printTpl.getCreationDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
            supplierMap.put("recFolderId", printTpl::getRecFolderId);
            supplierMap.put("tableName", printTpl::getTableName);
            supplierMap.put("numIndex", printTpl::getNumIndex);
        }
    },

    zddmsentitiesoauth2OAuth2Client {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName, Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            OAuth2Client oAuth2Client = (OAuth2Client) object;

            supplierMap.put("id", oAuth2Client::getId);
            supplierMap.put("clientId", oAuth2Client::getClientId);
            supplierMap.put("clientName", oAuth2Client::getClientName);
            supplierMap.put("creator", oAuth2Client::getCreator);
            supplierMap.put("creatorFullname", oAuth2Client::getCreatorFullname);
            supplierMap.put("creationDate", () -> ZDDateUtils.format(oAuth2Client.getCreationDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
            supplierMap.put("enabled", oAuth2Client::getEnabled);
            supplierMap.put("accessTokenValidity", oAuth2Client::getAccessTokenValidity);
            supplierMap.put("refreshTokenValidity", oAuth2Client::getRefreshTokenValidity);
            supplierMap.put("numIndex", oAuth2Client::getNumIndex);
        }
    },

    zddmsentitiesUserTask {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName, Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            UserTask usertask = (UserTask) object;

            supplierMap.put("id", usertask::getId);
            supplierMap.put("type", usertask::getType);
            supplierMap.put("status", usertask::getStatus);
            supplierMap.put("times", usertask::getTimes);
            supplierMap.put("msgMap", usertask::getMsgMap);
            supplierMap.put("processMsgMap", usertask::getProcessMsgMap);
            supplierMap.put("errorMsgMap", usertask::getErrorMsgMap);
            supplierMap.put("creationDate", () -> ZDDateUtils.format(usertask.getCreationDate(), ObjectMapperUtils.dateFormatStrWithHHmm));
            supplierMap.put("lastHandleDate", () -> ZDDateUtils.format(usertask.getLastHandleDate(), ObjectMapperUtils.dateFormatStrWithHHmmss));

            UserTaskUtils.putUserTaskMap(supplierMap, usertask);
        }
    },

    zdrecordentitiesRecAutoRemind {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName, Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            RecAutoRemind recAutoRemind = (RecAutoRemind) object;

            supplierMap.put("id", recAutoRemind::getId);
            supplierMap.put("colName", recAutoRemind::getColName);
            supplierMap.put("remindStartDateColName", recAutoRemind::getRemindStartDateColName);
            supplierMap.put("remindEndDateColName", recAutoRemind::getRemindEndDateColName);
            supplierMap.put("remindType", recAutoRemind::getRemindType);
            supplierMap.put("title", recAutoRemind::getTitle);
        }
    },

    zdrecordentitiesRecRemind {
        @Override
        void run(ObjectMapper objectMapper, Object object, String actionName, Map<String, Supplier<Object>> supplierMap, Map<String, Object> props) {
            RecRemind recRemind = (RecRemind) object;

            UserService userService = ZDUtils.getBean(UserService.class);
            User remindUser = userService.getUserByUsername(recRemind.getRemindUsername());

            supplierMap.put("id", recRemind::getId);
            supplierMap.put("remindDate", recRemind::getRemindDate);
            supplierMap.put("remindFullnameAndUsername", () ->
                    remindUser != null ? remindUser.getFullnameAndUsername() : "");
            supplierMap.put("reminded", recRemind::isReminded);
            supplierMap.put("comment", recRemind::getComment);
            supplierMap.put("remainDays", recRemind::getRemainDays);
        }
    };

    abstract void run(ObjectMapper objectMapper, Object object, String actionName,
                      Map<String, Supplier<Object>> supplierMap, Map<String, Object> props);
}
