package zd.dms.repositories.user.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.LogMessage;
import zd.dms.entities.OU;
import zd.dms.repositories.user.OuRepository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OuRepositoryDaoImpl extends BaseRepositoryDaoImpl<OU, String> implements OuRepository {

    public OuRepositoryDaoImpl(Class<OU> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public OU getOUByUserId(String userId) {
        return findSingleObject("from OU where userId = ?1", userId);
    }

    @Override
    public OU getOUBySessionId(String sessionId) {
        return findSingleObject("from OU where sessionId = ?1", sessionId);
    }

    @Override
    public void deleteAll() {
        executeUpdate("delete from OU");
    }

    @Override
    public void deleteByUserId(String userId) {
        executeUpdate("delete from OU where userId = ?1", userId);
    }

    @Override
    public int getOUCount() {
        return NumberUtils.toInt(findObject("select count(*) from OU") + "");
    }

    @Override
    public void deleteByUserIdAndSessionId(String userId, String sessionId) {
        executeUpdate("delete from OU where userId = ? and sessionId = ?",
                userId, sessionId);
    }
}
