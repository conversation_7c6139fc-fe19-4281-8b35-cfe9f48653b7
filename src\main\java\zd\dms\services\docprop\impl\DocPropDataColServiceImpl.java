//                    佛祖保佑       永无BUG
//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                           O\  =  /O
//                        ____/`---'\____
//                      .'  \\|     |//  `.
//                     /  \\|||  :  |||//  \
//                    /  _||||| -:- |||||-  \
//                    |   | \\\  -  /// |   |
//                    | \_|  ''\---/''  |   |
//                    \  .-\__  `-`  ___/-. /
//                  ___`. .'  /--.--\  `. . __
//               ."" '<  `.___\_<|>_/___.'  >'"".
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |
//              \  \ `-.   \_ __\ /__ _/   .-` /  /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
package zd.dms.services.docprop.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.dms.entities.DocPropDataCol;
import zd.dms.entities.Folder;
import zd.dms.repositories.docprop.DocPropDataColRepository;
import zd.dms.services.docprop.DocPropDataColService;
import zd.dms.services.document.FolderService;

import java.util.LinkedList;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class DocPropDataColServiceImpl extends BaseJpaServiceImpl<DocPropDataCol, Long> implements DocPropDataColService {

    private final DocPropDataColRepository docPropDataColRepository;

    private final FolderService folderService;

    @Override
    public BaseRepository<DocPropDataCol, Long> getBaseRepository() {
        return docPropDataColRepository;
    }

    @Override
    public int getNumByColName(String colName) {
        return docPropDataColRepository.getNumByColName(colName);
    }

    @Override
    public void save(DocPropDataCol dataCol) {
        docPropDataColRepository.save(dataCol);
    }

    @Override
    public List<DocPropDataCol> getColumns() {
        return docPropDataColRepository.getColumns();
    }

    @Override
    public DocPropDataCol getPropDataColById(long id) {
        return docPropDataColRepository.get(id);
    }

    @Override
    public void updateDataCol(DocPropDataCol dataCol) {
        docPropDataColRepository.update(dataCol);
    }

    @Override
    public void deleteDataColById(long colId) {
        docPropDataColRepository.deleteById(colId);
    }

    @Override
    public DocPropDataCol getDocPropDataColByName(String colName) {
        return docPropDataColRepository.getDocPropDataColByName(colName);
    }

    @Override
    public List<String> getDocPropColByFolderId(long FolderId) {
        List<String> dateCol = new LinkedList<String>();
        Folder folder = folderService.getById(FolderId);
        if (folder == null) {
            return dateCol;
        }

        String docProperty = folder.getDocProperty();
        if (StringUtils.isBlank(docProperty)) {
            return dateCol;
        }
        String[] docPropArray = docProperty.split("\r\n");
        for (String propName : docPropArray) {
            DocPropDataCol dataCol = getDocPropDataColByName(propName);
            if (dataCol != null && ("date".equals(dataCol.getColType()) || "datetime".equals(dataCol.getColType()))) {
                dateCol.add(propName);
            }
        }
        return dateCol;
    }
}
