package zd.base.config.ftl;

import freemarker.cache.TemplateLoader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;

@Slf4j
@RequiredArgsConstructor
public class OSSTemplateLoader implements TemplateLoader {

    @Override
    public Object findTemplateSource(String name) throws IOException {
        String templatePath = name;
        return templatePath;
    }

    @Override
    public long getLastModified(Object templateSource) {
        return 0;
    }

    @Override
    public Reader getReader(Object templateSource, String encoding) throws IOException {
        String templatePath = (String) templateSource;
        return new InputStreamReader(new FileInputStream(templatePath), encoding != null ? encoding : StandardCharsets.UTF_8.name());

    }

    @Override
    public void closeTemplateSource(Object templateSource) throws IOException {
        // OSS客户端会自动关闭流，这里不需要额外操作
    }
}
