package zd.dms.controllers.user;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.BaseController;
import zd.base.service.BaseJpaService;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapUtils;
import zd.base.utils.model.ObjectMapperUtils;
import zd.base.utils.validate.ValidateUtils;
import zd.dms.entities.Group;
import zd.dms.services.user.GroupService;
import zd.dms.utils.user.GroupUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "GroupController", description = "部门Controller")
@RequestMapping("/group")
public class GroupController extends BaseController<Group, Long> {

    private final GroupService groupService;

    @Override
    public BaseJpaService<Group, Long> getBaseJpaService() {
        return groupService;
    }

    @Operation(summary = "获取子部门")
    @GetMapping("/list/{parentId}")
    @ZDLog("获取子部门")
    public JSONResultUtils<Object> listGroups(@PathVariable long parentId) {
        List<Group> resultsGroups = groupService.getChildren(parentId);

        GroupUtils.setChildrenCount(resultsGroups, parentId);

        return successData(ObjectMapperUtils.toMapList(resultsGroups, "list"));
    }

    @Operation(summary = "加载选择部门的目录树")
    @PostMapping("/getSelectedGroupTree")
    @ZDLog("加载选择部门的目录树")
    public JSONResultUtils<Object> getSelectedGroupTree(@RequestBody Map<String, Object> params) {
        boolean selectUserEnabled = MapUtils.getBooleanValue(params, "selectUserEnabled", false);
        boolean selectGroupEnabled = MapUtils.getBooleanValue(params, "selectGroupEnabled", false);
        boolean selectPositionEnabled = MapUtils.getBooleanValue(params, "selectPositionEnabled", false);

        Map<String, Object> props = new HashMap<>();
        props.put("selectGroupEnabled", selectGroupEnabled);

        String parentIdStr = MapUtils.getString(params, "parentId", "");
        String parentCode = "";
        if (StringUtils.isNotBlank(parentIdStr)) {
            parentCode = parentIdStr.replace("g-", "");
        }

        if (StringUtils.isNotBlank(parentCode) && !"0".equals(parentCode)) {
            Group parentGroup = groupService.getGroupByCode(parentCode);
            if (parentGroup == null) {
                return successData(new ArrayList<>());
            }

            List<Group> resultsGroups = parentGroup.getChildren();

            // 获取所有子部门数据, 使用一个sql一次性查询，防止出现多个sql
            List<String> childrenCodes = resultsGroups.stream().map(Group::getCode).toList();
            Map<String, Long> childCount = groupService.getChildCount(childrenCodes);

            // 封装每个部门子部门数量
            resultsGroups.forEach(g -> {
                String code = g.getCode();
                Long count = childCount.get(code);
                if (count == null) {
                    count = 0L;
                }

                if (count <= 0) {
                    if (selectPositionEnabled) {
                        List<Map<String, Object>> positionsTreeList = GroupUtils.getPositionsTreeList(g);
                        if (CollectionUtils.isNotEmpty(positionsTreeList)) {
                            count = 1L;
                        }
                    }

                    if (count <= 0 && selectUserEnabled) {
                        count = (long) g.getUsers().size();
                    }
                }


                g.setChildCount(count);
            });

            List<Map<String, Object>> resultsData = ObjectMapperUtils.toMapList(resultsGroups, "selectedJson", props);
            if (selectPositionEnabled) {
                resultsData.addAll(GroupUtils.getPositionsTreeList(parentGroup));
            }

            if (selectUserEnabled) {
                resultsData.addAll(GroupUtils.getUsersTreeList(parentGroup));
            }

            return successData(resultsData);
        }

        List<Map<String, Object>> results = new ArrayList<>();
        List<String> selectedUserGroupIds = ZDMapUtils.getListStringValue(params, "selectedUserGroupIds");
        Map<String, Object> topGroupMap = GroupUtils.getTreeGroupsWithTopGroup(selectedUserGroupIds, selectUserEnabled, selectPositionEnabled, props);

        results.add(topGroupMap);
        boolean isWorkflow = MapUtils.getBooleanValue(params, "isWorkflow", false);
        if (isWorkflow) {
            results.addAll(GroupUtils.getWorkflowTreeList());
        }

        // 创建自动提醒选择提醒用户时添加特殊用户列表
        boolean isAutoRemind = MapUtils.getBooleanValue(params, "isAutoRemind", false);
        if (selectUserEnabled && isAutoRemind) {
            results.addAll(GroupUtils.getSpecialUsersList());
        }

        return successData(results);
    }

    @Operation(summary = "搜索选择部门的目录树")
    @PostMapping("/searchSelectedGroupTree")
    @ZDLog("搜索选择部门的目录树")
    public JSONResultUtils<Object> searchSelectedGroupTree(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> results = new ArrayList<>();

        String keywords = MapUtils.getString(params, "keywords", "");
        if (StringUtils.isBlank(keywords)) {
            return successData(results);
        }

        boolean selectGroupEnabled = MapUtils.getBooleanValue(params, "selectGroupEnabled", false);
        Map<String, Object> props = new HashMap<>();
        props.put("selectGroupEnabled", selectGroupEnabled);

        boolean selectUserEnabled = MapUtils.getBooleanValue(params, "selectUserEnabled", false);
        boolean selectPositionEnabled = MapUtils.getBooleanValue(params, "selectPositionEnabled", false);
        Map<String, Object> topGroupMap = GroupUtils.searchTreeGroups(keywords, selectUserEnabled, selectPositionEnabled, props);
        if (MapUtils.isNotEmpty(topGroupMap)) {
            results.add(topGroupMap);
        }

        return successData(results);
    }

    @Operation(summary = "获取部门的目录树")
    @PostMapping("/getDepartmentTree")
    @ZDLog("获取部门的目录树")
    public JSONResultUtils<Object> getDepartmentTree(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> results = new ArrayList<>();

        String keywords = MapUtils.getString(params, "keywords", "");

        boolean selectGroupEnabled = MapUtils.getBooleanValue(params, "selectGroupEnabled", true);
        Map<String, Object> props = new HashMap<>();
        props.put("selectGroupEnabled", selectGroupEnabled);

        Map<String, Object> topGroupMap = null;
        if (StringUtils.isBlank(keywords)) {
            topGroupMap = GroupUtils.getTreeTopGroup(props);
        } else {
            topGroupMap = GroupUtils.searchTreeGroups(keywords, false, false, props);
        }

        if (MapUtils.isNotEmpty(topGroupMap)) {
            results.add(topGroupMap);
        }

        return successData(results);
    }

    @Operation(summary = "获取选择部门的选择列表")
    @PostMapping("/getSelectedOptions")
    @ZDLog("获取选择部门的选择列表")
    public JSONResultUtils<Object> getSelectedOptions(@RequestBody Map<String, Object> params) {
        List<String> selectedUserGroupIds = ZDMapUtils.getListStringValue(params, "selectedUserGroupIds");
        List<Map<String, Object>> results = GroupUtils.getSelectedOptions(selectedUserGroupIds);

        return successData(results);
    }

    @Operation(summary = "创建部门")
    @PostMapping("/create")
    @ZDLog("创建部门")
    public JSONResultUtils<Object> create(@RequestBody Map<String, Object> params) {
        String code = MapUtils.getString(params, "code", "");
        ValidateUtils.isTrue(StringUtils.isNotBlank(code), "部门编码必填");

        String name = MapUtils.getString(params, "name", "");
        ValidateUtils.isTrue(StringUtils.isNotBlank(name), "部门名称必填");

        String parentCode = MapUtils.getString(params, "parentCode", "");
        ValidateUtils.isTrue(StringUtils.isNotBlank(parentCode), "请选择父部门");

        return groupService.createGroup(params);
    }

    @Operation(summary = "更新部门")
    @PostMapping("/update")
    @ZDLog("更新部门")
    public JSONResultUtils<Object> update(@RequestBody Map<String, Object> params) {
        String code = MapUtils.getString(params, "code", "");
        ValidateUtils.isTrue(StringUtils.isNotBlank(code), "请选择部门");

        return groupService.updateGroup(params);
    }

    @Operation(summary = "删除部门")
    @PostMapping("/delete/{id}")
    @ZDLog("删除部门")
    public JSONResultUtils<Object> delete(@PathVariable Long id, HttpServletRequest request) {
        ValidateUtils.isTrue(id != null && id > 0, "请选择部门");
        Group group = groupService.getGroupById(id);
        if (group == null) {
            return JSONResultUtils.error("部门不存在");
        }

        Group parent = group.getParent();
        Long parentId = parent.getId();

        groupService.deleteGroup(group);

        Map<String, Object> resultsMap = new HashMap<>();
        resultsMap.put("parentId", parentId);

        return JSONResultUtils.successWithData(resultsMap);
    }
}
