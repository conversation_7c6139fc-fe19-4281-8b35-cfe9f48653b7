package zd.dms.entities;

import jakarta.persistence.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.base.utils.ZDUtils;
import zd.dms.services.user.UserService;

import java.util.Date;


/**
 * DocumentVersion Domain Object
 *
 * <AUTHOR>
 * @version $Revision$, $Date$
 */
@Entity
@Table(name = "docversion")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class DocumentVersion extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = 7278484277354258018L;

    /**
     * 版本号
     */
    @Index(name = "i_dver_vn")
    private int versionNumber;

    /**
     * 作者名，使用全名
     */
    @Column(nullable = false)
    private String creatorFullname;

    /**
     * 内容
     */
    @Column(name = "comment1", length = Length.LOB_DEFAULT)
    private String comment;

    /**
     * 文件内容
     */
    @Column(length = Length.LOB_DEFAULT)
    private String content;

    /**
     * file hash
     */
    private String hash;

    private String docId;

    /**
     * 创建时间
     */
    @Index(name = "i_dver_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 版本
     */
    private String docVersion;

    /**
     * 默认构造器
     */
    public DocumentVersion() {
        super();
        creationDate = new Date();
    }

    public String getDisplayRev() {
        return StringUtils.leftPad(String.valueOf(versionNumber), 2, "0");
    }

    /**
     * @see Object#equals(Object)
     */
    @Override
    public boolean equals(Object o) {
        if (!(o instanceof DocumentVersion)) {
            return false;
        }

        final DocumentVersion dn = (DocumentVersion) o;

        return new EqualsBuilder().appendSuper(super.equals(dn)).append(creatorFullname, dn.getCreatorFullname())
                .append(versionNumber, dn.getVersionNumber()).append(docId, dn.getDocId())
                .append(comment, dn.getComment()).isEquals();
    }

    /**
     * @see Object#hashCode()
     */
    @Override
    public int hashCode() {
        return new HashCodeBuilder().appendSuper(super.hashCode()).append(creatorFullname).append(versionNumber)
                .append(docId).append(comment).toHashCode();
    }

    public String getCreatorAndFullName() {
        if (StringUtils.isNotBlank(creator)) {
            User userByUsername = ZDUtils.getBean(UserService.class).getUserByUsername(creator);
            if (userByUsername != null) {
                return userByUsername.getFullname() + "(" + creator + ")";
            }
        }

        return creator;
    }

    public String getCreatorFullname() {
        return creatorFullname;
    }

    public void setCreatorFullname(String creatorFullname) {
        this.creatorFullname = creatorFullname;
    }

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public int getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(int versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getDocVersion() {
        return docVersion;
    }

    public void setDocVersion(String docVersion) {
        this.docVersion = docVersion;
    }
}
