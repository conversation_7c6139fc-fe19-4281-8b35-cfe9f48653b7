package zd.base.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import zd.dms.utils.JSONUtils;

import java.text.ParseException;
import java.util.*;

@Slf4j
public class ZDMapUtils {

    public static Map<String, Object> getMap(String key, String value) {
        Map<String, Object> map = new HashMap<>();
        if (StringUtils.isBlank(key)) {
            return map;
        }

        map.put(key, value);
        return map;
    }

    public static Map<String, Object> getMapValue(Map map, String key) {
        if (MapUtils.isEmpty(map)) {
            return new HashMap<>();
        }

        Object o = map.get(key);
        if (o instanceof Map) {
            return (Map<String, Object>) o;

        }

        return new HashMap<>();
    }

    public static List<Long> getListLongValue(Map map, String key) {
        if (MapUtils.isEmpty(map)) {
            return new ArrayList<Long>();
        }

        Object o = map.get(key);
        if (o == null) {
            return new ArrayList<Long>();
        }

        if (o instanceof List) {
            List list = (List<Long>) o;
            List<Long> results = new ArrayList();
            for (Object obj : list) {
                if (obj == null) {
                    results.add(null);
                } else {
                    results.add(NumberUtils.toLong(obj + "", 0L));
                }
            }

            return results;
        }

        return new ArrayList<Long>();
    }

    public static List<String> getListStringValue(Map map, String key) {
        if (MapUtils.isEmpty(map)) {
            return new ArrayList<String>();
        }

        Object o = map.get(key);
        if (o == null) {
            return new ArrayList<String>();
        }

        if (o instanceof List) {
            List list = (List<String>) o;
            List<String> results = new ArrayList();
            for (Object obj : list) {
                if (obj == null) {
                    results.add(null);
                } else {
                    results.add(obj + "");
                }
            }

            return results;
        }

        return new ArrayList<String>();
    }

    public static List<Integer> getListIntValue(Map map, String key) {
        if (MapUtils.isEmpty(map)) {
            return new ArrayList<Integer>();
        }

        Object o = map.get(key);
        if (o == null) {
            return new ArrayList<Integer>();
        }

        if (o instanceof List) {
            List list = (List<Integer>) o;
            List<Integer> results = new ArrayList();
            for (Object obj : list) {
                if (obj == null) {
                    results.add(null);
                } else {
                    results.add(NumberUtils.toInt(obj + "", 0));
                }
            }

            return results;
        }

        return new ArrayList<Integer>();
    }

    public static List<Map<String, Object>> getListMapValue(Map map, String key) {
        if (MapUtils.isEmpty(map)) {
            return new ArrayList<>();
        }

        Object o = map.get(key);
        if (o == null) {
            return new ArrayList<>();
        }

        if (o instanceof List) {
            return (List<Map<String, Object>>) o;
        }

        return new ArrayList<>();
    }

    public static Date getDate(Map map, String key) {
        if (MapUtils.isEmpty(map)) {
            return null;
        }

        Object o = map.get(key);
        if (o == null) {
            return null;
        }

        if (o instanceof Date) {
            return (Date) o;
        }

        if (o instanceof String) {
            try {
                return DateUtils.parseDate(o + "", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM-dd");
            } catch (ParseException e) {
                log.error("getDate parse error", e);
            }

            return null;
        }

        return null;
    }

    public static Map<String, Object> getMapVlaue(Map map, String key) {
        if (MapUtils.isEmpty(map)) {
            return null;
        }

        Object o = map.get(key);
        if (o == null) {
            return null;
        }

        if (o instanceof Map) {
            return (Map<String, Object>) o;
        }

        if (o instanceof String) {
            return JSONUtils.parseObject(0 + "", Map.class);
        }

        return null;
    }

    public static void setMapList(Map<String, List<Map<String, Object>>> srcMap, String key, Map<String, Object> valueMap) {
        if (srcMap == null || StringUtils.isBlank(key) || MapUtils.isEmpty(valueMap)) {
            return;
        }


        List<Map<String, Object>> maps = srcMap.computeIfAbsent(key, k -> new ArrayList<>());
        maps.add(valueMap);
    }

    public static void setMapList(Map<String, List<String>> srcMap, String key, String value) {
        if (srcMap == null || StringUtils.isBlank(key) || StringUtils.isBlank(value)) {
            return;
        }


        List<String> maps = srcMap.computeIfAbsent(key, k -> new ArrayList<>());
        if (maps.contains(value)) {
            return;
        }

        maps.add(value);
    }

    public static void setMapList(Map<String, List<Long>> srcMap, String key, long value) {
        if (srcMap == null) {
            return;
        }


        List<Long> maps = srcMap.computeIfAbsent(key, k -> new ArrayList<>());
        if (maps.contains(value)) {
            return;
        }

        maps.add(value);
    }

    public static boolean isContains(List<String> list, String value) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }

        return list.contains(value);
    }
}
