package zd.base.utils.redis;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import zd.base.utils.system.SpringUtils;

import java.util.concurrent.TimeUnit;

public class ZDRedisLockUtils {

    private static final RedissonClient redissonClient = SpringUtils.getBean(RedissonClient.class);


    /**
     * 同步锁
     *
     * @param lockKey
     * @param time
     * @param timeUnit
     * @return
     */
    public static RLock lock(String lockKey, long time, TimeUnit timeUnit) {
        if (!ZDRedisUtils.REDIS_ENABLED) {
            return null;
        }

        RLock lock = getLock(lockKey);
        if (lock != null) {
            lock.lock(time, timeUnit);
        }

        return lock;
    }

    /**
     * 尝试获取锁，失败时返回false
     *
     * @param lock
     * @param timeSeconds
     * @return
     */
    public static boolean tryLock(RLock lock, long timeSeconds) {
        if (!ZDRedisUtils.REDIS_ENABLED) {
            return true;
        }

        if (lock == null) {
            return true;
        }

        try {
            return lock.tryLock(5, timeSeconds, TimeUnit.SECONDS);
        } catch (Exception e) {

        }

        return true;
    }

    public static RLock lock(String lockKey) {
        if (!ZDRedisUtils.REDIS_ENABLED) {
            return null;
        }

        RLock lock = getLock(lockKey);
        if (lock != null) {
            lock.lock();
        }

        return lock;
    }

    public static RLock getLock(String lockKey) {
        if (!ZDRedisUtils.REDIS_ENABLED) {
            return null;
        }

        return redissonClient.getLock(lockKey);
    }

    public static void unlock(RLock lock) {
        if (!ZDRedisUtils.REDIS_ENABLED) {
            return;
        }

        if (lock == null) {
            return;
        }

        if (!lock.isHeldByCurrentThread()) {
            return;
        }

        lock.unlock();
    }
}
