package zd.base.config.ftl;

import freemarker.cache.TemplateLoader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.services.s3.model.HeadObjectResponse;
import zd.base.utils.system.PropsUtils;
import zd.base.utils.system.SystemInitUtils;
import zd.dms.utils.JSONUtils;
import zd.dms.utils.oss.common.OSSCommonUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
public class CommonFileTemplateLoader implements TemplateLoader {

    @Override
    public Object findTemplateSource(String name) throws IOException {
        try {
            String basePath = PropsUtils.getProps("ftl.dir.path", SystemInitUtils.getHomeDir() + "/printftl/");
            String ossPath = PropsUtils.getProps("ftl.oss.path", "home/printftl/");

            String filename = name;
            // 1. 传入的参数只是文件名
            if (!name.startsWith("{") || !name.endsWith("}.ftl")) {
                // 1.1 检查本地是否存在对应模板文件
                Path templatePath = Paths.get(basePath, filename);
                log.info("Finding template: {}", templatePath);
                if (Files.exists(templatePath)) {
                    log.info("Finding template: {} results:{}", templatePath, "true");
                    return "file:" + templatePath;
                }

                // 1.2 检查S3是否存在对应模板文件
                boolean exists = OSSCommonUtils.exists(ossPath + filename);
                if (exists) {
                    return "oss:" + ossPath + filename;
                }

                return null;
            }

            // 2. 传入所有参数
            if (name.endsWith(".ftl")) {
                name = name.substring(0, name.length() - 4);
            }
            Map<String, Object> params = JSONUtils.parseObject(name, Map.class, new HashMap<String, Object>());

            String type = MapUtils.getString(params, "type", "");
            if ("otherTpl".equals(type)) {
                String otherPrintType = MapUtils.getString(params, "otherPrintType", "");
                filename = "print" + otherPrintType + ".ftl";
            } else {
                filename = "print" + type + ".ftl";
            }

            // 2.1 检查本地是否存在对应数据目录模板文件
            String recFolderPath = "";
            long recFolderId = MapUtils.getLongValue(params, "recFolderId", 0);
            if (recFolderId > 0) {
                recFolderPath = recFolderId + "/" + filename;
                Path templatePath = Paths.get(basePath, recFolderPath);
                log.info("Finding template: {}", templatePath);
                if (Files.exists(templatePath)) {
                    log.info("Finding template: {} results:{}", templatePath, "true");
                    return "file:" + templatePath;
                }
            }

            // 2.2 检查本地是否存在对应数据表模板文件
            String tablePath = "";
            String tableName = MapUtils.getString(params, "tableName", "");
            if (StringUtils.isNotBlank(tableName)) {
                tablePath = tableName + "/" + filename;
                Path templatePath = Paths.get(basePath, tableName + "/" + filename);
                log.info("Finding template: {}", templatePath);
                if (Files.exists(templatePath)) {
                    log.info("Finding template: {} results:{}", templatePath, "true");
                    return "file:" + templatePath;
                }
            }

            // 2.3 检查本地是否存在对应模板文件
            Path templatePath = Paths.get(basePath, filename);
            log.info("Finding template: {}", templatePath);
            if (Files.exists(templatePath)) {
                log.info("Finding template: {} results:{}", templatePath, "true");
                return "file:" + templatePath;
            }

            // 2.4 检查S3是否存在对应数据目录模板文件
            if (StringUtils.isNotBlank(recFolderPath)) {
                boolean exists = OSSCommonUtils.exists(ossPath + recFolderPath);
                if (exists) {
                    return "oss:" + ossPath + recFolderPath;
                }
            }

            // 2.5 检查S3是否存在对应数据表模板文件
            if (StringUtils.isNotBlank(tablePath)) {
                boolean exists = OSSCommonUtils.exists(ossPath + tablePath);
                if (exists) {
                    return "oss:" + ossPath + tablePath;
                }
            }

            // 2.6 检查S3是否存在对应模板文件
            boolean exists = OSSCommonUtils.exists(ossPath + filename);
            if (exists) {
                return "oss:" + ossPath + filename;
            }

            return null;
        } catch (
                Exception e) {
            log.error("Error finding template: " + name, e);
            return null;
        }
    }

    @Override
    public long getLastModified(Object templateSource) {
        try {
            String filePath = templateSource + "";
            if (filePath.startsWith("file:")) {
                filePath = filePath.substring(5);
                Path templatePath = Paths.get(filePath);
                return Files.getLastModifiedTime(templatePath).toMillis();
            }

            if (filePath.startsWith("oss:")) {
                filePath = filePath.substring(4);
                long epochMilli = OSSCommonUtils.getLastModifiedEpochMilli(filePath);
                if (epochMilli > 0) {
                    return epochMilli;
                }
            }

            return -1;
        } catch (Exception e) {
            return -1;
        }
    }

    @Override
    public Reader getReader(Object templateSource, String encoding) throws IOException {
        String filePath = templateSource + "";

        log.info("getReader template: {} ", filePath);
        try {
            if (filePath.startsWith("file:")) {
                filePath = filePath.substring(5);
                return new InputStreamReader(
                        Files.newInputStream(Paths.get(filePath)),
                        encoding != null ? encoding : StandardCharsets.UTF_8.name()
                );
            }

            if (filePath.startsWith("oss:")) {
                filePath = filePath.substring(4);
                return new InputStreamReader(
                        OSSCommonUtils.downloadInputStreamFromOSS(filePath),
                        encoding != null ? encoding : StandardCharsets.UTF_8.name()
                );
            }

            throw new IOException("Could not load template: " + filePath);
        } catch (Exception e) {
            log.error("Error getting template reader: " + filePath, e);
            throw new IOException("load error template: " + filePath, e);
        }
    }

    @Override
    public void closeTemplateSource(Object templateSource) {
        // 不需要特别的清理操作，Java会自动关闭文件流
    }
}
