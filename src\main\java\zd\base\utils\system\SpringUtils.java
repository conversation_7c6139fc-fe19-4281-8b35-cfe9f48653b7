package zd.base.utils.system;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class SpringUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringUtils.applicationContext = applicationContext;
    }

    public static Object getBean(String beanName) {
        return applicationContext.getBean(beanName);
    }

    public static <T> T getBean(Class<T> beanClass) {
        return applicationContext.getBean(beanClass);
    }

    public static <T> T getBean(String beanName, Class<T> beanClass) {
        return applicationContext.getBean(beanName, beanClass);
    }

    public static Environment getEnvironment() {
        return SpringUtils.getBean(Environment.class);
    }

    public static String getProps(String key) {
        return getProps(key, "");
    }

    public static String getProps(String key, String defaultValue) {
        return StringUtils.defaultIfEmpty(getEnvironment().getProperty(key), defaultValue);
    }

    public static int getIntProps(String key) {
        return getIntProps(key, 0);
    }

    public static int getIntProps(String key, int defaultValue) {
        return NumberUtils.toInt(getProps(key), defaultValue);
    }

    public static long getLongProps(String key) {
        return getLongProps(key, 0L);
    }

    public static long getLongProps(String key, long defaultValue) {
        return NumberUtils.toLong(getProps(key), defaultValue);
    }

    public static boolean getBooleanProps(String key) {
        return getBooleanProps(key, false);
    }

    public static boolean getBooleanProps(String key, boolean defaultValue) {
        String propsValue = getProps(key);
        if (StringUtils.isBlank(propsValue)) {
            return defaultValue;
        }

        return BooleanUtils.toBoolean(propsValue);
    }
}