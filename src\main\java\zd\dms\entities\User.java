package zd.dms.entities;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.lucene.analysus.ja.dict.ModeDefinition;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.entities.AbstractEntity;
import zd.base.entities.PropertyAware;
import zd.base.utils.model.ObjectMapperUtils;
import zd.base.utils.system.PropsUtils;
import zd.base.utils.system.SpringUtils;
import zd.dms.io.ZDIOUtils;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.position.PositionUtils;
import zd.dms.services.security.PasswordUtils;
import zd.dms.services.security.RoleUtils;
import zd.dms.services.user.GroupService;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.services.user.UserService;
import zd.dms.utils.PropertyUtils;
import zd.dms.utils.TextUtils;
import zd.dms.utils.WebUtils;
import zd.dms.utils.db.ZDJoinTableGroupUserUtils;

import java.text.DecimalFormat;
import java.util.*;

/**
 * User Domain Object
 *
 * <AUTHOR>
 * @version $Revision$
 */
@Entity
@Table(name = "tdms_user")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class User extends AbstractEntity implements PropertyAware {

    private static final Logger log = LoggerFactory.getLogger(User.class);

    /**
     * Serial
     */
    private static final long serialVersionUID = 5006587923583141491L;

    /**
     * Decimal Format
     */
    private static final DecimalFormat df = new DecimalFormat("#####0.00");

    /**
     * 用户名，unique
     */
    @Column(unique = true, nullable = false)
    @Index(name = "i_user_username")
    private String username;

    /**
     * 全名
     */
    private String fullname;

    /**
     * 密码
     */
    @Column(nullable = false)
    private String password;

    /**
     * E-Mail
     */
    @Index(name = "i_user_email")
    private String email;

    /**
     * 员工编号
     */
    @Index(name = "i_user_code")
    private String code;

    /**
     * 移动电话
     */
    private String mobilePhone;

    private String officePhone;

    private String homePhone;

    /**
     * 登录IP限制
     */
    private String ipRestrict;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 从钉钉获取回来的userId
     */
    @Index(name = "i_user_dingUserId")
    private String dingUserId;

    /**
     * 备注
     */
    private String memo;

    private String theme;

    /**
     * 用户注册所用的IP地址
     */
    private String regIp;

    private Date lastLoginDate;

    private Integer autoLockMin;

    /**
     * 排序号
     */
    @Index(name = "i_user_numIndex")
    private Integer numIndex;

    /**
     * 上次登录的IP地址
     */
    private String lastIp;

    /**
     * 本账号是否可用
     */
    @Column(nullable = false)
    @Index(name = "i_user_enabled")
    private boolean enabled;

    @Index(name = "i_dingEmployeeId_cd")
    private String dingEmployeeId;

    @Index(name = "i_qywxId_cd")
    private String qywxId;

    @Index(name = "i_feishuId_cd")
    private String feishuId;

    @Transient
    private Map<String, String> propertiesMap;

    @Column(length = Length.LOB_DEFAULT)
    private String properties;

    @Column(length = Length.LOB_DEFAULT)
    private String positions;

    @Column(length = Length.LOB_DEFAULT)
    @Getter
    @Setter
    private String roles;

    @Transient
    private List<String> roleList;

    @Column(length = Length.LOB_DEFAULT)
    private String desktopSettings;

    /**
     * 用户合法性检查字段
     */
    private String vali;

    private String syncString;

    @Transient
    private String plainPassword;

    /**
     * 预留的userIdCode，用于用户系统对接使用
     */
    private String userIdCode;

    /**
     * 创建时间
     */
    @Index(name = "i_user_cd")
    @Column(nullable = false)
    private Date creationDate;

    // BaseboardSN
    private String bbsn;

    // CPUProcessorId
    private String cpid;

    // DiskSN
    private String dsn;

    // Mac
    private String mac;

    // 不验证客户端信息
    private Boolean notVerifyClient;

    // 客户端信息是否完善
    private Boolean clientInfoComplete;

    /**
     * 完成审批时: 作为流程发起人对象
     */
    @Transient
    private User sponsorUser;

    /**
     * 修改前用户名
     */
    @Transient
    private String oldUsername;

    /**
     * 用户移动设备的类型
     */
    private String deviceType;

    /**
     * 用户移动设备id(推送时使用)
     */
    private String deviceId;

    private Boolean mobileAccess;

    @Getter
    @Setter
    private Date expiredDate;

    @Transient
    @Setter
    private List<Group> groups;

    /**
     * 默认构造器
     */
    public User() {
        super();
        enabled = true;
        theme = "default";
        ipRestrict = "";
        creationDate = new Date();
    }

    /**
     * 构造器
     *
     * @param username 用户名
     * @param password 密码
     */
    public User(String username, String password) {
        this();
        this.username = username;
        this.password = password;
    }

    public List<Map<String, Object>> getPositionsList() {
        List<Position> userPositions = PositionUtils.getUserPositions(this);
        List<Map<String, Object>> positionsList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userPositions)) {
            for (Position positionObj : userPositions) {
                Map<String, Object> positionMap = new HashMap<>();
                positionMap.put("groupName", positionObj.getGroupName());
                positionMap.put("groupCode", positionObj.getGroupCode());
                positionMap.put("position", positionObj.getPosition());

                positionsList.add(positionMap);
            }
        }

        return positionsList;
    }

    public List<Group> getGroups() {
        if (groups == null) {
            GroupService groupService = SpringUtils.getBean(GroupService.class);
            groups = groupService.getGroupsByUserId(this.id);
        }

        return groups;
    }

    public List<Map<String, Object>> getGroupMaps() {
        getGroups();
        if (groups == null) {
            return new ArrayList<>();
        }

        return ObjectMapperUtils.toMapList(groups);
    }

    /**
     * 重新加载js与css, 修改js或css后，全部重新加载，文件为jscss-min.js, 任意配置一个字符串
     *
     * @return
     */
    public String getRefreshJsAndCss() {
        return PropsUtils.getProps("refreshJsAndCss");
    }

    public String getSecondaryAuthPassword() {
        return StringUtils.defaultIfBlank(getProperty("secondaryAuthPassword"), password);
    }

    /**
     * 获取用户的所有顶级部门，不包括所有部门
     *
     * @return 该用户的所有顶级部门
     */
    public Set<Group> getTopGroups() {
        Set<Group> result = new HashSet<Group>();
        for (Group g : getGroups()) {
            if (g == null) {
                continue;
            }

            if ("所有部门".equals(g.getName())) {
                result.add(g);
                continue;
            }

            Group parent = g.getParent();
            Group current = g;
            while (parent != null) {
                if ("所有部门".equals(parent.getName())) {
                    result.add(current);
                    break;
                }
                current = parent;
                parent = parent.getParent();
            }
        }
        return result;
    }

    /**
     * 用户是否已过期
     *
     * @return
     */
    public boolean isExpired() {
        if (expiredDate != null && !UserGroupUtils.ADMIN_USERNAME.equals(username)) {
            return new Date().getTime() > expiredDate.getTime();
        }

        return false;
    }

    public User getWorkflowAgentUser() {
        String workflowAgent = getProperty("workflowAgent");
        if (StringUtils.isBlank(workflowAgent)) {
            return null;
        }

        Map<String, Object> workflowAgentMap = com.alibaba.fastjson.JSONArray.parseObject(workflowAgent, Map.class);
        boolean workflowAgentEnable = MapUtils.getBoolean(workflowAgentMap, "workflowAgentEnable", false);
        String workflowAgentUserName = MapUtils.getString(workflowAgentMap, "workflowAgentUser", "");

        if (!workflowAgentEnable || StringUtils.isBlank(workflowAgentUserName)) {
            return null;
        }

        UserService userService = SpringUtils.getBean(UserService.class);
        return userService.getUserByUsername(workflowAgentUserName);
    }

    public String getWorkflowAgentUserAndFullname() {
        User workflowAgentUser = getWorkflowAgentUser();
        if (workflowAgentUser != null) {
            return workflowAgentUser.getFullnameAndUsername();
        }

        return "";
    }

    public String getSruname() {
        if (StringUtils.isBlank(fullname)) {
            return "";
        }

        return StringUtils.substring(fullname, 0, 1);
    }

    public boolean getAutoShowJsSignature() {
        if (StringUtils.isBlank(getProperty("autoShowJsSignature"))) {
            return false;
        }

        return BooleanUtils.toBoolean(getProperty("autoShowJsSignature"));
    }

    public void setAutoShowJsSignature(boolean value) {
        setProperty("autoShowJsSignature", String.valueOf(value));
    }

    public boolean getAutoShowDataSearch() {
        if (StringUtils.isBlank(getProperty("autoShowDataSearch"))) {
            return false;
        }

        return BooleanUtils.toBoolean(getProperty("autoShowDataSearch"));
    }

    public void setAutoShowDataSearch(boolean value) {
        setProperty("autoShowDataSearch", String.valueOf(value));
        if (true) {
            setAutoShowDesktop(false);
        }
    }

    public boolean isUsbAuthOnly() {
        return BooleanUtils.toBoolean(getProperty("usbAuthOnly"));
    }

    public void setUsbAuthOnly(boolean value) {
        setProperty("usbAuthOnly", String.valueOf(value));
    }

    public boolean isDisableIMPopup() {
        return BooleanUtils.toBoolean(getProperty("disableIMPopup"));
    }

    public void setDisableIMPopup(boolean value) {
        setProperty("disableIMPopup", String.valueOf(value));
    }

    public boolean isBreathBackGroundImg() {
        String property = getProperty("breathBackGroundImg");
        if (StringUtils.isBlank(property)) {
            property = "true";
        }

        return BooleanUtils.toBoolean(property);
    }

    public void setBreathBackGroundImg(boolean value) {
        setProperty("breathBackGroundImg", String.valueOf(value));
    }

    public void setIpRestrictData(String ipString) {
        if (StringUtils.isNotBlank(ipString)) {
            setProperty("ipRestrict", ipString);
            ipRestrict = null;
        } else {
            setProperty("ipRestrict", "");
        }
    }

    public String getIpRestrictData() {
        if (StringUtils.isNotBlank(ipRestrict)) {
            return ipRestrict;
        } else {
            return getProperty("ipRestrict");
        }
    }

    public List<String> getIpRestrictList() {
        if (StringUtils.isNotBlank(ipRestrict)) {
            return WebUtils.getIpListFromString(ipRestrict);
        } else {
            return WebUtils.getIpListFromString(getProperty("ipRestrict"));
        }
    }

    public boolean isInHideList() {
        return UserGroupUtils.isInHideUser(getUsername());
    }

    public boolean isHasMobileAccess() {
        if (mobileAccess != null) {
            return mobileAccess;
        }

        return false;

		/*
		String mobileUsernames = SystemConfigManager.getInstance().getProperty("mobileUsernames");
		if (StringUtils.isBlank(mobileUsernames)) {
			return false;
		}
		String[] mobileNamesArray = mobileUsernames.split("\n");
		for (String name : mobileNamesArray) {
			if (StringUtils.isNotBlank(name) && username.equals(name.trim())) {
				return true;
			}
		}

		return false;
		 */
    }

    public void resetDesktopSettings() {
        Map<Integer, Map<String, String>> desktopSettingsMap = new HashMap<Integer, Map<String, String>>();
        Map<String, String> blockMap1 = new HashMap<String, String>();
        Map<String, String> blockMap2 = new HashMap<String, String>();
        blockMap1.put("ltype", "daishen");
        blockMap1.put("rtype", "yifa");
        blockMap2.put("ltype", "myCommons");
        blockMap2.put("rtype", "myLendings");
        desktopSettingsMap.put(1, blockMap1);
        desktopSettingsMap.put(2, blockMap2);

        desktopSettings = JSON.toJSONString(desktopSettingsMap.values());
    }

    public void deleteDesktopBlockByLine(int line) {
        Map<Integer, Map<String, String>> desktopSettingsMap = getDesktopSettingsMap();
        if (desktopSettingsMap.containsKey(line)) {
            desktopSettingsMap.remove(line);
        }

        desktopSettings = JSON.toJSONString(desktopSettingsMap.values());
    }

    public void addDesktopBlock(int afterLine) {
        Map<Integer, Map<String, String>> result = new HashMap<Integer, Map<String, String>>();
        Map<Integer, Map<String, String>> desktopSettingsMap = getDesktopSettingsMap();

        int i = 1;
        for (Map<String, String> blockMap : desktopSettingsMap.values()) {
            if (i == afterLine) {
                result.put(i, blockMap);
                i++;
                Map<String, String> newBlockMap = new HashMap<String, String>();
                newBlockMap.put("ltype", "new");
                newBlockMap.put("rtype", "new");
                result.put(i, newBlockMap);
            } else {
                result.put(i, blockMap);
            }
            i++;
        }

        desktopSettings = JSON.toJSONString(result.values());

    }

    public void setDesktopBlockSetting(int blockIndex, String key, String value) {
        Map<Integer, Map<String, String>> desktopSettingsMap = getDesktopSettingsMap();
        Map<String, String> blockMap = getDesktopBlockSettings(blockIndex);
        blockMap.put(key, value);
        desktopSettingsMap.put(blockIndex, blockMap);

        desktopSettings = JSON.toJSONString(desktopSettingsMap.values());
    }

    public Map<String, String> getDesktopBlockSettings(int blockIndex) {
        Map<Integer, Map<String, String>> desktopSettingsMap = getDesktopSettingsMap();
        if (desktopSettingsMap.containsKey(blockIndex)) {
            return desktopSettingsMap.get(blockIndex);
        }

        return new HashMap<String, String>();
    }

    public Map<Integer, Map<String, String>> getDesktopSettingsMap() {
        Map<Integer, Map<String, String>> result = new HashMap<Integer, Map<String, String>>();
        if (StringUtils.isBlank(desktopSettings)) {
            return result;
        }

        log.debug("getDesktopSettingsMap: {}", desktopSettings);

        JSONArray ja = JSON.parseArray(desktopSettings);
        for (int i = 0; i < ja.size(); i++) {
            try {
                JSONObject jo = (JSONObject) ja.getJSONObject(i);
                Map<String, String> blockMap = new HashMap<String, String>();
                int blockIndex = i + 1;
                if (blockIndex > -1) {
                    blockMap.put("ltype", jo.getString("ltype"));

                    if (jo.containsKey("lsize")) {
                        blockMap.put("lsize", jo.getString("lsize"));
                    }
                    if (jo.containsKey("lprop")) {
                        blockMap.put("lprop", jo.getString("lprop"));
                    }

                    blockMap.put("rtype", jo.getString("rtype"));

                    if (jo.containsKey("rsize")) {
                        blockMap.put("rsize", jo.getString("rsize"));
                    }

                    if (jo.containsKey("rprop")) {
                        blockMap.put("rprop", jo.getString("rprop"));
                    }
                    result.put(blockIndex, blockMap);
                }
            } catch (Throwable t) {
                log.debug("getDesktopSettingsMap ex", t);
            }
        }

        return result;
    }

    public Group getFirstGroup() {
        List<Group> groups = getGroups();
        if (groups.size() == 0) {
            return null;
        }

        return groups.get(0);
    }

    public boolean isSyncEnabled() {
        return PasswordUtils.checkPassword(this.getId() + "-syncEnabled", syncString);
    }

    public void enableSync() {
        syncString = PasswordUtils.genPassHash(this.getId() + "-syncEnabled");
    }

    public void disableSync() {
        syncString = null;
    }

    public String getMyDocUsedDisplay() {
        double size = ((double) ZDIOUtils.getMyDocStorageUsed(this.getUsername())) / (1024 * 1024);
        String result = df.format(size) + "MB";
        return result;
    }

    public void genVali() {
        this.vali = PasswordUtils.genPassHash(this.username);
    }

    public boolean isValid() {
        return PasswordUtils.checkPassword(this.username, this.vali);
    }

    public boolean isMySpaceEnabled() {
        if (SystemConfigManager.getInstance().getIntProperty("myDocLimit") < 0) {
            return false;
        }

        if (StringUtils.isBlank(getProperty("mySpaceEnabled"))) {
            return true;
        }

        // log.debug("isMySpaceEnabled: {}", getProperty("mySpaceEnabled"));
        return BooleanUtils.toBoolean(getProperty("mySpaceEnabled"));
    }

    public void setMySpaceEnabled(boolean value) {
        setProperty("mySpaceEnabled", String.valueOf(value));
    }

    public boolean isOutLinkMyDocs() {

        if (StringUtils.isBlank(getProperty("outLinkMyDocs"))) {
            return false;
        }

        return BooleanUtils.toBoolean(getProperty("outLinkMyDocs"));
    }

    public void setOutLinkMyDocs(boolean value) {
        setProperty("outLinkMyDocs", String.valueOf(value));
    }

    public boolean getShowWelcomePage() {
        if (StringUtils.isBlank(getProperty("showWelcomePage"))) {
            return true;
        }

        return BooleanUtils.toBoolean(getProperty("showWelcomePage"));
    }

    public void setShowWelcomePage(boolean value) {
        setProperty("showWelcomePage", String.valueOf(value));
    }

    public boolean getAutoShowDesktop() {
        // 如果是档案版，默认不显示个人桌面，无个人桌面功能
        if (ModeDefinition.d()) {
            return false;
        }

        if (getAutoShowDataSearch()) {
            return false;
        }

        if (StringUtils.isBlank(getProperty("autoShowDesktop"))) {
            return true;
        }

        return BooleanUtils.toBoolean(getProperty("autoShowDesktop"));
    }

    public void setAutoShowDesktop(boolean value) {
        setProperty("autoShowDesktop", String.valueOf(value));
    }

    public boolean getAcceptDocMailNotice() {
        if (SystemConfigManager.getInstance().getBooleanProperty("forceSendMail")) {
            return true;
        }

        if (StringUtils.isBlank(getProperty("acceptDocMailNotice"))) {
            return true;
        }

        return BooleanUtils.toBoolean(getProperty("acceptDocMailNotice"));
    }

    public void setAcceptDocMailNotice(boolean value) {
        setProperty("acceptDocMailNotice", String.valueOf(value));
    }

    public String getFullnameAndUsername() {
        return fullname + " (" + username + ")";
    }

    public String getUsernameAndFullname() {
        return username + " (" + fullname + ")";
    }

    public String getUsernameAndId() {
        return username + "@@" + id + "";
    }

    public String getType() {
        return "User";
    }

    public boolean isSystemAdmin() {
        return RoleUtils.isUserGroupInRole(this, Role.SYSTEM_ADMIN);
    }

    public boolean isReceptionAdmin() {
        return RoleUtils.isUserGroupInRole(this, Role.RECEPTION_ADMIN);
    }

    public List<String> getRoleList() {
        if (roleList == null) {
            roleList = new ArrayList<>();
            if (StringUtils.isBlank(roles)) {
                return roleList;
            }

            String[] split = roles.split(",");
            for (String s : split) {
                if (StringUtils.isBlank(s)) {
                    continue;
                }

                s = s.replace("**", "");
                if (StringUtils.isBlank(s)) {
                    continue;
                }

                if (roleList.contains(s)) {
                    continue;
                }

                roleList.add(s);
            }
        }

        return roleList;
    }

    public String getRole() {
        return StringUtils.join(getRoleList(), ",");
    }

    public void addRole(String role) {
        if (StringUtils.isBlank(role)) {
            return;
        }

        String tempRoles = getRoles();
        if (StringUtils.isBlank(tempRoles)) {
            setRoles("**" + role + "**");
            return;
        }

        if (tempRoles.contains("**" + role + "**")) {
            return;
        }

        setRoles(tempRoles + ",**" + role + "**");
    }

    public void removeRole(String role) {
        if (StringUtils.isBlank(role)) {
            return;
        }

        List<String> tempRoleList = getRoleList();
        tempRoleList.remove(role);

        RoleUtils.setUserRoles(this, tempRoleList);
    }

    public String getRoleString() {
        boolean systemAdmin = RoleUtils.isUserGroupInRole(this, Role.SYSTEM_ADMIN);
        boolean userAdmin = RoleUtils.isUserGroupInRole(this, Role.USER_ADMIN);
        boolean workflowAdmin = RoleUtils.isUserGroupInRole(this, Role.WORKFLOW_ADMIN);
        boolean limitedAdmin = RoleUtils.isUserGroupInRole(this, Role.LIMITED_ADMIN);
        boolean noticeAdmin = RoleUtils.isUserGroupInRole(this, Role.NOTICE_ADMIN);
        boolean ecmAdmin = RoleUtils.isUserGroupInRole(this, Role.ECM_ADMIN);
        boolean dccAdmin = RoleUtils.isUserGroupInRole(this, Role.DCC_ADMIN);
        boolean receptionAdmin = RoleUtils.isUserGroupInRole(this, Role.RECEPTION_ADMIN);
        boolean logsAdmin = RoleUtils.isUserGroupInRole(this, Role.LOGS_ADMIN);

        List<String> roleStrings = new ArrayList<String>();
        if (systemAdmin) {
            roleStrings.add("系统管理员");
        }
        if (userAdmin) {
            roleStrings.add("用户管理员");
        }
        if (workflowAdmin) {
            roleStrings.add("流程管理员");
        }
        if (limitedAdmin) {
            roleStrings.add("后台管理员");
        }
        if (noticeAdmin) {
            roleStrings.add("通知公告管理员");
        }
        if (ecmAdmin) {
            roleStrings.add("档案管理员");
        }
        if (dccAdmin) {
            roleStrings.add("DCC管理员");
        }
        if (receptionAdmin) {
            roleStrings.add("前台管理员");
        }
        if (logsAdmin) {
            roleStrings.add("安全审计员");
        }

        return StringUtils.join(roleStrings, ",");
    }

    public String getGroupIds() {
        return StringUtils.join(getGroupIdsList(), ",");
    }

    public List<Long> getGroupIdsList() {
        return ZDJoinTableGroupUserUtils.getGroupIds(id);
    }

    public List<String> getGroupCodesList() {
        List<String> groupIds = new ArrayList<String>();
        for (Group g : getGroups()) {
            if (groupIds.contains(g.getCode())) {
                continue;
            }

            groupIds.add(g.getCode());
        }

        return groupIds;
    }

    public String getGroupsNamesWithSpace() {
        List<String> groupNames = new ArrayList<String>();
        for (Group g : getGroups()) {
            groupNames.add(g.getDisplayFullname());
        }

        return StringUtils.join(groupNames, " ");
    }

    public String getGroupsNamesWithComma() {
        List<String> groupNames = new ArrayList<String>();
        for (Group g : getGroups()) {
            groupNames.add(g.getDisplayFullname());
        }

        return StringUtils.join(groupNames, ",");
    }

    public String getGroupUniqueNamesWithComma() {
        List<String> groupNames = new ArrayList<String>();
        for (Group g : getGroups()) {
            groupNames.add(g.getName());
        }

        return StringUtils.join(groupNames, ",");
    }

    public String getDisplayGroups() {
        List<String> groupNames = new ArrayList<String>();
        for (Group g : getGroups()) {
            groupNames.add(g.getDisplayFullname());
        }

        return StringUtils.join(groupNames, "<br/>");
    }

    /**
     * 设置属性
     *
     * @param name  名称
     * @param value 值
     */
    public void setProperty(String name, String value) {
        initPropertiesMap();
        propertiesMap.put(name, value);
        PropertyUtils.updateProperties(this);
    }

    /**
     * 移除属性
     *
     * @param name 要移除的属性名称
     */
    public void removeProperty(String name) {
        initPropertiesMap();
        propertiesMap.remove(name);
    }

    /**
     * 获取属性
     *
     * @param name 名称
     * @return 值
     */
    public String getProperty(String name) {
        initPropertiesMap();
        return propertiesMap.get(name);
    }

    /**
     * 返回用户名
     *
     * @return 用户名
     */
    public String getUsername() {
        return TextUtils.escapeXml(username);
    }

    /**
     * 设置新用户名
     *
     * @param username 新用户名
     */
    public void setUsername(String username) {
        // 修改用户名时进行记录修改前用户名
        if (StringUtils.isNotBlank(username) && StringUtils.isBlank(oldUsername)) {
            this.oldUsername = this.username;
        }

        this.username = username;
    }

    /**
     * @return Returns the fullname.
     */
    public String getFullname() {
        return TextUtils.escapeXml(fullname);
    }

    /**
     * @param fullname The fullName to set.
     */
    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    /**
     * 获取密码
     *
     * @return 密码
     */
    public String getPassword() {
        return password;
    }

    /**
     * 设置新密码
     *
     * @param password 要设置的新密码
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * @return Returns the email.
     */
    public String getEmail() {
        return TextUtils.escapeXml(email);
    }

    /**
     * @param email The email to set.
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * @return Returns the enabled.
     */
    public boolean getEnabled() {
        return enabled;
    }

    /**
     * @param enabled The enabled to set.
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    /**
     * @see Object#equals(Object)
     */
    @Override
    public boolean equals(Object o) {
        if (!(o instanceof User)) {
            return false;
        }

        final User u = (User) o;

        return new EqualsBuilder().appendSuper(super.equals(u)).append(username, u.getUsername()).isEquals();
    }

    /**
     * @see Object#hashCode()
     */
    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(username).toHashCode();
    }

    /**
     * @return Returns the memo.
     */
    public String getMemo() {
        return TextUtils.escapeXml(memo);
    }

    /**
     * @param memo The memo to set.
     */
    public void setMemo(String memo) {
        this.memo = memo;
    }

    /**
     * @return Returns the mobilePhone.
     */
    public String getMobilePhone() {
        return TextUtils.escapeXml(mobilePhone);
    }

    /**
     * @param mobilePhone The mobilePhone to set.
     */
    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    /**
     * @return the city
     */
    public String getCity() {
        return TextUtils.escapeXml(city);
    }

    /**
     * @param city the city to set
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * @return the province
     */
    public String getProvince() {
        return TextUtils.escapeXml(province);
    }

    /**
     * @param province the province to set
     */
    public void setProvince(String province) {
        this.province = province;
    }

    /**
     * @return the regIp
     */
    public String getRegIp() {
        return TextUtils.escapeXml(regIp);
    }

    /**
     * @param regIp the regIp to set
     */
    public void setRegIp(String regIp) {
        this.regIp = regIp;
    }

    public Map<String, String> getPropertiesMap() {
        initPropertiesMap();
        return propertiesMap;
    }

    public void setPropertiesMap(Map<String, String> propertiesMap) {
        this.propertiesMap = propertiesMap;
    }

    public void initPropertiesMap() {
        if (this.propertiesMap == null) {
            this.propertiesMap = new HashMap<>();

            PropertyUtils.updatePropertiesMap(this);
        }
    }

    public void setProperties(String properties) {
        this.properties = properties;
    }

    public String getProperties() {
        return properties;
    }

    public String getTheme() {
        return "default";
    }

    public void setTheme(String theme) {
        this.theme = theme;
    }

    public Date getLastLoginDate() {
        return lastLoginDate;
    }

    public void setLastLoginDate(Date lastLoginDate) {
        this.lastLoginDate = lastLoginDate;
    }

    public String getOfficePhone() {
        return TextUtils.escapeXml(officePhone);
    }

    public void setOfficePhone(String officePhone) {
        this.officePhone = officePhone;
    }

    public String getHomePhone() {
        return TextUtils.escapeXml(homePhone);
    }

    public void setHomePhone(String homePhone) {
        this.homePhone = homePhone;
    }

    public String getLastIp() {
        return TextUtils.escapeXml(lastIp);
    }

    public void setLastIp(String lastIp) {
        this.lastIp = lastIp;
    }

    public String getIpRestrict() {
        return ipRestrict;
    }

    public void setIpRestrict(String ipRestrict) {
        this.ipRestrict = ipRestrict;
    }

    public String getPositions() {
        return TextUtils.escapeXml(positions);
    }

    public void setPositions(String positions) {
        this.positions = positions;
    }

    public String getVali() {
        return vali;
    }

    public void setVali(String vali) {
        this.vali = vali;
    }

    public String getSyncString() {
        return syncString;
    }

    public void setSyncString(String syncString) {
        this.syncString = syncString;
    }

    public String getDesktopSettings() {
        return desktopSettings;
    }

    public void setDesktopSettings(String desktopSettings) {
        this.desktopSettings = desktopSettings;
    }

    public Integer getNumIndex() {
        if (numIndex == null) {
            return 9999;
        }

        return numIndex;
    }

    public void setNumIndex(Integer numIndex) {
        this.numIndex = numIndex;
    }

    public Integer getAutoLockMin() {
        if (autoLockMin == null) {
            return 0;
        }

        return autoLockMin;
    }

    public void setAutoLockMin(Integer autoLockMin) {
        this.autoLockMin = autoLockMin;
    }

    public String getPlainPassword() {
        if (StringUtils.isBlank(plainPassword)) {
            return "";
        }

        return plainPassword;
    }

    public void setPlainPassword(String plainPassword) {
        this.plainPassword = plainPassword;
    }

    public String getDingEmployeeId() {
        return TextUtils.escapeXml(dingEmployeeId);
    }

    public void setDingEmployeeId(String dingEmployeeId) {
        this.dingEmployeeId = dingEmployeeId;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getUserIdCode() {
        return TextUtils.escapeXml(userIdCode);
    }

    public void setUserIdCode(String userIdCode) {
        this.userIdCode = userIdCode;
    }

    public String getQywxId() {
        return TextUtils.escapeXml(qywxId);
    }

    public void setQywxId(String qywxId) {
        this.qywxId = qywxId;
    }

    public User getSponsorUser() {
        return sponsorUser;
    }

    public void setSponsorUser(User sponsorUser) {
        this.sponsorUser = sponsorUser;
    }

    public String getDingUserId() {
        return dingUserId;
    }

    public void setDingUserId(String dingUserId) {
        this.dingUserId = dingUserId;
    }

    public String getOldUsername() {
        if (StringUtils.isBlank(oldUsername)) {
            return username;
        }

        return oldUsername;
    }

    public void setOldUsername(String oldUsername) {
        this.oldUsername = oldUsername;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getBbsn() {
        return bbsn;
    }

    public void setBbsn(String bbsn) {
        this.bbsn = bbsn;
    }

    public String getCpid() {
        return cpid;
    }

    public void setCpid(String cpid) {
        this.cpid = cpid;
    }

    public String getDsn() {
        return dsn;
    }

    public void setDsn(String dsn) {
        this.dsn = dsn;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public Boolean getNotVerifyClient() {
        if (UserGroupUtils.ADMIN_USERNAME.equals(username)) {
            return true;
        }

        if (notVerifyClient == null) {
            return false;
        }

        return notVerifyClient;
    }

    public void setNotVerifyClient(Boolean notVerifyClient) {
        this.notVerifyClient = notVerifyClient;
    }

    public Boolean getClientInfoComplete() {
        if (clientInfoComplete == null) {
            return false;
        }

        return clientInfoComplete;
    }

    public void setClientInfoComplete(Boolean clientInfoComplete) {
        this.clientInfoComplete = clientInfoComplete;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getFeishuId() {
        return feishuId;
    }

    public void setFeishuId(String feishuId) {
        this.feishuId = feishuId;
    }

    public Boolean getMobileAccess() {
        return mobileAccess;
    }

    public void setMobileAccess(Boolean mobileAccess) {
        this.mobileAccess = mobileAccess;
    }
}
