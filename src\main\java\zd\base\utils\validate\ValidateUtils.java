package zd.base.utils.validate;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import zd.base.exception.IllegalArgumentException;
import zd.base.utils.constant.ErrorCodeDefine;
import zd.base.utils.constant.ErrorCodeUtils;

import java.util.Collection;
import java.util.Map;

public class ValidateUtils {

    /**
     * 验证表达式，如果表达式为true不做处理；如果为false抛出BxIllegalArgumentException非法参数异常，
     * 在全局拦截器中拦截到
     *
     * @param expression     需要判断的表达式
     * @param errorCodeOrMsg 错误码或者错误信息。如果是定义过的错误码，msg从 ErrorCodeDefine.ERROR_CODE_MSG
     *                       获取;否则作为消息回传，code是ErrorCodeDefine.UNDEFINED_ERROR
     */
    public static void isTrue(final boolean expression, final String errorCodeOrMsg) {
        if (!expression) {
            if (ErrorCodeUtils.hasDefined(errorCodeOrMsg)) {
                throw new IllegalArgumentException(errorCodeOrMsg, ErrorCodeUtils.getMsg(errorCodeOrMsg));
            } else {
                throw new IllegalArgumentException(ErrorCodeUtils.getUndefinedErrorCode(), errorCodeOrMsg);
            }
        }
    }

    public static void isNotNull(Object obj, String errorCodeOrMsg) {
        isTrue(obj != null, errorCodeOrMsg);

        if (obj instanceof String) {
            isTrue(StringUtils.isNotBlank(obj + ""), errorCodeOrMsg);
        } else if (obj instanceof Number) {
            isTrue(((Number) obj).doubleValue() != 0D, errorCodeOrMsg);
        }
    }

    public static void isMapNotNull(Map params, String... keys) {
        if (MapUtils.isEmpty(params)) {
            throw new IllegalArgumentException("错误的请求参数", ErrorCodeUtils.getMsg("错误的请求参数"));
        }

        if (keys == null) {
            return;
        }

        for (String key : keys) {
            isNotNull(params, key, "参数：" + key + " 不能为空");
        }
    }

    public static void isNotNull(Map params, String key, String errorCodeOrMsg) {
        if (MapUtils.isEmpty(params)) {
            throw new IllegalArgumentException("错误的请求参数", ErrorCodeUtils.getMsg("错误的请求参数"));
        }

        if (StringUtils.isBlank(key)) {
            return;
        }

        isNotNull(params.get(key), errorCodeOrMsg);
    }

    public static <T extends Collection<?>> T notEmpty(T collection) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new IllegalArgumentException(ErrorCodeDefine.UNDEFINED_ERROR, "集合参数为空");
        }

        return collection;
    }

    public static <T extends Collection<?>> T notEmpty(T collection, String message, Object... values) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new IllegalArgumentException(ErrorCodeDefine.UNDEFINED_ERROR, getMessage(message, values));
        }

        return collection;
    }

    public static <T extends CharSequence> T notEmpty(T str, String message, Object... values) {
        if (StringUtils.isEmpty(str)) {
            throw new IllegalArgumentException(ErrorCodeDefine.UNDEFINED_ERROR, getMessage(message, values));
        }

        return str;
    }

    public static int inclusiveBetween(int start, int end, int value, String message) {
        if (value < start || value > end) {
            throw new IllegalArgumentException(ErrorCodeDefine.UNDEFINED_ERROR, message);
        }

        return value;
    }

    private static String getMessage(String message, Object... values) {
        return ArrayUtils.isEmpty(values) ? message : String.format(message, values);
    }
}
