//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                           O\  =  /O
//                        ____/`---'\____
//                      .'  \\|     |//  `.
//                     /  \\|||  :  |||//  \
//                    /  _||||| -:- |||||-  \
//                    |   | \\\  -  /// |   |
//                    | \_|  ''\---/''  |   |
//                    \  .-\__  `-`  ___/-. /
//                  ___`. .'  /--.--\  `. . __
//               ."" '<  `.___\_<|>_/___.'  >'"".
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |
//              \  \ `-.   \_ __\ /__ _/   .-` /  /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
//                    佛祖保佑       永无BUG
package zd.dms.io;

import java.io.FilterOutputStream;
import java.io.IOException;
import java.io.OutputStream;

/**
 * XorOutputStream encodes it output by Xoring all input bytes with the byte
 * mask given in the constructor. Use a XorInputStream to decode the output of
 * XorOutputStream by givening them both the same mask in their constructors.
 * XorInputStream and XorOutputStream can be used for a very weak form of
 * encryption.
 * 
 * @version 1.0 14 Feb 1998
 * <AUTHOR> Whitney (<a
 *         href=mailto:<EMAIL>><EMAIL></a>)
 */
public class DOutputStream extends FilterOutputStream {
	byte codeMask = 0;

	public DOutputStream(OutputStream out, byte mask) {
		super(out);
		codeMask = mask;
	}

	/**
	 * Writes the specified <code>byte</code> and Xor's it with the streams
	 * mask.
	 * 
	 * @param output the <code>byte</code>.
	 * @exception IOException if an I/O error occurs.
	 */
	public void write(int output) throws IOException {
		if (output < 0) // not a byte,
		{
			out.write(output);
		} else
			out.write(((byte) output) ^ codeMask);
	}

	/**
	 * Writes <code>length</code> bytes from the specified <code>byte</code>
	 * array starting at offset <code>offset</code> and Xor's each byte with the
	 * streams mask.
	 * 
	 * @param output the data.
	 * @param offset the start offset in the data.
	 * @param length the number of bytes to write.
	 * @exception IOException if an I/O error occurs.
	 */
	public void write(byte output[], int offset, int length) throws IOException {
		byte[] coded = new byte[length];
		for (int i = 0; i < length; i++) {
			coded[i] = (byte) (output[offset + i] ^ (byte) codeMask);
		}
		out.write(coded);
	}
}
