package zd.dms.filter;

import jakarta.servlet.*;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.analysus.ja.dict.ConcurrentDictionary;
import org.springframework.stereotype.Component;
import zd.base.context.UserContextHolder;
import zd.base.utils.system.SpringUtils;
import zd.dms.entities.User;
import zd.dms.services.user.UserService;
import zd.dms.utils.SessionUtils;
import zd.dms.utils.ZDAuthUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
@WebFilter(filterName = "zdLoginFilter", urlPatterns = "/*")
public class ZDLoginFilter implements Filter {

    private volatile List<String> ignore;

    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest request = (HttpServletRequest) req;
        HttpServletResponse response = (HttpServletResponse) res;

        logLastActiveTime(request);

        UserService userService = SpringUtils.getBean(UserService.class);
        User currentUser = userService.getPageUser(request);

        if (currentUser != null && !isLoginUrl(request)) {
            String userId = currentUser.getId();
            if (!isIgnore(request)) {
                if (!ConcurrentDictionary.isOnline(userId)) {
                    boolean au = ConcurrentDictionary.au(request.getSession(), currentUser.getUsername(), false);
                    if (!au) {
                        response.addHeader("webstatus", "notLoggedIn");
                        response.addHeader("webstatusmsgs", "not_online");
                        return;
                    }
                } else {
                    boolean vou = ConcurrentDictionary.vou(request.getSession());
                    if (!vou) {
                        response.addHeader("webstatus", "notLoggedIn");
                        response.addHeader("webstatusmsgs", "other_login");
                        return;
                    }

                    ConcurrentDictionary.updateLastAccessDate(userId);
                }
            }

            setUserContext(currentUser);

            // 判断能否访问H5页面
            String uri = request.getRequestURI();
            if (StringUtils.isNotBlank(uri) && uri.startsWith("/m/") && !currentUser.isHasMobileAccess()) {
                return;
            }

            // logincookie remove
            /*ZDTokenUtils.refreshToken(request, response, false);*/
            SessionUtils.refreshToken(request, response);

            chain.doFilter(request, response);
            return;
        } else {
            setUserContext(null);

            // 防止session丢失，只能放到else中，并在本if判断结束之后重新判断一次
            if (isIgnore(request)) {
                chain.doFilter(request, response);
                return;
            }
        }

        if (isLoggedIn(request) || isIgnore(request)) {
            chain.doFilter(request, response);
            return;
        }

        if (ZDAuthUtils.isAuth(request, response, "")) {
            chain.doFilter(request, response);
            return;
        }

        // 检查cookie
        // logincookie remove
        /*if (authenticationService.isCookied(request, response)) {
            chain.doFilter(request, response);
            return;
        }*/

        response.addHeader("webstatus", "notLoggedIn");
        return;
    }

    private void logLastActiveTime(HttpServletRequest request) {
        String servletPath = request.getServletPath();

        if (StringUtils.isBlank(servletPath)) {
            return;
        }

        if (servletPath.contains(".html")) {
            if (servletPath.equals("/user/hb.html") || servletPath.equals("/user/lockScreen.html")) {
                return;
            }

            Date now = new Date();
            request.getSession().setAttribute("lastActiveTime", now);
            // log.debug("logLastActiveTime: {}", now);

            if (request.getSession().getAttribute("autoLocked") == null) {
                request.getSession().setAttribute("autoLocked", false);
            }
        }
    }

    private void setUserContext(User user) {
        // Test时使用
        UserContextHolder.setUser(user);
    }

    private boolean isIgnore(HttpServletRequest request) {
        String servletPath = request.getServletPath();
        if (StringUtils.isBlank(servletPath)) {
            return false;
        }

        initIgnoreUrl();

        for (String s : ignore) {
            if (servletPath.startsWith(s)) {
                return true;
            }
        }

        return false;
    }

    private boolean isLoginUrl(HttpServletRequest request) {
        String servletPath = request.getServletPath();
        if (StringUtils.isBlank(servletPath)) {
            return false;
        }

        return servletPath.equals(SpringUtils.getProps("zd.loginFiler.loginUrl", "/login"));
    }

    private void initIgnoreUrl() {
        if (ignore == null) {
            synchronized ("loginFilterIgnoreUrl") {
                if (ignore == null) {
                    ignore = new ArrayList<>();
                    String loginFilerIgnore = SpringUtils.getProps("zd.loginFiler.ignore", "/login, /druid, /opening, /api");

                    String[] split = loginFilerIgnore.split(",");
                    for (String url : split) {
                        if (StringUtils.isBlank(url)) {
                            continue;
                        }

                        ignore.add(url.trim());
                    }
                }
            }
        }
    }

    private boolean isLoggedIn(HttpServletRequest request) {
        User user = UserContextHolder.getUser();

        if (user != null && user.getEnabled()) {
            return true;
        }
        return false;
    }

    @Override
    public void destroy() {

    }

    @Override
    public void init(FilterConfig arg0) throws ServletException {

    }
}
