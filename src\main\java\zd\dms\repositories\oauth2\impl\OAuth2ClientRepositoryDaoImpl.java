package zd.dms.repositories.oauth2.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.oauth2.OAuth2Client;
import zd.dms.repositories.oauth2.OAuth2ClientRepository;
import zd.dms.utils.repositories.SpecTools;

public class OAuth2ClientRepositoryDaoImpl extends BaseRepositoryDaoImpl<OAuth2Client, String> implements OAuth2ClientRepository {

    public OAuth2ClientRepositoryDaoImpl(Class<OAuth2Client> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public OAuth2Client findByClientId(String clientId) {
        Specification<OAuth2Client> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<OAuth2Client> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("clientId", clientId);

            return specTools.getRestriction();
        };

        return findSingleObject(spec);
    }

    @Override
    public Page getOAuth2ClientPage(int pageNumber, int pageSize) {
        Specification<OAuth2Client> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<OAuth2Client> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.asc("numIndex");
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }
}
