package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.entities.AbstractEntity;
import zd.base.utils.system.SpringUtils;
import zd.dms.services.document.DocumentService;
import zd.dms.utils.TextUtils;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

@Entity
@Table(name = "outlink")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class OutLink extends AbstractEntity {

    private static Logger log = LoggerFactory.getLogger(OutLink.class);

    /**
     * serial
     */
    private static final long serialVersionUID = -2048179553154117821L;

    /**
     * URL链接，唯一
     */
    @Column(unique = true, nullable = false)
    private String url;

    /**
     * 访问数
     */
    private int visitCount;

    /**
     * 访问数限制
     */
    private int visitCountLimit;

    /**
     * 下载数限制
     */
    private Integer downloadCountLimit;

    /**
     * 文档下载数Map，文档id为key，下载数为value
     */
    @Column(length = Length.LOB_DEFAULT)
    private String downloadDocMap;

    /**
     * 下载数
     */
    private int downloadCount;

    /**
     * 阅读数
     */
    private int readCount;

    /**
     * 过期日期
     */
    @Index(name = "i_olink_expdate")
    private Date expirationDate;

    /**
     * 密码
     */
    private String password;

    /**
     * 外发标题
     */
    private String title;

    /**
     * 外发描述
     */
    private String description;

    private boolean allowDownload;

    private boolean allowPrint;

    /**
     * 文档id或目录id列表，,号分隔
     */
    @Column(length = Length.LOB_DEFAULT)
    private String fileIds;

    /**
     * 文档文件名或目录名列表，:号分隔
     */
    @Column(length = Length.LOB_DEFAULT)
    private String filenames;

    /**
     * 目录id
     */
    private long folderId;

    /**
     * 创建者全名
     */
    private String creatorFullname;

    /**
     * 创建时间
     */
    @Index(name = "i_olink_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 外发类型  1为目录及文件
     */
    private String type;

    public OutLink() {
        super();
        visitCount = 0;
        visitCountLimit = 0;
        downloadCount = 0;
        readCount = 0;
        creationDate = new Date();
    }

    public String getHtmlDesc() {
        return TextUtils.toHtml(getDescription());
    }

    public boolean isReachDownloadLimit() {
        if (downloadCountLimit == null || downloadCountLimit == 0) {
            return false;
        }

        return true;
    }

    public boolean isReachVisitLimit() {
        if (visitCountLimit == 0) {
            return false;
        }

        return visitCount >= visitCountLimit;
    }

    public boolean isExpired() {
        if (expirationDate != null) {
            if (new Date().getTime() > expirationDate.getTime()) {
                return true;
            }
        }

        return false;
    }

    public List<String> getIdList() {
        List<String> idList = new LinkedList<String>();
        if (StringUtils.isNotBlank(fileIds)) {
            String[] tempArr = fileIds.split(",");
            for (String s : tempArr) {
                if (StringUtils.isNotBlank(s)) {
                    idList.add(s.trim());
                }
            }
        }

        return idList;
    }

    public List<String> getFilenameList() {
        List<String> filenameList = new LinkedList<String>();
        if (StringUtils.isNotBlank(filenames)) {
            String[] tempArr = filenames.split(":");
            for (String s : tempArr) {
                if (StringUtils.isNotBlank(s)) {
                    filenameList.add(s.trim());
                }
            }
        }

        return filenameList;
    }

    public String getFilenameString() {
        return StringUtils.join(getFilenameList(), ", ");
    }

    public String getFilenameStringWithLink() {
        List<String> idList = getIdList();
        List<String> filenameList = getFilenameList();

        List<String> displayList = new LinkedList<String>();
        int i = 0;
        for (String aId : idList) {
            String filename = filenameList.get(i);
            StringBuilder sb = new StringBuilder();
            sb.append("<a href=\"javascript:void(0)\" onclick=\"");
            sb.append("loadFolder(");
            sb.append(getCurrentFolderId(aId));
            sb.append(", null, '");
            sb.append(aId);
            sb.append("');\">");
            sb.append(filename);
            sb.append("</a>");
            displayList.add(sb.toString());
            i++;
        }

        return StringUtils.join(displayList, ", ");
    }

    public String getFilenameStringWithLinkFolder() {
        List<String> idList = getIdList();
        List<String> filenameList = getFilenameList();

        List<String> displayList = new LinkedList<String>();
        int i = 0;
        for (String aId : idList) {
            if (aId.startsWith("folder_")) { // 如果是 目录
                aId = aId.substring(7);
                String filename = filenameList.get(i);
                StringBuilder sb = new StringBuilder();
                sb.append("<a href=\"javascript:void(0)\" onclick=\"");
                sb.append("loadFolder('");
                sb.append(aId);
                sb.append("', null,null");
                sb.append(");\">");
                sb.append(filename);
                sb.append("</a>");
                displayList.add(sb.toString());
                i++;
            } else { // 如果是 文件
                String filename = filenameList.get(i);
                StringBuilder sb = new StringBuilder();
                sb.append("<a href=\"javascript:void(0)\" onclick=\"");
                sb.append("loadFolder(");
                sb.append(getCurrentFolderId(aId));
                sb.append(", null, '");
                sb.append(aId);
                sb.append("');\">");
                sb.append(filename);
                sb.append("</a>");
                displayList.add(sb.toString());
                i++;
            }
        }

        return StringUtils.join(displayList, ", ");
    }

    private long getCurrentFolderId(String docId) {
        if (folderId > 0) {
            return folderId;
        }

        DocumentService documentService = SpringUtils.getBean(DocumentService.class);
        Document doc = documentService.getDocumentById(docId);
        if (doc == null) {
            return 0;
        }

        return doc.getFolderId();
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getVisitCount() {
        return visitCount;
    }

    public void setVisitCount(int visitCount) {
        this.visitCount = visitCount;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getTitle() {
        return TextUtils.escapeXml(title);
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return TextUtils.escapeXml(description);
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFileIds() {
        return fileIds;
    }

    public void setFileIds(String fileIds) {
        this.fileIds = fileIds;
    }

    public String getFilenames() {
        return filenames;
    }

    public void setFilenames(String filenames) {
        this.filenames = filenames;
    }

    public String getCreatorFullname() {
        return creatorFullname;
    }

    public void setCreatorFullname(String creatorFullname) {
        this.creatorFullname = creatorFullname;
    }

    public int getVisitCountLimit() {
        return visitCountLimit;
    }

    public void setVisitCountLimit(int visitCountLimit) {
        this.visitCountLimit = visitCountLimit;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public boolean isAllowDownload() {
        return allowDownload;
    }

    public void setAllowDownload(boolean allowDownload) {
        this.allowDownload = allowDownload;
    }

    public boolean isAllowPrint() {
        return allowPrint;
    }

    public void setAllowPrint(boolean allowPrint) {
        this.allowPrint = allowPrint;
    }

    public int getDownloadCount() {
        return downloadCount;
    }

    public void setDownloadCount(int downloadCount) {
        this.downloadCount = downloadCount;
    }

    public int getReadCount() {
        return readCount;
    }

    public void setReadCount(int readCount) {
        this.readCount = readCount;
    }

    public long getFolderId() {
        return folderId;
    }

    public void setFolderId(long folderId) {
        this.folderId = folderId;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getDownloadCountLimit() {
        return downloadCountLimit;
    }

    public void setDownloadCountLimit(Integer downloadCountLimit) {
        this.downloadCountLimit = downloadCountLimit;
    }

    public String getDownloadDocMap() {
        return downloadDocMap;
    }

    public void setDownloadDocMap(String downloadDocMap) {
        this.downloadDocMap = downloadDocMap;
    }
}
