package org.apache.lucene.analysus.ja.dict;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.ConstantsBuildNumber;
import zd.base.utils.system.SystemInitUtils;
import zd.dms.Constants;
import zd.dms.services.config.SystemConfigManager;

import java.io.File;
import java.text.DateFormat;
import java.util.*;

public class ModeDefinition {

    private static final Logger log = LoggerFactory.getLogger(ModeDefinition.class);

    private static boolean freeVersion = false;

    /**
     * 检查免费版标识文件是否存在 如果存在就强制设定为免费版限制
     */
	/*
	static class FE extends TimerTask {
		@Override
		public void run() {
			File f = new File(BootstrapManager.getInstance().getIndexDir(),
					"/conf/badwords.txt");
			if (f.exists()) {
				log.debug("免费版标记存在，强制限制为免费版");
				ObjectLoader.getEntitiesUtils().setC(5);
				NumbersUtils.lout();
				trialMarkExists = true;

				// 如果用户数大于5，说明被破解，直接退出
				if (ObjectLoader.getEntitiesUtils().cl() > 5) {
					System.exit(0);
				}

				// 如果检测锁返回true，直接退出
				if (ObjectLoader.getEntitiesUtils().fk()) {
					System.exit(0);
				}

				// 如果检测专业版返回true，直接退出
				if (ObjectLoader.getEntitiesUtils().p2()) {
					System.exit(0);
				}
			} else {
				log.debug("无免费版标记");
				trialMarkExists = false;
			}
		}
	}

	static {
		Timer timer = new Timer();
		// 每60分钟执行一次
		timer.schedule(new FE(), 0, 3600000);
		// timer.schedule(new FE(), 0, 30000);
	}
	*/

    // 体验时间倒计时
    static class SingletonHolder extends TimerTask {
        @Override
        public void run() {
            checkSK();
        }
    }

    static {
        Timer timer = new Timer();
        // 每60分钟执行一次
        timer.schedule(new SingletonHolder(), 120000, 3600000);
    }

    /**
     * 检查软加密
     */
    public static void checkSK() {
        TaskDictionary.getMainDefinition().cd();

        if (!sk()) {
            return;
        }

        // 如果时间限制是-1，则无限制
        if (tt() == -1) {
            return;
        }

        int currentCount = ctt();
        currentCount++;
        if (currentCount > tt()) {
            TaskDictionary.getMainDefinition().csk();

            // 清除软加密串
            SystemConfigManager scm = SystemConfigManager.getInstance();
            scm.removeProperty("sk");
            scm.save();
            return;
        }

        try {
            File f = new File(SystemInitUtils.getIndexDir(), "/conf/swords.txt");
            FileUtils.writeStringToFile(f, String.valueOf(currentCount), "UTF-8");
        } catch (Throwable e) {
        }
    }

    /**
     * 获取当前已经使用系统的时间
     *
     * @return
     */
    public static int ctt() {
        File f = new File(SystemInitUtils.getIndexDir(), "/conf/swords.txt");

        int currentTimeUsed = 0;
        if (f.exists()) {
            try {
                currentTimeUsed = NumberUtils.toInt(FileUtils.readFileToString(f, "UTF-8"), 0);
            } catch (Throwable e) {
            }
        }

        return currentTimeUsed;
    }

    /**
     * 获取产品名称及版本
     *
     * @return
     */
    public static String getObject1() {
        String productAndEdition = Constants.getProductName();

        if (freeVersion) {
            return productAndEdition += "(体验版) ";
        }

        if (TaskDictionary.getMainDefinition().fk()) {
			/*if (TaskDictionary.getMainDefinition().hp("grp")) {
				productAndEdition += "(集团版) ";
			} else if (TaskDictionary.getMainDefinition().hp("ent")) {
				productAndEdition += "(企业版) ";
			} else if (TaskDictionary.getMainDefinition().hp("gov")) {
				productAndEdition += "(政务版) ";
			} else if (TaskDictionary.getMainDefinition().p2()) {
				productAndEdition += "(专业版) ";
			} else if (TaskDictionary.getMainDefinition().w()) {
				productAndEdition += "(图文版) ";
			} else if (TaskDictionary.getMainDefinition().u()) {
				productAndEdition += "(旗舰版) ";
			} else if (TaskDictionary.getMainDefinition().r()) {
				productAndEdition += "(ECM版) ";
			} else if (TaskDictionary.getMainDefinition().d()) {
				productAndEdition += "(档案版) ";
			} else if (TaskDictionary.getMainDefinition().sda()) {
				productAndEdition += "(档案单机版) ";
			} else if (TaskDictionary.getMainDefinition().dc()) {
				productAndEdition += "(文控版) ";
			} else {
				productAndEdition += " ";
			}*/

            if (TaskDictionary.getMainDefinition().r()) {
                productAndEdition += "(平台版) ";
            } else if (TaskDictionary.getMainDefinition().d()) {
                productAndEdition += "(档案版) ";
            }

			/*String ngen_edition = ZDUtils.getEdition();
			productAndEdition = productAndEdition + "(" + ngen_edition + ") ";*/
        } else {
            productAndEdition += "(体验版) ";
        }

        return productAndEdition;
    }

    /**
     * 获取产品名称及版本号
     *
     * @return
     */
    public static String getObjectv() {
        String productAndVersion = getObject1();
        productAndVersion += (SystemConfigManager.getInstance().getMarjorVersion() + "." + SystemConfigManager
                .getInstance().getStaticBuildNumber());

        return productAndVersion;
    }

    /**
     * 完善管理员界面的系统信息，防止被轻易发现加密方式
     *
     * @param results
     */
    public static void csi(Map<String, Object> results) {
        results.put("软件名称", ModeDefinition.getObjectv());

        results.put("产品标识", ConstantsBuildNumber.getGitId());

        results.put("产品分支", ConstantsBuildNumber.getGitBranch());

        results.put("加密锁号", TaskDictionary.getMainDefinition().ki());

        // 体验版提示
        if (TaskDictionary.getMainDefinition().sk()) {
            results.put("加密锁号", "体验版");

            String trialTime = "无限制";
            if (tt() > -1) {
                trialTime = tt() + "小时";
            }
            results.put("体验时间", trialTime);
        }

        if (TaskDictionary.getMainDefinition().getC() > 0) {
            results.put("授权用户数", String.valueOf(TaskDictionary.getMainDefinition().getC()));
        } else if (TaskDictionary.getMainDefinition().getBFC() > 0) {
            results.put("并发用户数", String.valueOf(TaskDictionary.getMainDefinition().getBFC()));
        }

        if (TaskDictionary.getMainDefinition().getSUC() > 0) {
            results.put("同步用户数", String.valueOf(TaskDictionary.getMainDefinition().getSUC()));
        }

        if (TaskDictionary.getMainDefinition().getMUC() > 0) {
            results.put("移动用户数", String.valueOf(TaskDictionary.getMainDefinition().getMUC()));
        }

        if (TaskDictionary.getMainDefinition().getRTC() > 0) {
            results.put("数据表", String.valueOf(TaskDictionary.getMainDefinition().getRTC()));
        }

        results.put("服务到期时间", String.valueOf(TaskDictionary.getMainDefinition().sed()));

        List<String> modulesList = getCurrentModuleList();
        String modulesDisplay = "无";
        if (!modulesList.isEmpty()) {
            modulesDisplay = StringUtils.join(modulesList, ", ");
            results.put("模块", modulesDisplay);
        }

        results.put("系统时间", DateFormat.getDateTimeInstance(DateFormat.FULL, DateFormat.FULL).format(new Date()));
        results.put("系统时区", TimeZone.getDefault().getDisplayName());
    }

    public static List<String> getCurrentModuleList() {
        List<String> modulesList = new LinkedList<String>();
        if (TaskDictionary.getMainDefinition().hm("ggOffice")) {
            modulesList.add("高级Office");
        }
        if (TaskDictionary.getMainDefinition().hm("sso")) {
            modulesList.add("单点登录");
        }
        if (TaskDictionary.getMainDefinition().hm("sms")) {
            modulesList.add("短信");
        }
        if (TaskDictionary.getMainDefinition().hm("advFile")) {
            modulesList.add("高级文档操作");
        }
        if (TaskDictionary.getMainDefinition().hm("clum")) {
            modulesList.add("集群(主)");
        }
        if (TaskDictionary.getMainDefinition().hm("clus")) {
            modulesList.add("集群(子)");
        }
        if (TaskDictionary.getMainDefinition().hm("outLink")) {
            modulesList.add("外发");
        }
        if (TaskDictionary.getMainDefinition().hm("nkSign")) {
            modulesList.add("高级签章");
        }
        if (TaskDictionary.getMainDefinition().hm("sap")) {
            modulesList.add("SAP集成");
        }
        if (TaskDictionary.getMainDefinition().hm("pdfwm")) {
            modulesList.add("PDF水印");
        }
        if (TaskDictionary.getMainDefinition().hm("docwm")) {
            modulesList.add("Word水印");
        }
        if (TaskDictionary.getMainDefinition().hm("docprint")) {
            modulesList.add("打印编码");
        }
        if (TaskDictionary.getMainDefinition().hm("ocrEngine")) {
            modulesList.add("OCR引擎");
        }
        if (TaskDictionary.getMainDefinition().hm("warehouse")) {
            modulesList.add("虚拟库房");
        }
        if (TaskDictionary.getMainDefinition().hm("vizArc")) {
            modulesList.add("可视化档案馆");
        }
        if (TaskDictionary.getMainDefinition().hm("advScan")) {
            modulesList.add("高级扫描");
        }
        if (TaskDictionary.getMainDefinition().hm("bizScene")) {
            modulesList.add("业务场景化");
        }
        if (TaskDictionary.getMainDefinition().hm("hwSig")) {
            modulesList.add("手写签名");
        }
        if (TaskDictionary.getMainDefinition().hm("video")) {
            modulesList.add("影像转换");
        }
        if (TaskDictionary.getMainDefinition().hm("bdc")) {
            modulesList.add("客户端绑定");
        }
        if (TaskDictionary.getMainDefinition().hm("clfd")) {
            modulesList.add("脱敏模块");
        }
        if (TaskDictionary.getMainDefinition().hm("oos")) {
            modulesList.add("协同编辑");
        }

        return modulesList;
    }

    /**
     * 是否为标准版
     *
     * @return
     */
    public static boolean s() {
        return hw("wdt");
    }

    /**
     * 是否为专业版
     *
     * @return
     */
    public static boolean p2() {
        return hw("wdp");
    }

    /**
     * 是否为图文版
     *
     * @return
     */
    public static boolean w() {
        return hw("wdw");
    }

    /**
     * 使用为旗舰版
     *
     * @return
     */
    public static boolean u() {
        return hw("wdu");
    }

    /**
     * 使用为ECM版
     *
     * @return
     */
    public static boolean r() {
        return hw("wdr");
    }

    /**
     * 使用为ECM档案专版
     *
     * @return
     */
    public static boolean d() {
        return hw("wdd");
    }

    /**
     * 使用为ECM档案单机版
     *
     * @return
     */
    public static boolean sd() {
        return hw("sda");
    }

    /**
     * 使用为文控版
     *
     * @return
     */
    public static boolean dc() {
        return hw("dcc");
    }

    /**
     * 是否为试用版
     *
     * @return
     */
    public static boolean t() {
        if (freeVersion) {
            return true;
        }

        return TaskDictionary.getMainDefinition().fk() == false;
    }

    /**
     * 是否含有高级Office模块
     *
     * @return
     */
    public static boolean mgo() {
        if (freeVersion) {
            return false;
        }

        return TaskDictionary.getMainDefinition().hm("ggOffice");
    }

    /**
     * 是否含有SSO模块
     *
     * @return
     */
    public static boolean msso() {
        if (freeVersion) {
            return false;
        }

        return TaskDictionary.getMainDefinition().hm("sso");
    }

    /**
     * 是否含有短信模块
     *
     * @return
     */
    public static boolean msms() {
        if (freeVersion) {
            return false;
        }

        return TaskDictionary.getMainDefinition().hm("sms");
    }

    /**
     * 是否含有高级文档操作模块
     *
     * @return
     */
    public static boolean maf() {
        if (freeVersion) {
            return false;
        }

        return TaskDictionary.getMainDefinition().hm("advFile");
    }

    /**
     * 是否含有目录邮箱集成
     *
     * @return
     */
    public static boolean mfm() {
        if (freeVersion) {
            return false;
        }

        return TaskDictionary.getMainDefinition().hm("folderMail");
    }

    /**
     * 是否含有NK签章
     *
     * @return
     */
    public static boolean mns() {
        if (freeVersion) {
            return false;
        }

        return TaskDictionary.getMainDefinition().hm("nkSign");
    }

    /**
     * 是否含有外发
     *
     * @return
     */
    public static boolean mol() {
        if (freeVersion) {
            return false;
        }

        return TaskDictionary.getMainDefinition().hm("outLink");
    }

    public static boolean sk() {
        return TaskDictionary.getMainDefinition().sk();
    }

    public static int tt() {
        return TaskDictionary.getMainDefinition().tt();
    }

    public static boolean hw(String wdtString) {
        if (freeVersion) {
            return false;
        }

        if ("wdd".equals(wdtString)) {
            return TaskDictionary.getMainDefinition().d();
        } else if ("sda".equals(wdtString)) {
            return TaskDictionary.getMainDefinition().sda();
        } else if ("wdr".equals(wdtString)) {
            return d() || sd() || TaskDictionary.getMainDefinition().r();
        } else if ("dcc".equals(wdtString)) {
            return TaskDictionary.getMainDefinition().dc();
        } else if ("wdu".equals(wdtString)) {
            return dc() || d() || sd() || r() || TaskDictionary.getMainDefinition().u();
        } else if ("wdw".equals(wdtString)) {
            return dc() || d() || sd() || r() || u() || TaskDictionary.getMainDefinition().w();
        } else if ("wdp".equals(wdtString)) {
            return dc() || d() || sd() || r() || w() || TaskDictionary.getMainDefinition().p2();
        } else if ("wdt".equals(wdtString)) {
            return dc() || d() || sd() || r() || u() || w() || p2() || TaskDictionary.getMainDefinition().s();
        }

        return false;
    }
}
