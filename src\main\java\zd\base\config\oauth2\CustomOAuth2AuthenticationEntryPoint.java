package zd.base.config.oauth2;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.server.resource.BearerTokenError;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;
import zd.base.utils.JSONResultUtils;

import java.io.IOException;

/**
 * 自定义OAuth2认证入口点
 * 用于处理未认证或认证失败的请求
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomOAuth2AuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final ObjectMapper objectMapper;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException)
            throws IOException {
        log.error("OAuth2认证失败: {}", authException.getMessage(), authException);

        HttpStatus status = HttpStatus.UNAUTHORIZED;

        JSONResultUtils<Object> objectJSONResultUtils = null;
        if (authException instanceof OAuth2AuthenticationException) {
            OAuth2Error error = ((OAuth2AuthenticationException) authException).getError();
            String errorCode = error.getErrorCode();
            String errorDescription = error.getDescription();

            // 对于BearerTokenError，我们直接使用UNAUTHORIZED状态码
            if (error instanceof BearerTokenError) {
                status = HttpStatus.UNAUTHORIZED;
            }

            objectJSONResultUtils = JSONResultUtils.errorWithMsg(errorCode, errorDescription);
        } else {
            objectJSONResultUtils = JSONResultUtils.errorWithMsg("authentication_error", authException.getMessage());
        }

        response.setStatus(status.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        objectMapper.writeValue(response.getWriter(), objectJSONResultUtils);
    }
}
