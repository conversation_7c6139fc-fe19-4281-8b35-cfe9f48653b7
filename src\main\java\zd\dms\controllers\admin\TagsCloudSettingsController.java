package zd.dms.controllers.admin;

import io.micrometer.common.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapUtils;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.utils.JSONUtils;
import zd.dms.utils.admin.TagsCloudUtils;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "TagsCloudSettingsController", description = "词云标签设置Controller")
@RequestMapping("/admin/settings/tagsCloud")
public class TagsCloudSettingsController extends ControllerSupport {

    private SystemConfigManager scm = SystemConfigManager.getInstance();

    @Operation(summary = "获取标签词云设置")
    @GetMapping("/getAllTags")
    @ZDLog("获取标签词云设置")
    public JSONResultUtils<Object> getAllTags() {
        String tagsCloudStr = scm.getProperty("tagsCloud");
        List<Map<String, Object>> results = null;
        if (StringUtils.isNotBlank(tagsCloudStr)) {
            results = JSONUtils.parseObject(tagsCloudStr, List.class);
        }

        if (CollectionUtils.isEmpty(results)) {
            results = TagsCloudUtils.getDefaultTagsClouds();
        }

        return JSONResultUtils.successWithData(results);
    }

    @Operation(summary = "获取标签词云数量")
    @GetMapping("/getTagsCount")
    @ZDLog("获取标签词云数量")
    public JSONResultUtils<Object> getTagsCount() {
        Map<String, Object> tagsCount = TagsCloudUtils.getTagsCount();
        return JSONResultUtils.successWithData(tagsCount);
    }

    @Operation(summary = "保存标签词云设置")
    @PostMapping("/saveAllTags")
    @ZDLog("保存标签词云设置")
    public JSONResultUtils<Object> saveAllTags(@RequestBody Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            return error("参数为空");
        }

        List<Map<String, Object>> tagsCloud = ZDMapUtils.getListMapValue(params, "tagsCloud");
        scm.setProperty("tagsCloud", JSONUtils.toJSONString(tagsCloud, "[]"));

        return success();
    }
}
