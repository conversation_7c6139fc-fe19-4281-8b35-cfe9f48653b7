package zd.dms.repositories.im;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.InstantMessage;

import java.util.Date;
import java.util.List;

public interface InstantMessageRepository extends BaseRepository<InstantMessage, Long> {

    void deleteMessagesByUsername(String username);

    void deleteMessagesBeforeDate(Date date);

    List<InstantMessage> getMessagesUnread(String username);

    void setRecevied(InstantMessage im);

    List<InstantMessage> searchMessages(String keyword, String type,
                                        String username);

    Page getMessages(String type, String username, int pageNumber, int pageSize);

    Page getReceviedMessages(String type, String username, int pageNumber,
                             int pageSize);

    List<InstantMessage> getLatestMessages(int count, String type,
                                           String target, String sender);

    void deleteMsgById(long id);

    int getCount();

    int getUnreadCount(String username);

    void receiveAllMsg(String username);
}
