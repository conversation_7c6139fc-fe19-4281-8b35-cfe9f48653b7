package zd.dms.entities;

import jakarta.persistence.*;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.entities.AbstractEntity;
import zd.base.entities.PropertyAware;
import zd.base.utils.system.SpringUtils;
import zd.dms.services.document.FolderService;
import zd.dms.services.rule.FolderRuleService;
import zd.dms.services.rule.RuleUtils;
import zd.dms.utils.PropertyUtils;
import zd.dms.utils.TextUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "folder_rule")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class FolderRule extends AbstractEntity implements PropertyAware {

	private static final Logger log = LoggerFactory.getLogger(FolderRule.class);

	/**
	 * Serial
	 */
	private static final long serialVersionUID = 1L;

	public static final String ACTION_UPLOAD = "1";

	public static final String ACTION_RENAME = "2";

	public static final String ACTION_MOVE = "3";

	public static final String ACTION_UPDATE = "4";

	public static final String ACTION_VERSIONCHANGE = "5";

	public static final String ACTION_SNCHANGE = "6";

	public static final String ACTION_PIEND = "7";

	public static final String ACTION_COPY = "8";

	public static final String ACTION_REMIND = "9";

	public static final String ACTION_DEL_PI = "A";

	public static final String[] ACTION_TYPES = new String[] { "1", "2", "3", "4", "5", "6", "7", "8", "9", "A" };

	@Index(name = "i_frule_folderid")
	private long folderId;

	private String creatorFullname;

	/**
	 * 规则名称
	 */
	private String name;

	/**
	 * 规则描述
	 */
	private String description;

	/**
	 * 是否可用
	 */
	@Column(nullable = false)
	private boolean enabled;

	/**
	 * 规则动作
	 */
	private String actionType;

	@Transient
	private Map<String, String> propertiesMap;

	@Column(length = Length.LOB_DEFAULT)
	private String properties;

	/**
	 * 创建时间
	 */
	@Index(name = "i_frule_cd")
	@Column(nullable = false)
	private Date creationDate;

	/**
	 * 默认构造器
	 */
	public FolderRule() {
		super();
		enabled = true;
		creationDate = new Date();
	}

	public String getDisplayActionType() {
		return RuleUtils.getDisplayActionType(actionType);
	}

	public Folder getFolder() {
		FolderService folderService = SpringUtils.getBean(FolderService.class);
		return folderService.getById(folderId);
	}

	public List<FolderRuleAction> getFolderRuleActions() {
		FolderRuleService folderRuleService = SpringUtils.getBean(FolderRuleService.class);
		return folderRuleService.getActionsByRule(this.id);
	}

	public List<FolderRuleCondition> getFolderRuleConditions() {
		FolderRuleService folderRuleService = SpringUtils.getBean(FolderRuleService.class);
		return folderRuleService.getConditionsByRule(this.id);
	}

	/**
	 * 设置属性
	 * 
	 * @param name 名称
	 * @param value 值
	 */
	public void setProperty(String name, String value) {
		initPropertiesMap();
		propertiesMap.put(name, value);
		PropertyUtils.updateProperties(this);
	}

	/**
	 * 移除属性
	 * 
	 * @param name 要移除的属性名称
	 */
	public void removeProperty(String name) {
		initPropertiesMap();
		propertiesMap.remove(name);
	}

	/**
	 * 获取属性
	 * 
	 * @param name 名称
	 * @return 值
	 */
	public String getProperty(String name) {
		initPropertiesMap();
		return propertiesMap.get(name);
	}

	public String getName() {
		return TextUtils.escapeXml(name);
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return TextUtils.escapeXml(description);
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public boolean isEnabled() {
		return enabled;
	}

	public void setEnabled(boolean enabled) {
		this.enabled = enabled;
	}

	public Map<String, String> getPropertiesMap() {
		initPropertiesMap();
		return propertiesMap;
	}

	public void setPropertiesMap(Map<String, String> propertiesMap) {
		this.propertiesMap = propertiesMap;
	}

	public void initPropertiesMap() {
		if (this.propertiesMap == null) {
			this.propertiesMap = new HashMap<>();

			PropertyUtils.updatePropertiesMap(this);
		}
	}

	public String getProperties() {
		return properties;
	}

	public void setProperties(String properties) {
		this.properties = properties;
	}

	public String getActionType() {
		return actionType;
	}

	public void setActionType(String ruleAction) {
		this.actionType = ruleAction;
	}

	public long getFolderId() {
		return folderId;
	}

	public void setFolderId(long folderId) {
		this.folderId = folderId;
	}

	public String getCreatorFullname() {
		return creatorFullname;
	}

	public void setCreatorFullname(String creatorFullname) {
		this.creatorFullname = creatorFullname;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}
}
