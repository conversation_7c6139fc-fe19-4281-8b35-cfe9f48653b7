package zd.base.tasks;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zd.base.utils.system.PropsUtils;
import zd.base.utils.system.SpringUtils;
import zd.dms.entities.LogMessage;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.log.LogService;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.services.wx.WxSyncService;
import zd.dms.utils.wx.WxBaseUtils;

@Component
@Slf4j
public class SyncGroupAndUserForWxTask extends AbstractTask {

    @Autowired
    private WxSyncService wxSyncService;

    @Autowired
    private LogService logService;

    @Scheduled(cron = "0 0 23 * * ?")
    @Scheduled(initialDelay = 60000)
    @Override
    public void work() {
        if (!PropsUtils.getBoolProps("WxSyncEnabled", false)) {
            return;
        }

        SystemConfigManager scm = SystemConfigManager.getInstance();
        String qywxCorpId = scm.getProperty("qywxCorpId");
        String qywxCorpSecret = scm.getProperty("qywxCorpSecret");
        if (StringUtils.isBlank(qywxCorpId) || StringUtils.isBlank(qywxCorpSecret)) {
            logService.addMessage(LogMessage.LEVEL_ERROR, UserGroupUtils.ADMIN_USERNAME, "127.0.0.1", "自动同步失败，原因：未设置corpId或者secret");
        }
        try {
            String accessToken = WxBaseUtils.getAccessToken(qywxCorpId, qywxCorpSecret);
            wxSyncService.sync(accessToken, "127.0.0.1");
        } catch (Exception e) {
            logService.addMessage(LogMessage.LEVEL_ERROR, UserGroupUtils.ADMIN_USERNAME, "127.0.0.1", "自动同步失败，原因：" +
                    ("".equals(e.getMessage()) ? "系统错误" : e.getMessage()));
        }
    }
}
