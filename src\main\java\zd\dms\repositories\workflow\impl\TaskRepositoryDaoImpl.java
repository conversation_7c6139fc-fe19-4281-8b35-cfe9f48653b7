package zd.dms.repositories.workflow.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.User;
import zd.dms.repositories.workflow.TaskRepository;
import zd.dms.utils.PageableUtils;
import zd.dms.utils.repositories.PredicateUtils;
import zd.dms.utils.repositories.SpecTools;
import zd.dms.workflow.entities.SSTask;
import zd.record.utils.ECMUtils;
import zd.record.utils.RecordDBUtils;
import zd.record.utils.ZDStringEscapeUtils;

import java.util.*;

@Slf4j
public class TaskRepositoryDaoImpl extends BaseRepositoryDaoImpl<SSTask, String> implements TaskRepository {

    public TaskRepositoryDaoImpl(Class<SSTask> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public Page getForwardTasks(String username, String resourceType, final String recPiType, int page, int pageSize) {
        if (!ECMUtils.filterSearchSql(recPiType)) {
            return PageableUtils.genPage(null, page, pageSize, 0);
        }

        Specification<SSTask> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSTask> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("beforeForwardAssignee", username);
            specTools.eq("resourceType", resourceType);

            String tempRecPiType = StringUtils.trim(recPiType);
            if (StringUtils.isNotBlank(tempRecPiType) && !"all".equals(tempRecPiType)) {
                if ("lend".equalsIgnoreCase(tempRecPiType)) {
                    specTools.or(PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendElectric"), PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendPaper"));
                } else {
                    specTools.equal("pdType", tempRecPiType);
                }
            }

            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec, page, pageSize);
    }

    @Override
    public Page getAgentTasks(String username, String resourceType, int page, int pageSize) {
        Specification<SSTask> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSTask> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("beforeAgentAssignee", username);
            specTools.eq("resourceType", resourceType);
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec, page, pageSize);
    }

    @Override
    public SSTask getUniqueUnCompleteTask(String piId, String nodeId, String username) {
        Specification<SSTask> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSTask> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("piId", piId);
            specTools.eq("nodeId", nodeId);
            specTools.eq("assignee", username);

            specTools.ne("status", SSTask.STATUS_COMPLETED);
            specTools.ne("status", SSTask.STATUS_CHEXIAO);
            specTools.ne("status", SSTask.STATUS_NODE_CHEXIAO);
            specTools.ne("status", SSTask.STATUS_PI_CHEXIAO);

            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findSingleObject(spec);
    }

    @Override
    public List<SSTask> searchTask(String keywords, User operator, int status, String resType, Date startDate, Date endDate) {
        if ((StringUtils.isBlank(keywords) && startDate == null && endDate == null) || operator == null) {
            return new ArrayList<SSTask>();
        }

        keywords = ZDStringEscapeUtils.escapeSql(keywords);

        String hql = "from SSTask p where assignee = '" + operator.getUsername() + "' and status = " + status +
                " and resourceType='" + resType + "'";

        if (StringUtils.isNotBlank(keywords)) {
            hql += " and (resourceName like '%" + keywords + "%' OR piCreator like '%" + keywords + "%' OR piCreatorFullname like '%" + keywords + "%')";
        }

        List<Object> queryList = new LinkedList<Object>();
        if (startDate != null) {
            hql += " and creationDate > ?1 ";
            queryList.add(startDate);
        }
        if (endDate != null) {
            hql += " and creationDate < ?2 ";
            queryList.add(endDate);
        }

        Object[] array = queryList.toArray();

        return findAll(hql, array);
    }

    @Override
    public int getPendingTaskCount(String piId, String nodeId) {
        return NumberUtils.toInt(String
                .valueOf(findObject("select count(*) from SSTask where status=1 and piId='" + piId +
                        "' and nodeId='" + nodeId + "'")));
    }

    @Override
    public int getUnCompleteTaskCount(String username) {
        String queryString = "select count(*) from wftask where status != 100 and status != 4 and status != 5 and status != 6 and assignee='" +
                username + "'";

        return RecordDBUtils.checkJdbcTemplate().queryForObject(queryString, Integer.class);
    }

    @Override
    public List<Map<String, Object>> getUnCompleteTaskCountMap(String username) {
        String queryString = String.format("select count(*) count,resourceType,pdType from wftask where status != 100 and status != 4 and status != 5 and status != 6 and assignee='%s' group by resourceType,pdType", username);

        return RecordDBUtils.checkJdbcTemplate().queryForList(queryString);
    }

    @Override
    public int getUnCompleteTaskCount(String username, String resourceType, String recPiType) {
        String queryString = "select count(*) from wftask where status != 100 and status != 4 and status != 5 and assignee='" +
                username + "'";

        if (StringUtils.isNotBlank(resourceType)) {
            queryString += " and resourceType = '" + resourceType + "'";
        }

        recPiType = StringUtils.trim(recPiType);
        if (StringUtils.isNotBlank(recPiType) && !"all".equals(recPiType)) {
            if ("lend".equalsIgnoreCase(recPiType)) {
                queryString += " and (pdType = 'lendElectric' or pdType = 'lendPaper')";
            } else {
                queryString += " and pdType = '" + recPiType + "'";
            }
        }

        return RecordDBUtils.checkJdbcTemplate().queryForObject(queryString, Integer.class);
    }

    @Override
    public List<SSTask> getTasks(String piId, String nodeId) {
        Specification<SSTask> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSTask> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("piId", piId);
            specTools.eq("nodeId", nodeId);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<SSTask> getTasksSortByEndDate(String piId, String nodeId) {
        Specification<SSTask> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSTask> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("piId", piId);
            specTools.eq("nodeId", nodeId);
            specTools.desc("endDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public Page getTasks(String username, String resourceType, int page, int pageSize, int status) {
        Specification<SSTask> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSTask> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("assignee", username);
            specTools.eq("resourceType", resourceType);
            if (status > -1) {
                specTools.eq("status", status);
            }
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec, page, pageSize);
    }

    @Override
    public Page getCompletedTasks(String username, String resourceType, final String recPiType, int page, int pageSize) {
        if (!ECMUtils.filterSearchSql(recPiType)) {
            return PageableUtils.genPage(null, page, pageSize, 0);
        }

        Specification<SSTask> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSTask> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("assignee", username);
            specTools.eq("resourceType", resourceType);
            specTools.eq("status", SSTask.STATUS_PENGDING);

            String tempRecPiType = StringUtils.trim(recPiType);
            if (StringUtils.isNotBlank(tempRecPiType) && !"all".equals(tempRecPiType)) {
                if ("lend".equalsIgnoreCase(tempRecPiType)) {
                    specTools.or(PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendElectric"), PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendPaper"));
                } else {
                    specTools.equal("pdType", tempRecPiType);
                }
            }

            specTools.desc("endDate");

            return specTools.getRestriction();
        };

        return findAll(spec, page, pageSize);
    }

    @Override
    public Page searchUnCompleteTasks(String keywords, String username, String resourceType, final String pdType, int page, int pageSize) {
        if (!ECMUtils.filterSearchSql(keywords)) {
            return PageableUtils.genPage(null, page, pageSize, 0);
        }

        if (!ECMUtils.filterSearchSql(pdType)) {
            return PageableUtils.genPage(null, page, pageSize, 0);
        }

        Specification<SSTask> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSTask> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("assignee", username);
            specTools.eq("resourceType", resourceType);

            specTools.ne("status", SSTask.STATUS_COMPLETED);
            specTools.ne("status", SSTask.STATUS_CHEXIAO);
            specTools.ne("status", SSTask.STATUS_NODE_CHEXIAO);
            specTools.ne("status", SSTask.STATUS_PI_CHEXIAO);

            String tempRecPiType = StringUtils.trim(pdType);
            if (StringUtils.isNotBlank(tempRecPiType) && !"all".equals(tempRecPiType)) {
                if ("lend".equalsIgnoreCase(tempRecPiType)) {
                    specTools.or(PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendElectric"), PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendPaper"));
                } else {
                    specTools.equal("pdType", tempRecPiType);
                }
            }

            if (StringUtils.isNotBlank(keywords)) {
                Predicate resourceNameLike = PredicateUtils.like(root, criteriaBuilder, "resourceName", keywords);
                Predicate piNameLike = PredicateUtils.like(root, criteriaBuilder, "piName", keywords);
                Predicate piCreatorLike = PredicateUtils.like(root, criteriaBuilder, "piCreator", keywords);
                Predicate piCreatorFullnameLike = PredicateUtils.like(root, criteriaBuilder, "piCreatorFullname", keywords);

                specTools.or(resourceNameLike, piNameLike, piCreatorLike, piCreatorFullnameLike);
            }

            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec, page, pageSize);
    }

    @Override
    public Page searchTransferTasks(String keywords, String username, String resourceType, String pdType, int page,
                                    int pageSize) {

        if (!ECMUtils.filterSearchSql(keywords)) {
            return PageableUtils.genPage(null, page, pageSize, 0);
        }

        if (!ECMUtils.filterSearchSql(pdType)) {
            return PageableUtils.genPage(null, page, pageSize, 0);
        }

        Specification<SSTask> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSTask> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("sourceUsername", username);
            specTools.eq("resourceType", resourceType);

            String tempRecPiType = StringUtils.trim(pdType);
            if (StringUtils.isNotBlank(tempRecPiType) && !"all".equals(tempRecPiType)) {
                if ("lend".equalsIgnoreCase(tempRecPiType)) {
                    specTools.or(PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendElectric"), PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendPaper"));
                } else {
                    specTools.equal("pdType", tempRecPiType);
                }
            }

            if (StringUtils.isNotBlank(keywords)) {
                Predicate resourceNameLike = PredicateUtils.like(root, criteriaBuilder, "resourceName", keywords);
                Predicate piNameLike = PredicateUtils.like(root, criteriaBuilder, "piName", keywords);
                Predicate piCreatorLike = PredicateUtils.like(root, criteriaBuilder, "piCreator", keywords);
                Predicate piCreatorFullnameLike = PredicateUtils.like(root, criteriaBuilder, "piCreatorFullname", keywords);

                specTools.or(resourceNameLike, piNameLike, piCreatorLike, piCreatorFullnameLike);

            }

            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec, page, pageSize);
    }

    @Override
    public Page searchCompleteTasks(String keywords, String username, String resourceType, final String pdType, int page, int pageSize) {
        if (!ECMUtils.filterSearchSql(keywords)) {
            return PageableUtils.genPage(null, page, pageSize, 0);
        }

        if (!ECMUtils.filterSearchSql(pdType)) {
            return PageableUtils.genPage(null, page, pageSize, 0);
        }

        Specification<SSTask> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSTask> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("assignee", username);
            specTools.eq("resourceType", resourceType);

            specTools.in("status", SSTask.STATUS_COMPLETED, SSTask.STATUS_CHEXIAO, SSTask.STATUS_NODE_CHEXIAO, SSTask.STATUS_PI_CHEXIAO);

            String tempRecPiType = StringUtils.trim(pdType);
            if (StringUtils.isNotBlank(tempRecPiType) && !"all".equals(tempRecPiType)) {
                if ("lend".equalsIgnoreCase(tempRecPiType)) {
                    specTools.or(PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendElectric"), PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendPaper"));
                } else {
                    specTools.equal("pdType", tempRecPiType);
                }
            }

            if (StringUtils.isNotBlank(keywords)) {
                Predicate resourceNameLike = PredicateUtils.like(root, criteriaBuilder, "resourceName", keywords);
                Predicate piNameLike = PredicateUtils.like(root, criteriaBuilder, "piName", keywords);
                Predicate piCreatorLike = PredicateUtils.like(root, criteriaBuilder, "piCreator", keywords);
                Predicate piCreatorFullnameLike = PredicateUtils.like(root, criteriaBuilder, "piCreatorFullname", keywords);

                specTools.or(resourceNameLike, piNameLike, piCreatorLike, piCreatorFullnameLike);

            }

            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec, page, pageSize);
    }

    @Override
    public List<SSTask> getUnCompleteTaskList(String username, String resourceType) {
        Specification<SSTask> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSTask> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("assignee", username);
            specTools.eq("resourceType", resourceType);

            specTools.ne("status", SSTask.STATUS_COMPLETED);
            specTools.ne("status", SSTask.STATUS_CHEXIAO);
            specTools.ne("status", SSTask.STATUS_NODE_CHEXIAO);
            specTools.ne("status", SSTask.STATUS_PI_CHEXIAO);

            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<SSTask> getTasks(String piId) {
        Specification<SSTask> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSTask> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("piId", piId);

            specTools.desc("endDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<SSTask> getUncompleteTaskByPi(String piId) {
        if (StringUtils.isBlank(piId)) {
            return new ArrayList<SSTask>();
        }

        String hql = "from SSTask p where piId = '" + piId + "' and status = 1";
        return findAll(hql);
    }

    @Override
    public void deleteTasksByPI(String piId) {
        String hql = "delete from SSTask where piId = ?1";
        executeUpdate(hql, piId);
    }

    @Override
    public void deleteCarbonCopyByPI(String piId) {
        String hql = "delete from SSCarbonCopy where piId = ?1";
        executeUpdate(hql, piId);
    }

    /*@Override
    public void deleteTasksByNodes(String piId, List<SSTaskNode> nodes) {
        if (nodes == null || nodes.size() == 0) {
            log.warn("wrong nodes size: {}", nodes);
            return;
        }

        StringBuilder sb = new StringBuilder();
        sb.append("delete from SSTask where piId='");
        sb.append(piId);
        sb.append("' and (");

        int index = 0;
        for (SSTaskNode node : nodes) {
            if (index > 0) {
                sb.append(" OR ");
            }

            sb.append("nodeId='");
            sb.append(node.getId());
            sb.append("'");
            index++;
        }

        sb.append(")");

        log.debug("deleteTasksByNodes hql: {}", sb.toString());

        executeUpdate(sb.toString());
    }*/

    @Override
    public void deleteTasksByNodes(String piId, String nodeId) {
        String hql = "delete from SSTask where piId=? and nodeId=?1";
        executeUpdate(hql, piId, nodeId);
    }

    @Override
    public List<SSTask> getAllUnCompleteTasks() {
        String hql = "from SSTask p where status = 1";
        return findAll(hql);
    }
}
