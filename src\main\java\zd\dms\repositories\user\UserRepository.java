package zd.dms.repositories.user;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.Group;
import zd.dms.entities.User;

import java.util.List;
import java.util.Set;

public interface UserRepository extends BaseRepository<User, String> {

    List<User> getUsersByGroupId(long groupId);

    User getUserByQywxEmployeeId(String qywxId);

    User getUserByFeishuUserId(String userId);

    int getUserCountByGroup(Group group);

    /**
     * 获取指定用户名对应的User对象
     *
     * @param username 要获取的用户名
     * @return 指定用户名对应的User对象，用户不存在就返回null
     */
    User getUser(String username);

    List<User> getUserByUsernames(List<String> usernames);

    User getUserByDing(String dingEmployeeId);

    User getUserByIdCode(String idCode);

    User getUserByCode(String code);

    List<User> getUsersByGroup(Group group);

    /**
     * 获取有角色的用户列表
     *
     * @return 有角色的用户列表
     */
    List<User> getUsersInRole();

    /**
     * 根据用户名或全名搜索用户
     *
     * @param name
     * @return
     */
    List<User> searchUserByName(String name);

    /**
     * 获取用户数
     *
     * @return value
     */
    int getUC();

    /**
     * 获取已设置了同步的用户数
     *
     * @return
     */
    int getUserCountWithSync();

    int getUserCountWithSync(User exclude);

    int getUserCount(boolean enabled);

    void updateNullNumIndex(int numIndex);

    /**
     * 获取所有从丁丁回来的用户的userId
     *
     * @return
     */
    public Set<String> getDingUserIds();

    User getUserByDingUserId(String dingUserId);

    List<User> searchUserByName(String name, String nameSeparator);

    Page getPageUsers(List<String> userIds, int pageNumber, int pageSize);

    Page getUserPage(List<String> userIds, int pageNumber, int pageSize, String username);
}
