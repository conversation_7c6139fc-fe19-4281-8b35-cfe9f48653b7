import {Menu} from '../types/menu';
import {Role} from "config/user/role";

export const MAIN_MENU_TOP_ITEMS: Array<Menu> = [
  {
    id: '01',
    label: '个人中心',
    icon: 'mdi-account-circle-outline',
    fixed: true,
    children: [
      {
        id: '0101',
        label: '借阅中心',
        inset: 1,
        to: '/lendCenter'
      },
      {
        id: '0102',
        label: '提示消息',
        inset: 1,
        to: '/instantMessage'
      },
      {
        id: '0103',
        label: '档案年报',
        inset: 1,
        to: '/reportTemplates'
      },
      // {
      //   id: '0104',
      //   label: '外发',
      //   inset: 1,
      // },
      // {
      //   id: '0105',
      //   label: '报表中心',
      //   inset: 1,
      // },
      {
        id: '0106',
        label: '任务中心',
        inset: 1,
        to: '/usertask'
      },
    ],
  },
  {
    id: '02',
    label: '审批中心',
    icon: 'mdi-sitemap-outline',
    to: '/approval',
    fixed: true,
  },
  {
    id: '03',
    label: '档案门户',
    icon: 'mdi-view-dashboard-outline',
    to: '/portal',
    fixed: true,
  },
  {
    id: '04',
    label: '档案搜索',
    icon: 'mdi-file-search-outline',
    to: '/search',
    fixed: true,
  },
  // {
  //   id: '4',
  //   label: '档案收集',
  //   icon: 'note_add',
  //   to: '/record/collection',
  // },
  // {
  //   id: '5',
  //   label: '档案整理',
  //   icon: 'task',
  //   to: '/record/dataArrangement',
  // },
  // {
  //   id: '6',
  //   label: '档案利用',
  //   icon: 'description',
  //   children: [
  //     {
  //       id: '61',
  //       label: '档案库',
  //       inset: 1,
  //       to: '/record/roomStorage',
  //     },
  //     {
  //       id: '62',
  //       label: '移交库',
  //       inset: 1,
  //       to: '/record/transfer',
  //     },
  //     {
  //       id: '63',
  //       label: '专题库',
  //       inset: 1,
  //       to: '/record/subject',
  //     },
  //     {
  //       id: '64',
  //       label: '编研库',
  //       inset: 1,
  //       to: '/record/compilation',
  //     },
  //     {
  //       id: '65',
  //       label: '法规库',
  //       inset: 1,
  //       to: '/record/legal',
  //     },
  //   ],
  // },
  // {
  //   id: '7',
  //   label: '档案销毁',
  //   icon: 'delete_forever',
  //   children: [
  //     {
  //       id: '71',
  //       label: '销毁库',
  //       inset: 1,
  //     },
  //     {
  //       id: '72',
  //       label: '销毁鉴定',
  //       inset: 1,
  //     },
  //   ],
  // },
  // {
  //   id: '8',
  //   label: '档案统计',
  //   icon: 'leaderboard',
  // },
  // {
  //   id: '9',
  //   label: '库房管理',
  //   icon: 'shelves',
  //   children: [
  //     {
  //       id: '91',
  //       label: '库房监控',
  //       inset: 1,
  //     },
  //     {
  //       id: '93',
  //       label: '虚拟库房',
  //       inset: 1,
  //     },
  //     {
  //       id: '94',
  //       label: '可视化档案馆',
  //       inset: 1,
  //     },
  //   ],
  // },
  // {
  //   id: 10,
  //   label: '文档管理',
  //   icon: 'icon-folder',
  // },
];

export const MAIN_MENU_BOTTOM_ITEMS: Array<Menu> = [
  {
    id: '05',
    label: '流程表单',
    icon: 'mdi-folder-arrow-left-right-outline',
    to: '/record/workflow',
    cacheName: 'RecordListPage',
    roles: [Role.SYSTEM_ADMIN, Role.ECM_ADMIN],
    fixed: true,
  },
  {
    id: '06',
    label: '系统设置',
    icon: 'mdi-cog-outline',
    to: '/admin',
    cacheName: 'AdminIndexPage',
    roles: [Role.SYSTEM_ADMIN, Role.ECM_ADMIN, Role.LIMITED_ADMIN, Role.USER_ADMIN, Role.LOGS_ADMIN, Role.WORKFLOW_ADMIN, Role.DCC_ADMIN],
    fixed: true,
  },
  // {
  //   id: '07',
  //   label: '帮助中心',
  //   icon: 'mdi-help-circle-outline',
  //   roles: [],
  //   fixed: true,
  //   children: [
  //     {
  //       id: '0701',
  //       label: '关于系统',
  //       inset: 1,
  //     },
  //   ],
  // },
];

export const APPROVAL_MENU_ITEMS = [
  {
    id: 'rec-all',
    label: '全部审批',
    typeName: 'rec-all',
    resourceType: 'rec',
    pdType: 'all',
  },
  {
    id: 'rec-normal',
    label: '档案审批',
    typeName: 'rec-normal',
    resourceType: 'rec',
    pdType: 'normal',
  },
  {
    id: 'rec-lend',
    label: '借阅审批',
    typeName: 'rec-lend',
    resourceType: 'rec',
    pdType: 'lend',
  },
  {
    id: 'rec-destroy',
    label: '销毁审批',
    typeName: 'rec-destroy',
    resourceType: 'rec',
    pdType: 'destroy',
  },
  {
    id: 'rec-archive',
    label: '归档审批',
    typeName: 'rec-archive',
    resourceType: 'rec',
    pdType: 'archive',
  },
];

export const SETTINGS_MENU_ITEMS = [
  // 系统管理
  {
    label: '系统管理',
    type: 'label',
  },
  {
    label: '系统信息',
    to: 'system-info',
  },
  {
    label: '系统设置',
    to: 'settings',
  },
  {
    label: '组织架构',
    to: 'organization',
  },
  {
    label: '岗位管理',
    to: 'position',
  },
  {
    label: '目录分区',
    to: 'folderArea',
  },
  {
    type: 'separator',
  },
  // 业务管理
  {
    label: '业务管理',
    type: 'label',
  },
  {
    label: '档案表管理',
    to: 'dataStructManager',
  },
  {
    label: '打印模板',
    to: 'printTplManager',
  },
  {
    label: '流程管理',
    to: 'workflowManager',
  },
  // {
  //   label: '档案报表',
  // },
  // {
  //   label: '虚拟库房',
  // },
  {
    type: 'separator',
  },
  // 运维管理
  {
    label: '运维管理',
    type: 'label',
  },
  {
    label: '邮件设置',
    to: 'mailSettings',
  },
  {
    label: 'OAuth2管理',
    to: 'oauth2Client',
  },
  {
    label: '消息队列',
    to: 'ZDMQManager',
  },
  {
    label: '缓存管理',
    to: 'redisCache',
  },
  {
    label: '索引管理',
    to: 'esManager',
  },
  // {
  //   label: 'LDAP设置',
  // },
  {
    label: '系统监控',
    to: 'systemMonitor',
  },
];
