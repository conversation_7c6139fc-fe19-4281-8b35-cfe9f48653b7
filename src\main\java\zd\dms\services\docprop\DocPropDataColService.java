//                    佛祖保佑       永无BUG
//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                           O\  =  /O
//                        ____/`---'\____
//                      .'  \\|     |//  `.
//                     /  \\|||  :  |||//  \
//                    /  _||||| -:- |||||-  \
//                    |   | \\\  -  /// |   |
//                    | \_|  ''\---/''  |   |
//                    \  .-\__  `-`  ___/-. /
//                  ___`. .'  /--.--\  `. . __
//               ."" '<  `.___\_<|>_/___.'  >'"".
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |
//              \  \ `-.   \_ __\ /__ _/   .-` /  /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
package zd.dms.services.docprop;

import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.dms.entities.DocPropDataCol;

import java.util.List;

@Transactional
public interface DocPropDataColService extends BaseJpaService<DocPropDataCol, Long> {
    public int getNumByColName(String colName);

    public void save(DocPropDataCol dataCol);

    public List<DocPropDataCol> getColumns();

    public DocPropDataCol getPropDataColById(long id);

    public void updateDataCol(DocPropDataCol dataCol);

    public void deleteDataColById(long colId);

    public DocPropDataCol getDocPropDataColByName(String colName);

    public List<String> getDocPropColByFolderId(long FolderId);
}
