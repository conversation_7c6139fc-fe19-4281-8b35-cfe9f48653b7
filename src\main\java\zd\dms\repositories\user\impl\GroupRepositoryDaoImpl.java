package zd.dms.repositories.user.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.JdbcTemplate;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.Group;
import zd.dms.repositories.user.GroupRepository;
import zd.dms.utils.PageableUtils;
import zd.dms.utils.db.ZDJoinTableGroupUserUtils;
import zd.dms.utils.repositories.QueryParamsUtils;
import zd.record.utils.RecordDBUtils;

import java.util.*;

public class GroupRepositoryDaoImpl extends BaseRepositoryDaoImpl<Group, Long> implements GroupRepository {

    public GroupRepositoryDaoImpl(Class<Group> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<Group> getGroupsByUserId(String userId) {
        if (StringUtils.isBlank(userId)) {
            return new ArrayList<>();
        }

        String sql = "select g.* from tdms_group g left join " + RecordDBUtils.getPgName(ZDJoinTableGroupUserUtils.TABLENAME) + " gu on g.id=gu." + RecordDBUtils.getPgName("GROUP_ID") + " where gu." + RecordDBUtils.getPgName("USER_ID") + "='" + userId + "' order by g.numIndex,g.creationDate";
        Query nativeQuery = this.entityManager.createNativeQuery(sql, Group.class);
        return nativeQuery.getResultList();
    }

    @Override
    public List<Map<String, Object>> getChildCount(List<String> parentCodes) {
        if (CollectionUtils.isEmpty(parentCodes)) {
            return new ArrayList<>();
        }

        JdbcTemplate jt = RecordDBUtils.checkJdbcTemplate();
        String sql = String.format("select count(*) count,g.parentCode from tdms_group g where g.parentCode in ('%s') group by g.parentCode", StringUtils.join(parentCodes, "','"));

        return jt.queryForList(sql);
    }

    @Override
    public List<Group> getChildren(String parentCode) {
        if (StringUtils.isBlank(parentCode)) {
            return new ArrayList<>();
        }

        Specification<Group> spec = (root, criteriaQuery, criteriaBuilder) -> {
            /*Join group = root.join("parent", JoinType.LEFT);
            Predicate parentIdEq = criteriaBuilder.equal(group.get("id"), parentId);*/

            Predicate parentCodeEq = criteriaBuilder.equal(root.get("parentCode"), parentCode);

            Order numIndexAsc = criteriaBuilder.asc(root.get("numIndex"));
            Order creationDateAsc = criteriaBuilder.asc(root.get("creationDate"));

            return criteriaQuery.where(parentCodeEq).orderBy(numIndexAsc, creationDateAsc).getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public int getGC() {
        return NumberUtils.toInt(String.valueOf(findObject("select count(*) from Group")));
    }

    @Override
    public List<Group> findGroups(String keywords) {
        return findGroups(keywords, "");
    }

    @Override
    public List<Group> getGroupForLdapDelete() {
        Specification<Group> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            Predicate ldapId = criteriaBuilder.isNotNull(root.get("ldapId"));
            predicates.add(ldapId);

            return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
        };

        return findAll(spec);
    }

    @Override
    public Group getGroupByLdapId(String ldapId) {
        return findSingleObject("from Group where ldapId = ?1", ldapId);
    }

    @Override
    public Group getGroup(String name) {
        return findSingleObject("from Group where name = ?1", name);
    }

    @Override
    public List<Group> getGroups() {
        Specification<Group> spec = (root, criteriaQuery, criteriaBuilder) -> {
            Order numIndexAsc = criteriaBuilder.asc(root.get("numIndex"));
            Order creationDateAsc = criteriaBuilder.asc(root.get("creationDate"));

            return criteriaQuery.orderBy(numIndexAsc, creationDateAsc).getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<Group> getTopGroups() {
        Specification<Group> spec = (root, criteriaQuery, criteriaBuilder) -> {
            /*Predicate parentNull = criteriaBuilder.isNull(root.get("parent"));*/

            Predicate parentNull = criteriaBuilder.isNull(root.get("parentCode"));
            Order numIndexAsc = criteriaBuilder.asc(root.get("numIndex"));
            Order creationDateAsc = criteriaBuilder.asc(root.get("creationDate"));

            return criteriaQuery.where(parentNull).orderBy(numIndexAsc, creationDateAsc).getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<Group> getGroupsInRole() {
        return findAll("from Group g where exists elements(g.roles)");
    }

    @Override
    public Page findGroupsByGroupName(int pageNumber, int pageSize, String groupName, String orderProperty, boolean asc) {
        Specification<Group> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.isNotBlank(groupName)) {
                Predicate nameLike = criteriaBuilder.like(root.get("name"), "%" + groupName + "%");
                predicates.add(nameLike);
            }

            Order orderPropertyOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, orderProperty, asc);

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, orderPropertyOrder);
        };

        return findAll(spec, PageableUtils.getPageable(pageNumber, pageSize));
    }

    @Override
    public List<Group> findGroups(String keywords, boolean hasAllGroup) {
        String hql = "";
        Map<String, Object> params = new HashMap<>();
        params.put("keywords", keywords);

        String likeHqlParamsName = QueryParamsUtils.getLikeHqlParamsName(keywords);
        if (hasAllGroup) {
            hql = "from Group g where (name like " + likeHqlParamsName + ") order by code asc";
        } else {
            hql = "from Group g where name (like " + likeHqlParamsName + ") and name != '所有部门' order by code asc";
        }

        return findAll(hql, params);
    }

    @Override
    public List<Group> getGroupsHasOrder(String orderProperty, boolean asc) {
        String hql = "";
        if (asc) {
            hql = "from Group g order by ?1 asc";
        } else {
            hql = "from Group g order by ?1 desc";
        }
        return findAll(hql, orderProperty);
    }

    @Override
    public Group getGroupByCode(String code) {
        return findSingleObject("from Group where code = ?1", code);
    }

    @Override
    public Group getGroupByGroupIdCode(String groupIdCode) {
        return findSingleObject("from Group where groupIdCode = ?1", groupIdCode);
    }

    @Override
    public Group getGroupBySyncDingGroupId(String syncDingGroupId) {
        return findSingleObject("from Group where syncDingGroupId = ?1", syncDingGroupId);
    }

    @Override
    public Group getGroupBySyncQywxGroupId(String qywxGroupId) {
        return findSingleObject("from Group where syncQywxGroupId = ?1", qywxGroupId);
    }

    @Override
    public Set<String> getSyncDingGroupIds() {
        String hql = "select syncDingGroupId from Group";
        TypedQuery<String> query = this.entityManager.createQuery(hql, String.class);
        List<String> datas = query.getResultList();
        if (datas == null || datas.size() == 0) {
            return null;
        }

        Set<String> result = new HashSet<String>(datas);
        if (result.contains(null)) {
            result.remove(null);
        }

        return result;
    }

    @Override
    public List<Group> findGroups(String keywords, String separator) {
        if (StringUtils.isBlank(keywords)) {
            return new ArrayList<Group>(0);
        }

        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isEmpty(separator)) {
            String keywordsLike = QueryParamsUtils.getLikeHqlParamsName("keywords");
            String hql = "from Group g where (name like " + keywordsLike + ") and name !='所有部门' ";
            params.put("keywords", keywords);
            return findAll(hql, params);
        }

        String[] keywordsArray = keywords.trim().split(separator);
        List<String> sqlWhere = new ArrayList<String>();
        for (String keyword : keywordsArray) {
            if (StringUtils.isNotBlank(keyword)) {
                String keywordLike = QueryParamsUtils.getLikeHqlParamsName(keyword);
                sqlWhere.add(" name like " + keywordLike);
                params.put(keyword, keyword);
            }
        }
        String hql = "from Group g where name != '所有部门' and (";
        hql += StringUtils.join(sqlWhere, " or ");
        hql += ")";
        return findAll(hql, params);
    }
}
