import {api} from 'utils/axios';
import {OAUTH2_CLIENT_PATH} from 'config/resources';

export const oauth2ClientApis = {
  save: (params: any): any => api.post(`${OAUTH2_CLIENT_PATH}/save`, params),
  update: (params: any): any => api.post(`${OAUTH2_CLIENT_PATH}/update`, params),
  delete: (id: string): any => api.get(`${OAUTH2_CLIENT_PATH}/delete/${id}`),
  list: (params: any): any => api.post(`${OAUTH2_CLIENT_PATH}/list`, params),
  get: (id: string): any => api.get(`${OAUTH2_CLIENT_PATH}/get/${id}`),
  batchDelete: (params: any): any => api.post(`${OAUTH2_CLIENT_PATH}/batchDelete`, params),
};
