package zd.dms.repositories.log;

import zd.base.repositories.BaseRepository;
import zd.dms.entities.UserOperateLog;

import java.util.List;

public interface UserOperateLogRepository extends BaseRepository<UserOperateLog, Long> {

    List<UserOperateLog> getLogs(String username, int type);

    List<UserOperateLog> getLogs(int type, int startIndex, int pageSize);

    void createUserOpLog(String operator, String operatorFullname,
                         int operateType, String filename, String folderPath, String docId,
                         long folderId);

    void deleteOutdatedLogs(String operator, int operateType);

    void deleteLogByUserAndTypeAndDocId(String operator, int operateType,
                                        String docId);

    void deleteAllUserOperateLogs(String operator);

    void deleteLogByDocId(String docId);
}
