package zd.dms.repositories.rule;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.FolderRule;

import java.util.List;

public interface FolderRuleRepository extends BaseRepository<FolderRule, String> {

    List<FolderRule> getRulesByFolder(long folderId);

    List<FolderRule> getRulesByFolderAndType(long folderId, String type);

    void deleteRulesByFolder(long folderId);

    Page getAllRules(int page, int pageSize);
}
