package zd.base.exception;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.constant.ErrorCodeDefine;
import zd.dms.services.security.UnauthorizedException;

/**
 * 全局异常处理器
 */
@RestControllerAdvice
@Slf4j
public class BaseExceptionHandler {

    @ExceptionHandler(value = Throwable.class)
    public JSONResultUtils<Object> errorHandler(HttpServletRequest reqest, HttpServletResponse response, Exception e) {
        // SpringMVC映射的参数没传值，导致映射参数失败
        if (e instanceof HttpMessageNotReadableException) {
            log.error("MyExceptionHandler errorHandler", e);
            return JSONResultUtils.error("必传参数为空");
        } else if (e instanceof HttpRequestMethodNotSupportedException) {
            log.debug("MyExceptionHandler errorHandler", e);
            return JSONResultUtils.error("u100001");
        } else if (e instanceof IllegalArgumentException) {
            IllegalArgumentException exception = (IllegalArgumentException) e;
            if (exception.isWriteLog()) {
                log.debug("MyExceptionHandler errorHandler", e);
            }

            return JSONResultUtils.errorWithMsg(exception.getErrorCode(), exception.getMessage());
        } else if (e instanceof java.lang.IllegalArgumentException) {
            log.error("MyExceptionHandler errorHandler", e);
            return JSONResultUtils.error(e.getMessage());
        } else if (e instanceof UnauthorizedException) {
            String message = e.getMessage();
            if (StringUtils.isBlank(message)) {
                message = "无权使用此功能";
            }

            return JSONResultUtils.errorWithMsg(message, message);
        } else if (e instanceof CommonException) {
            CommonException exception = (CommonException) e;
            return JSONResultUtils.errorWithMsg(exception.getErrorCode(), exception.getMessage());
        }

        log.error("MyExceptionHandler errorHandler", e);
        return JSONResultUtils.error(ErrorCodeDefine.SYSTEM_ERROR);
    }
}
