package zd.base.utils.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.jdbc.core.JdbcTemplate;
import zd.base.utils.ZDMapTool;
import zd.base.utils.redis.ZDRedisUtils;
import zd.base.utils.system.PropsUtils;
import zd.dms.entities.Document;
import zd.dms.utils.JSONUtils;
import zd.dms.utils.PageableUtils;
import zd.record.entities.DataCol;
import zd.record.entities.DataStruct;
import zd.record.entities.RecFolder;
import zd.record.utils.RecordDBUtils;

import java.util.*;
import java.util.Map.Entry;

public class ESUtils {

    public static final String RECORD_ES_INDEX = PropsUtils.getProps("esIndexName", "record");

    public static final boolean ES_ENABLE = PropsUtils.getBoolProps("esEnable", true);

    private static String esHost = PropsUtils.getProps("esHost", "http://localhost:9200/");

    // 全局使用ik分词器，当数据量过大时，配置此参数，减少内存占用及提高搜索速度
    // 配置后只能根据有含义的短语搜索，英文及数字必须使用整串字符，不能只搜索部分内容
    public static boolean ES_USE_IK = PropsUtils.getBoolProps("esUseIK", false);

    // es from + size不能大于10000， 因此返回值最大为10000，此值可配置
    public static int ES_MAX_RESULTS_COUNT = PropsUtils.getIntProps("esMaxResultsCount", 10000);

    public static final String REDIS_ES_COLS = "ES_Record_colName";

    public static void main(String[] args) {
        Map<String, Object> params = new HashMap<String, Object>();

        params.put("id", "法规库");
        params.put("文本", "文本5");
        params.put("目录ID", 1);
        params.put("删除人用户名", "");

        createDocumet(MapUtils.getString(params, "id"), "record", params);
        getAllDocuments("test3");

//        Map<String, Object> searchParams = new HashMap<String, Object>();
//        searchParams.put("发布单位", "测试");
//        searchParams.put("文件编号", "a3");
//
//        List<Long> folderIds = new ArrayList<Long>();
//        folderIds.add(1L);
//        folderIds.add(2L);
//
//        esHost = "http://localhost:9200/";
//
//        Page searchDocument = searchDocument("record", searchParams, null, 1, 100);
//        System.out.println(searchDocument.getContent());
    }

    public static void updateDocument(DataStruct dataStruct, DataCol c, long id) {
        if (!ES_ENABLE) {
            return;
        }

        if (dataStruct == null) {
            return;
        }

        List<String> cols = new ArrayList<String>();
        cols.add(c.getName());

        String tableName = dataStruct.getTableName();
        Map<String, Object> recordData = RecordDBUtils.getRecordByTableName(id, cols, tableName);
        if (MapUtils.isEmpty(recordData)) {
            return;
        }

        Map<String, Object> props = new HashMap<String, Object>();
        props.put(c.getName(), recordData.get(c.getName()));

        updateDocument(props, id, tableName);
    }

    public static void setRecMainDataToNullDocumentIndex(RecFolder folder, long parentRecId) {
        if (folder == null) {
            return;
        }

        JdbcTemplate jt = RecordDBUtils.checkJdbcTemplate();
        String sql = "select * from " + folder.getTableName() + " where 主数据ID=?";
        List<Map<String, Object>> records = jt.queryForList(sql, parentRecId);
        if (records == null || records.size() <= 0) {
            return;
        }

        for (Map<String, Object> recordData : records) {
            if (MapUtils.isEmpty(recordData)) {
                continue;
            }

            long recId = MapUtils.getLongValue(recordData, "ID");
            Map<String, Object> props = new HashMap<String, Object>();

            props.put("主数据ID", null);
            props.put("主数据表", null);

            updateDocument(props, recId, folder);
        }
    }

    public static boolean createDocument(Map<String, Object> props, long recId, RecFolder recFolder) {
        if (!ES_ENABLE) {
            return true;
        }

        if (recFolder == null) {
            return true;
        }

        if (!recFolder.isDataFolder()) {
            return true;
        }

        return createDocument(props, recId, recFolder.getTableName());
    }

    public static boolean createDocument(Map<String, Object> props, long recId, String tableName) {
        if (!ES_ENABLE) {
            return true;
        }

        if (recId <= 0 || StringUtils.isBlank(tableName)) {
            return true;
        }

        String indexId = getIndexId(tableName, recId);
        return createDocument(props, indexId);
    }

    public static boolean createDocument(Map<String, Object> props, String indexId) {
        if (!ES_ENABLE) {
            return true;
        }

        if (StringUtils.isBlank(indexId)) {
            return true;
        }

        if (props == null) {
            props = new HashMap<>();
        }

        int index = indexId.lastIndexOf("_");
        if (index > 0) {
            long id = NumberUtils.toLong(indexId.substring(index + 1), 0L);
            String tableName = indexId.substring(0, index);

            if (id > 0 && StringUtils.isNotBlank(tableName)) {
                Map<String, Object> currentRecordData = RecordDBUtils.getRecordByTableName(id, null, tableName);
                if (currentRecordData != null) {
                    currentRecordData.putAll(props);
                }

                props = ESDataUtils.getEsRecordData(tableName, indexId, currentRecordData);
                if (MapUtils.isEmpty(props)) {
                    return true;
                }
            }
        }

        return createDocumet(indexId, RECORD_ES_INDEX, props);
    }

    public static boolean createDocument(Document doc) {
        if (!ES_ENABLE) {
            return true;
        }

        if (doc == null) {
            return true;
        }

        String indexId = getIndexId(doc);

        Map<String, Object> props = ESDataUtils.getEsDocumentData(doc, indexId);

        return createDocumet(indexId, RECORD_ES_INDEX, props);
    }

    public static boolean createDocumet(String id, String docIndexName, Map<String, Object> params) {
        if (!ES_ENABLE) {
            return true;
        }

        if (MapUtils.isEmpty(params)) {
            return true;
        }

        String json = ESJSONUtils.toJsonString(params);
        String results = ESHttpUtils.postJsonHttpSSLResponse(esHost + docIndexName + "/_doc/" + id, json);
        if (StringUtils.isBlank(results)) {
            return false;
        }

        JSONObject jsonObject = ESJSONUtils.parseJSONObject(results);
        if (jsonObject == null) {
            return false;
        }

        String errors = ESJSONUtils.getJSONString(jsonObject, "error");
        return StringUtils.isBlank(errors);
    }

    public static boolean batchCreateDocument(String docIndexName, List<Map<String, Object>> esDatas) {
        if (!ES_ENABLE) {
            return true;
        }

        if (CollectionUtils.isEmpty(esDatas)) {
            return true;
        }

        List<String> params = new ArrayList<>();
        for (Map<String, Object> esData : esDatas) {
            String indexId = MapUtils.getString(esData, "esId", "");
            if (StringUtils.isBlank(indexId)) {
                continue;
            }

            Map<String, Object> indexIdMap = ZDMapTool.getInstance().put("index", ZDMapTool.getInstance().put("_id", indexId).getMap()).getMap();
            params.add(JSONUtils.toJSONString(indexIdMap, "{}"));
            params.add(JSONUtils.toJSONString(esData, "{}"));
        }

        String json = StringUtils.join(params, "\n") + "\n";
        String results = ESHttpUtils.postJsonHttpSSLResponse(esHost + docIndexName + "/_bulk", json);
        if (StringUtils.isBlank(results)) {
            return false;
        }

        JSONObject jsonObject = ESJSONUtils.parseJSONObject(results);
        if (jsonObject == null) {
            return false;
        }

        String errors = ESJSONUtils.getJSONString(jsonObject, "errors");
        return "false".equals(errors);
    }

    public static void updateDocument(Map<String, Object> props, long recId, RecFolder recFolder) {
        if (!ES_ENABLE) {
            return;
        }

        if (recFolder == null) {
            return;
        }

        if (!recFolder.isDataFolder()) {
            return;
        }

        updateDocument(props, recId, recFolder.getTableName());
    }

    public static void updateDocument(Map<String, Object> props, long recId, String tableName) {
        if (!ES_ENABLE) {
            return;
        }

        if (recId <= 0 || StringUtils.isBlank(tableName)) {
            return;
        }

        String indexId = getIndexId(tableName, recId);
        updateDocument(props, indexId);
    }

    public static void updateDocument(Map<String, Object> props, String indexId) {
        if (!ES_ENABLE) {
            return;
        }

        // 全部使用新建数据功能，更新时缺少字段不能添加
        createDocument(props, indexId);

// updateDocumet(indexId, RECORD_ES_INDEX, props);
    }

    public static void updateDocument(Document doc) {
        if (!ES_ENABLE) {
            return;
        }

        // 全部使用新建数据功能，更新时缺少字段不能添加
        createDocument(doc);
    }

    public static boolean updateRecDocContent(long recId, String tableName, String docContent) {
        String indexId = getIndexId(tableName, recId);

        Map<String, Object> props = new HashMap<>();
        props.put("DOCCONTENT", docContent);

        return updateDocument(indexId, RECORD_ES_INDEX, props);
    }

    public static boolean updateRecZDTags(long recId, String tableName, List<String> tags) {
        String indexId = getIndexId(tableName, recId);

        if (tags == null) {
            tags = new ArrayList<>();
        }

        Map<String, Object> props = new HashMap<>();
        props.put("ZD_TAGS", tags);
        props.put("ZD_TAGS_COUNT", tags.size());

        return updateDocument(indexId, RECORD_ES_INDEX, props);
    }

    public static boolean updateDocContent(Document doc, String docContent) {
        String indexId = getIndexId(doc);

        Map<String, Object> props = new HashMap<>();
        props.put("DOCCONTENT", docContent);

        return updateDocument(indexId, RECORD_ES_INDEX, props);
    }

    public static Map<String, Object> getTasCount(String docIndexName, int size) {
        Map<String, Object> termsMap = getMap("field", "ZD_TAGS");
        if (size <= 0) {
            size = 1000;
        }

        if (size > 10000) {
            size = 10000;
        }
        termsMap.put("size", size);

        Map<String, Object> distinctValuesMap = getMap("terms", termsMap);
        Map<String, Object> aggsMap = getMap("distinct_values", distinctValuesMap);
        Map<String, Object> tagsAggs = getMap("aggs", aggsMap);
        tagsAggs.put("size", 0);

        String response = ESHttpUtils.postJsonHttpSSLResponse(esHost + docIndexName + "/_search", JSONUtils.toJSONString(tagsAggs, "{}"));
        if (StringUtils.isBlank(response)) {
            return new HashMap<>();
        }

        JSONObject jsonObject = ESJSONUtils.parseJSONObject(response);
        if (jsonObject == null) {
            return new HashMap<>();
        }

        JSONObject aggregations = ESJSONUtils.getJSONObject(jsonObject, "aggregations");
        if (aggregations == null) {
            return new HashMap<>();
        }

        JSONObject distinctValues = ESJSONUtils.getJSONObject(aggregations, "distinct_values");
        if (distinctValues == null) {
            return new HashMap<>();
        }

        JSONArray buckets = ESJSONUtils.getJSONArray(distinctValues, "buckets");
        if (buckets == null) {
            return new HashMap<>();
        }

        Map<String, Object> map = new HashMap<>();
        for (int i = 0; i < buckets.size(); i++) {
            try {
                JSONObject o = (JSONObject) buckets.get(i);
                String key = ESJSONUtils.getJSONString(o, "key");
                if (StringUtils.isBlank(key)) {
                    continue;
                }

                String docCount = ESJSONUtils.getJSONString(o, "doc_count");
                if (StringUtils.isBlank(docCount)) {
                    continue;
                }

                map.put(key, docCount);
            } catch (Exception e) {

            }
        }

        return map;
    }

    public static boolean updateDocument(String id, String docIndexName, Map<String, Object> params) {
        if (!ES_ENABLE) {
            return true;
        }

        if (MapUtils.isEmpty(params)) {
            return true;
        }

        Map<String, Object> updateJsonMap = new HashMap<>();
        updateJsonMap.put("doc", params);

        String json = ESJSONUtils.toJsonString(updateJsonMap);
        String results = ESHttpUtils.postJsonHttpSSLResponse(esHost + docIndexName + "/_update/" + id, json);
        if (StringUtils.isBlank(results)) {
            return false;
        }

        JSONObject jsonObject = ESJSONUtils.parseJSONObject(results);
        if (jsonObject == null) {
            return false;
        }

        String errors = ESJSONUtils.getJSONString(jsonObject, "error");
        return StringUtils.isBlank(errors);
    }

    public static boolean deleteDocument(long recId, RecFolder recFolder) {
        if (!ES_ENABLE) {
            return true;
        }

        if (recFolder == null) {
            return true;
        }

        if (!recFolder.isDataFolder()) {
            return true;
        }

        return deleteDocument(recId, recFolder.getTableName());
    }

    public static boolean deleteDocument(long recId, String tableName) {
        if (!ES_ENABLE) {
            return true;
        }

        if (recId <= 0 || StringUtils.isBlank(tableName)) {
            return true;
        }

        String indexId = getIndexId(tableName, recId);
        return deleteDocument(indexId, RECORD_ES_INDEX);
    }

    public static boolean deleteDocument(Document doc) {
        if (!ES_ENABLE) {
            return true;
        }

        if (doc == null) {
            return true;
        }

        String indexId = getIndexId(doc);
        return deleteDocument(indexId, RECORD_ES_INDEX);
    }

    public static void createIndex(String docIndexName) {
        ESHttpUtils.putHttpSSLResponse(esHost + docIndexName, "");
    }

    public static void deleteIndex(String docIndexName) {
        ESHttpUtils.deleteHttpSSLResponse(esHost + docIndexName);
        ZDRedisUtils.delete(ESUtils.REDIS_ES_COLS);
    }

    public static boolean deleteDocument(String id, String docIndexName) {
        if (!ES_ENABLE) {
            return true;
        }

        String results = ESHttpUtils.deleteHttpSSLResponse(esHost + docIndexName + "/_doc/" + id);
        if (StringUtils.isBlank(results)) {
            return false;
        }

        JSONObject jsonObject = ESJSONUtils.parseJSONObject(results);
        if (jsonObject == null) {
            return false;
        }

        String errors = ESJSONUtils.getJSONString(jsonObject, "error");
        return StringUtils.isBlank(errors);
    }

    public static void putMapping(String docIndexName, Map<String, Object> mappingMap) {
        ESHttpUtils.putHttpSSLResponse(esHost + docIndexName + "/_mapping", JSONUtils.toJSONString(mappingMap, ""));
    }

    public static String getMapping(String docIndexName) {
        return ESHttpUtils.getHttpSSLResponse(esHost + docIndexName + "/_mapping");
    }

    public static List<Map<String, Object>> getAllIndex() {
        String httpSSLResponse = ESHttpUtils.getHttpSSLResponse(esHost + "_cat/indices?v");
        if (StringUtils.isBlank(httpSSLResponse)) {
            return new ArrayList<>();
        }

        List<Map<String, Object>> results = new ArrayList<>();
        String[] split = httpSSLResponse.split("\n");

        List<String> headers = null;
        for (String str : split) {
            while (str.contains("  ")) {
                str = str.replace("  ", " ");
            }

            String[] valueArr = str.split(" ");
            if (headers == null) {
                headers = Arrays.asList(valueArr);
            } else {
                Map<String, Object> indexMap = new HashMap<>();
                for (int i = 0; i < valueArr.length; i++) {
                    if (headers.size() <= i) {
                        continue;
                    }

                    String key = headers.get(i);
                    String value = valueArr[i];
                    indexMap.put(key, value);
                }

                results.add(indexMap);
            }
        }

        return results;
    }

    public static void getAllDocuments(String docIndexName) {
        ESHttpUtils.getHttpSSLResponse(esHost + docIndexName + "/_search");
    }

    public static Page searchDocument(String docIndexName, Map<String, Object> params, List<Long> recFolderIds, List<Long> folderIds, List<String> tags, int tagMatchCount, String scoreScript, List<String> excludeEsIds, String dataType,
                                      int pageNumber, int pageSize) {
        String searchJson = getSearchJson(params, recFolderIds, folderIds, tags, tagMatchCount, scoreScript, excludeEsIds, dataType, pageNumber, pageSize);
        String httpSSLResponse = ESHttpUtils.postJsonHttpSSLResponse(esHost + docIndexName + "/_search", searchJson);
        Page page = getRecordsPageByJson(httpSSLResponse, pageNumber, pageSize);
        if (page == null) {
            return PageableUtils.genPage(new ArrayList<Map<String, Object>>(), pageNumber, pageSize, 0);
        }

        return page;
    }

    public static Page searchDocument(String docIndexName, Map<String, Object> params, List<Long> recFolderIds, List<Long> folderIds, List<String> tags, int tagMatchCount, String scoreScript, List<String> excludeEsIds, String dataType) {
        String searchJson = getSearchJson(params, recFolderIds, folderIds, tags, tagMatchCount, scoreScript, excludeEsIds, dataType, 0, 0);
        String httpSSLResponse = ESHttpUtils.postJsonHttpSSLResponse(esHost + docIndexName + "/_search", searchJson);

        Page page = getRecordsPageByJson(httpSSLResponse, 0, 0);
        if (page == null) {
            return PageableUtils.genPage(new ArrayList<Map<String, Object>>(), 0, 0, 0);
        }

        return page;
    }

    private static String getSearchJson(Map<String, Object> params, List<Long> recFolderIds, List<Long> folderIds, List<String> tags, int tagMatchCount, String scoreScript, List<String> excludeEsIds, String dataType, int pageNumber, int pageSize) {
        Map<String, Object> highlightField = new HashMap<>();

        Map<String, Object> boolMap = new HashMap<String, Object>();

        // mustList
        List<Map<String, Object>> mustList = new ArrayList<Map<String, Object>>();

        // 数据类型
        if (StringUtils.isNotBlank(dataType)) {
            mustList.add(getTermMap("DATA_TYPE", dataType));
        }

        // 目录ID
        if (CollectionUtils.isNotEmpty(recFolderIds) || CollectionUtils.isNotEmpty(folderIds)) {
            List<Map<String, Object>> folderIdShouldList = new ArrayList<>();

            // 搜索档案数据
            if (CollectionUtils.isNotEmpty(recFolderIds)) {
                if (StringUtils.isBlank(dataType) || ESDataUtils.DATA_TYPE_REC.equals(dataType)) {
                    List<Map<String, Object>> recFolderIdMustList = new ArrayList<>();
                    recFolderIdMustList.add(getTermMap("DATA_TYPE", ESDataUtils.DATA_TYPE_REC));
                    recFolderIdMustList.add(getTermsMap("目录ID", recFolderIds));
                    folderIdShouldList.add(getMap("bool", getMap("must", recFolderIdMustList)));
                }
            }

            // 搜索文档数据
            if (CollectionUtils.isNotEmpty(folderIds)) {
                if (StringUtils.isBlank(dataType) || ESDataUtils.DATA_TYPE_DOC.equals(dataType)) {
                    List<Map<String, Object>> folderIdMustList = new ArrayList<>();
                    folderIdMustList.add(getTermMap("DATA_TYPE", ESDataUtils.DATA_TYPE_DOC));
                    folderIdMustList.add(getTermsMap("目录ID", folderIds));
                    folderIdShouldList.add(getMap("bool", getMap("must", folderIdMustList)));
                }
            }

            mustList.add(getMap("bool", getMap("should", folderIdShouldList)));
        } else {
            // 搜索目录ID大于0的数据
            mustList.add(getExistsMap("目录ID"));
            mustList.add(getMap("range", getMap("目录ID", getMap("gt", 0))));
        }

        // 字段条件
        if (MapUtils.isNotEmpty(params)) {
            Map<String, Object> mustBoolMap = new HashMap<String, Object>();

            List<Map<String, Object>> mustShouldList = new ArrayList<Map<String, Object>>();

            Map<String, Map<String, List<String>>> strAndSplitKeyrowsMap = new HashMap<>();
            Set<Entry<String, Object>> entrySet = params.entrySet();
            for (Entry<String, Object> entry : entrySet) {
                String key = entry.getKey();
                Object value = entry.getValue();

                String valueStr = value + "";
                if (StringUtils.isBlank(valueStr)) {
                    continue;
                }

                // 当绝对匹配时，移除DOCCONTENT字段
                if (valueStr.startsWith("\"") && valueStr.endsWith("\"")) {
                    if ("DOCCONTENT".equals(key)) {
                        continue;
                    }

                    valueStr.substring(1, valueStr.length() - 1);
                    if (StringUtils.isBlank(valueStr)) {
                        continue;
                    }
                }

                if (ES_USE_IK || ESDataUtils.isIkCols(key)) {
                    // 分词搜索
                    mustShouldList.add(getMatchMap(key, value + ""));
                } else {
                    String regex = "^[a-zA-Z0-9]*$";
                    boolean matches = valueStr.matches(regex);
                    if (matches) {
                        // 含有英文时, 可以匹配部分数据
                        mustShouldList.add(getWildcardMap(key, "*" + value + "*"));
                    } else {
                        // 中文匹配数据
//                            mustShouldList.add(getMatchPhraseMap(key, value + ""));

                        Map<String, List<String>> stringListMap = strAndSplitKeyrowsMap.get(value + "");
                        if (stringListMap == null) {
                            stringListMap = splitKeyrows(value + "");
                            strAndSplitKeyrowsMap.put(value + "", stringListMap);
                        }

                        List<String> enKeywords = stringListMap.get("enKeywords");
                        if (CollectionUtils.isNotEmpty(enKeywords)) {
                            for (String enKeyword : enKeywords) {
                                mustShouldList.add(getWildcardMap(key, "*" + enKeyword + "*"));
                            }
                        }

                        List<String> cnKeywords = stringListMap.get("cnKeywords");
                        if (CollectionUtils.isNotEmpty(cnKeywords)) {
                            for (String cnKeyword : cnKeywords) {
                                mustShouldList.add(getMatchPhraseMap(key, cnKeyword));
                            }
                        }
                    }
                }

                highlightField.put(key, new HashMap<>());
            }

            mustBoolMap.put("should", mustShouldList);

            mustList.add(getMap("bool", mustBoolMap));
        }

        if (CollectionUtils.isNotEmpty(tags)) {
            Map<String, Object> zdTagsMap = getMap("terms", tags);
            if (tagMatchCount <= 0) {
                tagMatchCount = 1;
            }

            zdTagsMap.put("minimum_should_match_script", getMap("source", tagMatchCount + ""));

            Map<String, Object> termsSetMap = getMap("ZD_TAGS", zdTagsMap);
            mustList.add(getMap("terms_set", termsSetMap));
        }

        // 非删除数据
//        List<Map<String, Object>> mustNotList = new ArrayList<Map<String, Object>>();
//        mustNotList.add(getWildcardMap("删除人用户名", "*"));
//        boolMap.put("should", getEmptyShouldList("删除人用户名"));
        mustList.add(getMap("bool", getMap("should", getEmptyShouldList("删除人用户名"))));

        boolMap.put("must", mustList);

        if (CollectionUtils.isNotEmpty(excludeEsIds)) {
            Map<String, Object> termsMap = getMap("esId", excludeEsIds);
            Map<String, Object> mustNotMap = getMap("terms", termsMap);
            boolMap.put("must_not", mustNotMap);
        }

		/*List<Map<String, Object>> filterList = new ArrayList<Map<String, Object>>();
		boolMap.put("filter", filterList);*/
        Map<String, Object> queryMap = getMap("bool", boolMap);
        Map<String, Object> functionScoreMap = getMap("query", queryMap);

        functionScoreMap.put("score_mode", "sum");
        functionScoreMap.put("boost_mode", "replace");

        if (StringUtils.isBlank(scoreScript)) {
            scoreScript = "return _score;";
        }

        Map<String, Object> sourceMap = getMap("source", scoreScript);
        Map<String, Object> scriptMap = getMap("script", sourceMap);
        Map<String, Object> scriptScore = getMap("script_score", scriptMap);
        List<Map<String, Object>> funtionsList = new ArrayList<>();
        funtionsList.add(scriptScore);

        functionScoreMap.put("functions", funtionsList);

        Map<String, Object> functionScoreQueryMap = getMap("function_score", functionScoreMap);
        Map<String, Object> resultMap = getMap("query", functionScoreQueryMap);

        // 高亮
        Map<String, Object> highlightMap = getMap("fields", highlightField);
        highlightMap.put("pre_tags", "<em>");
        highlightMap.put("post_tags", "</em>");
        highlightMap.put("fragment_size", 30);
        resultMap.put("highlight", highlightMap);

        // 分页
        if (pageSize > 0 && pageNumber > 0) {
            int from = pageSize * (pageNumber - 1);

            if (from > ES_MAX_RESULTS_COUNT) {
                from = ES_MAX_RESULTS_COUNT;
                pageSize = 0;
            } else {
                int end = from + pageSize;
                if (end > ES_MAX_RESULTS_COUNT) {
                    pageSize = ES_MAX_RESULTS_COUNT - from;
                }
            }

            resultMap.put("from", from);
            resultMap.put("size", pageSize);
        }

        // 排序
        List<Map<String, Object>> sortList = new ArrayList<Map<String, Object>>();
        sortList.add(getMap("_score", getMap("order", "desc")));
        sortList.add(getMap("创建时间", getMap("order", "desc")));

        resultMap.put("sort", sortList);

        return ESJSONUtils.toJsonString(resultMap);
    }

    private static List<Map<String, Object>> getEmptyShouldList(String colName) {
        List<Map<String, Object>> mustNotList = new ArrayList<>();
        Map<String, Object> existsMap = getMap("exists", getMap("field", colName));
        mustNotList.add(existsMap);
        Map<String, Object> mustNotMap = getMap("must_not", mustNotList);
        Map<String, Object> mustNotBoolMap = getMap("bool", mustNotMap);

        List<Map<String, Object>> shoudList = new ArrayList<>();
        shoudList.add(mustNotBoolMap);
        shoudList.add(getMap("term", getMap(colName, "")));

        return shoudList;
    }

    private static Map<String, List<String>> splitKeyrows(String tempKeywords) {
        char[] charArray = tempKeywords.toCharArray();

        List<String> enKeywords = new ArrayList<>();
        List<String> cnKeywords = new ArrayList<>();

        StringBuilder splitKeywords = new StringBuilder();
        boolean cnChar = true;
        for (char c : charArray) {
            if (c >= 'A' && c <= 'Z' || c >= 'a' && c <= 'z' || c >= '0' && c <= '9') {
                if (cnChar) {
                    if (StringUtils.isNotBlank(splitKeywords.toString())) {
                        cnKeywords.add(splitKeywords.toString());
                    }

                    splitKeywords = new StringBuilder();
                }

                splitKeywords.append(c);
                cnChar = false;
            } else {
                if (!cnChar) {
                    if (StringUtils.isNotBlank(splitKeywords.toString())) {
                        enKeywords.add(splitKeywords.toString());
                    }

                    splitKeywords = new StringBuilder();
                }

                splitKeywords.append(c);
                cnChar = true;
            }
        }

        if (StringUtils.isNotBlank(splitKeywords.toString())) {
            if (cnChar) {
                cnKeywords.add(splitKeywords.toString());
            } else {
                enKeywords.add(splitKeywords.toString());
            }
        }

        Map<String, List<String>> enAndCnKeywords = new HashMap<>();
        enAndCnKeywords.put("enKeywords", enKeywords);
        enAndCnKeywords.put("cnKeywords", cnKeywords);

        return enAndCnKeywords;
    }

    private static Page getRecordsPageByJson(String json, int pageNumber, int pageSize) {
        if (StringUtils.isBlank(json)) {
            return null;
        }

        JSONObject resultJSONObject = ESJSONUtils.parseJSONObject(json);
        if (resultJSONObject == null) {
            return null;
        }

        JSONObject hitsJSONObject = ESJSONUtils.getJSONObject(resultJSONObject, "hits");
        if (hitsJSONObject == null) {
            return null;
        }

        int totalCount = 0;
        JSONObject totalJSONObject = ESJSONUtils.getJSONObject(hitsJSONObject, "total");
        if (totalJSONObject != null) {
            totalCount = NumberUtils.toInt(ESJSONUtils.getJSONString(totalJSONObject, "value", ""), 0);
        }

        if (totalCount > ES_MAX_RESULTS_COUNT) {
            totalCount = ES_MAX_RESULTS_COUNT;
        }

        List<Map<String, Object>> records = new ArrayList<Map<String, Object>>();
        JSONArray recordHitsgetJSONArray = ESJSONUtils.getJSONArray(hitsJSONObject, "hits");
        if (ESJSONUtils.isNotEmpty(recordHitsgetJSONArray)) {
            for (int i = 0; i < recordHitsgetJSONArray.size(); i++) {
                JSONObject recordJSONObject = (JSONObject) recordHitsgetJSONArray.get(i);
                if (recordJSONObject == null) {
                    continue;
                }

                Map<String, Object> record = null;
                try {
                    record = recordJSONObject.getObject("_source", Map.class);
                } catch (Throwable t) {
                    record = new HashMap<>();
                }

                Map<String, Object> highlightMap = null;
                try {
                    highlightMap = recordJSONObject.getObject("highlight", Map.class);
                } catch (Exception e) {
                    highlightMap = new HashMap<>();
                }

//                setDateCol(record);
                String dataType = MapUtils.getString(record, "DATA_TYPE");
                if (ESDataUtils.DATA_TYPE_REC.equals(dataType)) {
                    Map<String, Object> recordData = ESDataUtils.getDisplayRecordData(record);
                    Map<String, Object> highlightRecordData = ESDataUtils.getRecordData(highlightMap);
                    recordData.put("highlightRecord", ESDataUtils.getRecordDataWithDisplayColName(highlightRecordData, MapUtils.getString(recordData, "tableName", "")));
                    records.add(recordData);
                } else {
                    Map<String, Object> documentData = ESDataUtils.getDisplayDocumentData(record);
                    Map<String, Object> highlightRecordData = ESDataUtils.getRecordData(highlightMap);
                    ESDataUtils.replaceDocumentKey(highlightRecordData);

                    documentData.put("highlightRecord", highlightRecordData);
                    records.add(documentData);
                }
            }
        }

        return PageableUtils.genPage(records, pageNumber, pageSize, totalCount);
    }

    private static void setDateCol(Map<String, Object> record) {
        if (MapUtils.isEmpty(record)) {
            return;
        }

        Map<String, Object> props = new HashMap<String, Object>();
        Set<String> keySet = record.keySet();
        for (String key : keySet) {
            String value = MapUtils.getString(record, key, "");
            if (StringUtils.isNumeric(value)) {
                long longValue = NumberUtils.toLong(value, 0L);
                if (longValue > 1000000000000L) {
                    Date date = new Date(longValue);
                    props.put(key + "_date", date);
                }
            }
        }

        record.putAll(props);
    }

    public static String getIndexId(String tableName, long recId) {
        if (StringUtils.isBlank(tableName) || recId <= 0) {
            return "";
        }

        return tableName + "_" + recId;
    }

    public static String getIndexId(Document doc) {
        if (doc == null) {
            return "";
        }

        return "doc_" + doc.getId();
    }

    public static String getIndexId(RecFolder recFolder, long recId) {
        if (recFolder == null) {
            return "";
        }

        return getIndexId(recFolder.getTableName(), recId);
    }

    private static Map<String, Object> getTermMap(String key, String value) {
        return getMap("term", getMap(key, value));
    }

    private static Map<String, Object> getTermsMap(String key, List<Long> values) {
        return getMap("terms", getMap(key, values));
    }

    private static Map<String, Object> getMatchMap(String key, Object value) {
        return getMap("match", getMap(key, value));
    }

    private static Map<String, Object> getMatchPhraseMap(String key, Object value) {
        return getMap("match_phrase", getMap(key, value));
    }

    private static Map<String, Object> getExistsMap(String fieldName) {
        return getMap("exists", getMap("field", fieldName));
    }

    private static Map<String, Object> getWildcardMap(String key, Object value) {
        return getMap("wildcard", getMap(key, value));
    }

    private static Map<String, Object> getMap(String key, Object value) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put(key, value);
        return map;
    }
}
