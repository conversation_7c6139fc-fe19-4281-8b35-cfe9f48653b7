package zd.dms.services.document.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.dms.entities.Folder;
import zd.dms.entities.FolderArea;
import zd.dms.entities.ThreeAdminLog;
import zd.dms.repositories.log.ThreeAdminLogRepository;
import zd.dms.services.document.DocumentUtils;
import zd.dms.services.document.FolderAreaService;
import zd.dms.services.document.ThreeAdminLogService;
import zd.dms.utils.ThreeAdminUtils;
import zd.record.entities.RecFolder;

import java.util.Date;

@RequiredArgsConstructor
@Service
@Slf4j
public class ThreeAdminLogServiceImpl extends BaseJpaServiceImpl<ThreeAdminLog, Long> implements ThreeAdminLogService {

    private final ThreeAdminLogRepository threeAdminLogRepository;

    private final FolderAreaService folderAreaService;

    @Override
    public BaseRepository<ThreeAdminLog, Long> getBaseRepository() {
        return threeAdminLogRepository;
    }

    @Override
    public Page getLogs(int pageNumber, int pageSize, int type, int targetType, String username, Date startDate, Date endDate) {
        return threeAdminLogRepository.getLogs(pageNumber, pageSize, type, targetType, username, startDate, endDate);
    }

    @Override
    public ThreeAdminLog addLog(Folder folder, RecFolder recFolder, String targetUserGroup, int type, String msg, String operator, String fullname, String ipAddress, boolean logToParent) {
        if (!ThreeAdminUtils.isThreeAdminLog(operator, type)) {
            return null;
        }

        if (folder == null && recFolder == null && StringUtils.isBlank(targetUserGroup)) {
            log.debug("addLog null folder or userGroup");
            return null;
        }

        if (folder != null && folder.getFolderType() == Folder.TYPE_MYDOCUMENT) {
            return null;
        }

        ThreeAdminLog log = new ThreeAdminLog();

        if (StringUtils.isNotBlank(targetUserGroup)) {
            log.setTarget(targetUserGroup);
            log.setTargetType(ThreeAdminLog.TARGETTYPE_USER);
        } else {
            log.setTarget(getDisplayTargetFolder(folder, recFolder, logToParent));
            log.setTargetType(ThreeAdminLog.TARGETTYPE_RECEPTION);
        }

        log.setOperateType(type);
        log.setMsg(msg);
        log.setOperator(operator);
        log.setOperatorFullname(fullname);
        log.setIp(ipAddress);

        threeAdminLogRepository.save(log);

        return log;
    }

    private String getDisplayTargetFolder(Folder folder, RecFolder recFolder, boolean logToParent) {
        String displayFolder = "";

        if (folder != null) {
            if (logToParent) {
                // 当操作的目录没有父目录，且日志的对象需要记录为父目录时，将对象设置为目录分区
                if (folder.getParent() == null) {
                    FolderArea folderArea = folderAreaService.getFolderAreaByTreeName(folder.getTreeName());
                    if (folderArea == null) {
                        displayFolder = DocumentUtils.getDocLeftMenuTitle();
                    } else {
                        displayFolder = folderArea.getAreaName();
                    }
                } else {
                    displayFolder = folder.getParent().getAreaDisplayFolder();
                }

            } else {
                displayFolder = folder.getAreaDisplayFolder();
            }
        }

        if (recFolder != null) {
            if (logToParent) {
                if (recFolder.getParent() == null) {
                    FolderArea folderArea = folderAreaService.getFolderAreaByTreeName(recFolder.getTreeName());
                    if (folderArea == null) {
                        displayFolder = DocumentUtils.getDocLeftMenuTitle();
                    } else {
                        displayFolder = folderArea.getAreaName();
                    }
                } else {
                    displayFolder = recFolder.getParent().getAreaDisplayFolder();
                }

            } else {
                displayFolder = recFolder.getAreaDisplayFolder();
            }
        }

        return displayFolder;
    }
}
