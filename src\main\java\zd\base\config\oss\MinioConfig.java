package zd.base.config.oss;


import io.minio.MinioClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Data
@Configuration
public class MinioConfig {
    /**
     * 服务地址
     */
    @Value("${zd.oss.config.url}")
    private String url;

    /**
     * 用户名
     */
    @Value("${zd.oss.config.accessKey}")
    private String accessKey;

    /**
     * 密码
     */
    @Value("${zd.oss.config.secretKey}")
    private String secretKey;

    /**
     * 存储桶名称
     */
    @Value("${zd.oss.config.bucketName}")
    private String bucketName;

    @Bean
    public MinioClient getMinioClient() {
        if (StringUtils.isBlank(url)) {
            log.error("getMinioClient url null");
        }

        return MinioClient.builder().endpoint(url).credentials(accessKey, secretKey).build();
    }
}
