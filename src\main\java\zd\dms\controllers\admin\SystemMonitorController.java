package zd.dms.controllers.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDSystemMonitorUtils;

import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "SystemMonitorController", description = "系统监控Controller")
@RequestMapping("/admin/systemMonitor")
public class SystemMonitorController extends ControllerSupport {

    @Operation(summary = "获取列表")
    @PostMapping("/getSystemMonitors")
    @ZDLog("获取目录分区")
    public JSONResultUtils<Object> list(@RequestBody Map<String, Object> params) {
        return successData(ZDSystemMonitorUtils.getSystemMonitorWithStatus());
    }

    @Operation(summary = "保存监控配置")
    @PostMapping("/saveSystemMonitor")
    @ZDLog("saveSystemMonitor")
    public JSONResultUtils<Object> saveSystemMonitor(@RequestBody Map<String, Object> params) {
        ZDSystemMonitorUtils.saveSystemMonitor(params);
        return success();
    }

    @Operation(summary = "删除监控配置")
    @PostMapping("/deleteSystemMonitor")
    @ZDLog("删除监控配置")
    public JSONResultUtils<Object> deleteSystemMonitor(@RequestBody Map<String, Object> params) {
        String id = MapUtils.getString(params, "id", "");
        ZDSystemMonitorUtils.deleteSystemMonitor(id);

        return success();
    }

    @Operation(summary = "检测")
    @PostMapping("/checkStatus")
    @ZDLog("检测")
    public JSONResultUtils<Object> checkStatus(@RequestBody Map<String, Object> params) {
        ZDSystemMonitorUtils.startCheck();
        return success();
    }

    @Operation(summary = "更改监控启用状态")
    @PostMapping("/changeEnabled")
    @ZDLog("更改监控启用状态")
    public JSONResultUtils<Object> changeEnabled(@RequestBody Map<String, Object> params) {
        String id = MapUtils.getString(params, "id", "");
        boolean enabled = MapUtils.getBooleanValue(params, "enabled", true);

        ZDSystemMonitorUtils.changeSystemMonitorEnabled(id, enabled);

        return success();
    }

}
