package zd.base.tasks;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zd.dms.io.ZDIOUtils;

@Component
@Slf4j
public class FiveMinuteTask extends AbstractTask {

    @Scheduled(cron = "0 */5 * * * ?")
    @Scheduled(initialDelay = 60000)
    @Override
    public void work() {
        ZDIOUtils.deleteTempDirFiles();
    }
}
