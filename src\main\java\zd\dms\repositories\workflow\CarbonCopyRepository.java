package zd.dms.repositories.workflow;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.User;
import zd.dms.workflow.entities.SSCarbonCopy;

import java.util.List;

public interface CarbonCopyRepository extends BaseRepository<SSCarbonCopy, String> {

    Page getCarbonCopyByPage(String keywords, String username, String resourceType, String pdType, int page, int pageSize);

    int getUncompeletedCarbonCopyCount(String username);

    int getUncompeletedCarbonCopyCount(String username, String resTypeRec);

    List<SSCarbonCopy> searchCarbonCopy(String keywords, User currentUser, int status, String resType);
}
