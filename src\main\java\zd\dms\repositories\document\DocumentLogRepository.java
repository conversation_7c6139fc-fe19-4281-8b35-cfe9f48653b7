package zd.dms.repositories.document;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentLog;
import zd.dms.entities.User;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Revision$
 */
public interface DocumentLogRepository extends BaseRepository<DocumentLog, Long> {

    List<DocumentLog> getDocumentLogByDocument(Document doc, int type);

    List<DocumentLog> getTop100DocumentLogsByDocument(Document doc);

    void deleteAllLogsByDocument(Document doc);

    void deleteLogsBeforeDays(int days);

    Page getDocumentLog(int pageNumber, int pageSize, int type,
                        String username, Date startDate, Date endDate);

    List<DocumentLog> getLogsByDate(Date startDate, Date endDate, int type,
                                    String username);

    int getCount();

    int getUserDocLogCountByTypeAndDate(User user, int type, Date startDate,
                                        Date endDate);

    Page getDocumentTimeline(int pageNumber, int pageSize, Document doc, User user);
}
