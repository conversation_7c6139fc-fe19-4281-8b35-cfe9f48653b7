package zd.dms.services.document.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import zd.base.context.UserContextHolder;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.base.utils.validate.ValidateUtils;
import zd.dms.dto.document.DocumentLendingDTO;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentLending;
import zd.dms.entities.DocumentLog;
import zd.dms.entities.Folder;
import zd.dms.entities.FolderPermission;
import zd.dms.entities.InstantMessage;
import zd.dms.entities.User;
import zd.dms.repositories.document.DocumentLendingDao;
import zd.dms.repositories.document.DocumentLendingRepository;
import zd.dms.repositories.document.DocumentLogRepository;
import zd.dms.repositories.document.DocumentRepository;
import zd.dms.repositories.document.FolderRepository;
import zd.dms.repositories.user.UserRepository;
import zd.dms.services.document.DocumentLendingService;
import zd.dms.services.document.DocumentService;
import zd.dms.services.document.DocumentUtils;
import zd.dms.services.mail.MailService;
import zd.dms.services.security.FolderPermissionUtils;
import zd.dms.utils.WebUtils;
import zd.dms.utils.ZDLegalHolidaysUtils;
import zd.dms.utils.document.DocumentLendingSpecifications;

@RequiredArgsConstructor
@Service
@Slf4j
public class DocumentLendingServiceImpl extends BaseJpaServiceImpl<DocumentLending, Long> implements
    DocumentLendingService {

    private final DocumentLendingRepository documentLendingRepository;

    private final MailService mailService;

    private final UserRepository userRepository;

    private final DocumentService documentService;

    private final DocumentRepository documentRepository;

    private final DocumentLendingDao documentLendingDao;

    private final DocumentLogRepository documentLogRepository;
    private final FolderRepository folderRepository;

    @Override
    public BaseRepository<DocumentLending, Long> getBaseRepository() {
        return documentLendingRepository;
    }

    @Override
    public void createLendings(List<String> documentIds, List<String> borrowerUsernames, int lendHours,
        List<String> permissions, User lender) {
        Set<String> linkedDocumentIds = Sets.newHashSet();
        List<String> finalDocumentIds = Lists.newArrayList();
        for (String documentId : documentIds) {
            if (!documentId.startsWith("link_")) {
                finalDocumentIds.add(documentId);
                continue;
            }

            String finalDocumentId = documentId.substring(5);
            linkedDocumentIds.add(finalDocumentId);
            finalDocumentIds.add(finalDocumentId);
        }

        List<Document> documents = documentRepository.findAllById(finalDocumentIds);
        ValidateUtils.notEmpty(documents, "借出文档均不存在");

        List<User> borrowers = userRepository.getUserByUsernames(borrowerUsernames);
        ValidateUtils.notEmpty(borrowers, "借出用户均不存在");

        if (CollectionUtils.isEmpty(permissions)) {
            permissions = Lists.newArrayList();
        }
        permissions.add(FolderPermission.READ);
        List<String> linkedDocumentPermissions = List.of(FolderPermission.READ);

        for (Document document : documents) {
            if (!FolderPermissionUtils.checkPermission(lender, document, FolderPermission.EDIT)) {
                continue;
            }

            List<String> permissionList =
                linkedDocumentIds.contains(document.getId()) ? linkedDocumentPermissions : permissions;
            createLending(document, borrowers, lendHours, permissionList, lender);

            // 审计
            String usernameListString = StringUtils.join(borrowerUsernames, ", ");
            String permissionListLabelString = FolderPermissionUtils.buildPermissionLabelString(permissions, "，",
                "允许");
            String message = "将文档: %s 借阅给 %s, 时间 %d 小时，%s".formatted(document.getFilename(),
                usernameListString, lendHours, permissionListLabelString);
            DocumentLog documentLog = DocumentLog.of(lender, DocumentLog.TYPE_LENDING, document.getId(), message);
            documentLogRepository.save(documentLog);
        }
    }

    private void createLending(Document doc, List<User> borrowers, int lendHours, List<String> permissions,
        User lender) {
        Set<String> usernameSet = new HashSet<>();
        List<String> fullnameList = new LinkedList<>();
        for (User borrower : borrowers) {
            fullnameList.add(borrower.getFullnameAndUsername());
            String borrowerUsername = borrower.getUsername();
            usernameSet.add(formatUsername(borrowerUsername));

            String message = "将文档<b>%s</b>借阅给您，时间为%d小时，请点击左侧“个人中心-借阅中心-借给我的档案”查看"
                .formatted(DocumentUtils.getShortAltString(doc.getSummaryFilename30(), doc.getFilename()), lendHours);
            mailService.sendMessageAndMail("文档借阅提醒", message, InstantMessage.TYPE_DOC, lender, borrower);
        }

        DocumentLending documentLending = new DocumentLending();
        documentLending.setCreator(lender.getUsername());
        documentLending.setCreatorFullname(lender.getFullname());
        documentLending.setEndDate(ZDLegalHolidaysUtils.addHours(new Date(), lendHours));
        documentLending.setDocId(doc.getId());
        documentLending.setFileName(doc.getFilename());
        documentLending.setPermission(StringUtils.join(permissions, ""));
        documentLending.setUsernames(StringUtils.join(usernameSet, ","));
        documentLending.setFullnames(StringUtils.join(fullnameList, ", "));
        documentLendingRepository.save(documentLending);
    }

    @Override
    public void deleteByDocument(Document document) {
        documentLendingDao.deleteByDocument(document.getId());
    }

    @Override
    public void delete(DocumentLending lending) {
        documentLendingRepository.deleteById(lending.getId());
    }

    @Override
    public void deleteById(long id) {
        documentLendingRepository.deleteById(id);
    }

    @Override
    public void recallDocument(List<Long> ids) {
        List<DocumentLending> lendings = documentLendingDao.findAllById(ids);
        for (DocumentLending lending : lendings) {
            List<String> borrowerUsernames = sendMessagesToBorrowersThenReturnBorrowerUsernames(lending);

            saveAuditLog(lending, borrowerUsernames);
        }

        documentLendingDao.deleteAllById(ids);
    }

    private List<String> sendMessagesToBorrowersThenReturnBorrowerUsernames(DocumentLending documentLending) {
        return Arrays.stream(documentLending.getUsernames().split(","))
            .map(this::computeRealUsername)
            .peek(username -> sendMessageToBorrower(username, documentLending))
            .toList();
    }

    private void saveAuditLog(DocumentLending documentLending, List<String> targetUserNames) {
        User currentUser = UserContextHolder.getUser();
        String message = "手工将借给：%s 的文档: %s 收回"
            .formatted(StringUtils.join(targetUserNames, ", "), documentLending.getFileName());
        documentService.addLog(documentLending.getDocument(), DocumentLog.TYPE_LENDING, message,
            currentUser.getUsername(), currentUser.getFullname(), WebUtils.getIPAddress());
    }

    private void sendMessageToBorrower(String targetUsername, DocumentLending documentLending) {
        User targetuser = userRepository.getUser(targetUsername);
        if (targetuser == null) {
            return;
        }

        mailService.sendMessageAndMail(
            "回收文档提醒",
            "回收借阅文档<b>" +
                DocumentUtils.getShortAltString(documentLending.computeFileName30(),
                    documentLending.getFileName()) + "</b>", InstantMessage.TYPE_DOC,
            UserContextHolder.getUser(), targetuser);
    }

    private String computeRealUsername(String username) {
        return username.replace("**", "").trim();
    }

    private String formatUsername(String username) {
        return "**" + username.trim() + "**";
    }

    @Override
    public void deleteExpiredLendings() {
        List<DocumentLending> expired = documentLendingRepository.getExpiredLendings();
        for (DocumentLending dl : expired) {
            // 发送回收提示信息
            String[] usernames = dl.getUsernames().split(",");
            List<String> realUsernames = new LinkedList<>();
            for (String username : usernames) {
                if (username.length() <= 4) {
                    continue;
                }

                String realUsername = username.substring(2, username.length() - 2);
                User sendTo = userRepository.getUser(realUsername);
                if (sendTo != null) {
                    realUsernames.add(realUsername);

                    mailService.sendMessageAndMail(
                        "回收借阅文档提醒",
                        "系统自动回收借阅文档<b>" +
                            DocumentUtils.getShortAltString(dl.getDocument().getSummaryFilename30(), dl
                                .getDocument().getFilename()) + "</b>", InstantMessage.TYPE_DOC, null,
                        sendTo);
                }
            }

            log.debug("删除过期借阅： {}", dl.getDocument().getFilename());

            // 增加文档审计日志
            documentService.addLogFromAdmin(dl.getDocument(), DocumentLog.TYPE_LENDING,
                "系统自动将借给：" + StringUtils.join(realUsernames, ", ") + " 的过期文档: " + dl.getDocument()
                    .getFilename() +
                    " 收回");

            documentLendingRepository.delete(dl);
        }
    }

    @Override
    public DocumentLending getDocumentLendingById(long id) {
        return documentLendingDao.findById(id).orElse(null);
    }

    @Override
    public List<DocumentLending> getDocumentLendingsByCreator(String creator) {
        return documentLendingDao.findByCreator(creator);
    }

    @Override
    public List<DocumentLending> getDocumentLendings(Document document) {
        return documentLendingDao.findByDocumentId(document.getId());
    }

    @Override
    public List<DocumentLending> getAllDocumentLendings() {
        return documentLendingRepository.findAll(Sort.by(Sort.Order.desc("creationDate")));
    }

    @Override
    public Page<DocumentLendingDTO> searchLendFromMe(String currentUsername, String fileName, String borrowerFullName,
        Pageable pageable) {
        var spec = DocumentLendingSpecifications.searchLendFromMe(currentUsername, fileName, borrowerFullName);

        return getDocumentLendingDTOS(pageable, spec);
    }

    @Override
    public Page<DocumentLendingDTO> searchLendToMe(String currentUsername, String fileName, String lenderFullname,
        Pageable pageable) {
        var spec = DocumentLendingSpecifications.searchLendToMe(currentUsername, fileName, lenderFullname);

        return getDocumentLendingDTOS(pageable, spec);
    }

    @NotNull
    private Page<DocumentLendingDTO> getDocumentLendingDTOS(Pageable pageable, Specification<DocumentLending> spec) {
        Page<DocumentLending> page = documentLendingDao.findAll(spec, pageable);
        if (page.isEmpty()) {
            return Page.empty(pageable);
        }

        Map<Long, DocumentLendingDTO> dtoMap = mapToDTO(page.getContent());
        return page.map(lending -> dtoMap.get(lending.getId()));
    }

    private Map<Long, DocumentLendingDTO> mapToDTO(List<DocumentLending> lendings) {
        List<String> docIds = lendings.stream().map(DocumentLending::getDocId).toList();
        List<Document> documents = documentRepository.findAllById(docIds);

        Map<String, Document> docMap = documents.stream()
            .collect(Collectors.toMap(Document::getId, Function.identity()));

        Map<String, String> documentPathMap = getDocumentPath(documents);
        Map<Long, DocumentLendingDTO> lendingDTOMap = Maps.newHashMapWithExpectedSize(lendings.size());
        for (DocumentLending lending : lendings) {
            Long lendingId = lending.getId();
            String docId = lending.getDocId();
            Document doc = docMap.get(docId);
            DocumentLendingDTO documentLendingDTO = new DocumentLendingDTO()
                .setId(lendingId)
                .setFolderId(doc.getFolderId())
                .setCreateTime(lending.getCreationDate())
                .setDocumentId(docId)
                .setPermissions(lending.getPermission())
                .setFileName(doc.getFilename())
                .setFileExtension(doc.getExtension())
                .setRemainTime(lending.getRemainDaysAndHours())
                .setFileSize(doc.getDisplaySize())
                .setDisplayLender("%s(%s)".formatted(lending.getCreatorFullname(), lending.getCreator()))
                .setDisplayBorrowers(lending.getFullnames())
                .setReturnTime(lending.getEndDate())
                .setPath(documentPathMap.get(docId));
            lendingDTOMap.put(lendingId, documentLendingDTO);
        }

        return lendingDTOMap;
    }

    private Map<String, String> getDocumentPath(List<Document> documents) {
        List<Long> folderIds = documents.stream().map(Document::getFolderId).toList();
        Map<Long, Folder> folderMap = folderRepository.findAllById(folderIds).stream()
            .collect(Collectors.toMap(Folder::getId, Function.identity()));

        Map<String, String> documentPathMap = Maps.newHashMapWithExpectedSize(documents.size());
        for (Document doc : documents) {
            String path = DocumentUtils.getAreaDisplayDocumentPath(folderMap.get(doc.getFolderId()), doc);
            documentPathMap.put(doc.getId(), path);
        }

        return documentPathMap;
    }
}
