package zd.dms.services.oauth2.impl;

import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.base.utils.JSONResultUtils;
import zd.dms.entities.oauth2.OAuth2Client;
import zd.dms.repositories.oauth2.OAuth2ClientRepository;
import zd.dms.services.oauth2.OAuth2ClientService;

import java.util.List;
import java.util.Map;

/**
 * OAuth2客户端服务实现类
 */
@Service
@RequiredArgsConstructor
public class OAuth2ClientServiceImpl extends BaseJpaServiceImpl<OAuth2Client, String> implements OAuth2ClientService {

    @Qualifier("OAuth2ClientRepository")
    private final OAuth2ClientRepository oauth2ClientRepository;

    private final PasswordEncoder passwordEncoder;

    @Override
    public BaseRepository<OAuth2Client, String> getBaseRepository() {
        return oauth2ClientRepository;
    }

    @Override
    public JSONResultUtils<Object> save(Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            return JSONResultUtils.error("参数不能为空");
        }

        String clientId = MapUtils.getString(params, "clientId", "");
        if (StringUtils.isBlank(clientId)) {
            return JSONResultUtils.error("clientId不能为空");
        }

        OAuth2Client existsClient = findByClientId(clientId);
        if (existsClient != null) {
            return JSONResultUtils.error("clientId已存在");
        }

        String clientSecret = MapUtils.getString(params, "clientSecret", "");
        if (StringUtils.isBlank(clientSecret)) {
            return JSONResultUtils.error("clientSecret不能为空");
        }

        String clientName = MapUtils.getString(params, "clientName", "");
        int accessTokenValidity = MapUtils.getIntValue(params, "accessTokenValidity", 3600);
        int refreshTokenValidity = MapUtils.getIntValue(params, "refreshTokenValidity", 86400);

        OAuth2Client client = new OAuth2Client();
        client.setClientId(clientId);
        client.setClientSecret(clientSecret);
        client.setEnabled(true);
        client.setClientName(clientName);
        client.setAccessTokenValidity(accessTokenValidity);
        client.setAccessTokenValidity(refreshTokenValidity);
        save(client);

        return JSONResultUtils.successWithMsg("保存成功");
    }

    @Override
    public JSONResultUtils<Object> update(Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            return JSONResultUtils.error("参数不能为空");
        }

        String id = MapUtils.getString(params, "id", "");
        if (StringUtils.isBlank(id)) {
            return JSONResultUtils.error("id不能为空");
        }

        OAuth2Client client = findById(id);
        if (client == null) {
            return JSONResultUtils.error("客户端不存在");
        }


        String clientId = MapUtils.getString(params, "clientId", "");
        if (StringUtils.isNotBlank(clientId)) {
            if (!clientId.equals(client.getClientId())) {
                OAuth2Client existsClient = findByClientId(clientId);
                if (existsClient != null) {
                    return JSONResultUtils.error("clientId已存在");
                }

                client.setClientId(clientId);
            }
        }

        String clientSecret = MapUtils.getString(params, "clientSecret", "");
        if (StringUtils.isNotBlank(clientSecret)) {
            client.setClientSecret(passwordEncoder.encode(clientSecret));
        }

        String clientName = MapUtils.getString(params, "clientName", "");
        if (StringUtils.isNotBlank(clientName)) {
            client.setClientName(clientName);
        }

        int accessTokenValidity = MapUtils.getIntValue(params, "accessTokenValidity", 0);
        if (accessTokenValidity > 0) {
            client.setAccessTokenValidity(accessTokenValidity);
        }
        int refreshTokenValidity = MapUtils.getIntValue(params, "refreshTokenValidity", 0);
        if (refreshTokenValidity > 0) {
            client.setRefreshTokenValidity(refreshTokenValidity);
        }

        Object enabledObj = params.get("enabled");
        if (enabledObj != null) {
            client.setEnabled(BooleanUtils.toBoolean(enabledObj + ""));
        }

        oauth2ClientRepository.update(client);

        return JSONResultUtils.successWithMsg("更新成功");
    }

    @Override
    public Page list(@RequestBody Map<String, Object> params) {
        int pageNumber = MapUtils.getIntValue(params, "pageNumber", 1);
        int pageSize = MapUtils.getIntValue(params, "pageSize", 10);
        return oauth2ClientRepository.getOAuth2ClientPage(pageNumber, pageSize);
    }

    @Override
    public OAuth2Client findByClientId(String clientId) {
        return oauth2ClientRepository.findByClientId(clientId);
    }

    @Override
    public void save(OAuth2Client client) {
        // 如果是新客户端或者密钥已更改，则加密密钥
        if (client.getId() == null || client.getClientSecret() != null) {
            client.setClientSecret(passwordEncoder.encode(client.getClientSecret()));
        }
        oauth2ClientRepository.save(client);
    }

    @Override
    public List<OAuth2Client> findAll() {
        return oauth2ClientRepository.findAll();
    }


    @Override
    public void deleteById(String id) {
        oauth2ClientRepository.deleteById(id);
    }

    @Override
    public boolean validateClientCredentials(String clientId, String clientSecret) {
        OAuth2Client client = findByClientId(clientId);
        if (client == null || !client.getEnabled()) {
            return false;
        }

        return passwordEncoder.matches(clientSecret, client.getClientSecret());
    }
}
