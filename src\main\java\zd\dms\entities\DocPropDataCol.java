package zd.dms.entities;

import com.alibaba.fastjson.JSON;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.dms.utils.TextUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "docprop_datacol")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class DocPropDataCol extends AbstractSequenceEntity {
    /**
     * 序列化
     */
    private static final long serialVersionUID = -7828321424584456160L;

    private String name;

    private String colType;

    private int colLength;

    private boolean required;

    private boolean searchField;

    private Boolean colEditable;

    /**
     * 用户类型格式: 1-用户名 ;2-用户姓名 ;3-用户姓名(用户名);4-用户名(用户姓名)
     */
    private Integer userColumnType;

    private boolean weiyi;

    private String fillingDescription;

    @Column(length = Length.LOB_DEFAULT)
    private String options;

    @Column(length = Length.LOB_DEFAULT)
    private String sqlStr;

    private String formula;
    private Integer decimalLength;

    private String linkTable;

    private String defaultValue;

    private Boolean nowAsDefault;

    private Boolean syncWithParent;

    private Integer numIndex;

    @Transient
    private boolean sysDefault = false;

    @Transient
    private String i18nKey;

    /**
     * 创建时间
     */
    @Index(name = "i_datacol_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 默认构造器
     */
    public DocPropDataCol() {
        super();
        decimalLength = 0;
        fillingDescription = "";
        defaultValue = "";
        creationDate = new Date();
    }

    public DocPropDataCol(String name, String colType) {
        this();
        this.name = name;
        this.colType = colType;
    }

    public DocPropDataCol(String name, String colType, boolean sysDefault, String i18nKey) {
        this(name, colType);
        this.sysDefault = sysDefault;
        this.i18nKey = i18nKey;
    }

    public String getDisplayName() {
        if (!sysDefault) {
            return name;
        } else {
            // TODO ngcopy
			/*String localized = LocalizedTextUtil.getInstance().findDefaultText(i18nKey, ActionContext.getContext().getLocale());
			if (StringUtils.isNotBlank(localized)) {
				return localized;
			}*/
            return name;
        }
    }

    public String getJsonOptions() {
        List<Map<String, String>> l = new LinkedList<Map<String, String>>();
        for (String s : getOptionList()) {
            Map<String, String> m = new HashMap<String, String>();
            m.put(s, s);
            l.add(m);
        }

        return JSON.toJSONString(l);
    }

    public String getHtmlFillingDescription() {
        return TextUtils.toHtml(fillingDescription);
    }

    public List<String> getOptionList() {
        List<String> result = new LinkedList<String>();
        if (StringUtils.isNotBlank(options)) {
            String[] arr = options.split("\n");
            for (String s : arr) {
                if (StringUtils.isBlank(s)) {
                    continue;
                }

                s = StringUtils.trim(s);
                if (!result.contains(s)) {
                    result.add(s);
                }
            }
        }

        return result;
    }

    public boolean isImgType() {
        return "img".equals(colType);
    }

    public boolean isAttachmentType() {
        return "attachment".equals(colType);
    }

    public boolean isSqlTextType() {
        return "sqltext".equals(colType);
    }

    public boolean isSqlIntType() {
        return "sqlint".equals(colType);
    }

    public boolean isSqlDateType() {
        return "sqldate".equals(colType);
    }

    public boolean isDigitType() {
        return "int".equals(colType) || "number".equals(colType) || "sqlint".equals(colType);
    }

    public boolean isNumberType() {
        return "number".equals(colType);
    }

    public boolean isSelectType() {
        return "select".equals(colType) || "radio".equals(colType) || "singleSelect".equals(colType);
    }

    public boolean isLinkType() {
        return "link".equals(colType);
    }

    public boolean isFormulaType() {
        return "formula".equals(colType);
    }

    public boolean isMultiselect() {
        return "multipleSelect".equals(colType) || "checkbox".equals(colType);
    }

    public boolean isDateType() {
        if ("date".equals(colType)) {
            return true;
        }

        return false;
    }

    public boolean isDatetimeType() {
        if ("datetime".equals(colType)) {
            return true;
        }

        return false;
    }

    public boolean isFulltextType() {
        return "text".equals(colType) || "textarea".equals(colType) || "htmlTextarea".equals(colType) ||
                "select".equals(colType) || "radio".equals(colType) || "singleSelect".equals(colType) ||
                "multipleSelect".equals(colType) || "checkbox".equals(colType) || "link".equals(colType) ||
                "sqltext".equals(colType);
    }

    public boolean isTextType() {
        return "text".equals(colType) || "textarea".equals(colType) || "htmlTextarea".equals(colType);
    }

    public boolean isTextareaType() {
        return "textarea".equals(colType);
    }

    public boolean isHtmlTextareaType() {
        return "htmlTextarea".equals(colType);
    }

    public boolean isUserType() {
        return "user".equals(colType);
    }

    /**
     * @see Object#equals(Object)
     */
    @Override
    public boolean equals(Object o) {
        if (!(o instanceof DocPropDataCol)) {
            return false;
        }

        final DocPropDataCol dn = (DocPropDataCol) o;

        return new EqualsBuilder().appendSuper(super.equals(dn)).isEquals();
    }

    /**
     * @see Object#hashCode()
     */
    @Override
    public int hashCode() {
        return new HashCodeBuilder().appendSuper(super.hashCode()).toHashCode();
    }

    public String getName() {
        return name;
    }

    public void setName(String tableName) {
        this.name = tableName;
    }

    public String getColType() {
        return colType;
    }

    public void setColType(String tags) {
        this.colType = tags;
    }

    public String getOptions() {
        return options;
    }

    public void setOptions(String comment) {
        this.options = comment;
    }

    public int getColLength() {
        return colLength;
    }

    public void setColLength(int fieldLength) {
        this.colLength = fieldLength;
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

    public boolean isSearchField() {
        return searchField;
    }

    public void setSearchField(boolean searchField) {
        this.searchField = searchField;
    }

    public boolean isWeiyi() {
        return weiyi;
    }

    public void setWeiyi(boolean unique) {
        this.weiyi = unique;
    }

    public String getFormula() {
        return formula;
    }

    public void setFormula(String formula) {
        this.formula = formula;
    }

    public Integer getDecimalLength() {
        if (decimalLength == null) {
            return 2;
        }

        return decimalLength;
    }

    public void setDecimalLength(Integer decimalLength) {
        this.decimalLength = decimalLength;
    }

    public String getLinkTable() {
        return linkTable;
    }

    public void setLinkTable(String linkTable) {
        this.linkTable = linkTable;
    }

    public String getFillingDescription() {
        return fillingDescription;
    }

    public void setFillingDescription(String fillingDescription) {
        this.fillingDescription = fillingDescription;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public Boolean getNowAsDefault() {
        if (nowAsDefault == null) {
            return false;
        }

        return nowAsDefault;
    }

    public void setNowAsDefault(Boolean nowAsDefault) {
        this.nowAsDefault = nowAsDefault;
    }

    public Integer getNumIndex() {
        if (numIndex == null) {
            return 9999;
        }

        return numIndex;
    }

    public void setNumIndex(Integer numIndex) {
        this.numIndex = numIndex;
    }

    public Boolean getSyncWithParent() {
        if (syncWithParent == null) {
            return false;
        }

        return syncWithParent;
    }

    public void setSyncWithParent(Boolean syncWithParent) {
        this.syncWithParent = syncWithParent;
    }

    public String getSqlStr() {
        return sqlStr;
    }

    public void setSqlStr(String sql) {
        this.sqlStr = sql;
    }

    public boolean isSysDefault() {
        return sysDefault;
    }

    public void setSysDefault(boolean sysDefault) {
        this.sysDefault = sysDefault;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Boolean getColEditable() {
        if (colEditable == null) {
            return true;
        }

        return colEditable;
    }

    public void setColEditable(Boolean colEditable) {
        this.colEditable = colEditable;
    }

    public Integer getUserColumnType() {
        return userColumnType;
    }

    public void setUserColumnType(Integer userColumnType) {
        this.userColumnType = userColumnType;
    }

}