package zd.base.utils.model;

import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 对象转map
 */
@Slf4j
public class ObjectMapperUtils {

    public static final String dateFormatStrWithHHmm = "yyyy-MM-dd HH:mm";

    public static final String dateFormatStrWithHHmmss = "yyyy-MM-dd HH:mm:ss";
    public static final String dateFormatStrWithHHmmSlash = "yyyy/MM/dd HH:mm";
    public static final String dateFormatStr = "yyyy-MM-dd";

    public static List<Map<String, Object>> toMapList(Collection<? extends Object> collection) {
        return toMapList(collection, null, null);
    }

    public static List<Map<String, Object>> toMapList(Collection<? extends Object> collection, Map<String, Object> props) {
        return toMapList(collection, null, props);
    }

    public static List<Map<String, Object>> toMapList(Collection<? extends Object> collection, String actionName) {
        return toMapList(collection, actionName, null);
    }

    public static List<Map<String, Object>> toMapList(Collection<? extends Object> collection, String actionName,
                                                      Map<String, Object> props) {
        if (collection == null) {
            collection = Collections.emptyList();
        }

        return collection.stream().map(item -> ObjectMapperUtils.of(item, actionName, props).toMap())
                .collect(Collectors.toList());
    }

    public static Map<String, Object> toListMapWithPart(List<? extends Object> list, long partStart) {
        return toListMapWithPart(list, partStart, null);
    }

    /**
     * @param list
     * @param partStart
     * @param actionName
     * @return {"results"}
     */
    public static Map<String, Object> toListMapWithPart(List<? extends Object> list, long partStart, String actionName) {
        Map<String, Object> result = new HashMap();

        if (list == null) {
            list = new ArrayList<>();
        }

        if (list.size() == 0) {
            partStart = 0;
        }

        result.put("partStart", partStart);
        List<Map<String, Object>> results = list.stream().map(item -> ObjectMapperUtils.of(item, actionName).toMap())
                .collect(Collectors.toList());
        result.put("results", results);

        return result;
    }

    public static Map<String, Object> toMap(Object object) {
        return of(object, null).toMap();
    }

    public static Map<String, Object> toMap(Object object, String actionName) {
        return of(object, actionName).toMap();
    }

    public static Map<String, Object> toMap(Object object, String actionName, Map<String, Object> props) {
        return of(object, actionName, props).toMap();
    }

    public static ObjectMapper of(Object object) {
        return of(object, null);
    }

    public static ObjectMapper of(Object object, String actionName) {
        return of(object, actionName, null);
    }

    /**
     * @param object
     * @param actionName 分组
     * @param actionName 自定义属性
     * @return
     */
    public static ObjectMapper of(Object object, String actionName, Map<String, Object> props) {
        if (object == null) {
            return new ObjectMapper(object, actionName);
        }

        ObjectMapper objectMapper = new ObjectMapper(object, actionName, props);
        String enumName = getEnumName(object);
        try {
            ObjectMapperEnum objectMapperEnum = ObjectMapperEnum.valueOf(enumName);
            objectMapperEnum.run(objectMapper, object, actionName, objectMapper.getSupplierMap(), props);
        } catch (Exception e) {
            log.debug("objectMapperEnum No enum constant {}", enumName);
            return objectMapper;
        }

        return objectMapper;
    }

    private static String getEnumName(Object object) {
        if (object == null) {
            return "";
        }

        String className = object.getClass().toString();

        className = className.replace(".", "").replace("class ", "");

        // 处理javaassist代理对象
        int javaAssistStrIndex = className.indexOf("_$$_");
        if (javaAssistStrIndex > -1) {
            className = className.substring(0, javaAssistStrIndex);
        }

        return className;
    }
}
