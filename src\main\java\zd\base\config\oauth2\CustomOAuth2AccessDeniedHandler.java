package zd.base.config.oauth2;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;
import zd.base.utils.JSONResultUtils;

import java.io.IOException;

/**
 * 自定义OAuth2访问被拒处理器
 * 用于处理已认证但权限不足的请求
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomOAuth2AccessDeniedHandler implements AccessDeniedHandler {

    private final ObjectMapper objectMapper;

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException)
            throws IOException {
        log.error("OAuth2访问被拒: {}", accessDeniedException.getMessage(), accessDeniedException);

        JSONResultUtils<Object> objectJSONResultUtils = JSONResultUtils.errorWithMsg("access_denied", accessDeniedException.getMessage() != null ?
                accessDeniedException.getMessage() : "访问被拒绝，权限不足");

        response.setStatus(HttpStatus.FORBIDDEN.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        objectMapper.writeValue(response.getWriter(), objectJSONResultUtils);
    }
}
