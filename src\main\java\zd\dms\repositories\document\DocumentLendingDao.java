package zd.dms.repositories.document;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import zd.dms.entities.DocumentLending;

import java.util.List;

public interface DocumentLendingDao extends JpaRepository<DocumentLending, Long>, JpaSpecificationExecutor<DocumentLending> {

    @Modifying
    @Query("delete from DocumentLending where docId = ?1")
    void deleteByDocument(String docId);

    @Query("from DocumentLending where creator = ?1 order by id desc")
    List<DocumentLending> findByCreator(String creator);

    @Query("from DocumentLending where docId = ?1 order by id desc")
    List<DocumentLending> findByDocumentId(String documentId);

    @Modifying
    @Query("delete from DocumentLending where endDate <= current_timestamp")
    void deleteExpiredLendings();

    @Query("from DocumentLending where endDate <= current_timestamp")
    List<DocumentLending> getExpiredLendings();

    @Query("select count(*) from DocumentLending where docId = :documentId and usernames like :usernamePattern")
    long countByDocumentAndBorrowerUsername(@Param("documentId") String documentId, @Param("usernamePattern") String usernamePattern);
}
