package zd.dms.repositories.log.impl;

import jakarta.persistence.EntityManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.ThreeAdminLog;
import zd.dms.repositories.log.ThreeAdminLogRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.Date;
import java.util.List;

public class ThreeAdminLogRepositoryDaoImpl extends BaseRepositoryDaoImpl<ThreeAdminLog, Long> implements ThreeAdminLogRepository {

    public ThreeAdminLogRepositoryDaoImpl(Class<ThreeAdminLog> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<ThreeAdminLog> getLogsByDate(Date startDate, Date endDate, int type, int targetType, String username) {
        Specification<ThreeAdminLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<ThreeAdminLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            if (startDate != null) {
                specTools.ge("creationDate", startDate);
            }
            if (endDate != null) {
                specTools.le("creationDate", endDate);
            }

            if (type > 0) {
                specTools.eq("operateType", type);
            }

            if (targetType > 0) {
                specTools.eq("targetType", targetType);
            }

            if (StringUtils.isNotBlank(username)) {
                specTools.eq("operator", username);
            }

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public Page getLogs(int pageNumber, int pageSize, int type, int targetType, String username, Date startDate, Date endDate) {
        Specification<ThreeAdminLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<ThreeAdminLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            if (startDate != null) {
                specTools.ge("creationDate", startDate);
            }
            if (endDate != null) {
                specTools.le("creationDate", endDate);
            }

            if (type > 0) {
                specTools.eq("operateType", type);
            }

            if (targetType > 0) {
                specTools.eq("targetType", targetType);
            }

            if (StringUtils.isNotBlank(username)) {
                specTools.eq("operator", username);
            }

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }
}
