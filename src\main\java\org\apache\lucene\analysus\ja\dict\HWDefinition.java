package org.apache.lucene.analysus.ja.dict;

import org.apache.commons.lang3.StringUtils;
import org.xvolks.jnative.JNative;
import org.xvolks.jnative.Type;
import zd.base.utils.ZDUtils;
import zd.base.utils.system.PropsUtils;
import zd.base.utils.system.SystemInitUtils;
import zd.dms.utils.ProcessResult;
import zd.dms.utils.SystemUtils;

import java.io.File;
import java.net.NetworkInterface;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;

public class HWDefinition {

    /**
     * mac address
     *
     * @return
     */
    public static Set<String> getMA() {
        Set<String> results = new HashSet<String>();

        try {
            Enumeration<NetworkInterface> e = NetworkInterface.getNetworkInterfaces();// 返回所有网络接口的一个枚举实例
            while (e.hasMoreElements()) {
                NetworkInterface ni = e.nextElement();

                if (ni != null) {
                    byte[] mac = ni.getHardwareAddress();
                    if (mac != null && mac.length > 0) {
                        String result = "M:";
                        for (int i = 0; i < mac.length; i++) {
                            result += String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : "");
                        }
                        if (!result.startsWith("M:00-00-00-00-00")) {
                            results.add(result);
                        }
                    }
                }
            }
        } catch (Throwable t) {
            t.printStackTrace();
        }

        return results;
    }

    /**
     * cpu id
     *
     * @return
     */
    public static String getCID() {
        try {
            System.loadLibrary("hi");// InterfaceFun是dll文件
            // 参数说明InterfaceFun dll名,AddZhiYe函数名
            JNative jnative = new JNative("hi", "getCPUInfomation");
            // 设置此函数的返回值
            jnative.setRetVal(Type.STRING);
            int i = 0;
            // 赋予参数值
            jnative.setParameter(i++, Type.INT, "1");
            // 函数执行
            jnative.invoke();
            // 打印函数返回值
            String cpuId = jnative.getRetVal();
            return cpuId;
        } catch (Throwable e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 硬盘序列号
     *
     * @return
     */
    public static String getISN() {
        try {
            System.loadLibrary("hi");// InterfaceFun是dll文件
            // 参数说明InterfaceFun dll名,AddZhiYe函数名
            JNative jnative = new JNative("hi", "getIdeSN");
            // 设置此函数的返回值
            jnative.setRetVal(Type.STRING);
            // 函数执行
            jnative.invoke();
            // 打印函数返回值
            String ideSN = jnative.getRetVal();
            return ideSN;
        } catch (Throwable e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 主板号
     *
     * @return
     */
    public static String getBSN() {
        String result = "";
        try {
            String cmd = SystemInitUtils.INSTALLATION_DIR + "/tomcat/bin/dd.exe -t 2";
            File file = new File(SystemInitUtils.INSTALLATION_DIR + "/tomcat/bin/dd.exe");
            if (!file.exists()) {
                cmd = SystemInitUtils.getHomeDir() + "/bin/dd.exe -t 2";
            }

            if (StringUtils.isNotBlank(PropsUtils.getProps("hwcmd"))) {
                cmd = PropsUtils.getProps("hwcmd");
            }

            ProcessResult pr = SystemUtils.execWithResult(cmd);
            if (pr == null) {
                ZDUtils.debug("getBSN error pr null", null);
                return result;
            }

            String[] strArr = StringUtils.split(pr.getExitOutput(), "\n");
            for (String str : strArr) {
                if (StringUtils.isNotBlank(str)) {
                    str = StringUtils.trim(str);
                    if (str.startsWith("Serial Number:")) {
                        result = StringUtils.trim(StringUtils.substringAfter(str, "Serial Number:"));
                        break;
                    }
                }
            }
        } catch (Throwable t) {
            ZDUtils.error("getBSN error", t);
        }

        return result;
    }
}
