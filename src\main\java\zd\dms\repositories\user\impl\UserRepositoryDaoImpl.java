package zd.dms.repositories.user.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.Group;
import zd.dms.entities.User;
import zd.dms.repositories.user.UserRepository;
import zd.dms.utils.PageableUtils;
import zd.dms.utils.db.ZDJoinTableGroupUserUtils;
import zd.dms.utils.repositories.PredicateUtils;
import zd.dms.utils.repositories.QueryParamsUtils;
import zd.dms.utils.repositories.SpecTools;
import zd.record.utils.RecordDBUtils;

import java.util.*;

public class UserRepositoryDaoImpl extends BaseRepositoryDaoImpl<User, String> implements UserRepository {

    public UserRepositoryDaoImpl(Class<User> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<User> getUsersByGroupId(long groupId) {
        if (groupId <= 0) {
            return new ArrayList<>();
        }

        String sql = "select u.* from tdms_user u left join " + RecordDBUtils.getPgName(ZDJoinTableGroupUserUtils.TABLENAME) + " gu on u.id=gu." + RecordDBUtils.getPgName("USER_ID") + " where gu." + RecordDBUtils.getPgName("GROUP_ID") + "=" + groupId + " order by u.numIndex,u.creationDate";
        Query nativeQuery = this.entityManager.createNativeQuery(sql, User.class);
        return nativeQuery.getResultList();
    }

    @Override
    public User getUserByQywxEmployeeId(String qywxId) {
        /*String hql = "from User where qywxId = :qywxId";
        Map<String, Object> params = new HashMap<>();
        params.put("qywxId", qywxId);
        return findSingleObject(hql, params);*/

        return findSingleObject("from User where qywxId = ?1", qywxId);
    }

    @Override
    public User getUserByFeishuUserId(String userId) {
        return findSingleObject("from User where feishuId = ?1", userId);
    }

    @Override
    public int getUserCountByGroup(Group group) {
        Specification spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            Join groups = root.join("groups", JoinType.LEFT);
            Predicate groupIdEq = criteriaBuilder.equal(groups.get("id"), group.getId());

            predicates.add(groupIdEq);
            return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
        };

        long count = count(spec);
        return (int) count;
    }

    @Override
    public User getUser(String username) {
        Specification<User> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("username"), username));

            return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
        };

        return findSingleObject(spec);
    }

    @Override
    public List<User> getUserByUsernames(List<String> usernames) {
        Specification<User> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<User> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);
            specTools.in("username", usernames);
            specTools.eq("enabled", true);
            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public User getUserByDing(String dingEmployeeId) {
        return findSingleObject("from User where dingEmployeeId = ?1", dingEmployeeId);
    }

    @Override
    public User getUserByIdCode(String idCode) {
        return findSingleObject("from User where userIdCode = ?1", idCode);
    }

    @Override
    public User getUserByCode(String code) {
        return findSingleObject("from User where username = ?1", code);
    }

    @Override
    public List<User> getUsersByGroup(Group group) {
        Specification<User> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            Join groups = root.join("groups", JoinType.LEFT);
            Predicate groupIdEq = criteriaBuilder.equal(groups.get("id"), group.getId());

            predicates.add(groupIdEq);
            return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
        };

        return findAll(spec);
    }

    @Override
    public List<User> getUsersInRole() {
        /*Specification<User> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            Predicate groupIdEq = criteriaBuilder.isNotEmpty(root.get("roles"));
            predicates.add(groupIdEq);

            return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
        };

        return findAll(spec);*/


        /*return findAll("from User u where u.roles IS NOT EMPTY");
        return findAll("from User u where exists elements(u.roles)");
        return findAll("from User u where size(u.roles) > 0");*/

        return findAll("from User u where exists elements(u.roles)");
    }


    @Override
    public List<User> searchUserByName(String name) {
        return searchUserByName(name, "");
    }

    @Override
    public int getUC() {
        return NumberUtils.toInt(String.valueOf(findObject("select count(*) from User")));
    }

    @Override
    public int getUserCountWithSync() {
        return NumberUtils.toInt(String
                .valueOf(findObject("select count(*) from User u where u.syncString is not null")));
    }

    @Override
    public int getUserCountWithSync(User exclude) {
        if (exclude == null) {
            return getUserCountWithSync();
        }

        return NumberUtils.toInt(String.valueOf(findObject(
                "select count(*) from User u where u.syncString is not null and u.username != ?1", exclude.getUsername())));
    }

    @Override
    public int getUserCount(boolean enabled) {
        return NumberUtils.toInt(String.valueOf(findObject("select count(*) from User u where u.enabled = ?1",
                enabled)));
    }

    @Override
    public void updateNullNumIndex(int numIndex) {
        executeUpdate("update User u set u.numIndex=?1 where u.numIndex is null", numIndex);
    }

    @Override
    public Set<String> getDingUserIds() {
        String hql = "select dingUserId from User";
        TypedQuery<String> query = this.entityManager.createQuery(hql, String.class);
        List<String> datas = query.getResultList();
        if (datas == null || datas.size() == 0) {
            return null;
        }

        Set<String> result = new HashSet<String>(datas);
        if (result.contains(null)) {
            result.remove(null);
        }

        return result;
    }

    @Override
    public User getUserByDingUserId(String dingUserId) {
        return findSingleObject("from User where dingUserId = ?1", dingUserId);
    }

    @Override
    public List<User> searchUserByName(String name, String nameSeparator) {
        if (StringUtils.isEmpty(nameSeparator)) {
            String nameLike = QueryParamsUtils.getLikeHqlParamsName("name");
            String hql = "from User u where username like " + nameLike + " or fullname like " + nameLike;
            Map<String, Object> params = new HashMap<>();
            params.put("name", name);
            return findAll(hql, params);
        }

        String[] keywordsArray = name.trim().split(nameSeparator);
        List<String> sqlWhere = new ArrayList<String>();

        Map<String, Object> params = new HashMap<>();
        for (String keyword : keywordsArray) {
            if (StringUtils.isNotBlank(keyword)) {
                String keywordLike = QueryParamsUtils.getLikeHqlParamsName(keyword);
                sqlWhere.add(" username like " + keywordLike + " or fullname like " + keywordLike);
                params.put(keyword, keyword);
            }
        }

        String hql = "from User u where ";
        hql += StringUtils.join(sqlWhere, " or ");
        return findAll(hql, params);
    }

    @Override
    public Page getPageUsers(List<String> userIds, int pageNumber, int pageSize) {
        if (CollectionUtils.isEmpty(userIds)) {
            return PageableUtils.genPage(new ArrayList<>(), pageNumber, pageSize, 0);
        }

        Specification<User> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            Predicate idIn = PredicateUtils.in(root, criteriaBuilder, "id", userIds);
            predicates.add(idIn);

            Order orderPropertyOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "numIndex", true);

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, orderPropertyOrder);
        };


        return findAll(spec, PageableUtils.getPageable(pageNumber, pageSize));
    }

    @Override
    public Page getUserPage(List<String> userIds, int pageNumber, int pageSize, String username) {
        if (CollectionUtils.isEmpty(userIds)) {
            return PageableUtils.genPage(new ArrayList<>(), pageNumber, pageSize, 0);
        }

        Specification<User> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.isNotBlank(username)) {
                predicates.add(criteriaBuilder.like(root.get("username"), "%" + username + "%"));
            }

            Predicate idIn = PredicateUtils.in(root, criteriaBuilder, "id", userIds);
            predicates.add(idIn);

            Order orderPropertyOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "numIndex", true);

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, orderPropertyOrder);
        };

        return findAll(spec, PageableUtils.getPageable(pageNumber, pageSize));
    }
}
