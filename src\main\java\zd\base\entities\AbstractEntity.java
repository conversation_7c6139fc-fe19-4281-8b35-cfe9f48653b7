package zd.base.entities;

import jakarta.persistence.*;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.GenericGenerator;
import zd.base.context.UserContextHolder;
import zd.dms.entities.User;
import zd.dms.utils.TextUtils;

import java.io.Serializable;

/**
 * Abstract Entity Object，包括所有实体对象中 相同的部分。
 */
@MappedSuperclass
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public abstract class AbstractEntity implements Serializable {

    /**
     * serial
     */
    private static final long serialVersionUID = 8638012128994380583L;

    /**
     * Identifier
     */
    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    @Column(length = 32)
    protected String id;

    /**
     * 创建者
     */
    protected String creator;

    /**
     * 默认构造器
     */
    public AbstractEntity() {
        // 从UserContextHolder中取得当前的用户名
        User currentUser = UserContextHolder.getUser();
        creator = currentUser != null ? currentUser.getUsername() : null;
    }

    /**
     * @return Returns the id.
     */
    public String getId() {
        return TextUtils.escapeXml(id);
    }

    /**
     * @param id The id to set.
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * @see Object#equals(Object)
     */
    @Override
    public boolean equals(Object o) {
        if (!(o instanceof AbstractEntity)) {
            return false;
        }

        final AbstractEntity t = (AbstractEntity) o;

        return new EqualsBuilder().append(id, t.getId()).append(creator, t.getCreator()).isEquals();
    }

    /**
     * @see Object#hashCode()
     */
    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(id).append(creator).toHashCode();
    }

    /**
     * Get Creator
     *
     * @return Creator
     */
    public String getCreator() {
        return TextUtils.escapeXml(creator);
    }

    /**
     * Set Creator
     *
     * @param creator Creator name
     */
    public void setCreator(String creator) {
        this.creator = creator;
    }
}
