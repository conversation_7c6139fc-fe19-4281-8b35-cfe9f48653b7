//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                           O\  =  /O
//                        ____/`---'\____
//                      .'  \\|     |//  `.
//                     /  \\|||  :  |||//  \
//                    /  _||||| -:- |||||-  \
//                    |   | \\\  -  /// |   |
//                    | \_|  ''\---/''  |   |
//                    \  .-\__  `-`  ___/-. /
//                  ___`. .'  /--.--\  `. . __
//               ."" '<  `.___\_<|>_/___.'  >'"".
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |
//              \  \ `-.   \_ __\ /__ _/   .-` /  /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
//                    佛祖保佑       永无BUG
package zd.dms.io;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.analysus.ja.dict.ModeDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.utils.system.PropsUtils;
import zd.base.utils.system.SystemInitUtils;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentSignature;
import zd.dms.entities.DocumentTemplate;
import zd.dms.entities.Folder;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.pdf.PDFUtils;
import zd.dms.utils.TextUtils;
import zd.dms.utils.ZDFileUtils;
import zd.dms.utils.oss.OSSUtils;

import java.io.*;
import java.util.Calendar;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * 操作文档加密的类
 *
 * <AUTHOR>
 */
public class ZDIOUtils {

    private static final Logger log = LoggerFactory.getLogger(ZDIOUtils.class);

    private static final byte XM = 97;

    private static final Set<String> badDirNames = new HashSet<String>();

    private static final Calendar calendar = Calendar.getInstance();

    private static boolean useOSSCloudStorage = PropsUtils.getBoolProps("useOSSCloudStorage",
            false);

    static {
        badDirNames.add("backups");
        badDirNames.add("database");
        badDirNames.add("db");
        badDirNames.add("sync");
        badDirNames.add("mysql");
        badDirNames.add("index");
        badDirNames.add("logs");
        badDirNames.add("export");
        badDirNames.add("temp");
    }

    /**
     * 获取指定格式文件流
     *
     * @param document
     * @param version
     * @param type
     * @return
     * @throws FileNotFoundException
     */
    public static InputStream getActualDocTypeInputStream(Document document, int version, String type)
            throws FileNotFoundException {
        File docTypeFile = getActualDocTypeFile(document, version, type);
        if (useOSSCloudStorage) {
            return new FileInputStream(docTypeFile);
        }

        if (docTypeFile != null) {
            if (FE()) {
                return getDIStream(docTypeFile);
            } else {
                return new FileInputStream(docTypeFile);
            }
        }

        return null;
    }

    /**
     * 获取指定格式文件，未解密
     *
     * @param document
     * @param version
     * @param type
     * @return
     */
    public static File getActualDocTypeFile(Document document, int version, String type) {
        File docTypeFile = getDocTypeFile(document, version, true, type);
        if (docTypeFile != null && docTypeFile.exists()) {
            return docTypeFile;
        } else {
            return getDocFile(document, version, false);
        }
    }

    /**
     * 保存指定格式文件
     *
     * @param document
     * @param version
     * @param uploadFile
     * @param type
     * @throws IOException
     */
    public static void saveDocType(Document document, int version, File uploadFile, String type) throws IOException {
        if (useOSSCloudStorage) {
            log.debug("saveNewDoc useOSSCloudStorage {}", useOSSCloudStorage);
            OSSUtils.saveDocType(document, version, uploadFile, type);
        } else {
            File docTypeFile = getDocTypeFile(document, version, true, type);
            if (FE()) {
                ef(uploadFile, docTypeFile);
            } else {
                FileUtils.copyFile(uploadFile, docTypeFile);
                // FileUtils.deleteQuietly(uploadFile);
            }
        }
    }

    /**
     * 获取指定格式文件
     *
     * @param document
     * @param version
     * @param createDir
     * @param type
     * @return
     */
    public static File getDocTypeFile(Document document, int version, boolean createDir, String type) {
        if (useOSSCloudStorage) {
            return OSSUtils.getDocTypeFile(document, version, type);
        } else {
            File fileDir = getDocFileDir(document, createDir);
            File file = new File(fileDir, type + version);
            return file;
        }
    }

    /**
     * 获取指定格式文件
     *
     * @param document
     * @param version
     * @param type
     * @return
     */
    public static File getDocTypeDIFile(Document document, int version, String type) {
        File docTypeFile = getDocTypeFile(document, version, false, type);
        if (useOSSCloudStorage) {
            return docTypeFile;
        }

        if (docTypeFile != null) {
            if (FE()) {
                File tempFile = ZDFileUtils.getTempFile(0);
                try {
                    FileUtils.copyInputStreamToFile(getDIStream(docTypeFile), tempFile);
                    return tempFile;
                } catch (IOException e) {
                    return null;
                }
            } else {
                return docTypeFile;
            }
        }

        return null;
    }

    /**
     * 删除指定格式指定版本历史文件
     *
     * @param document
     * @param version
     * @param type
     */
    public static void deleteDocTypeFile(Document document, int version, String type) {
        if (useOSSCloudStorage) {
            OSSUtils.deleteDocTypeFile(document, version, type);
        } else {
            File docTypeFile = getDocTypeFile(document, version, false, type);
            FileUtils.deleteQuietly(docTypeFile);
        }
    }

    /**
     * 获取文档视频预览的InputStream，如果开启了加密功能，则使用加密
     *
     * @param document 文档
     * @param version  版本
     * @return
     * @throws FileNotFoundException
     */
    public static InputStream getDocPreviewInputStream(Document document, int version, String type)
            throws FileNotFoundException {
        File docTypeFile = getDocTypeFile(document, version, false, type);
        if (useOSSCloudStorage) {
            return new FileInputStream(docTypeFile);
        } else {
            if (docTypeFile != null) {
                if (FE()) {
                    return getDIStream(docTypeFile);
                } else {
                    return new FileInputStream(docTypeFile);
                }
            }
        }

        return null;
    }

    /**
     * 获取用于阅读的Pdf文件流
     *
     * @param document
     * @param version
     * @return
     * @throws FileNotFoundException
     */
    public static InputStream getActualDocConvertPdfInputStream(Document document, int version)
            throws FileNotFoundException {
        File docConvertPdf = getActualDocConvertPdfFile(document, version);
        if (useOSSCloudStorage) {
            return new FileInputStream(docConvertPdf);
        }

        if (docConvertPdf != null) {
            if (FE()) {
                return getDIStream(docConvertPdf);
            } else {
                return new FileInputStream(docConvertPdf);
            }
        }

        return null;
    }

    /**
     * 获取用于阅读的Pdf文件，未解密
     *
     * @param document
     * @param version
     * @return pdf不存在时返回null
     */
    public static File getActualDocConvertPdfFile(Document document, int version) {
        File docConvertPdfFileDIFile = getDocConvertPdfFile(document, version, true);
        if (docConvertPdfFileDIFile != null && docConvertPdfFileDIFile.exists()) {
            return docConvertPdfFileDIFile;
        } else {
            // 如果pdf文件不存在，需要返回null
            return null;
        }
    }

    /**
     * 保存Pdf预览文件
     *
     * @param document
     * @param version
     * @param uploadFile
     * @throws IOException
     */
    public static void saveDocConvertPdf(Document document, int version, File uploadFile) throws IOException {
        if (useOSSCloudStorage) {
            OSSUtils.saveDocType(document, version, uploadFile, "pdf");
        } else {
            File docConvertPdfFile = getDocConvertPdfFile(document, version, true);
            if (FE()) {
                ef(uploadFile, docConvertPdfFile);
            } else {
                FileUtils.copyFile(uploadFile, docConvertPdfFile);
                // FileUtils.deleteQuietly(uploadFile);
            }
        }
    }

    /**
     * 获取Pdf预览文件
     *
     * @param document
     * @param version
     * @param createDir
     * @return
     */
    public static File getDocConvertPdfFile(Document document, int version, boolean createDir) {
        if (useOSSCloudStorage) {
            return OSSUtils.getDocTypeFile(document, version, "pdf");
        }

        File fileDir = getDocFileDir(document, createDir);
        File docConvertPdfFile = new File(fileDir, "pdf" + version);
        return docConvertPdfFile;
    }

    /**
     * 获取Pdf预览文件
     *
     * @param document
     * @param version
     * @return
     */
    public static File getDocConvertPdfDIFile(Document document, int version) {
        File docConvertPdfFile = getDocConvertPdfFile(document, version, false);
        if (useOSSCloudStorage) {
            return docConvertPdfFile;
        }

        if (docConvertPdfFile != null) {
            if (FE()) {
                File tempFile = ZDFileUtils.getTempFile(0);
                try {
                    FileUtils.copyInputStreamToFile(getDIStream(docConvertPdfFile), tempFile);
                    return tempFile;
                } catch (IOException e) {
                    return null;
                }
            } else {
                return docConvertPdfFile;
            }
        }

        return null;
    }

    /**
     * 删除指定版本历史文件,Pdf
     *
     * @param document
     * @param version
     */
    public static void deleteDocConvertPdfFile(Document document, int version) {
        if (useOSSCloudStorage) {
            OSSUtils.deleteDocTypeFile(document, version, "pdf");
        } else {
            File docConvertPdfFile = getDocConvertPdfFile(document, version, false);
            FileUtils.deleteQuietly(docConvertPdfFile);
        }
    }

    /**
     * 获取文档Pdf的InputStream，如果开启了加密功能，则使用加密
     *
     * @param document 文档
     * @param version  版本
     * @return
     * @throws FileNotFoundException
     */
    public static InputStream getDocConvertPdfInputStream(Document document, int version) throws FileNotFoundException {
        File docConvertPdfFile = getDocConvertPdfFile(document, version, false);
        if (useOSSCloudStorage) {
            return new FileInputStream(docConvertPdfFile);
        }

        if (docConvertPdfFile != null) {
            if (FE()) {
                return getDIStream(docConvertPdfFile);
            } else {
                return new FileInputStream(docConvertPdfFile);
            }
        }

        return null;
    }

    /**
     * 获取用于播放的视频文件流
     *
     * @param document
     * @param version
     * @return
     * @throws FileNotFoundException
     */
    public static InputStream getActualDocPreviewInputStream(Document document, int version)
            throws FileNotFoundException {
        File docPreviewFile = getActualDocPreviewFile(document, version);
        if (useOSSCloudStorage) {
            return new FileInputStream(docPreviewFile);
        }

        if (docPreviewFile != null) {
            if (FE()) {
                return getDIStream(docPreviewFile);
            } else {
                return new FileInputStream(docPreviewFile);
            }
        }

        return null;
    }

    /**
     * 获取用于播放的视频文件，未解密
     *
     * @param document
     * @param version
     * @return
     */
    public static File getActualDocPreviewFile(Document document, int version) {
        File docPreviewDIFile = getDocPreviewFile(document, version, true);
        if (docPreviewDIFile != null && docPreviewDIFile.exists()) {
            return docPreviewDIFile;
        } else {
            return getDocFile(document, version, false);
        }
    }

    /**
     * 保存视频预览文件
     *
     * @param document
     * @param version
     * @param uploadFile
     * @throws IOException
     */
    public static void saveDocPreview(Document document, int version, File uploadFile) throws IOException {
        if (useOSSCloudStorage) {
            OSSUtils.saveDocType(document, version, uploadFile, "preview");
        } else {
            File docPreviewFile = getDocPreviewFile(document, version, true);
            if (FE()) {
                ef(uploadFile, docPreviewFile);
            } else {
                FileUtils.copyFile(uploadFile, docPreviewFile);
                // FileUtils.deleteQuietly(uploadFile);
            }
        }
    }

    /**
     * 获取视频预览文件
     *
     * @param document
     * @param version
     * @param createDir
     * @return
     */
    public static File getDocPreviewFile(Document document, int version, boolean createDir) {
        if (useOSSCloudStorage) {
            return OSSUtils.getDocTypeFile(document, version, "preview");
        } else {
            File fileDir = getDocFileDir(document, createDir);
            File previewFile = new File(fileDir, "preview" + version);
            return previewFile;
        }
    }

    /**
     * 获取视频预览文件
     *
     * @param document
     * @param version
     * @return
     */
    public static File getDocPreviewDIFile(Document document, int version) {
        File docPreviewFile = getDocPreviewFile(document, version, false);
        if (useOSSCloudStorage) {
            return docPreviewFile;
        }

        if (docPreviewFile != null) {
            if (FE()) {
                File tempFile = ZDFileUtils.getTempFile(0);
                try {
                    FileUtils.copyInputStreamToFile(getDIStream(docPreviewFile), tempFile);
                    return tempFile;
                } catch (IOException e) {
                    return null;
                }
            } else {
                return docPreviewFile;
            }
        }

        return null;
    }

    /**
     * 删除指定版本历史文件
     *
     * @param document
     * @param version
     */
    public static void deleteDocPreviewFile(Document document, int version) {
        if (useOSSCloudStorage) {
            OSSUtils.deleteDocTypeFile(document, version, "preview");
        } else {
            File docPreviewFile = getDocPreviewFile(document, version, false);
            FileUtils.deleteQuietly(docPreviewFile);
        }
    }

    /**
     * 获取文档视频预览的InputStream，如果开启了加密功能，则使用加密
     *
     * @param document 文档
     * @param version  版本
     * @return
     * @throws FileNotFoundException
     */
    public static InputStream getDocPreviewInputStream(Document document, int version) throws FileNotFoundException {
        File docPreviewFile = getDocPreviewFile(document, version, false);
        if (useOSSCloudStorage) {
            return new FileInputStream(docPreviewFile);
        }

        if (docPreviewFile != null) {
            if (FE()) {
                return getDIStream(docPreviewFile);
            } else {
                return new FileInputStream(docPreviewFile);
            }
        }

        return null;
    }

    public static void deleteTempDirFiles() {
        log.debug("开始删除临时文件");
        File tempDir = SystemInitUtils.getTempDir();
        File[] files = tempDir.listFiles();
        for (File f : files) {
            doDeleteTempDirFiles(f);
        }
    }

    private static void doDeleteTempDirFiles(File fileOrDir) {
        if (fileOrDir == null) {
            return;
        }

        if (fileOrDir.isDirectory()) {
            if (fileOrDir.getName().startsWith("unzip") || fileOrDir.getName().startsWith("mailtemp-") ||
                    fileOrDir.getName().startsWith("3h-")) {
                log.debug("doDeleteTempDirFiles skip unzip folders、mailtemp、3 hours files");
                return;
            }
            File[] files = fileOrDir.listFiles();
            for (File f : files) {
                doDeleteTempDirFiles(f);
            }
        } else {
            FileUtils.deleteQuietly(fileOrDir);
            log.debug("删除临时文件: {}", fileOrDir.getAbsolutePath());
        }
    }

    public static boolean FE() {
        return SystemConfigManager.getInstance().getBooleanProperty("fe");
    }

    public static boolean FH() {
        return SystemConfigManager.getInstance().getBooleanProperty("fh");
    }

    public static File getTempFile() {
        String filename = RandomStringUtils.random(10, true, true);
        File f = new File(SystemInitUtils.getTempDir(), filename);

        while (f.exists()) {
            filename = RandomStringUtils.random(10, true, true);
            log.debug("getTempFile 文件已存在，新随机: {}", filename);
            f = new File(SystemInitUtils.getTempDir(), filename);
        }

        return f;
    }

    public static void clearTempDir() {
        File tempDir = SystemInitUtils.getTempDir();

        if (tempDir != null && tempDir.exists() && tempDir.isDirectory()) {
            for (File f : tempDir.listFiles()) {
                FileUtils.deleteQuietly(f);
            }
        }
    }

    public static void moveDocumentsDir(File oriDir, File destDir) throws IOException {
        if (oriDir == null || destDir == null) {
            throw new IOException("目录为null");
        }

        destDir.mkdirs();

        if (badDirNames.contains(destDir.getName().toLowerCase())) {
            throw new IOException(destDir.getName() + " 是保留目录名，请重新选择一个");
        }

        if (oriDir.getAbsolutePath().toLowerCase().equals(destDir.getAbsolutePath().toLowerCase())) {
            throw new IOException("原目录与目标目录相同");
        }

        if (!oriDir.isDirectory() || !destDir.isDirectory()) {
            throw new IOException("不是目录");
        }

        if (!oriDir.canRead()) {
            throw new IOException("原目录不可读");
        }

        if (!destDir.canWrite()) {
            throw new IOException("目标目录不可写");
        }

        try {
            FileUtils.copyDirectory(oriDir, destDir, false);
            FileUtils.deleteQuietly(oriDir);
        } catch (Throwable t) {
            FileUtils.deleteQuietly(destDir);
            log.debug("moveDocumentDir 文件复制失败", t);
            throw new IOException("文件复制失败", t);
        }
    }

    /**
     * 转换所有的文档到加密格式
     *
     * @throws IOException
     */
    public static void convertAllDocsToFE() throws IOException {
        File docsFolder = SystemInitUtils.getDocumentsDir();
        Collection<File> allDocs = FileUtils.listFiles(docsFolder, null, true);
        for (File docFile : allDocs) {
            if ("ps".equals(FilenameUtils.getExtension(docFile.getName()))) {
                continue;
            } else if ("tn".equals(docFile.getName())) {
                continue;
            } else if ("stb".equals(FilenameUtils.getExtension(docFile.getName()))) {
                continue;
            } else if ("ttb".equals(FilenameUtils.getExtension(docFile.getName()))) {
                continue;
            } else if ("tn".equals(FilenameUtils.getExtension(docFile.getName()))) {
                continue;
            } else {
                ef(docFile, false);
            }
        }
    }

    /**
     * 保存文档
     *
     * @param document
     * @param uploadFile
     * @throws IOException
     */
    public static void saveNewDoc(Document document, File uploadFile) throws IOException {
        if (useOSSCloudStorage) {
            log.debug("saveNewDoc useOSSCloudStorage {}", useOSSCloudStorage);
            OSSUtils.saveNewDoc(document, uploadFile);
        } else {
            saveDoc(document, SystemConfigManager.getInstance().getDefaultDocRevision(), uploadFile);
        }
    }

    /**
     * 保存文档到指定版本
     *
     * @param document
     * @param version
     * @param uploadFile
     * @throws IOException
     */
    public static void saveDoc(Document document, int version, File uploadFile) throws IOException {
        if (useOSSCloudStorage) {
            OSSUtils.saveDoc(document, version, uploadFile);
        } else {
            File docFileObj = getDocFile(document, version, version == SystemConfigManager.getInstance()
                    .getDefaultDocRevision());

            if (FE()) {
                ef(uploadFile, docFileObj);
            } else {
                FileUtils.copyFile(uploadFile, docFileObj);
                // FileUtils.deleteQuietly(uploadFile);
            }
        }
    }

    public static void copyDocVersionFile(Document document, long logId) {
        if (useOSSCloudStorage) {
            OSSUtils.copyDocVersionFile(document, logId);
        } else {
            File newestDocFile = getDocFile(document, document.getNewestVersion(), false);
            File docVersionFile = getDocVersionFile(document, logId);
            if (newestDocFile.exists() && newestDocFile.canRead()) {
                try {
                    FileUtils.copyFile(newestDocFile, docVersionFile);
                } catch (IOException e) {
                    log.debug("copyDocVersionFile ex", e);
                }
            }
        }
    }

    public static boolean copyDocVersionFile(Document document, File srcFile, long logId) {
        if (useOSSCloudStorage) {
            return OSSUtils.copyDocVersionFile(document, srcFile, logId);
        } else {
            if (srcFile == null || !srcFile.exists()) {
                log.error("copyDocVersionFile srcFile null");
                return false;
            }

            File docVersionFile = getDocVersionFile(document, logId);
            try {
                FileUtils.copyFile(srcFile, docVersionFile);
                return true;
            } catch (IOException e) {
                log.debug("copyDocVersionFile ex", e);
            }
        }

        return false;
    }

    public static File getSmallThumb(Document document, int versionNumber) {
        File fileDir = getDocFileDir(document, false);
        File docFile = new File(fileDir, String.valueOf(versionNumber) + ".stb");
        return docFile;
    }

    public static InputStream getSmallThumbInputStream(Document document, int versionNumber)
            throws FileNotFoundException {
        File smallThumbFile = getSmallThumb(document, versionNumber);

        if (smallThumbFile.exists()) {
            return new FileInputStream(smallThumbFile);
        } else {
            return null;
        }
    }

    public static File getTinyThumb(Document document, int versionNumber) {
        File fileDir = getDocFileDir(document, false);
        File docFile = new File(fileDir, String.valueOf(versionNumber) + ".ttb");
        return docFile;
    }

    public static InputStream getTinyThumbInputStream(Document document, int versionNumber)
            throws FileNotFoundException {
        File tinyThumbFile = getTinyThumb(document, versionNumber);

        if (tinyThumbFile.exists()) {
            return new FileInputStream(tinyThumbFile);
        } else {
            return null;
        }
    }

    public static void deleteDocVersion(Document document, int versionNumber) {
        if (useOSSCloudStorage) {
            OSSUtils.deleteFileFromOSS(OSSUtils.getDocFilePath(document, versionNumber));
        } else {
            FileUtils.deleteQuietly(ZDIOUtils.getDocFile(document, versionNumber, false));
        }

        FileUtils.deleteQuietly(ZDIOUtils.getGGDocFile(document, versionNumber));
        FileUtils.deleteQuietly(ZDIOUtils.getSmallThumb(document, versionNumber));
        FileUtils.deleteQuietly(ZDIOUtils.getTinyThumb(document, versionNumber));
        FileUtils.deleteQuietly(ZDIOUtils.getThumbnailFile(document, versionNumber));
    }

    public static void deleteDocDir(Document document) {
        if (useOSSCloudStorage) {
            OSSUtils.deleteDocDir(document);
        } else {
            File docFileDir = ZDIOUtils.getDocFileDir(document, false);
            FileUtils.deleteQuietly(docFileDir);
        }
    }

    public static void rollBackVersion(Document doc, File sourceVersionFile, File newestVersionFile, int sourceVersion)
            throws IOException {

        if (useOSSCloudStorage) {
            OSSUtils.rollBackVersion(doc, sourceVersionFile, newestVersionFile, sourceVersion);
        } else {
            FileUtils.copyFile(sourceVersionFile, newestVersionFile);

            // 是否有金格格式
            File ggSourceFile = getGGDocFile(doc, sourceVersion);
            if (ggSourceFile.exists()) {
                log.debug("rollBackVersion: {} 同时回退金格格式", doc.getFilename());
                FileUtils.copyFile(ggSourceFile, getGGDocFile(doc, doc.getNewestVersion()));
            }

            // 尝试转换pdf
            if ("pdf".equalsIgnoreCase(doc.getExtension()) && ModeDefinition.p2() &&
                    SystemConfigManager.getInstance().getBooleanProperty(SystemConfigManager.PDF_SWF_ENABLED_KEY)) {
                if (!FE()) {
                    log.debug("未加密，直接转换");
                    tryConvertPdf(doc, sourceVersionFile, false);
                } else {
                    File decTemp = df(sourceVersionFile, true);
                    log.debug("已加密，先复制临时文件：{}", decTemp.getAbsolutePath());
                    tryConvertPdf(doc, decTemp, true);
                }
            }
        }
    }

    public static boolean tryConvertUploadedPdf(Document doc, boolean now) throws IOException {
        if (useOSSCloudStorage) {
            return OSSUtils.tryConvertUploadedPdf(doc, now);
        } else {
            if (doc == null) {
                log.warn("tryConvertUploadedPdf null doc");
                return false;
            }

            if (ModeDefinition.p2() &&
                    SystemConfigManager.getInstance().getBooleanProperty(SystemConfigManager.PDF_SWF_ENABLED_KEY)) {

                File newestFile = getDocFile(doc, doc.getNewestVersion(), false);

                if (!FE()) {
                    log.debug("tryConvertUploadedPdf: 未加密，直接转换");
                    return tryConvertPdf(doc, newestFile, false, now);
                } else {
                    File decTemp = df(newestFile, true);
                    log.debug("tryConvertUploadedPdf: 已加密，先复制临时文件：{}", decTemp.getAbsolutePath());
                    return tryConvertPdf(doc, decTemp, true, now);
                }
            }

            return false;
        }
    }

    public static boolean tryConvertPdf(Document doc, File uploadFile, boolean deleteSource) {
        return tryConvertPdf(doc, uploadFile, deleteSource, false);
    }

    public static boolean tryConvertPdf(Document doc, File uploadFile, boolean deleteSource, boolean now) {
        if (useOSSCloudStorage) {
            return OSSUtils.tryConvertPdf(doc, uploadFile, deleteSource, now);
        } else {
            if ("pdf".equalsIgnoreCase(doc.getExtension())) {
                if (ModeDefinition.p2() &&
                        SystemConfigManager.getInstance().getBooleanProperty(SystemConfigManager.PDF_SWF_ENABLED_KEY)) {
                    // 只有专业版转换
                    if (now) {
                        return PDFUtils.convertToSwf(doc, uploadFile, deleteSource);
                    } else {
                        PDFUtils.convertToSwfInThread(doc, uploadFile, deleteSource);
                        return false;
                    }
                }
            }

            return false;
        }
    }

    public static void saveDocTpl(DocumentTemplate docTpl, File uploadFile) throws IOException {
        File tplFolder = new File(SystemInitUtils.getDocumentsDir(), "/tpls");
        File tplFile = new File(tplFolder, docTpl.getId());
        tplFolder.mkdirs();

        if (FE()) {
            ef(uploadFile, tplFile);
        } else {
            FileUtils.copyFile(uploadFile, tplFile);
            FileUtils.deleteQuietly(uploadFile);
        }
    }

    public static void deleteDocTpl(DocumentTemplate docTpl) throws IOException {
        File tplFile = new File(SystemInitUtils.getDocumentsDir(), "/tpls/" + docTpl.getId());
        FileUtils.deleteQuietly(tplFile);
    }

    public static File getDocTplFile(DocumentTemplate docTpl) {
        if (docTpl == null) {
            return null;
        }

        File tplFile = new File(SystemInitUtils.getDocumentsDir(), "/tpls/" + docTpl.getId());

        return tplFile;
    }

    public static InputStream getDocTplInputStream(DocumentTemplate docTpl) throws FileNotFoundException {

        if (docTpl == null) {
            return null;
        }

        File tplFile = new File(SystemInitUtils.getDocumentsDir(), "/tpls/" + docTpl.getId());
        if (tplFile != null) {
            if (FE()) {
                return getDIStream(tplFile);
            } else {
                return new FileInputStream(tplFile);
            }
        }
        return null;
    }

    public static void saveDocSig(DocumentSignature docSig, File uploadFile) throws IOException {
        File tplFolder = new File(SystemInitUtils.getDocumentsDir(), "/sigs");
        File tplFile = new File(tplFolder, docSig.getId());
        tplFolder.mkdirs();

        if (FE()) {
            ef(uploadFile, tplFile);
        } else {
            FileUtils.copyFile(uploadFile, tplFile);
            FileUtils.deleteQuietly(uploadFile);
        }
    }

    public static void deleteDocSig(DocumentSignature docSig) throws IOException {
        File tplFile = new File(SystemInitUtils.getDocumentsDir(), "/sigs/" + docSig.getId());
        FileUtils.deleteQuietly(tplFile);
    }

    public static File getDocSigFile(DocumentSignature docSig) {
        if (docSig == null) {
            return null;
        }

        File tplFile = new File(SystemInitUtils.getDocumentsDir(), "/sigs/" + docSig.getId());

        return tplFile;
    }

    public static InputStream getDocSigInputStream(DocumentSignature docSig) throws FileNotFoundException {

        if (docSig == null) {
            return null;
        }

        File sigFile = new File(SystemInitUtils.getDocumentsDir(), "/sigs/" + docSig.getId());
        if (sigFile != null) {
            if (FE()) {
                return getDIStream(sigFile);
            } else {
                return new FileInputStream(sigFile);
            }
        }

        return null;
    }

    /**
     * 获取文档的InputStream，如果开启了加密功能，则使用加密
     *
     * @param document 文档
     * @param version  版本
     * @return
     * @throws FileNotFoundException
     */
    public static InputStream getDocInputStream(Document document, int version) throws FileNotFoundException {
        if (useOSSCloudStorage) {
            return OSSUtils.getDocInputStream(document, version);
        } else {
            File docFile = getDocFile(document, version, false);
            if (docFile != null) {
                if (FE()) {
                    return getDIStream(docFile);
                } else {
                    return new FileInputStream(docFile);
                }
            }
        }

        return null;
    }

    public static InputStream getDocVersionInputStream(Document document, long docVersion) throws FileNotFoundException {
        if (useOSSCloudStorage) {
            return OSSUtils.getDocVersionInputStream(document, docVersion);
        } else {
            File docFile = getDocVersionFile(document, docVersion);
            if (docFile != null) {
                if (FE()) {
                    return getDIStream(docFile);
                } else {
                    return new FileInputStream(docFile);
                }
            }
        }

        return null;
    }

    public static File getDocFileDir(Document document, boolean createDir) {
        if (!createDir) {
            File finalFileDir = getDocFileDir_New(document, createDir);
            if (finalFileDir == null || !finalFileDir.exists()) {
                log.debug("getDocFileDir 返回旧格式路径: {}", document.getFilename());
                finalFileDir = getDocFileDir_Old(document, createDir);
                if (finalFileDir == null || !finalFileDir.exists()) {
                    finalFileDir = getDocFileDir_New(document, createDir);
                }
            }
            return finalFileDir;
        } else {
            return getDocFileDir_New(document, createDir);
        }
    }

    public static void updateDocFileDirToNewFormat(Document document) throws IOException {
        try {
            File oldDir = getDocFileDir_Old(document, false);
            if (oldDir.exists()) {
                calendar.setTime(document.getCreationDate());
                int year = calendar.get(Calendar.YEAR);
                int month = calendar.get(Calendar.MONTH) + 1;
                int day = calendar.get(Calendar.DAY_OF_MONTH);
                File destDir = new File(getStorageRootDir(document), year + "/" + month + "/" + day);

                FileUtils.moveDirectoryToDirectory(oldDir, destDir, true);
            }

        } catch (IOException e) {
            throw e;
        }
    }

    public static File getDocFileDir_New(Document document, boolean createDir) {
        if (document == null) {
            return null;
        }

        if (document.getFolder().getFolderType() == Folder.TYPE_MYDOCUMENT) {
            return getMyDocFileDir(document);
        } else {
            calendar.setTime(document.getCreationDate());
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;
            int day = calendar.get(Calendar.DAY_OF_MONTH);

            File docFileDir = new File(getStorageRootDir(document), year + "/" + month + "/" + day + "/" +
                    document.getId());
            if (createDir && docFileDir != null && !docFileDir.exists()) {
                docFileDir.mkdirs();
            }

            return docFileDir;
        }
    }

    public static File getDocFileDir_Old(Document document, boolean createDir) {
        if (document == null) {
            return null;
        }

        if (document.getFolder().getFolderType() == Folder.TYPE_MYDOCUMENT) {
            return getMyDocFileDir(document);
        } else {
            File docFileDir = new File(getStorageRootDir(document), document.getId());

            if (createDir && docFileDir != null && !docFileDir.exists()) {
                docFileDir.mkdirs();
            }

            return docFileDir;
        }
    }

    private static File getStorageRootDir(Document document) {
        File docRootDir = SystemInitUtils.getDocumentsDir();
        if (StringUtils.isNotBlank(document.getStorage())) {
            if (!"0".equals(document.getStorage())) {
                String dirPath = StorageUtils.getStorageDirPath(document.getStorage());
                if (StringUtils.isNotBlank(dirPath)) {
                    docRootDir = new File(dirPath);
                }
            }
        }

        return docRootDir;
    }

    /**
     * 获取旧版本的我的文档的存储位置
     *
     * @param document
     * @return
     */
    public static File getOldMyDocFileDir(Document document) {
        File docFileDir = new File(SystemInitUtils.getDocumentsDir(), document.getId());
        return docFileDir;
    }

    public static boolean moveDoc(Folder targetFolder, Document doc) {
        Folder oriFolder = doc.getFolder();

        if (oriFolder.getFolderType() == Folder.TYPE_MYDOCUMENT && targetFolder.getFolderType() == Folder.TYPE_PUBLIC) {
            log.debug("将 {} 的个人文档 {} 移动到公共目录", doc.getCreator(), doc.getFilename());
            File oldMyDocDir = getMyDocFileDir(doc);

            if (oldMyDocDir.exists() && oldMyDocDir.isDirectory()) {
                try {
                    File newDocDir = new File(SystemInitUtils.getDocumentsDir(), doc.getId());
                    if (newDocDir.exists()) {
                        FileUtils.deleteDirectory(newDocDir);
                    }

                    FileUtils.moveDirectory(oldMyDocDir, newDocDir);
                    log.debug("MyDocument: {} moved to pubDoc", doc.getFilename());
                    return true;
                } catch (IOException e) {
                    log.debug("moveOldMyDocToNew error", e);
                    return false;
                }
            }
        }

        return true;
    }

    public static File getMyDocDir(String username) {
        File docFileDir = new File(SystemInitUtils.getDocumentsDir(), "/my/" +
                TextUtils.escapeXml(TextUtils.filterFilename(username)));
        docFileDir.mkdirs();

        return docFileDir;
    }

    public static long getMyDocStorageUsed(String username) {
        return FileUtils.sizeOfDirectory(getMyDocDir(username));
    }

    public static File getMyDocFileDir(Document document) {
        File docFileDir = new File(SystemInitUtils.getDocumentsDir(), "/my/" +
                TextUtils.escapeXml(TextUtils.filterFilename(document.getCreator())) + "/" + document.getId());
        docFileDir.mkdirs();
        log.debug("getFrom myDoc: {}", docFileDir.getAbsolutePath());

        return docFileDir;
    }

    public static void updateOldMyDocToNew(Document document) throws IOException {

        if (document == null) {
            log.debug("updateOldMyDocToNew null doc");
            return;
        }

        File oldMyDocDir = getOldMyDocFileDir(document);

        if (oldMyDocDir == null || !oldMyDocDir.exists() || !oldMyDocDir.canRead()) {
            log.debug("updateOldMyDocToNew can't update document: {}", document.getFilename());
        }

        File newMyDocDir = getMyDocFileDir(document);
        if (newMyDocDir.exists()) {
            FileUtils.deleteDirectory(newMyDocDir);
        }

        try {
            FileUtils.moveDirectory(oldMyDocDir, newMyDocDir);
            log.debug("MyDocument: {} updated", document.getFilename());
        } catch (IOException e) {
            log.debug("moveOldMyDocToNew error", e);
            throw e;
        }
    }

    /**
     * 根据版本获取指定文档的File对象
     *
     * @param document 文档
     * @param version  版本
     * @return
     */
    public static File getDocFile(Document document, int version, boolean createDir) {
        if (useOSSCloudStorage) {
            return OSSUtils.getDocFile(document, version);
        } else {
            File fileDir = getDocFileDir(document, createDir);
            File docFile = new File(fileDir, String.valueOf(version));
            return docFile;
        }
    }

    /**
     * 获取版本文档
     *
     * @param document
     * @param logId
     * @return
     */
    public static File getDocVersionFile(Document document, long logId) {
        if (useOSSCloudStorage) {
            return OSSUtils.getDocVersionFile(document, logId);
        } else {
            File fileDir = getDocFileDir(document, true);
            File docFile = new File(fileDir, "/v/" + String.valueOf(logId));
            return docFile;
        }
    }

    /**
     * 根据版本获取指定文档的File对象
     *
     * @param document 文档
     * @param version  版本
     * @return
     */
    public static File getGGDocFile(Document document, int version) {
        File fileDir = getDocFileDir(document, false);
        File docFile = new File(fileDir, String.valueOf(version) + ".ggf");
        return docFile;
    }

    /**
     * 获取指定文档的最新版本File对象
     *
     * @param document
     * @return
     */
    public static File getNewestDocFile(Document document) {
        return getDocFile(document, document.getNewestVersion(), false);
    }

    /**
     * 获取文档内容
     *
     * @param document 文档
     * @param version  版本
     * @return 文档内容
     * @throws IOException
     */
    public static String getDocumentContent(Document document, int version) throws IOException {
        File docFile = getDocFile(document, version, false);
        if (docFile != null && docFile.exists() && docFile.canRead()) {
            try {
                if (FE()) {
                    InputStream in = getDIStream(docFile);
                    String content = IOUtils.toString(in, document.getEncoding());
                    return content;
                } else {
                    return FileUtils.readFileToString(docFile, document.getEncoding());
                }
            } catch (IOException e) {
                throw e;
            }
        }

        return null;
    }

    /**
     * 获取图片文档的预览图File对象
     *
     * @param document 图片文档
     * @param version  版本
     * @return 预览图对象
     */
    public static File getThumbnailFile(Document document, int version) {
        File fileDir = new File(SystemInitUtils.getDocumentsDir(), String.valueOf(document.getId()));
        File tnFile = new File(fileDir, version + ".tn");

        if (!tnFile.exists()) {
            fileDir = getDocFileDir_New(document, false);
            tnFile = new File(fileDir, version + ".tn");
        }

        return tnFile;
    }

    /**
     * 加密文件
     *
     * @param srcFile  源文件
     * @param destFile 目标文件
     * @throws IOException
     */
    public static void ef(File srcFile, File destFile) throws IOException {
        if (!srcFile.exists()) {
            throw new FileNotFoundException("找不到enc源文件: " + srcFile.getAbsolutePath());
        }

        FileOutputStream tempOut = null;
        InputStream xin = null;
        try {
            tempOut = new FileOutputStream(destFile);
            xin = getDIStream(srcFile);
            IOUtils.copyLarge(xin, tempOut);
        } finally {
            IOUtils.closeQuietly(tempOut);
            IOUtils.closeQuietly(xin);
        }
    }

    /**
     * 加密指定文件
     *
     * @param f         要加密的文件
     * @param encToTemp 是否加密至临时文件，如果为true，则不删除原文件，并返回临时文件。如果为false，则用加密后临时文件替换原文件
     * @return 加密后的文件
     * @throws IOException
     */
    public static File ef(File f, boolean encToTemp) throws IOException {
        if (!f.exists()) {
            throw new FileNotFoundException("找不到enc源文件: " + f.getAbsolutePath());
        }

        File tempFile = new File(f.getParentFile(), f.getName() + ".tmp");

        FileOutputStream tempOut = null;
        InputStream xin = null;
        try {
            tempOut = new FileOutputStream(tempFile);
            xin = getDIStream(f);
            IOUtils.copyLarge(xin, tempOut);
        } finally {
            IOUtils.closeQuietly(tempOut);
            IOUtils.closeQuietly(xin);
        }

        if (!encToTemp) {
            FileUtils.deleteQuietly(f);
            FileUtils.moveFile(tempFile, f);
            return f;
        } else {
            return tempFile;
        }
    }

    /**
     * 解密指定文件
     *
     * @param f         要解密的文件
     * @param decToTemp 是否解密至临时文件，如果为true，则不删除原文件，并返回临时文件。如果为false，则用解密后临时文件替换原文件
     * @return 解密后的文件
     * @throws IOException
     */
    public static File df(File f, boolean decToTemp) throws IOException {
        if (!f.exists()) {
            throw new FileNotFoundException("找不到dec源文件: " + f.getAbsolutePath());
        }

        File tempFile = new File(f.getParentFile(), f.getName() + ".tmp");

        InputStream oriIn = null;
        OutputStream xout = null;
        try {
            oriIn = new FileInputStream(f);
            xout = getDOStream(tempFile);
            IOUtils.copyLarge(oriIn, xout);
        } finally {
            IOUtils.closeQuietly(oriIn);
            IOUtils.closeQuietly(xout);
        }

        if (!decToTemp) {
            FileUtils.deleteQuietly(f);
            FileUtils.moveFile(tempFile, f);
            return f;
        } else {
            return tempFile;
        }
    }

    public static InputStream getDIStream(File f) throws FileNotFoundException {
        if (!f.exists()) {
            throw new FileNotFoundException("找不到dec in源文件: " + f.getAbsolutePath());
        }

        FileInputStream deIn = new FileInputStream(f);
        return new DInputStream(deIn, XM);
    }

    public static OutputStream getDOStream(File f) throws FileNotFoundException {
        FileOutputStream deOut = new FileOutputStream(f);
        return new DOutputStream(deOut, XM);
    }

    // 金格相关IO操作
    public static void saveGGDoc(Document document, int version, File uploadFile) throws IOException {

        File docFileObj = getGGDocFile(document, version);

        if (FE()) {
            ef(uploadFile, docFileObj);
        } else {
            FileUtils.copyFile(uploadFile, docFileObj);
            FileUtils.deleteQuietly(uploadFile);
        }
    }

    public static InputStream getGGDocInputStream(Document document, int version) throws FileNotFoundException {

        File docFile = getGGDocFile(document, version);
        if (docFile != null && docFile.exists()) {
            log.debug("getGGDocInputStream: {} 返回金格格式", document.getFilename());
            if (FE()) {
                return getDIStream(docFile);
            } else {
                return new FileInputStream(docFile);
            }
        } else {
            log.debug("getGGDocInputStream: {} 未找到金格格式，返回普通格式", document.getFilename());
            return getDocInputStream(document, version);
        }
    }

    public static boolean getUseOSSCloudStorage() {
        return useOSSCloudStorage;
    }
}
