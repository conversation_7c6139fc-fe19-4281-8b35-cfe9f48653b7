package zd.base.utils.system;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import zd.base.utils.file.PropertiesFileUtils;

import java.io.File;
import java.util.Properties;
import java.util.Set;

@Slf4j
public class PropsUtils {

    private static Properties ecmProps;

    public static String getPropsFileName() {
        String active = SpringUtils.getProps("zd.profiles.active", "");
        if (StringUtils.isNotBlank(active)) {
            return "app-" + active + ".properties";
        }

        return "app.properties";
    }

    public static void loadProps() {
        String propsFileName = getPropsFileName();
        ecmProps = PropertiesFileUtils.getProperties(propsFileName);
    }

    public static void loadHomeProps() {
        File homePropsDir = new File(SystemInitUtils.getHomeDir(), "app.properties");
        if (!homePropsDir.exists() || !homePropsDir.isFile()) {
            return;
        }

        Properties homeProps = PropertiesFileUtils.getPropertiesByFilePath(homePropsDir.getAbsolutePath());

        Set<Object> keys = homeProps.keySet();
        for (Object key : keys) {
            if (ecmProps.contains(key)) {
                continue;
            }

            ecmProps.put(key, homeProps.get(key));
        }
    }

    public static String getProps(String key) {
        return getProps(key, "");
    }

    public static String getProps(String key, String defaultValue) {
        if (ecmProps == null) {
            log.debug("getProps appProps uninit");
            return "";

        }
        return StringUtils.defaultIfEmpty(ecmProps.getProperty(key), defaultValue);
    }

    public static int getIntProps(String key) {
        return getIntProps(key, 0);
    }

    public static int getIntProps(String key, int defaultValue) {
        return NumberUtils.toInt(getProps(key), defaultValue);
    }

    public static long getLongProps(String key) {
        return getLongProps(key, 0L);
    }

    public static long getLongProps(String key, long defaultValue) {
        return NumberUtils.toLong(getProps(key), defaultValue);
    }

    public static float getFloatProps(String key) {
        return getFloatProps(key, 0f);
    }

    public static float getFloatProps(String key, float defaultValue) {
        return NumberUtils.toFloat(getProps(key), defaultValue);
    }

    public static double getDoubleProps(String key) {
        return getDoubleProps(key, 0d);
    }

    public static double getDoubleProps(String key, double defaultValue) {
        return NumberUtils.toDouble(getProps(key), defaultValue);
    }

    public static boolean getBoolProps(String key) {
        return getBoolProps(key, false);
    }

    public static boolean getBoolProps(String key, boolean defaultValue) {
        String propsValue = getProps(key);
        if (StringUtils.isBlank(propsValue)) {
            return defaultValue;
        }

        return BooleanUtils.toBoolean(propsValue);
    }
}
