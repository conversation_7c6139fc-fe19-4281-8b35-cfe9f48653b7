package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.dms.utils.TextUtils;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "lendrequest")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class LendRequest extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = -8718430230997144569L;

    /**
     * 申请者全名
     */
    private String creatorFullname;

    /**
     * 文件名描述
     */
    @Column(length = Length.LOB_DEFAULT)
    private String filenameDesc;

    /**
     * 申请描述
     */
    @Column(name = "comment1")
    private String comment;

    /**
     * 申请文档id列表，逗号分隔
     */
    @Column(length = Length.LOB_DEFAULT)
    private String requestDocIds;

    /**
     * 批准文档id
     */
    @Column(length = Length.LOB_DEFAULT)
    private String approvedDocIds;

    /**
     * 审核人用户名列表 使用**username**分隔
     */
    private String approverUsernames;

    /**
     * 申请权限
     */
    private String requestPermission;

    /**
     * 批准权限
     */
    private String approvedPermission;

    /**
     * 请求借阅小时
     */
    private int requestHour;

    /**
     * 批准借阅小时
     */
    private int approvedHour;

    /**
     * 审批通过人用户名
     */
    private String approvedByUsername;

    /**
     * 审批通过人全名
     */
    private String approvedByFullname;

    /**
     * 审批通过时间
     */
    private Date approvedDate;

    /**
     * 是否已经通过审批
     */
    private boolean approved;

    /**
     * 创建时间
     */
    @Index(name = "i_lr_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 默认构造器
     */
    public LendRequest() {
        super();
        creationDate = new Date();
    }

    public String getHtmlComment() {
        return TextUtils.toHtml(comment);
    }

    public String[] getApprovedDocIdArray() {
        if (StringUtils.isBlank(approvedDocIds)) {
            return new String[]{};
        }

        return StringUtils.split(approvedDocIds, ",");
    }

    public String[] getRequestDocIdArray() {
        if (StringUtils.isBlank(requestDocIds)) {
            return new String[]{};
        }

        return StringUtils.split(requestDocIds, ",");
    }

    /**
     * 申请时长
     *
     * @return
     */
    public String getRequestTime() {
        return requestHour % 24 == 0 ? requestHour / 24 + "天" : requestHour + "小时";
    }

    /**
     * 批准时长
     *
     * @return
     */
    public String getApprovedTime() {
        return approvedHour % 24 == 0 ? approvedHour / 24 + "天" : approvedHour + "小时";
    }

    public String getApproverUsernames() {
        return approverUsernames;
    }

    public void setApproverUsernames(String usernames) {
        this.approverUsernames = usernames;
    }

    public String getCreatorFullname() {
        return creatorFullname;
    }

    public void setCreatorFullname(String creatorFullname) {
        this.creatorFullname = creatorFullname;
    }

    public String getRequestPermission() {
        return requestPermission;
    }

    public void setRequestPermission(String permission) {
        this.requestPermission = permission;
    }

    public String getApprovedPermission() {
        return approvedPermission;
    }

    public void setApprovedPermission(String approvedPermission) {
        this.approvedPermission = approvedPermission;
    }

    public String getRequestDocIds() {
        return requestDocIds;
    }

    public void setRequestDocIds(String requestDocIds) {
        this.requestDocIds = requestDocIds;
    }

    public String getApprovedDocIds() {
        return approvedDocIds;
    }

    public void setApprovedDocIds(String approvedDocIds) {
        this.approvedDocIds = approvedDocIds;
    }

    public String getFilenameDesc() {
        return filenameDesc;
    }

    public void setFilenameDesc(String filenameDesc) {
        this.filenameDesc = filenameDesc;
    }

    public int getRequestHour() {
        return requestHour;
    }

    public void setRequestHour(int requestHour) {
        this.requestHour = requestHour;
    }

    public int getApprovedHour() {
        return approvedHour;
    }

    public void setApprovedHour(int approvedHour) {
        this.approvedHour = approvedHour;
    }

    public String getApprovedByUsername() {
        return approvedByUsername;
    }

    public void setApprovedByUsername(String approvedByUsername) {
        this.approvedByUsername = approvedByUsername;
    }

    public String getApprovedByFullname() {
        return approvedByFullname;
    }

    public void setApprovedByFullname(String approvedByFullname) {
        this.approvedByFullname = approvedByFullname;
    }

    public Date getApprovedDate() {
        return approvedDate;
    }

    public void setApprovedDate(Date approvedDate) {
        this.approvedDate = approvedDate;
    }

    public void setApproved(boolean approved) {
        this.approved = approved;
    }

    public boolean isApproved() {
        return approved;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
