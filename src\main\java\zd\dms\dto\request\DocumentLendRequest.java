package zd.dms.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import zd.dms.utils.Constants;

@Schema(
    description = "文档借阅请求",
    requiredProperties = {
        "documentIds",
        "borrowerUserNames",
        "timeUnit",
        "timeValue",
        "permissions"
    })
@Data
public class DocumentLendRequest {

    @Schema(description = "文档ID数组")
    private List<String> documentIds;

    @Schema(description = "借出人用户名数组")
    private List<String> borrowerUserNames;

    @Schema(description = "借阅时间单位")
    private String timeUnit;

    @Schema(description = "借阅时间数值")
    private int timeValue;

    @Schema(description = "权限字符串数组")
    private List<String> permissions;

    public int computeTimeValue() {
        if (Constants.TimeUnit.DAY.equalsIgnoreCase(this.timeUnit)) {
            return this.timeValue * 24;
        }

        return this.timeValue;
    }
}
