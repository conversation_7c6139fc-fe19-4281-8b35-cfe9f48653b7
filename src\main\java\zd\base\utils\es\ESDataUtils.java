package zd.base.utils.es;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import zd.base.context.UserContextHolder;
import zd.base.utils.ZDMapTool;
import zd.base.utils.redis.ZDRedisUtils;
import zd.base.utils.system.SpringUtils;
import zd.dms.entities.Document;
import zd.dms.entities.Folder;
import zd.dms.services.document.DocumentService;
import zd.dms.services.document.FolderService;
import zd.dms.utils.JSONUtils;
import zd.record.entities.DataCol;
import zd.record.entities.DataStruct;
import zd.record.entities.RecFolder;
import zd.record.service.datastruct.DataStructService;
import zd.record.service.datastruct.DataStructUtils;
import zd.record.service.folder.RecFolderService;
import zd.record.service.security.RecFolderPermissionUtils;
import zd.record.utils.RecordDBUtils;
import zd.record.utils.record.RecordDataUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ESDataUtils {

    private static final DataStructService dataStructService = SpringUtils.getBean(DataStructService.class);

    private static final RecFolderService recFolderService = SpringUtils.getBean(RecFolderService.class);

    private static final DocumentService documentService = SpringUtils.getBean(DocumentService.class);

    private static final FolderService folderService = SpringUtils.getBean(FolderService.class);

    private static final String RECORD_ES_DATA_COLS = "record_es_dataCols";

    private static List<String> ES_IK_COLS = null;

    public static final String DATA_TYPE_REC = "REC";

    public static final String DATA_TYPE_DOC = "DOC";

    public static void createDefaultIndex() {
        if (!ESUtils.ES_ENABLE) {
            return;
        }

        ESUtils.deleteIndex(ESUtils.RECORD_ES_INDEX);
        ESUtils.createIndex(ESUtils.RECORD_ES_INDEX);
        ESDataUtils.putDefaultRecordMappings();
        ESDataUtils.putAllDataCol();
        ESDataUtils.reindexAllRecords();
        new Thread(ESDataUtils::reindexDocuments).start();
    }

    public static void addFieldMapping(Map<String, Object> propertiesMap, String fieldName, String type) {
        Map<String, Object> fieldMap = new HashMap<>();

        if ("ZD_TAGS".equals(fieldName)) {
            fieldMap.put("type", type);
        } else if ("keyword".equals(type)) {
            fieldMap.put("type", "keyword");
//            fieldMap.put("type", "text");
//            Map<String, Object> keywordMap = ZDMapTool.getInstance().put("type", "keyword").put("ignore_above", 256).getMap();
//            Map<String, Object> fieldsMap = ZDMapTool.getInstance().put("keyword", keywordMap).getMap();
//            fieldMap.put("fields", fieldsMap);
        } else if ("date".equals(type)) {
            fieldMap.put("type", "long");
        } else if ("text".equals(type)) {
            fieldMap.put("type", type);

            if (ESUtils.ES_USE_IK || isIkCols(fieldName)) {
                // 默认分词器
                fieldMap.put("analyzer", "ik_max_word");
                fieldMap.put("search_analyzer", "ik_smart");
            }
        } else {
            fieldMap.put("type", type);
        }

        propertiesMap.put(fieldName, fieldMap);
    }

    public static void putAllDataCol() {
        List<DataCol> allDataCols = dataStructService.getAllDataCols();
        Map<String, Object> mappingMap = new HashMap<>();
        List<String> containsDataCol = new ArrayList<>();

        Map<String, Object> propertiesMap = new HashMap<>();
        mappingMap.put("properties", propertiesMap);
        for (DataCol dataCol : allDataCols) {
            addFieldMapping(propertiesMap, containsDataCol, dataCol);
        }

        ESUtils.putMapping(ESUtils.RECORD_ES_INDEX, mappingMap);

        ZDRedisUtils.delete(500, ESUtils.REDIS_ES_COLS);
    }

    public static void reindexAllRecords() {
        List<DataStruct> allDataStruct = dataStructService.getAllDataStruct();
        for (DataStruct dataStruct : allDataStruct) {
            new Thread(() -> reindexDataStructRecords(dataStruct)).start();
        }
    }

    public static void reindexDataStructRecords(DataStruct dataStruct) {
        if (dataStruct == null) {
            return;
        }

        String tableName = dataStruct.getTableName();
        if (StringUtils.isBlank(tableName)) {
            return;
        }

        log.debug("reindex tableName:{} start", tableName);

        int startIndex = 0;
        int rowCount = 1000;
        while (true) {
            log.debug("start reindex tableName:{} startIndex:{} rowCount:{}", tableName, startIndex, rowCount);
            List<Map<String, Object>> records = RecordDBUtils.getRecords(null, tableName, startIndex, rowCount, "", null, 0, "");
            if (CollectionUtils.isEmpty(records)) {
                break;
            }

            List<Map<String, Object>> esRecords = new ArrayList<>();
            for (Map<String, Object> recordData : records) {
                if (MapUtils.isEmpty(recordData)) {
                    continue;
                }

                long recId = MapUtils.getLongValue(recordData, "ID", 0);
                if (recId <= 0) {
                    continue;
                }

                String indexId = tableName + "_" + recId;
                Map<String, Object> esRecordData = getEsRecordData(tableName, indexId, recordData);
                esRecords.add(esRecordData);
            }

            ESUtils.batchCreateDocument(ESUtils.RECORD_ES_INDEX, esRecords);

            log.debug("end reindex tableName:{} startIndex:{} rowCount:{}", tableName, startIndex, rowCount);
            if (records.size() < rowCount) {
                break;
            }

            startIndex += rowCount;
        }

        log.debug("reindex tableName:{} end", tableName);
    }

    public static void reindexDocuments() {

        log.debug("reindexDocuments start");

        int pageNumber = 0;
        int pageSize = 1000;
        while (true) {
            log.debug("start reindexDocuments pageNumber:{} pageSize:{}", pageNumber, pageSize);
            Page<Document> documentsWithoutAttach = documentService.getDocumentsWithoutAttach(pageNumber, pageSize);
            if (documentsWithoutAttach == null) {
                break;
            }

            List<Document> content = documentsWithoutAttach.getContent();
            if (CollectionUtils.isEmpty(content)) {
                break;
            }

            List<Map<String, Object>> esDocuments = new ArrayList<>();
            for (Document doc : content) {
                String indexId = ESUtils.getIndexId(doc);
                Map<String, Object> esDocumentData = getEsDocumentData(doc, indexId);
                esDocuments.add(esDocumentData);
            }

            ESUtils.batchCreateDocument(ESUtils.RECORD_ES_INDEX, esDocuments);

            log.debug("end reindexDocuments pageNumber:{} pageSize:{}", pageNumber, pageSize);

            if (content.size() < pageSize) {
                break;
            }

            pageNumber++;
        }

        log.debug("reindexDocuments end");
    }

    public static void putDataCol(DataCol dataCol) {
        if (!ESUtils.ES_ENABLE) {
            return;
        }

        List<String> esRecordCols = getEsRecordCols();

        String name = dataCol.getName();
        String colType = dataCol.getColType();

        String esColType = getEsColType(colType);
        String esColName = getEsColName(name, esColType);
        if (esRecordCols.contains(esColName)) {
            return;
        }

        Map<String, Object> mappingMap = new HashMap<>();
        Map<String, Object> propertiesMap = new HashMap<>();
        mappingMap.put("properties", propertiesMap);
        addFieldMapping(propertiesMap, esColName, esColType);

        ZDRedisUtils.delete(ESUtils.REDIS_ES_COLS);
        ESUtils.putMapping(ESUtils.RECORD_ES_INDEX, mappingMap);
        ZDRedisUtils.delete(500, ESUtils.REDIS_ES_COLS);
    }

    public static void addFieldMapping(Map<String, Object> propertiesMap, List<String> containsDataCol, DataCol dataCol) {
        String name = dataCol.getName();
        String colType = dataCol.getColType();

        String esColType = getEsColType(colType);
        String esColName = getEsColName(name, esColType);

        if (containsDataCol.contains(esColName)) {
            return;
        }
        containsDataCol.add(esColName);

        addFieldMapping(propertiesMap, esColName, esColType);
    }

    private static String getEsColType(String colType) {
        String esType = "text";
        if ("date".equals(colType) || "datetime".equals(colType)) {
            esType = "date";
        } else if ("number".equals(colType)) {
            esType = "double";
        }

        return esType;
    }

    private static String getEsColName(String name, String esColType) {
        return name + "__" + esColType;
    }

    public static List<String> getEsRecordCols() {
        String esRecordColName = ZDRedisUtils.getString(ESUtils.REDIS_ES_COLS);
        if (StringUtils.isBlank(esRecordColName)) {
            String mapping = ESUtils.getMapping(ESUtils.RECORD_ES_INDEX);
            if (StringUtils.isBlank(mapping)) {
                return new ArrayList<>();
            }

            JSONObject mappingObject = ESJSONUtils.parseJSONObject(mapping);
            JSONObject recordObject = ESJSONUtils.getJSONObject(mappingObject, ESUtils.RECORD_ES_INDEX);

            JSONObject mappingsObject = ESJSONUtils.getJSONObject(recordObject, "mappings");
            JSONObject propertiesObject = ESJSONUtils.getJSONObject(mappingsObject, "properties");

            Set<String> strings = propertiesObject.keySet();
            List<String> results = new ArrayList<>(strings);

            String jsonString = JSONUtils.toJSONString(results, "[]");
            ZDRedisUtils.set(ESUtils.REDIS_ES_COLS, jsonString);

            return results;
        }

        return JSONUtils.parseObject(esRecordColName, List.class);
    }

    public static Map<String, Object> getEsRecordData(String tableName, String indexId, Map<String, Object> recordData) {
        if (StringUtils.isBlank(tableName) || MapUtils.isEmpty(recordData)) {
            return new HashMap<>();
        }

        Map<String, Object> esRecordData = new HashMap<>();
        putDefaultCol(esRecordData, recordData, tableName, indexId, ESDataUtils.DATA_TYPE_REC);

        List<String> esTableDataCols = getEsTableDataCols(tableName);
        if (CollectionUtils.isNotEmpty(esTableDataCols)) {
            for (String esTableDataCol : esTableDataCols) {
                if (StringUtils.isBlank(esTableDataCol)) {
                    continue;
                }

                String[] split = esTableDataCol.split("__");
                if (split.length != 2) {
                    continue;
                }

                String colName = split[0];
                String esColType = split[1];

                if ("long".equals(esColType)) {
                    Object o = recordData.get(colName);
                    if (o instanceof Date date) {
                        esRecordData.put(esTableDataCol, date.getTime());
                    } else {
                        esRecordData.put(esTableDataCol, o);
                    }
                } else {
                    esRecordData.put(esTableDataCol, recordData.get(colName));
                }
            }
        }

        return esRecordData;
    }

    public static Map<String, Object> getEsDocumentData(Document doc, String indexId) {
        if (doc == null) {
            return new HashMap<>();
        }

        Map<String, Object> esDocumentData = new HashMap<>();
        Map<String, Object> docMap = new HashMap<>();
        docMap.put("创建人用户名", doc.getCreator());
        docMap.put("创建人姓名", doc.getCreatorFullname());
        docMap.put("创建时间", doc.getCreationDate());
        docMap.put("所属人用户名", doc.getCreator());
        docMap.put("所属人姓名", doc.getCreatorFullname());
        docMap.put("删除人用户名", doc.getDeletedBy());
        docMap.put("删除人姓名", doc.getDeletedByFullname());
        docMap.put("删除时间", doc.getDeleteDate());
        docMap.put("销毁人用户名", "");
        docMap.put("销毁人姓名", "");
        docMap.put("销毁时间", "");
        docMap.put("目录ID", doc.getFolderId());
        docMap.put("DOCFILENAME", doc.getFilename());
        docMap.put("DOCID", doc.getId());
        docMap.put("DOCEXT", doc.getExtension());
        docMap.put("DOCCONTENT", doc.getContent());
        docMap.put("ZD_TAGS", "");
        docMap.put("ID", doc.getId());

        putDefaultCol(esDocumentData, docMap, "", indexId, ESDataUtils.DATA_TYPE_DOC);

        return esDocumentData;
    }

    public static List<String> getEsTableDataCols(String tableName) {
        String recordEsDataCols = ZDRedisUtils.hgetString(RECORD_ES_DATA_COLS, tableName);
        if (StringUtils.isBlank(recordEsDataCols)) {
            DataStruct ds = dataStructService.getDataStructByName(tableName);
            if (ds != null) {
                List<String> results = new ArrayList<>();

                List<DataCol> columns = ds.getColumns();
                List<String> esRecordCols = getEsRecordCols();
                for (DataCol dataCol : columns) {
                    String name = dataCol.getName();
                    String colType = dataCol.getColType();

                    String esColType = getEsColType(colType);
                    String esColName = getEsColName(name, esColType);
                    if (!esRecordCols.contains(esColName)) {
                        continue;
                    }

                    if (results.contains(esColName)) {
                        continue;
                    }

                    results.add(esColName);
                }

                JSONUtils.toJSONString(results, "[]");
                ZDRedisUtils.hset(RECORD_ES_DATA_COLS, tableName, recordEsDataCols);

                return results;
            }
        }

        return JSONUtils.parseObject(recordEsDataCols, List.class);
    }

    public static Long getMapDateValue(Map<String, Object> map, String key) {
        Object o = map.get(key);
        if (o == null) {
            return null;
        }

        if (o instanceof Date) {
            Date date = (Date) o;
            return date.getTime();
        }

        return null;
    }

    private static List<Map<String, Object>> getDefaultColMaps() {

        List<Map<String, Object>> defaultColMaps = new ArrayList<>();

        defaultColMaps.add(getDefaultColMap("ID", "text"));
        defaultColMaps.add(getDefaultColMap("创建人用户名", "keyword"));
        defaultColMaps.add(getDefaultColMap("创建人姓名", "text"));
        defaultColMaps.add(getDefaultColMap("创建时间", "date"));
        defaultColMaps.add(getDefaultColMap("所属人用户名", "keyword"));
        defaultColMaps.add(getDefaultColMap("所属人姓名", "text"));
        defaultColMaps.add(getDefaultColMap("删除人用户名", "keyword"));
        defaultColMaps.add(getDefaultColMap("删除人姓名", "text"));
        defaultColMaps.add(getDefaultColMap("删除时间", "date"));
        defaultColMaps.add(getDefaultColMap("销毁人用户名", "keyword"));
        defaultColMaps.add(getDefaultColMap("销毁人姓名", "text"));
        defaultColMaps.add(getDefaultColMap("销毁时间", "date"));
        defaultColMaps.add(getDefaultColMap("tableName", "keyword"));
        defaultColMaps.add(getDefaultColMap("目录ID", "long"));
        defaultColMaps.add(getDefaultColMap("esId", "keyword"));
        defaultColMaps.add(getDefaultColMap("DATA_TYPE", "keyword"));
        defaultColMaps.add(getDefaultColMap("DOCFILENAME", "text"));
        defaultColMaps.add(getDefaultColMap("DOCID", "keyword"));
        defaultColMaps.add(getDefaultColMap("DOCEXT", "keyword"));
        defaultColMaps.add(getDefaultColMap("DOCCONTENT", "text"));

        defaultColMaps.add(getDefaultColMap("ZD_TAGS", "keyword"));
        defaultColMaps.add(getDefaultColMap("ZD_TAGS_COUNT", "integer"));

        defaultColMaps.add(getDefaultColMap("题名", "text"));
        defaultColMaps.add(getDefaultColMap("档号", "text"));
        defaultColMaps.add(getDefaultColMap("案卷号", "text"));
        defaultColMaps.add(getDefaultColMap("保管期限", "text"));

        defaultColMaps.add(getDefaultColMap("主数据ID", "keyword"));
        defaultColMaps.add(getDefaultColMap("主数据表", "keyword"));

        return defaultColMaps;
    }

    public static boolean isIkCols(String fieldName) {
        if (StringUtils.isBlank(fieldName)) {
            return false;
        }

        return getEsIKCols().contains(fieldName);
    }

    public static List<String> getEsIKCols() {
        if (ES_IK_COLS == null) {
            synchronized (ESDataUtils.class) {
                if (ES_IK_COLS == null) {
                    ES_IK_COLS = new ArrayList<>();

                    ES_IK_COLS.add("DOCCONTENT");
                }
            }
        }

        return ES_IK_COLS;
    }

    private static Map<String, Object> getDefaultColMap(String colName, String type) {
        return ZDMapTool.getInstance().put("colName", colName).put("type", type).getMap();
    }

    public static void putDefaultCol(Map<String, Object> esRecordData, Map<String, Object> recordData, String tableName, String indexId, String dataType) {
        if (esRecordData == null || recordData == null) {
            return;
        }

        List<Map<String, Object>> defaultColMaps = getDefaultColMaps();
        for (Map<String, Object> defaultColMap : defaultColMaps) {
            String colName = MapUtils.getString(defaultColMap, "colName", "");
            String type = MapUtils.getString(defaultColMap, "type", "");
            if (StringUtils.isBlank(colName) || StringUtils.isBlank(type)) {
                continue;
            }

            if ("esId".equals(colName)) {
                esRecordData.put("esId", indexId);
            } else if ("tableName".equals(colName)) {
                esRecordData.put("tableName", tableName);
            } else if ("DATA_TYPE".equals(colName)) {
                esRecordData.put("DATA_TYPE", dataType);
            } else if ("ZD_TAGS".equals(colName)) {
                String zdTags = MapUtils.getString(recordData, "ZD_TAGS", "[]");
                List list = JSONUtils.parseObject(zdTags, List.class, new ArrayList());
                esRecordData.put("ZD_TAGS", list);
                esRecordData.put("ZD_TAGS_COUNT", list.size());
            } else if ("ZD_TAGS_COUNT".equals(colName)) {

            } else if (ESDataUtils.DATA_TYPE_REC.equals(dataType) && "DOCCONTENT".equals(colName)) {
                String docId = MapUtils.getString(recordData, "DOCID", "");
                Document document = documentService.getDocumentById(docId);
                String docContent = "";
                if (document != null) {
                    docContent = document.getContent();
                }

                esRecordData.put("DOCCONTENT", docContent);
            } else if ("date".equals(type)) {
                esRecordData.put(colName, getMapDateValue(recordData, colName));
            } else {
                esRecordData.put(colName, recordData.get(colName));
            }
        }
    }

    public static List<String> getDefaultEsTableDataCol() {
        List<String> results = new ArrayList<>();

        List<Map<String, Object>> defaultColMaps = getDefaultColMaps();
        for (Map<String, Object> defaultColMap : defaultColMaps) {
            String colName = MapUtils.getString(defaultColMap, "colName", "");
            if (StringUtils.isBlank(colName)) {
                continue;
            }

            results.add(colName);
        }

        return results;
    }

    public static void putDefaultRecordMappings() {
        Map<String, Object> mappingMap = new HashMap<>();

        Map<String, Object> propertiesMap = new HashMap<>();
        mappingMap.put("properties", propertiesMap);

        List<Map<String, Object>> defaultColMaps = getDefaultColMaps();
        for (Map<String, Object> defaultColMap : defaultColMaps) {
            String colName = MapUtils.getString(defaultColMap, "colName", "");
            String type = MapUtils.getString(defaultColMap, "type", "");
            if (StringUtils.isBlank(colName) || StringUtils.isBlank(type)) {
                continue;
            }

            addFieldMapping(propertiesMap, colName, type);
        }

        ESUtils.putMapping(ESUtils.RECORD_ES_INDEX, mappingMap);
    }

    public static void putDefaultSearchParams(Map<String, Object> searchParams, String keywords) {
//        searchParams.put("创建人用户名", keywords);
//        searchParams.put("创建人姓名", keywords);
//        searchParams.put("所属人用户名", keywords);
//        searchParams.put("所属人姓名", keywords);

        searchParams.put("DOCFILENAME", keywords);
        searchParams.put("DOCCONTENT", keywords);

        searchParams.put("题名", keywords);
        searchParams.put("档号", keywords);
        searchParams.put("案卷号", keywords);
    }

    public static Map<String, Object> getSearchParams(String keywords, int pageNumber, int pageSize) {
        Map<String, Object> searchParams = new HashMap<>();

        List<String> esRecordCols = getEsRecordCols();
        for (String esRecordCol : esRecordCols) {
            if (esRecordCol.endsWith("__text")) {
                searchParams.put(esRecordCol, keywords);
            }
        }

        putDefaultSearchParams(searchParams, keywords);

        return searchParams;
    }

    public static Map<String, Object> getRecordData(Map<String, Object> esRecordData) {
        Map<String, Object> recordData = new HashMap<>();
        if (esRecordData == null) {
            return recordData;
        }

        Set<String> keys = esRecordData.keySet();
        for (String key : keys) {
            if (StringUtils.isBlank(key)) {
                continue;
            }

            String finalKey = key;
            Object value = esRecordData.get(key);

            if (key.endsWith("__text")) {
                finalKey = key.replace("__text", "");
            } else if (key.endsWith("__long")) {
                finalKey = key.replace("__long", "");
                value = getDateValue(value);
            } else if ("创建时间".equals(key) || "删除时间".equals(key) || "销毁时间".equals(key)) {
                value = getDateValue(value);
            }

            recordData.put(finalKey, value);
        }

        return recordData;
    }

    public static Map<String, Object> getRecordDataWithDisplayColName(Map<String, Object> recordData, String tableName) {
        if (StringUtils.isBlank(tableName)) {
            tableName = MapUtils.getString(recordData, "tableName", "");
        }

        if (StringUtils.isBlank(tableName)) {
            return recordData;
        }

        DataStruct dataStructByName = dataStructService.getDataStructByName(tableName);
        if (dataStructByName == null) {
            return recordData;
        }

        List<DataCol> dataCols = dataStructService.getDataCols(dataStructByName.getId());
        Map<String, String> colNameAndDisplayName = dataCols.stream().collect(Collectors.toMap(DataCol::getName, DataCol::getDisplayName));
        colNameAndDisplayName.putAll(DataStructUtils.DEFAULT_COL_NAME_DISPLAY_NAME);

        Map<String, Object> resultsRecordData = new HashMap<>();
        for (Map.Entry<String, Object> entry : recordData.entrySet()) {
            String key = entry.getKey();
            String finalKey = MapUtils.getString(colNameAndDisplayName, key, key);
            resultsRecordData.put(finalKey, entry.getValue());
        }

        return resultsRecordData;
    }

    public static Map<String, Object> getDisplayRecordData(Map<String, Object> esRecordData) {
        esRecordData.remove("DOCCONTENT");

        Map<String, Object> recordData = getRecordData(esRecordData);

        String tableName = MapUtils.getString(recordData, "tableName", "");
        DataStruct dataStructByName = dataStructService.getDataStructByName(tableName);
        if (dataStructByName == null) {
            return recordData;
        }
        String displayTableName = dataStructByName.getDisplayName();
        recordData.put("displayTableName", displayTableName);

        Map<String, String> mainColNameAndValue = RecordDataUtils.getMainColNameAndValue(dataStructByName, recordData);
        recordData.putAll(mainColNameAndValue);

        long recFolderId = MapUtils.getLongValue(recordData, "目录ID", 0L);
        if (recFolderId > 0) {
            RecFolder recFolder = recFolderService.getById(recFolderId);
            if (recFolder != null) {
                String displayFolder = recFolder.getAreaDisplayFolder();
                recordData.put("displayFolder", displayFolder);

                String myPermissions = recFolder.getMyPermissionsWithInit();
                recordData.put("myPermissions", myPermissions);

                Boolean attachType = recFolder.getAttachType();
                recordData.put("recFolderAttachType", attachType);

                long mainRecId = MapUtils.getLongValue(recordData, "主数据ID", 0L);
                String mainTableName = MapUtils.getString(recordData, "主数据表", "");
                Map<String, Object> mainRecordData = RecordDBUtils.getRecordByTableName(mainRecId, null, mainTableName);
                if (MapUtils.isNotEmpty(mainRecordData)) {
                    DataStruct mainDs = dataStructService.getDataStructByName(mainTableName);
                    Map<String, String> mainRecMainColNameAndValue = RecordDataUtils.getMainColNameAndValue(mainDs, mainRecordData);
                    mainRecordData.putAll(mainRecMainColNameAndValue);

                    long mainRecFolderId = MapUtils.getLongValue(mainRecordData, "目录ID", 0L);
                    /*RecFolder mainRecFolder = recFolderService.getById(mainRecFolderId);
                    if (mainRecFolder != null) {
                        mainRecordData.put("myPermissions", mainRecFolder.getMyPermissionsWithInit());
                    }*/

                    mainRecordData.put("myPermissions", RecFolderPermissionUtils.getRecFolderPermissionsWithRedis(mainRecFolderId, UserContextHolder.getUser()));

                    recordData.put("mainRecordData", mainRecordData);
                }
            }
        }

        return recordData;
    }

    public static Map<String, Object> getDisplayDocumentData(Map<String, Object> esDocumentData) {
        Map<String, Object> docData = getRecordData(esDocumentData);

        docData.put("mainColName", "DOCFILENAME");
        docData.put("mainColValue", MapUtils.getString(docData, "DOCFILENAME", ""));
        docData.put("mainColDisplayName", "文件名");

        long folderId = MapUtils.getLongValue(docData, "目录ID", 0L);
        if (folderId > 0) {
            Folder folder = folderService.getById(folderId);
            if (folder != null) {
                String displayFolder = folder.getAreaDisplayFolder();
                docData.put("displayFolder", displayFolder);

                String myPermissions = folder.getMyPermissionsWithInit();
                docData.put("myPermissions", myPermissions);
            }
        }

        return docData;
    }

    public static void replaceDocumentKey(Map<String, Object> documentData) {
        if (documentData.containsKey("DOCCONTENT")) {
            documentData.put("文件内容", documentData.get("DOCCONTENT"));
            documentData.remove("DOCCONTENT");
        }

        if (documentData.containsKey("DOCFILENAME")) {
            documentData.put("文件名", documentData.get("DOCFILENAME"));
            documentData.remove("DOCFILENAME");
        }

        if (documentData.containsKey("DOCEXT")) {
            documentData.put("文件后缀", documentData.get("DOCEXT"));
            documentData.remove("DOCEXT");
        }
    }

    private static Object getDateValue(Object value) {
        if (value != null) {
            long longValue = NumberUtils.toLong(value + "", 0L);
            if (longValue > 1000000000000L) {
                value = new Date(longValue);
            }
        }

        return value;
    }
}
