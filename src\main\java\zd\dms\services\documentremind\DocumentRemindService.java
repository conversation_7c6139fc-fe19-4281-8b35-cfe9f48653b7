package zd.dms.services.documentremind;

import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentRemind;
import zd.dms.entities.User;

import java.util.List;

@Transactional
public interface DocumentRemindService extends BaseJpaService<DocumentRemind, Long> {

    void createRemind(DocumentRemind documentRemind);

    void deleteRemind(DocumentRemind documentRemind);

    void deleteRemindById(long id);

    void deleteRemindByDocument(Document document);

    void updateRemind(DocumentRemind documentRemind);

    void createRelativeRemind(Document doc, int relativeNumber, String relativeUnit, String comment,
                              String[] selectedUserGroups, User operator);

    void createRemind(Document doc, String date, String comment, String[] selectedUserGroups, User operator);

    void updateRelativeRemind(long id, int relativeNumber, String relativeUnit, String comment,
                              String[] selectedUserGroups, User operator);

    void updateRemind(long id, String date, String comment, String[] selectedUserGroups, User opertator);

    void subcribeRemind(long id, User operator);

    void remindRelatives();

    void remind();

    // @Transactional(readOnly = true)
    // DocumentRemind getRemindByDocument(Document document);

    @Transactional(readOnly = true)
    DocumentRemind getRemindById(long id);

    @Transactional(readOnly = true)
    List<DocumentRemind> getRemindsByDocument(Document document);

    @Transactional(readOnly = true)
    List<DocumentRemind> getAllRelativeReminds();

    @Transactional(readOnly = true)
    List<DocumentRemind> getAllReminds();

    @Transactional(readOnly = true)
    List<DocumentRemind> getRemindsByUser(User user);

    @Transactional(readOnly = true)
    List<DocumentRemind> getExpiredReminds();

    /**
     * 根据文件名，提醒日期搜索我的提醒文档
     *
     * @param currentUser
     * @return
     */
    List<DocumentRemind> searchMyReminds(User currentUser, String fileNames, String advRemindsStartDate,
                                         String advRemindsEndDate);
}
