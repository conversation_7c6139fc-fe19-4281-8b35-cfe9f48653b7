package zd.dms.entities;

import jakarta.persistence.*;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.base.utils.system.SpringUtils;
import zd.dms.services.user.UserService;

import java.util.Date;


/**
 * DocumentNote Domain Object
 *
 * <AUTHOR>
 * @version $Revision$, $Date$
 */
@Entity
@Table(name = "folder_subscribe")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class FolderSubscribe extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = -6996227996015083578L;

    @Index(name = "i_fsub_username")
    private String username;

    @Index(name = "i_fsub_fid")
    private Long folderId;

    /**
     * 文档id
     */
    @Index(name = "i_fsub_docid")
    private String docId;

    /**
     * 创建时间
     */
    @Index(name = "i_fsubscribe_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 默认构造器
     */
    public FolderSubscribe() {
        super();
        creationDate = new Date();
    }

    public User getUser() {
        return SpringUtils.getBean(UserService.class).getUserByUsername(username);
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Long getFolderId() {
        return folderId;
    }

    public void setFolderId(Long folderId) {
        this.folderId = folderId;
    }

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
