package zd.base.config.oauth2;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.lang.NonNull;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import zd.base.utils.JSONResultUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.Map;

/**
 * 自定义OAuth2令牌端点过滤器
 * 用于处理令牌端点的异常
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomOAuth2TokenEndpointFilter extends OncePerRequestFilter {

    private final ObjectMapper objectMapper;

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull FilterChain filterChain)
            throws ServletException, IOException {
        // 记录请求信息
//        logRequest(request);

        try {
            filterChain.doFilter(request, response);
        } catch (OAuth2AuthenticationException ex) {
            handleOAuth2Exception(response, ex);
        } catch (AuthenticationException ex) {
            handleAuthenticationException(response, ex);
        } catch (Exception ex) {
            handleGenericException(response, ex);
        }
    }

    /**
     * 记录请求信息
     */
    private void logRequest(HttpServletRequest request) {
        try {
            log.debug("=== OAuth2 Request ===");
            log.debug("Method: {} URI: {} Query String: {}", request.getMethod(), request.getRequestURI(), request.getQueryString());

            // 记录请求头
            log.debug("=== Headers ===");
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String name = headerNames.nextElement();
                // 敏感信息可能需要脱敏处理
                if ("authorization".equalsIgnoreCase(name)) {
                    log.debug("{}: {}", name, maskAuthorizationHeader(request.getHeader(name)));
                } else {
                    log.debug("{}: {}", name, request.getHeader(name));
                }
            }

            // 记录请求参数
            log.debug("=== Parameters ===");
            Map<String, String[]> parameterMap = request.getParameterMap();
            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                for (String value : entry.getValue()) {
                    // 敏感信息可能需要脱敏处理
                    if ("client_secret".equals(entry.getKey()) || "password".equals(entry.getKey())) {
                        log.debug("{}: {}", entry.getKey(), "******");
                    } else {
                        log.debug("{}: {}", entry.getKey(), value);
                    }
                }
            }

            // 记录请求体
            if ("POST".equals(request.getMethod()) && request.getContentType() != null &&
                    request.getContentType().contains("application/x-www-form-urlencoded")) {
                // 对于表单请求，参数已经在上面记录了，这里不需要重复记录
                log.debug("=== Body ===");
                log.debug("Body content is form data, see Parameters section above");
            } else if ("POST".equals(request.getMethod()) && request.getContentType() != null &&
                    request.getContentType().contains("application/json")) {
                // 对于JSON请求，需要读取请求体
                log.debug("=== Body ===");
                String body = getRequestBody(request);
                log.debug(body);
            }
        } catch (Exception e) {
            log.error("记录请求信息失败", e);
        }
    }

    /**
     * 获取请求体内容
     */
    private String getRequestBody(HttpServletRequest request) {
        try {
            // 保存请求体内容
            String body = "";
            if (request.getInputStream() != null) {
                body = StreamUtils.copyToString(request.getInputStream(), StandardCharsets.UTF_8);
            }
            return body;
        } catch (IOException e) {
            log.error("读取请求体失败", e);
            return "";
        }
    }

    /**
     * 对授权头部进行脱敏处理
     */
    private String maskAuthorizationHeader(String authHeader) {
        if (authHeader == null) {
            return null;
        }

        if (authHeader.startsWith("Basic ")) {
            return "Basic ******";
        } else if (authHeader.startsWith("Bearer ")) {
            return "Bearer ******";
        } else {
            return "******";
        }
    }

    /**
     * 处理OAuth2认证异常
     */
    private void handleOAuth2Exception(HttpServletResponse response, OAuth2AuthenticationException ex) throws IOException {
        log.error("OAuth2令牌端点异常: {}", ex.getMessage(), ex);

        OAuth2Error error = ex.getError();
        JSONResultUtils<Object> objectJSONResultUtils = JSONResultUtils.errorWithMsg(error.getErrorCode(), error.getDescription() != null ? error.getDescription() : "OAuth2认证失败");

        response.setStatus(HttpStatus.BAD_REQUEST.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        objectMapper.writeValue(response.getWriter(), objectJSONResultUtils);
    }

    /**
     * 处理认证异常
     */
    private void handleAuthenticationException(HttpServletResponse response, AuthenticationException ex) throws IOException {
        log.error("认证异常: {}", ex.getMessage(), ex);

        JSONResultUtils<Object> authenticationError = JSONResultUtils.errorWithMsg("authentication_error", ex.getMessage() != null ? ex.getMessage() : "认证失败");

        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        objectMapper.writeValue(response.getWriter(), authenticationError);
    }

    /**
     * 处理通用异常
     */
    private void handleGenericException(HttpServletResponse response, Exception ex) throws IOException {
        log.error("令牌端点通用异常: {}", ex.getMessage(), ex);

        JSONResultUtils<Object> objectJSONResultUtils = JSONResultUtils.errorWithMsg("server_error", "服务器内部错误");

        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        objectMapper.writeValue(response.getWriter(), objectJSONResultUtils);
    }

    @Override
    protected boolean shouldNotFilter(@NonNull HttpServletRequest request) {
        return !request.getRequestURI().equals("/oauth2/token");
    }
}
