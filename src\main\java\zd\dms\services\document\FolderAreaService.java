//                    佛祖保佑       永无BUG
//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                           O\  =  /O
//                        ____/`---'\____
//                      .'  \\|     |//  `.
//                     /  \\|||  :  |||//  \
//                    /  _||||| -:- |||||-  \
//                    |   | \\\  -  /// |   |
//                    | \_|  ''\---/''  |   |
//                    \  .-\__  `-`  ___/-. /
//                  ___`. .'  /--.--\  `. . __
//               ."" '<  `.___\_<|>_/___.'  >'"".
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |
//              \  \ `-.   \_ __\ /__ _/   .-` /  /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
package zd.dms.services.document;

import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import zd.base.service.BaseJpaService;
import zd.base.utils.JSONResultUtils;
import zd.dms.entities.FolderArea;

import java.util.List;
import java.util.Map;

@Transactional
public interface FolderAreaService extends BaseJpaService<FolderArea, Long> {

    void createDefaultArea();

    @Transactional(readOnly = true)
    List<FolderArea> getDefaultAreas();

    @Transactional(readOnly = true)
    List<FolderArea> getAllFolderAreas();

    @Transactional(readOnly = true)
    Page getAllFolderAreasPage(int pageNumber, int pageSize);

    void saveFolderArea(FolderArea folderArea);

    @Transactional(readOnly = true)
    FolderArea getFolderAreaById(Long id);

    void deleteFolderAreaById(FolderArea folderArea);

    void updateFolderArea(FolderArea folderArea);

    List<FolderArea> getFolderAreasByTypeAndIsEnabled(String areaType, Boolean enabled, boolean asc);

    List<FolderArea> getFolderAreasByTypeAndIsEnabledAndIsoArea(String areaType, boolean enabled, boolean isoArea);

    @Transactional(readOnly = true)
    FolderArea getFolderAreaByTreeName(String treeName);

    JSONResultUtils<Object> createFolderArea(@RequestBody Map<String, Object> params);

    JSONResultUtils<Object> updateFolderArea(Map<String, Object> params);
}
