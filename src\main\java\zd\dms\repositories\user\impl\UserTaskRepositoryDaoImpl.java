package zd.dms.repositories.user.impl;

import jakarta.persistence.EntityManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.UserTask;
import zd.dms.repositories.user.UserTaskRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.Date;

public class UserTaskRepositoryDaoImpl extends BaseRepositoryDaoImpl<UserTask, String> implements UserTaskRepository {

    public UserTaskRepositoryDaoImpl(Class<UserTask> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public Page getUserTasks(String username, String type, String status, int pageNumber, int pageSize) {
        Specification<UserTask> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<UserTask> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("username", username);
            if (StringUtils.isNotBlank(type)) {
                specTools.eq("type", type);
            }

            if (StringUtils.isNotBlank(status)) {
                specTools.eq("status", status);
            }

            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }

    @Override
    public Page getUserTasksBeforeDate(Date beforeDate, int pageNumber, int pageSize) {
        Specification<UserTask> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<UserTask> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.lt("creationDate", beforeDate);
            specTools.asc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }

}
