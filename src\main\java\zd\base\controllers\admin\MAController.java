package zd.base.controllers.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.analysus.ja.dict.TaskDictionary;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import zd.base.annotation.ZDLog;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.system.PropsUtils;

import java.util.HashMap;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "MAController", description = "加密Controller")
@RequestMapping("/admin/ma")
public class MAController {


    @Operation(summary = "获取硬件码")
    @RequestMapping(value = "/hardId", method = {RequestMethod.GET, RequestMethod.POST})
    @ZDLog("获取硬件码")
    public JSONResultUtils<Map<String, Object>> hardId() {
        String hardId = TaskDictionary.getMainDefinition().eci();

        Map<String, Object> results = new HashMap<>();
        results.put("hardId", hardId);
        return JSONResultUtils.successWithData(results);
    }
}
