package zd.base.tasks;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zd.dms.io.ZDIOUtils;
import zd.dms.services.document.DocumentLendingService;
import zd.record.service.record.RecLendingService;

@Component
@Slf4j
public class TenMinuteTask extends AbstractTask {

    @Autowired
    private DocumentLendingService documentLendingService;

    @Autowired
    private RecLendingService recLendingService;

    /*@Autowired
    private TaskService taskService;*/

    @Scheduled(cron = "0 */10 * * * ?")
    @Scheduled(initialDelay = 60000)
    @Override
    public void work() {
        /*taskService.remindHourTask();
        taskService.handleTimeTask();*/
        documentLendingService.deleteExpiredLendings();
        recLendingService.returnExpiredElectricLendings();
        // 实体数据到期后自动催还
        recLendingService.remindLendPaper();
        // 数据即将到期进行提醒
        recLendingService.remainingTime();
        ZDIOUtils.deleteTempDirFiles();
    }
}
