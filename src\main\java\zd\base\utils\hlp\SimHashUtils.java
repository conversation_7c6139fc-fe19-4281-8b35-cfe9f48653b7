package zd.base.utils.hlp;

import java.util.*;

public class SimHashUtils {

    public static void test() {
        // 准备测试标题内容数据
        List<String> titleList = new ArrayList<>();
        titleList.add("有哪些养猫必须知道的冷知识");
        titleList.add("有哪些养猫必须知道的冷");
        titleList.add("有哪些养猫必须知道");
        titleList.add("有哪些养猫");
        titleList.add("有哪些");
        titleList.add("猫是一种哺乳动物");
        titleList.add("养狗的冷知识");
        titleList.add("你知道养狗的冷知识!");
        titleList.add("我喜欢吃饭。");
        titleList.add("在Elasticsearch中，你使用match来查询与指定句子相似的文档。Elasticsearch使用了内置的相似度评估器，评估相似度，比如standard或BM25。");
        titleList.add("在Elasticsearch中，你可以使用match查询来查找与指定句子相似的文档。为了评估相似度，Elasticsearch使用了内置的相似度评估器，比如standard或BM25。");
        titleList.add("如果你需要更复杂的相似度计算，例如使用其他相似度评估器或者调整查询时的相似度阈值，你可以使用match查询搭配operator参数设置为and（默认是or）来要求所有词条都要匹配，或者使用minimum_should_match来控制需要匹配的词条数量。");
        titleList.add("今年8月, 中国第四批预备航天员已于入队参加训练，不仅要执行空间站任务，未来也将执行载人登月任务。针对执行空间站任务和未来执行载人登月任务的新特点，既注重失重状态下生活工作与健康维护等基本技能，以及掌握出舱活动、设备维护维修、空间科学实/试验等技能，更面向未来载人登月任务，进一步培塑航天员从操控飞行器到驾驶月球车、从天体辨识到地质科考、从太空失重漂浮到月面负重行走的能力。");
        titleList.add("无线路由器的用处是什么");
        titleList.add("无线路由器怎么无线上网");
        titleList.add("无线上网卡与无线路由器使用方法");
        titleList.add("无线上网卡与无线路由器怎么用");
        titleList.add("无线上网卡和无线路由器怎么用");

        // 原始标题内容数据
        //String originalTitle = "在Elasticsearch中，你可以使用match查询来查找与指定句子相似的文档。为了评估相似度，Elasticsearch使用了内置的相似度评估器，比如standard或BM25。";
         String originalTitle = "我国第四批预备航天员已于今年8月入队参加训练，不仅要执行空间站任务，未来也将执行载人登月任务。针对执行空间站任务和未来执行载人登月任务的新特点，在训练内容设置上，既注重失重状态下生活工作与健康维护等基本技能，以及掌握出舱活动、设备维护维修、空间科学实/试验等技能，更面向未来载人登月任务，进一步培塑航天员从操控飞行器到驾驶月球车、从天体辨识到地质科考、从太空失重漂浮到月面负重行走的能力。";
//        String originalTitle = "无线上网卡和无线路由器怎么用";

        Map<String, Double> simHashMap = new HashMap<>(16, 0.75F);

        System.out.println("======================================");
        long startTime = System.currentTimeMillis();
        System.out.println("原始标题：" + originalTitle);

        // 计算相似度
        titleList.forEach(title -> {
            SimHashTool mySimHash_1 = new SimHashTool(title, 32);
            SimHashTool mySimHash_2 = new SimHashTool(originalTitle, 32);

            Double similar = mySimHash_1.getSimilar(mySimHash_2);

            simHashMap.put(title, similar);
        });

        // 打印测试结果到控制台
        /* simHashMap.forEach((title, similarity) -> {
            System.out.println("标题："+title+"-----------相似度："+similarity);
        });*/

        // 按相标题内容排序输出控制台
        Set<String> titleSet = simHashMap.keySet();
        Object[] titleArrays = titleSet.toArray();
        Arrays.sort(titleArrays, Collections.reverseOrder());

        System.out.println("-------------------------------------");
        for (Object title : titleArrays) {
            System.out.println("标题：" + title + "-----------相似度：" + simHashMap.get(title));
        }

        // 求得运算时长（单位：毫秒）
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        System.out.println("\n本次运算总耗时" + totalTime + "毫秒");

        System.out.println("======================================");
    }
}
