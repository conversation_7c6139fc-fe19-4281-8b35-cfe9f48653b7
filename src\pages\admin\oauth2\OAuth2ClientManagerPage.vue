<script setup lang="ts">
import {useQuasar} from 'quasar';
import {onActivated, onUnmounted, ref} from 'vue';
import {OAUTH2_CLIENT_MANAGER_COLUMNS} from 'config/columns';
import {oauth2ClientApis} from 'utils/api/admin/oauth2/oauth2-client-apis';
import {ZDTabUtils} from 'utils/layout/ZDTabUtils';
import OAuth2ClientDialogAdd from '../../../components/admin/oauth2/OAuth2ClientDialogAdd.vue';
import OAuth2ClientDialogEdit from '../../../components/admin/oauth2/OAuth2ClientDialogEdit.vue';
import RecListColumnValue from '../../../components/record/RecListColumnValue.vue';
import ZdPage from '../../../components/common/ZdPage.vue';


const $q = useQuasar();

const oAuth2Clients = ref([]);
const zdPage = ref();

const selectRecords = ref([]);

const getOAuth2ClientPage = async () => {
  const params = {
    pageNumber: zdPage.value?.getPageNumber(),
    pageSize: zdPage.value?.getPageSize(),
  }
  let data;
  try {
    data = await oauth2ClientApis.list(params);
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: error.message,
    });
    return;
  }
  oAuth2Clients.value = data.results;
  zdPage.value?.setTotal(data.totalCount);
}

const oAuth2ClientVisibilitychange = () => {
  if (document.visibilityState === 'visible') {
    getOAuth2ClientPage();
  }
}

const handleSelectionChange = (rows: []) => {
  selectRecords.value = rows;
}

const batchDelete = () => {
  $q.dialog({
    title: '删除OAuth2',
    message: '确定要删除指定的OAuth2吗？',
    cancel: true,
  }).onOk(async () => {
    const params = {
      ids: selectRecords.value.map((item: any) => item.id),
    };

    await oauth2ClientApis.batchDelete(params);

    getOAuth2ClientPage();
  });
}

const saveOAuth2Client = () => {
  $q.dialog({
    component: OAuth2ClientDialogAdd,
    componentProps: {
      
    },
  }).onOk(() => {
    $q.notify({
      type: 'positive',
      message: '新增OAuth2成功',
    });

    getOAuth2ClientPage();
  });
}

const editOAuth2Client = (row: any) => {
  $q.dialog({
    component: OAuth2ClientDialogEdit,
    componentProps: {
      id: row.id,
    },
  }).onOk(() => {
    $q.notify({
      type: 'positive',
      message: '更新OAuth2成功',
    });

    getOAuth2ClientPage();
  });
}

onActivated(() => {
  // keepalive组件关闭时防止刷新页面
  const activeTab = ZDTabUtils.getActiveTab();
  if (activeTab && activeTab.to === '/admin/oauth2Client') {
    getOAuth2ClientPage();
    setTimeout(() => {
      document.addEventListener('visibilitychange', oAuth2ClientVisibilitychange);
    }, 500)
  }
})

onUnmounted(() => {
  document.removeEventListener('visibilitychange', oAuth2ClientVisibilitychange);
})
</script>

<template>
  <div class="q-pa-sm full-height">
    <div class="row q-gutter-x-sm">
      <q-btn
        @click="saveOAuth2Client"
        label="新增"
        unelevated
        class="text-main-btn-theme"
        color="main-btn-theme"
      />

      <q-btn
        @click="batchDelete"
        label="删除"
        unelevated
        class="text-main-btn-theme"
        color="main-btn-theme"
      />
    </div>

    <div>
      <el-table
        ref="recordsTableRef"
        row-key="ID"
        class="ng-sticky-header-column-table q-mt-md"
        style="width: 100%; min-height: calc(100vh - 275px)"
        @selection-change="handleSelectionChange"
        highlight-current-row
        :data="oAuth2Clients"
        flat
      >
        <el-table-column
          fixed="left"
          type="selection"
          width="55">
        </el-table-column>
        <template :key="col.name" v-for="col in OAUTH2_CLIENT_MANAGER_COLUMNS">
          <el-table-column
            :prop="col.name"
            :label="col.label"
            align="left"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span v-if="col.name === 'enabled'">
                {{ scope.row.enabled ? '启用' : '禁用' }}
              </span>
              <span v-else>
                {{ scope.row[col.name] }}
              </span>
            </template>
          </el-table-column>
        </template>
        <el-table-column
          fixed="right"
          label="操作"
          width="200px"
        >

          <template #default="scope">
            <div class="flex justify-start items-center q-gutter-sm">
              <q-btn @click="editOAuth2Client(scope.row)" label="编辑" color="ng-theme" flat dense/>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="block q-ma-sm">
      <zd-page
        ref="zdPage"
        @load-data="getOAuth2ClientPage"
      />
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
