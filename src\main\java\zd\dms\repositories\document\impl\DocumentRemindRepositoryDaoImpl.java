package zd.dms.repositories.document.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.orm.hibernate5.HibernateCallback;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentRemind;
import zd.dms.entities.User;
import zd.dms.repositories.document.DocumentRemindRepository;
import zd.dms.utils.DocDBUtils;
import zd.dms.utils.repositories.PredicateUtils;
import zd.dms.utils.repositories.SpecTools;

import java.util.Date;
import java.util.List;

@Slf4j
public class DocumentRemindRepositoryDaoImpl extends BaseRepositoryDaoImpl<DocumentRemind, Long> implements DocumentRemindRepository {

    public DocumentRemindRepositoryDaoImpl(Class<DocumentRemind> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<DocumentRemind> getAllRelativeReminds() {
        Specification<DocumentRemind> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentRemind> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.isNull("remindDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void deleteByDocument(Document document) {
        String hql = "delete from DocumentRemind where docId = ?1";
        executeUpdate(hql, document.getId());
    }

    @Override
    public List<DocumentRemind> getAllReminds() {
        Specification<DocumentRemind> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentRemind> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.asc("remindDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<DocumentRemind> getRemindsByUser(User user) {
        Specification<DocumentRemind> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentRemind> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            Predicate likeC = PredicateUtils.like(root, criteriaBuilder, "userIds", "%**" + user.getId() + "**%");
            Predicate eqCreator = PredicateUtils.equal(root, criteriaBuilder, "creator", user.getUsername());
            specTools.or(likeC, eqCreator);
            specTools.asc("remindDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public DocumentRemind getRemindByDocument(Document document) {
        Specification<DocumentRemind> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentRemind> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("docId", document.getId());

            return specTools.getRestriction();
        };

        return findSingleObject(spec);
    }

    @Override
    public List<DocumentRemind> getRemindsByDocument(Document document) {
        Specification<DocumentRemind> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentRemind> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("docId", document.getId());

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void deleteById(long id) {
        String hql = "delete from DocumentRemind where id = ?1";
        executeUpdate(hql, id);
    }

    @Override
    public List<DocumentRemind> getExpiredReminds() {
        Specification<DocumentRemind> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentRemind> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.isNotNull("remindDate");
            specTools.le("remindDate", new Date());

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<DocumentRemind> searchMyReminds(User currentUser, String fileNames, String advRemindsStartDate, String advRemindsEndDate) {
        String whereClause = " 1=1";
        if (StringUtils.isNotBlank(fileNames)) {
            whereClause += " and t1.filename like '%" + fileNames + "%'";
        }

        if (StringUtils.isNotBlank(advRemindsStartDate)) {
            whereClause += " and t1.reminddate >= " + DocDBUtils.parseDateSQL(advRemindsStartDate + " 00:00:00");
        }

        if (StringUtils.isNotBlank(advRemindsEndDate)) {
            whereClause += " and t1.reminddate <= " + DocDBUtils.parseDateSQL(advRemindsEndDate + " 23:59:59");
        }

        final String sql = "select t3.* from(" +
                "select t1.* from(select dr.docId as docId,td.filename as filename,dr.reminddate as reminddate from(" +
                "select * from document_remind where creator = '" + currentUser.getUsername() +
                "' or userids like '%**" + currentUser.getId() +
                "**%')dr left join tdms_doc td on dr.docId = td.id)t1 " + "where " + whereClause + ")t2 " +
                "left join(select * from document_remind where creator = '" + currentUser.getUsername() +
                "' or userids like '%**" + currentUser.getId() +
                "**%')t3 on t2.docId = t3.docId order by t3.reminddate asc";
        log.debug("sql :{}", sql);

        Query nativeQuery = this.entityManager.createNativeQuery(sql, DocumentRemind.class);
        return nativeQuery.getResultList();
    }
}
