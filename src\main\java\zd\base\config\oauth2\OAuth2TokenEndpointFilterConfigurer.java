package zd.base.config.oauth2;

import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;

/**
 * OAuth2令牌端点过滤器配置器
 * 用于将自定义令牌端点过滤器添加到过滤器链中
 */
public class OAuth2TokenEndpointFilterConfigurer extends AbstractHttpConfigurer<OAuth2TokenEndpointFilterConfigurer, HttpSecurity> {

    private final CustomOAuth2TokenEndpointFilter filter;

    public OAuth2TokenEndpointFilterConfigurer(CustomOAuth2TokenEndpointFilter filter) {
        this.filter = filter;
    }

    @Override
    public void configure(HttpSecurity http) throws Exception {
        // 将过滤器添加到BasicAuthenticationFilter之前
        http.addFilterBefore(filter, BasicAuthenticationFilter.class);
    }
}
