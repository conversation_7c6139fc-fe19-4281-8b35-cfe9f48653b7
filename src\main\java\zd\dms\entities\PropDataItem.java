package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.dms.utils.TextUtils;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "propdataitem")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class PropDataItem extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = 8720941523775931508L;

    @Index(name = "i_pdi_keyname")
    private String keyName;

    @Column(length = Length.LOB_DEFAULT)
    private String keyValues;

    /**
     * 创建时间
     */
    @Index(name = "i_pdi_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 默认构造器
     */
    public PropDataItem() {
        super();
        creationDate = new Date();
    }

    public String getSummaryValues() {
        return StringUtils.abbreviate(keyValues, 20);
    }

    public List<String> getValueList() {
        List<String> result = new LinkedList<String>();

        if (StringUtils.isBlank(keyValues)) {
            return result;
        }

        String[] strArray = keyValues.split("\n");
        for (String s : strArray) {
            if (StringUtils.isNotBlank(s)) {
                result.add(s.trim());
            }
        }

        return result;
    }

    public String getKeyName() {
        return TextUtils.escapeXml(keyName);
    }

    public void setKeyName(String keyName) {
        this.keyName = keyName;
    }

    public String getKeyValues() {
        return TextUtils.escapeXml(keyValues);
    }

    public void setKeyValues(String keyValues) {
        this.keyValues = keyValues;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
