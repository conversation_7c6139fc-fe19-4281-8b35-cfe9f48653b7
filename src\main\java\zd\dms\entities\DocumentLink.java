package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Setter;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.base.utils.system.SpringUtils;
import zd.dms.services.document.DocumentService;
import zd.dms.services.user.UserService;

import java.util.Date;


/**
 * DocumentLink Domain Object
 *
 * <AUTHOR>
 * @version $Revision$, $Date$
 */
@Entity
@Table(name = "document_link")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class DocumentLink extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = -1769469886424508837L;

    public static final int TYPE_MY_COMMON = 1;

    public static final int TYPE_FOLDER_LINK = 2;

    public static final int TYPE_LENDING = 3;

    private int linkType;

    private String creatorFullname;

    @Index(name = "i_dlink_username")
    private String username;

    @Index(name = "i_dlink_fId")
    private Long folderId;

    @Index(name = "i_dlink_docId")
    private String docId;

    /**
     * 创建时间
     */
    @Index(name = "i_dlink_cd")
    @Column(nullable = false)
    private Date creationDate;

    @Transient
    @Setter
    private Document document;

    /**
     * 默认构造器
     */
    public DocumentLink() {
        super();
        creationDate = new Date();
    }

    public Document getDocument() {
        if (document == null) {
            SpringUtils.getBean(DocumentService.class).getDocumentById(docId);
        }

        return document;
    }

    public User getUser() {
        return SpringUtils.getBean(UserService.class).getUserByUsername(username);
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Long getFolderId() {
        return folderId;
    }

    public void setFolderId(Long folderId) {
        this.folderId = folderId;
    }

    public int getLinkType() {
        return linkType;
    }

    public void setLinkType(int linkType) {
        this.linkType = linkType;
    }

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getCreatorFullname() {
        return creatorFullname;
    }

    public void setCreatorFullname(String creatorFullname) {
        this.creatorFullname = creatorFullname;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
