package zd.dms.repositories.document.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentRelation;
import zd.dms.repositories.document.DocumentRelationRepository;
import zd.dms.utils.repositories.PredicateUtils;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;

public class DocumentRelationRepositoryDaoImpl extends BaseRepositoryDaoImpl<DocumentRelation, Long> implements DocumentRelationRepository {

    public DocumentRelationRepositoryDaoImpl(Class<DocumentRelation> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public void deleteDocumentRelation(Document document) {
        String hql = "delete from DocumentRelation where docId = ?1 or relatedDocId = ?2";
        executeUpdate(hql, document.getId(), document.getId());
    }

    @Override
    public void deleteRelationById(long id) {
        String hql = "delete from DocumentRelation where id = ?1";
        executeUpdate(hql, id);
    }

    @Override
    public void deleteRelation(Document doc, Document docRelated) {
        String hql = "delete from DocumentRelation where "
                + " (docId = ?1 and relatedDocId = ?2) or (docId = ?3 and relatedDocId = ?4)";
        executeUpdate(hql, doc.getId(), docRelated.getId(), docRelated.getId(), doc.getId());
    }

    @Override
    public List<DocumentRelation> getDocumentRelations(Document document) {
        Specification<DocumentRelation> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentRelation> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.or(PredicateUtils.equal(root, criteriaBuilder, "docId", document.getId()), PredicateUtils.equal(root, criteriaBuilder, "relatedDocId", document.getId()));

            return specTools.getRestriction();
        };

        return findAll(spec);
    }
}
