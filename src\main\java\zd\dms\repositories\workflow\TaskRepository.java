package zd.dms.repositories.workflow;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.User;
import zd.dms.workflow.entities.SSTask;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface TaskRepository extends BaseRepository<SSTask, String> {

    Page getForwardTasks(String username, String resourceType, String recPiType, int page, int pageSize);

    Page getAgentTasks(String username, String resourceType, int page, int pageSize);

    SSTask getUniqueUnCompleteTask(String piId, String nodeId, String username);

    List<SSTask> searchTask(String keywords, User operator, int status, String resType, Date startDate, Date endDate);

    int getPendingTaskCount(String piId, String nodeId);

    int getUnCompleteTaskCount(String username);

    List<Map<String, Object>> getUnCompleteTaskCountMap(String username);

    int getUnCompleteTaskCount(String username, String resourceType, String recPiType);

    List<SSTask> getTasks(String piId, String nodeId);

    List<SSTask> getTasksSortByEndDate(String piId, String nodeId);

    Page getTasks(String username, String resourceType, int page, int pageSize, int status);

    Page getCompletedTasks(String username, String resourceType, String recPiType, int page, int pageSize);

    Page searchUnCompleteTasks(String keywords, String username, String resourceType, String pdType, int page,
                               int pageSize);

    Page searchTransferTasks(String keywords, String username, String resourceType, String pdType, int page,
                             int pageSize);

    Page searchCompleteTasks(String keywords, String username, String resourceType, String pdType, int page,
                             int pageSize);

    List<SSTask> getUnCompleteTaskList(String username, String resourceType);

    List<SSTask> getTasks(String piId);

    List<SSTask> getUncompleteTaskByPi(String piId);

    void deleteTasksByPI(String piId);

    void deleteCarbonCopyByPI(String piId);

//    void deleteTasksByNodes(String piId, List<SSTaskNode> nodes);

    void deleteTasksByNodes(String piId, String nodeId);

    List<SSTask> getAllUnCompleteTasks();
}
