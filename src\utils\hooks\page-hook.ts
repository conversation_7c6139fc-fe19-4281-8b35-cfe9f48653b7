import { ref, watch } from "vue";

const usePage = (onChange: (page: number, size: number) => void) => {

  const currentPage = ref(1);
  const pageSize = ref(30);
  const total = ref(0);

  watch(() => pageSize.value, (newSize, oldSize) => {
    if (newSize !== oldSize && oldSize !== undefined) {
      if (currentPage.value === 1) {
        onChange(currentPage.value, pageSize.value);
      } else {
        currentPage.value = 1;
      }
    }
  });

  watch([currentPage], () => {
    onChange(currentPage.value, pageSize.value);
  });

  const refresh = () => {
    if (currentPage.value === 1) {
      onChange(currentPage.value, pageSize.value);
    } else {
      currentPage.value = 1;
    }
  }

  return {
    currentPage,
    pageSize,
    total,
    refresh
  }
}

export default usePage;
