package zd.dms.services.document;

import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentLink;
import zd.dms.entities.Folder;
import zd.dms.entities.User;

import java.util.List;

@Transactional
public interface DocumentLinkService extends BaseJpaService<DocumentLink, Long> {

    void delete(User user);

    void delete(Folder folder);

    void delete(Document document);

    void delete(DocumentLink link);

    void deleteById(long id);

    DocumentLink getDocumentLinkById(long id);

    @Transactional(readOnly = true)
    List<DocumentLink> getDocumentLinks(int type, Folder folder);

    @Transactional(readOnly = true)
    List<DocumentLink> getDocumentLinks(int type, Document document);

    @Transactional(readOnly = true)
    List<DocumentLink> getDocumentLinks(int type, User user);

    void publishToMyCommon(Document doc, User user);

    void publishToFolder(Document doc, Folder folder, User publisher);

    void removeLinkFromMyCommon(User user, Document doc);

    void removeLinkFromFolder(Folder folder, Document doc);

    /**
     * 根据文件名，更新时间对我常用的文档搜索
     *
     * @param typeMyCommon
     * @param username
     * @param fileNames
     * @param advUpdateStartDate
     * @param advUpdateEndDate
     * @return
     */
    List<DocumentLink> searchDocumentLinks(int typeMyCommon, String username, String fileNames,
                                           String advUpdateStartDate, String advUpdateEndDate);

    void linkDocumentRule(Document document, User user);
}
