package zd.dms.services.documentremind;

import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.dms.entities.DocAutoRemind;

import java.util.List;

@Transactional
public interface DocAutoRemindService extends BaseJpaService<DocAutoRemind, Long> {

    void doAutoReminds();

    void deleteById(long id);

    void deleteAllByFolderId(long folderId);

    List<DocAutoRemind> getRemindsByFolderId(long folderId);

    List<DocAutoRemind> getAllAutoReminds();

    void updateAutoRemind(DocAutoRemind ar);

    DocAutoRemind getAutoRemind(long id);

    void createAutoRemind(DocAutoRemind ar);
}
