package zd.dms.controllers.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.model.ObjectMapperUtils;
import zd.base.utils.validate.ValidateUtils;
import zd.dms.dto.PageResponse;
import zd.dms.entities.*;
import zd.dms.services.document.FolderAreaService;
import zd.dms.services.document.FolderService;
import zd.dms.services.security.FolderPermissionUtils;
import zd.dms.services.security.RoleUtils;
import zd.record.entities.RecFolder;
import zd.record.entities.RecFolderPermission;
import zd.record.service.folder.RecFolderService;
import zd.record.service.security.RecFolderPermissionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-05-06
 */
@RequiredArgsConstructor
@RestController
@Tag(name = "FolderAreaController", description = "目录分区Controller")
@RequestMapping("/admin/folderArea")
public class FolderAreaController extends ControllerSupport {

    private final FolderAreaService folderAreaService;

    private final FolderService folderService;

    private final RecFolderService recFolderService;

    @Operation(summary = "获取目录分区")
    @GetMapping("/get/{id}")
    @ZDLog("获取目录分区")
    public JSONResultUtils<Object> getById(@PathVariable String id) {

        FolderArea folderArea = folderAreaService.getFolderAreaById(NumberUtils.toLong(id));

        return successData(folderArea);
    }

    @Operation(summary = "获取目录分区")
    @PostMapping("/getFolderArea")
    @ZDLog("获取目录分区")
    public JSONResultUtils<Object> getFolderArea(@RequestBody Map<String, Object> params) {
        long folderAreaId = MapUtils.getLongValue(params, "folderAreaId", 0L);
        long recFolderId = MapUtils.getLongValue(params, "recFolderId", 0L);
        long folderId = MapUtils.getLongValue(params, "folderId", 0L);
        if (folderAreaId <= 0 && recFolderId <= 0 && folderId <= 0) {
            return error("参数错误");
        }

        FolderArea folderArea = null;
        if (folderAreaId > 0) {
            folderArea = folderAreaService.getFolderAreaById(folderAreaId);
        } else if (recFolderId > 0) {
            RecFolder recFolder = recFolderService.getById(recFolderId);
            String treeName = recFolder.getTreeNameWithParent();
            if (StringUtils.isBlank(treeName)) {
                List<FolderArea> defaultAreas = folderAreaService.getDefaultAreas();
                folderArea = defaultAreas.stream().filter(item -> FolderArea.TYPE_DATA.equals(item.getAreaType())).findFirst().get();
            } else {
                folderArea = folderAreaService.getFolderAreaByTreeName(treeName);
            }
        } else {
            Folder folder = folderService.getById(folderId);
            String treeName = folder.getTreeNameWithParent();
            if (StringUtils.isBlank(treeName)) {
                List<FolderArea> defaultAreas = folderAreaService.getDefaultAreas();
                folderArea = defaultAreas.stream().filter(item -> FolderArea.TYPE_DOC.equals(item.getAreaType())).findFirst().get();
            } else {
                folderArea = folderAreaService.getFolderAreaByTreeName(treeName);
            }
        }

        Map<String, Object> folderAreaMap = ObjectMapperUtils.toMap(folderArea, "");
        if (RoleUtils.isUserInRole(getCurrentUser(), Role.SYSTEM_ADMIN)) {
            String areaType = folderArea.getAreaType();
            if (FolderArea.TYPE_DOC.equals(areaType)) {
                folderAreaMap.put("myPermissions", FolderPermission.CREATE + FolderPermission.DELETE);
            } else {
                folderAreaMap.put("myPermissions", RecFolderPermission.CREATE + RecFolderPermission.DELETE);
            }
        }


        return successData(folderAreaMap);
    }

    @Operation(summary = "获取目录分区")
    @PostMapping("/getFolderAreas")
    @ZDLog("获取目录分区")
    public JSONResultUtils<Object> getFolderAreas(@RequestBody Map<String, Object> params) {
        String folderAreaType = MapUtils.getString(params, "folderAreaType", FolderArea.TYPE_DATA);
        List<FolderArea> folderAreas = folderAreaService.getFolderAreasByTypeAndIsEnabled(folderAreaType, true, true);

        return successData(ObjectMapperUtils.toMapList(folderAreas));
    }

    @Operation(summary = "获取含目录的目录分区")
    @PostMapping("/getFolderAreasWithFolder")
    @ZDLog("获取含目录的目录分区")
    public JSONResultUtils<Object> getFolderAreasWithFolder(@RequestBody Map<String, Object> params) {
        String folderAreaType = MapUtils.getString(params, "folderAreaType", FolderArea.TYPE_DATA);
        List<FolderArea> folderAreas = folderAreaService.getFolderAreasByTypeAndIsEnabled(folderAreaType, true, true);

        List<Map<String, Object>> folderAreaMapList = ObjectMapperUtils.toMapList(folderAreas);
        folderAreaMapList.forEach(item -> {
            long folderAreaId = MapUtils.getLongValue(item, "id", 0L);

            if (FolderArea.TYPE_DATA.equals(folderAreaType)) {
                List<RecFolder> resultsRecFolders = recFolderService.getTopRecFolderListByFolderAreaId(folderAreaId);
                resultsRecFolders = RecFolderPermissionUtils.getRecFolderChildrenWithPermissions(resultsRecFolders, RecFolderPermission.LIST, getCurrentUser());
                if (CollectionUtils.isEmpty(resultsRecFolders)) {
                    item.put("hasChildren", false);
                    item.put("children", new ArrayList<>());
                    return;
                }

                // 获取所有子目录数据, 使用一个sql一次性查询，防止出现多个sql
                /*List<Long> childrenIds = resultsRecFolders.stream().map(AbstractSequenceEntity::getId).toList();
                Map<Long, Long> childCount = recFolderService.getChildCount(childrenIds);

                // 封装每个目录子目录数量
                resultsRecFolders.forEach(f -> {
                    Long id = f.getId();
                    Long count = childCount.get(id);
                    if (count == null) {
                        count = 0L;
                    }

                    f.setChildCount(count);
                });*/

                List<Map<String, Object>> recFolderMapList = ObjectMapperUtils.toMapList(resultsRecFolders, "list");
                item.put("hasChildren", true);
                item.put("children", recFolderMapList);
            } else {
                List<Folder> resultsFolders = folderService.getTopFolderListByFolderAreaId(folderAreaId);
                resultsFolders = FolderPermissionUtils.getFolderChildrenWithPermissions(resultsFolders, FolderPermission.LIST, getCurrentUser());
                if (CollectionUtils.isEmpty(resultsFolders)) {
                    item.put("hasChildren", false);
                    item.put("children", new ArrayList<>());
                    return;
                }

                // 获取所有子目录数据, 使用一个sql一次性查询，防止出现多个sql
                /*List<Long> childrenIds = resultsRecFolders.stream().map(AbstractSequenceEntity::getId).toList();
                Map<Long, Long> childCount = recFolderService.getChildCount(childrenIds);

                // 封装每个目录子目录数量
                resultsRecFolders.forEach(f -> {
                    Long id = f.getId();
                    Long count = childCount.get(id);
                    if (count == null) {
                        count = 0L;
                    }

                    f.setChildCount(count);
                });*/

                List<Map<String, Object>> folderMapList = ObjectMapperUtils.toMapList(resultsFolders, "list");
                item.put("hasChildren", true);
                item.put("children", folderMapList);
            }
        });

        return successData(folderAreaMapList);
    }

    @Operation(summary = "获取目录分区")
    @GetMapping("/list")
    @ZDLog("获取目录分区列表")
    public JSONResultUtils<Object> listFolderAreas() {

        List<FolderArea> folderAreas = folderAreaService.getAllFolderAreas();

        return successData(ObjectMapperUtils.toMapList(folderAreas, "list"));
    }

    @Operation(summary = "获取有目录权限的目录分区")
    @GetMapping("/listWithFolderPerm")
    @ZDLog("获取有目录权限的目录分区")
    public JSONResultUtils<Object> listWithFolderPerm() {

        List<FolderArea> results = new ArrayList<>();
        List<FolderArea> folderAreas = folderAreaService.getAllFolderAreas();
        User currentUser = getCurrentUser();
        if (RoleUtils.isUserInRole(currentUser, Role.SYSTEM_ADMIN)) {
            return successData(ObjectMapperUtils.toMapList(folderAreas, "list"));
        }

        outer:
        for (FolderArea folderArea : folderAreas) {
            if (FolderArea.TYPE_DATA.equals(folderArea.getAreaType())) {
                List<RecFolder> resultsRecFolders = recFolderService.getTopRecFolderListByFolderAreaId(folderArea.getId());
                for (RecFolder recFolder : resultsRecFolders) {
                    if (RecFolderPermissionUtils.checkPermission(currentUser, recFolder, FolderPermission.LIST)) {
                        results.add(folderArea);
                        continue outer;
                    }
                }
            } else {
                List<Folder> resultsFolders = folderService.getTopFolderListByFolderAreaId(folderArea.getId());
                for (Folder folder : resultsFolders) {
                    if (FolderPermissionUtils.checkPermission(currentUser, folder, FolderPermission.LIST)) {
                        results.add(folderArea);
                        continue outer;
                    }
                }
            }
        }

        return successData(ObjectMapperUtils.toMapList(results, "list"));
    }

    @Operation(summary = "获取目录分区分页列表")
    @PostMapping("/getPage")
    @ZDLog("获取目录分区分页列表")
    public JSONResultUtils<Object> getPage(@RequestBody Map<String, Object> params) {
        int pageNumber = MapUtils.getIntValue(params, "pageNumber", 1);
        int pageSize = MapUtils.getIntValue(params, "pageSize", 10);

        Page folderAreas = folderAreaService.getAllFolderAreasPage(pageNumber, pageSize);

        return successData(PageResponse.of(folderAreas));
    }

    @Operation(summary = "创建目录分区")
    @PostMapping("/create")
    @ZDLog("创建目录分区")
    public JSONResultUtils<Object> create(@RequestBody Map<String, Object> params) {
        String areaName = MapUtils.getString(params, "areaName", "");
        ValidateUtils.isTrue(StringUtils.isNotBlank(areaName), "请填写分区名称");

        String areaType = MapUtils.getString(params, "areaType", "");
        ValidateUtils.isTrue(StringUtils.isNotBlank(areaType), "请选择区域类型");

        params.put("creatorFullname", getCurrentFullname());

        return folderAreaService.createFolderArea(params);
    }

    @Operation(summary = "删除目录分区")
    @PostMapping("/delete/{id}")
    @ZDLog("删除目录分区")
    public JSONResultUtils<Object> delete(@PathVariable String id) {
        ValidateUtils.isTrue(StringUtils.isNotBlank(id), "请选择目录分区");

        FolderArea folderArea = folderAreaService.getFolderAreaById(NumberUtils.toLong(id));
        if (folderArea == null) {
            return error("没有找到对应目录分区");
        }

        List<Folder> topFolders = folderService.getPubTopFoldersByTreeName(folderArea.getTreeName());
        if (topFolders != null && !topFolders.isEmpty()) {
            return error("目录分区：" + folderArea.getAreaName() + " 中存在目录，不能被删除");
        }

        List<RecFolder> topRecFolders = recFolderService.getTopRecFoldersByTreeName(folderArea.getTreeName(), "");
        if (topRecFolders != null && !topRecFolders.isEmpty()) {
            return error("目录分区：" + folderArea.getAreaName() + " 中存在目录，不能被删除");
        }

        folderAreaService.deleteFolderAreaById(folderArea);

        return success();
    }

    @Operation(summary = "更新目录分区")
    @PostMapping("/update")
    @ZDLog("更新目录分区")
    public JSONResultUtils<Object> update(@RequestBody Map<String, Object> params) {
        String id = MapUtils.getString(params, "id", "");
        ValidateUtils.isTrue(StringUtils.isNotBlank(id), "请选择目录分区");

        params.put("updatefullname", getCurrentFullname());

        return folderAreaService.updateFolderArea(params);
    }
}
