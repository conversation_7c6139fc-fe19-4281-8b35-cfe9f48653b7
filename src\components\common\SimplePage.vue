<script setup lang="ts">
import { ElPagination } from 'element-plus';
const props = withDefaults(defineProps<{disabled:boolean}>(),{
  disabled: false
})

</script>

<template>
  <div class="flex justify-end">
    <el-pagination
      background
      class="ng-theme"
      v-bind="$attrs"
      v-on="$attrs"
      :page-sizes="[30, 50, 100]"
      :pager-count="7"
      :disabled="props.disabled"
      layout="total, sizes, prev, pager, next, jumper"/>
  </div>
</template>

<style lang="scss" scoped></style>
