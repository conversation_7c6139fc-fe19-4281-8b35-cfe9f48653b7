package zd.dms.utils.oss.common;

import software.amazon.awssdk.services.s3.model.HeadObjectResponse;
import software.amazon.awssdk.services.s3.model.S3Object;
import zd.dms.entities.UserTask;
import zd.dms.utils.oss.AWSS3Utils;
import zd.dms.utils.usertask.UserTaskFileUtils;

import java.io.File;
import java.io.InputStream;
import java.util.List;

public class OSSAWS3Utils extends AbstractOSSUtils {

    public boolean doUploadToOSS(File uploadFile, String objectKey, int count) {
        return AWSS3Utils.doUploadToOSS(uploadFile, objectKey, count);
    }

    public File downloadFileFromOSS(String objectKey) {
        return AWSS3Utils.downloadFileFromOSS(objectKey);
    }

    public InputStream downloadInputStreamFromOSS(String objectKey) {
        return AWSS3Utils.downloadInputStreamFromOSS(objectKey);
    }

    public boolean deleteFileFromOSS(String objectKey) {
        return AWSS3Utils.deleteFileFromOSS(objectKey);
    }

    public void deleteUserTasksFile() {
        List<S3Object> results = AWSS3Utils.listFileFromOSS(UserTaskFileUtils.ossRootPath);
        if (results == null || results.isEmpty()) {
            return;
        }

        for (S3Object s3Object : results) {
            try {
                String tempObjectName = s3Object.key();
                UserTask userTask = getUserTask(tempObjectName);
                if (userTask == null) {
                    AWSS3Utils.deleteSingleFileFromOSS(tempObjectName);
                }
            } catch (Exception e) {
                // 忽略异常
            }
        }
    }

    public boolean exists(String objectKey) {
        return AWSS3Utils.exists(objectKey);
    }

    public long getLastModifiedEpochMilli(String objectKey) {
        HeadObjectResponse stat = AWSS3Utils.getStat(objectKey);
        if (stat != null) {
            return stat.lastModified().toEpochMilli();
        }

        return 0;
    }
}
