package zd.dms.services.document.threads;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.lucene.analysus.ja.dict.TaskDictionary;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.utils.system.PropsUtils;
import zd.base.utils.system.SpringUtils;
import zd.base.utils.system.SystemInitUtils;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentVersion;
import zd.dms.entities.User;
import zd.dms.io.ZDIOUtils;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.document.DocumentLinkService;
import zd.dms.services.document.DocumentService;
import zd.dms.utils.*;
import zd.dms.utils.abbyy.AbbyyOcrUtils;
import zd.dms.utils.ocr.OcrUtils;
import zd.record.utils.ECMUtils;

import java.io.File;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.locks.Lock;

public class PostDocThread extends AbstractThread {

    private static final Logger log = LoggerFactory.getLogger(PostDocThread.class);

    private static SystemConfigManager scm = SystemConfigManager.getInstance();

    private String docId;

    private String docFilename;

    private String extension;

    private String encoding;

    private File uploadFile;

    private int versionNumber;

    private User user;

    private DocumentService documentService = SpringUtils.getBean(DocumentService.class);

    public PostDocThread(String docId, String docFilename, File uploadFile, String extension, String encoding,
                         int versionNumber, User user) {
        this.docId = docId;
        this.docFilename = docFilename;
        this.uploadFile = uploadFile;
        this.extension = extension;
        this.encoding = encoding;
        this.versionNumber = versionNumber;
        this.user = user;
    }

    public void work() {
        // 获取同步锁
        Lock lock = SyncUtils.lock("postDocThread-" + docId);

        try {
            doHash();
            doImageConvert();
            // 本方法中会对文件重新命名，需要在最后执行
            doExtractText();
            // doConvertPdf();

            doExtractVideo();
        } catch (Throwable t) {
            log.error("PostDocThread run ex", t);
        } finally {
            // 解锁同步锁
            SyncUtils.unlock(lock);

            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
            }
            log.debug("------ PostDocThread cleanup: {}, {}", docFilename, uploadFile.getAbsolutePath());
            FileUtils.deleteQuietly(uploadFile);
        }
    }

    private Document getDoc() {
        Document document = getDocumentService().getDocumentById(docId);
        int tryCount = 5;
        while (document == null) {
            if (tryCount <= 0 || document != null) {
                break;
            }
            try {
                Thread.sleep(2000);
                log.debug("doExtractText Document not exists, sleep 2 secs");
            } catch (InterruptedException e) {
            }
            document = getDocumentService().getDocumentById(docId);
            tryCount--;
        }

        if (document == null) {
            throw new IllegalStateException("null doc for id: " + docId + ", filename: " + docFilename);
        }

        return document;
    }

    private void doHash() {
        Document document = getDoc();

        log.debug("doHash: {}", document.getFilename());
        // hash
        String hash = null;
        if (ZDIOUtils.FH()) {
            hash = HashUtils.getHashFile(uploadFile);
            if (StringUtils.isNotBlank(hash)) {
                document.setHash(hash);
                getDocumentService().updateDocument(document);

                DocumentVersion dv1 = getDocumentService().getVersion(document, versionNumber);
                if (dv1 != null) {
                    dv1.setHash(hash);
                    getDocumentService().updateVersion(dv1);
                }
            }
        }
    }

    private void doImageConvert() {
        if (!SystemConfigManager.getInstance().getBooleanProperty("enableAutoThumbnail")) {
            return;
        }

        if ("eps".equals(extension) || "pdf".equals(extension) || "ai".equals(extension) || "psd".equals(extension)) {
            Document document = getDoc();
            log.debug("doImageConvert: {}", document.getFilename());
            String filePath = ImageConversionUtils.convertImage(uploadFile);
            if (StringUtils.isNotBlank(filePath)) {
                File jpgFile = new File(filePath);
                try {
                    File tnFile = ZDIOUtils.getThumbnailFile(document, document.getNewestVersion());

                    if (tnFile != null) {
                        FileUtils.copyFile(jpgFile, tnFile);
                    }

                    document.setHasThumbnail(true);
                    getDocumentService().updateDocument(document, false);
                } catch (Throwable e) {
                    log.error("自动转换缩略图时出错", e);
                } finally {
                    FileUtils.deleteQuietly(jpgFile);
                }
            }
        }
    }

    private void doConvertPdf() {
        Document document = getDoc();

        log.debug("doConvertPdf: {}", document.getFilename());
        Hibernate.initialize(document);
        Hibernate.initialize(document.getFolder());
        ZDIOUtils.tryConvertPdf(document, uploadFile, false, false);
    }

    private void doExtractText() {
        String extractedText = null;

        Document document = getDoc();

        log.debug("doExtractText: {}", document.getFilename());

        if (!uploadFile.exists()) {
            log.error("doExtractText uploadFile: {} not exists", docFilename);
        }

        // 提取文档摘要
        if (OfficeUtils.isExtractable(docFilename) && !AbbyyOcrUtils.isMustAbbyyOcr(docFilename)) {
            extractedText = OfficeUtils.extractText(uploadFile, extension, encoding);
        }

        // 当开启ocr模块、文档摘要为空且后缀符合时，进行abbyyocr解析
        if (TaskDictionary.getMainDefinition().hm("ocrEngine") && StringUtils.isBlank(extractedText) &&
                OcrUtils.isNeedOcr(docFilename)) {

            String ocrServerAddress = PropsUtils.getProps("ocrServerAddress");

            OcrUtils.ocrDocByAbbyyOrTesseract(ocrServerAddress, docId, docFilename, uploadFile, versionNumber, user,
                    true);
        }

        log.debug("doExtractText filename: {} extracted", docFilename);

        if (StringUtils.isNotBlank(extractedText)) {
            // 更新文档对象
            document.setContent(extractedText);
            getDocumentService().updateDocument(document);
            log.debug("ExtractTextThread filename: {} success", docFilename);

            // 更新版本对象
            DocumentVersion newestDV = getDocumentService().getVersion(document, versionNumber);
            if (newestDV == null) {
                log.debug("ExtractTextThread filename: {} version: {} not exists, return", docFilename, versionNumber);
                return;
            }
            newestDV.setContent(extractedText);
            getDocumentService().updateVersion(newestDV);
            log.debug("ExtractTextThread filename: {} version success", docFilename);

            // 自动关联文件规则
            getDocumentLinkService().linkDocumentRule(document, user);
        }
    }

    private void doExtractVideo() {
        if (!TaskDictionary.getMainDefinition().hm("video")) {
            return;
        }

        Document document = getDoc();
        if (!document.isVideoFile()) {
            return;
        }

        log.debug("doExtractVideo filename:(} start", document.getFilename());
        // 1、获取视频信息，存入文档属性
        int timelen = -1;
        Map<String, String> videoMsg = FFmpegUtils.getVideoMsg(uploadFile.getAbsolutePath());
        if (videoMsg != null && videoMsg.size() >= 0) {
            for (Entry<String, String> en : videoMsg.entrySet()) {
                String docPropKey = en.getKey();
                String docPropValue = en.getValue();
                String docPropIndex = StringUtils.defaultIfBlank(document.getDocProp(docPropKey, "index"), "9999");
                document.setDocProp(docPropKey, docPropKey, docPropValue, docPropIndex);
            }
            getDocumentService().updateDocument(document, false);

            String videoTime = videoMsg.get("视频信息-时长");
            if (StringUtils.isNotBlank(videoTime)) {
                timelen = FFmpegUtils.getTimelen(videoTime);
            }
        }

        // 2、截取视频第5秒为视频截图，保存缩略图（小于5秒则取第一秒）
        try {
            // 存放截图的临时文件
            File tempConvertFolder = new File(SystemInitUtils.getTempDir(), "ffmpeg");
            tempConvertFolder.mkdirs();
            File resultFile = new File(tempConvertFolder, RandomStringUtils.randomAlphanumeric(10) + ".jpg");

            // 设置截图信息
            int frame = 15;
            String shotTime = PropsUtils.getProps("zdff_shotTime", "00:00:05");
            if (timelen != -1 && timelen < 5) {
                shotTime = "00:00:01";
            }
            FFmpegUtils.videoCapture(shotTime, frame, uploadFile.getAbsolutePath(), resultFile.getAbsolutePath());

            // 将截图临时文件内容复制到存放在doc的文件夹下的缩略图中
            File tnFile = ZDIOUtils.getThumbnailFile(document, document.getNewestVersion());
            if (resultFile.exists() && resultFile.length() > 0 && tnFile != null && resultFile != null) {
                FileUtils.copyFile(resultFile, tnFile);
                document.setHasThumbnail(true);
                getDocumentService().updateDocument(document, false);
            }
        } catch (Exception e) {
            log.error("doExtractVideo 生成视频截图错误", e);
        }

        // 3、生成可预览的小尺寸MP4文件
        String docPropKey = "视频信息-转码进度";
        String docPropValue = "转码中";
        String docPropIndex = StringUtils.defaultIfBlank(document.getDocProp(docPropKey, "index"), "9999");
        File docFileDir = ZDIOUtils.getDocFileDir(document, false);
        File priviewFile = new File(docFileDir, "preview" + document.getNewestVersion() + ".mp4");
        try {
            // 视频格式中包含unknown则不进行转换
            if (videoMsg == null || videoMsg.size() == 0) {
                return;
            }

            String videoFormatMsg = ECMUtils.getStringFromRecordMap(videoMsg.get("视频信息-格式"));
            if (StringUtils.isBlank(videoFormatMsg) || StringUtils.contains(videoFormatMsg, "unknown")) {
                docPropValue = "不支持的编码";
                document.setDocProp(docPropKey, docPropKey, docPropValue, docPropIndex);
                getDocumentService().updateDocument(document, false);
                log.debug("doExtractVideo videoFormatMsg contains unknown");
                return;
            }

            document.setDocProp(docPropKey, docPropKey, docPropValue, docPropIndex);
            getDocumentService().updateDocument(document, false);

            // 存放mp4的临时文件
            File tempConvertFolder = new File(SystemInitUtils.getTempDir(), "ffmpeg");
            tempConvertFolder.mkdirs();
            File resultFile = new File(tempConvertFolder, RandomStringUtils.randomAlphanumeric(10) + ".mp4");

            // 设置转码信息
            String bitRate = PropsUtils.getProps("zdff_transcodingMP4Bit", "600");
            String videoBitRateMsg = ECMUtils.getStringFromRecordMap(videoMsg.get("视频信息-码率"));
            if (StringUtils.isNotBlank(videoBitRateMsg) && videoBitRateMsg.contains("kb/s")) {
                // 视频转换的码率不能超过视频源码率
                videoBitRateMsg = StringUtils.substringBefore(videoBitRateMsg, "kb/s");
                int videoBitRate = NumberUtils.toInt(videoBitRateMsg);
                int defaultBitRate = NumberUtils.toInt(bitRate);
                if (defaultBitRate > videoBitRate) {
                    bitRate = videoBitRateMsg + "";
                }
            }

            bitRate += "k";
            String frameRate = PropsUtils.getProps("zdff_transcodingMP4Frame", "20");
            boolean withAudio = PropsUtils.getBoolProps("zdff_transcodingMP4WithAudio", true);
            FFmpegUtils.transcodingMP4(bitRate, frameRate, uploadFile.getAbsolutePath(), resultFile.getAbsolutePath(),
                    withAudio);

            // 将mp4文件内容复制到存在在doc文件夹下的preview.mp4中，不同版本的视频生成不同版本的预览文件，防止预览视频时占用文件导致更新操作生成预览视频失败
            if (docFileDir != null && docFileDir.exists() && resultFile != null && resultFile.exists() &&
                    resultFile.length() > 0) {
                ZDIOUtils.saveDocPreview(document, document.getNewestVersion(), resultFile);

                docPropValue = "转码成功";
                document.setDocProp(docPropKey, docPropKey, docPropValue, docPropIndex);
                getDocumentService().updateDocument(document, false);
            } else {
                docPropValue = "转码失败";
                document.setDocProp(docPropKey, docPropKey, docPropValue, docPropIndex);
                getDocumentService().updateDocument(document, false);
            }
        } catch (Exception e) {
            docPropValue = "转码失败";
            document.setDocProp(docPropKey, docPropKey, docPropValue, docPropIndex);
            getDocumentService().updateDocument(document, false);
            FileUtils.deleteQuietly(priviewFile);
            log.error("doExtractVideo 生成预览MP4文件失败", e);
        }

        log.debug("doExtractVideo filename:(} end", document.getFilename());
    }

    private static DocumentService getDocumentService() {
        return SpringUtils.getBean(DocumentService.class);
    }

    private static DocumentLinkService getDocumentLinkService() {
        return SpringUtils.getBean(DocumentLinkService.class);
    }
}
