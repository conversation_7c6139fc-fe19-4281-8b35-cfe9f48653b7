package zd.dms.services.ldap.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import zd.base.utils.ZDDateUtils;
import zd.base.utils.ZDUtils;
import zd.base.utils.system.PropsUtils;
import zd.dms.entities.Group;
import zd.dms.entities.User;
import zd.dms.exception.EntityAlreadyExistsException;
import zd.dms.exception.UserAmountExceededException;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.ldap.LDAPException;
import zd.dms.services.ldap.LDAPService;
import zd.dms.services.security.PasswordUtils;
import zd.dms.services.user.GroupService;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.services.user.UserService;
import zd.dms.services.user.exception.UserDisabledException;
import zd.dms.services.user.exception.UserNotExistsException;
import zd.dms.services.user.exception.WrongPasswordException;
import zd.dms.utils.Blowfish;
import zd.dms.utils.TextUtils;
import zd.dms.utils.db.ZDJoinTableGroupUserUtils;
import zd.dms.utils.ldap.LdapTools;
import zd.dms.utils.ldap.LdapUtils;
import zd.record.utils.ECMUtils;
import zd.record.utils.ZDStringEscapeUtils;

import javax.naming.Context;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.*;
import javax.naming.ldap.InitialLdapContext;
import javax.naming.ldap.LdapContext;
import java.util.*;

@RequiredArgsConstructor
@Service
@Slf4j
public class LDAPServiceImpl implements LDAPService {

    private final UserService userService;

    private final GroupService groupService;

    private static final String B_PASS = "sKLa133CVasSDT$52C1N<?/";

    private static final Blowfish blowfish = new Blowfish(B_PASS);

    private final SystemConfigManager scm = SystemConfigManager.getInstance();

    @Override
    public void auth(String username, String password) throws UserNotExistsException, UserDisabledException, WrongPasswordException, LDAPException {
        String ldapDomain = SystemConfigManager.getInstance().getProperty("ldap.domain");
        String ldapHost = SystemConfigManager.getInstance().getProperty("ldap.host");
        String ldapPort = SystemConfigManager.getInstance().getProperty("ldap.port");
        String ldapRoot = SystemConfigManager.getInstance().getProperty("ldap.root");
        String ldapServerType = SystemConfigManager.getInstance().getProperty("ldap.stype");

        if (StringUtils.isBlank(ldapDomain) || StringUtils.isBlank(ldapHost) || StringUtils.isBlank(ldapPort)) {
            throw new LDAPException("LDAP配置不完整，无法连接");
        }

        DirContext ctx = null;
        log.debug("ldapServerType: {}", ldapServerType);
        String account = "";
        if (StringUtils.isBlank(ldapServerType) || "ad".equals(ldapServerType)) {
            account = username + "@" + ldapDomain; // 用户名
        } else if ("domino".equals(ldapServerType)) {
            account = "CN=" + username + ",O=" + ldapDomain;
        } else if ("open".equals(ldapServerType)) {
            account = "uid=" + username + "," + ldapDomain;
        }

        Hashtable<String, String> env = new Hashtable<String, String>();
        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        env.put(Context.PROVIDER_URL, "ldap://" + ldapHost + ":" + ldapPort);
        env.put(Context.SECURITY_AUTHENTICATION, "simple");
        env.put(Context.SECURITY_PRINCIPAL, account);
        env.put(Context.SECURITY_CREDENTIALS, password);

        try {
            // 链接ldap
            ctx = new InitialDirContext(env);
        } catch (javax.naming.AuthenticationException e) {
            if (e.getMessage().contains("525")) {
                throw new UserNotExistsException();
            } else if (e.getMessage().contains("533")) {
                throw new UserDisabledException();
            } else {
                throw new WrongPasswordException();
            }
        } catch (Exception e) {
            if (e.getCause() != null && e.getCause().getMessage().contains("timed out")) {
                throw new LDAPException("LDAP服务器连接超时，请检查服务器");
            }
            throw new LDAPException(ZDStringEscapeUtils.escapeJavaScript(e.getMessage()));
        }
    }

    @Override
    public Map<String, Map<String, String>> loadLDAPUsers(String adminUsername, String adminPassword) throws LDAPException {
        String ldapDomain = SystemConfigManager.getInstance().getProperty("ldap.domain");
        String ldapHost = SystemConfigManager.getInstance().getProperty("ldap.host");
        String ldapPort = SystemConfigManager.getInstance().getProperty("ldap.port");
        String ldapRoot = SystemConfigManager.getInstance().getProperty("ldap.root");

        if (StringUtils.isBlank(ldapDomain) || StringUtils.isBlank(ldapHost) || StringUtils.isBlank(ldapPort) ||
                StringUtils.isBlank(ldapRoot) || StringUtils.isBlank(adminUsername)) {
            throw new LDAPException("LDAP配置不完整，无法连接");
        }

        if (StringUtils.isBlank(adminPassword)) {
            adminPassword = "";
        }

        String adminAccount = adminUsername + "@" + ldapDomain; // 用户名
        Hashtable<String, String> env = new Hashtable<String, String>();
        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        env.put(Context.PROVIDER_URL, "ldap://" + ldapHost + ":" + ldapPort);
        env.put(Context.SECURITY_AUTHENTICATION, "simple");
        env.put(Context.SECURITY_PRINCIPAL, adminAccount);
        env.put(Context.SECURITY_CREDENTIALS, adminPassword);

        log.debug("载入所有LDAP用户，管理员用户名：{}", adminAccount);

        Map<String, Map<String, String>> result = new HashMap<String, Map<String, String>>();

        try {
            LdapContext ctx = new InitialLdapContext(env, null);
            SearchControls searchCtls = new SearchControls(); // Create the
            searchCtls.setSearchScope(SearchControls.SUBTREE_SCOPE); // Specify
            String searchFilter = "objectClass=User"; // specify the LDAP search
            // specify the LDAP search filter
            // String searchFilter = "objectClass=organizationalUnit";

            String searchBase = ldapRoot;

            // Specify the attributes to return
            // String returnedAtts[] = {"memberOf"}; 定制返回属性
            String returnedAtts[] = {"url", "whenChanged", "employeeID", "name", "userPrincipalName",
                    "physicalDeliveryOfficeName", "departmentNumber", "telephoneNumber", "homePhone", "mobile",
                    "department", "sAMAccountName", "givenName", "sn", "mail", "userPrincipalName", "wwwHomepage"};

            // 设置返回属性集
            searchCtls.setReturningAttributes(returnedAtts);

            // Search for objects using the filter
            NamingEnumeration answers = ctx.search(searchBase, searchFilter, searchCtls);

            while (answers.hasMoreElements()) {
                SearchResult sr = (SearchResult) answers.next();
                Attributes attrs = sr.getAttributes();
                String username = (String) attrs.get("sAMAccountName").get(0);
                log.debug("loadLDAPUser: {}", username);

                if (StringUtils.isBlank(username)) {
                    continue;
                }

                if ("Administrator".equals(username) || "Guest".equals(username) || username.startsWith("SUPPORT")) {
                    log.debug("跳过系统用户");
                    continue;
                }

                String fullname = attrs.get("sn") != null && attrs.get("givenName") != null ? attrs.get("sn").get(0) +
                        "" + attrs.get("givenName").get(0) : "";

                if (StringUtils.isBlank(fullname)) {
                    log.debug("全名为空，使用用户名作为全名");
                    fullname = username;
                }

                String mobile = attrs.get("mobile") != null ? (String) attrs.get("mobile").get(0) : "";

                String officePhone = attrs.get("telephoneNumber") != null ? (String) attrs.get("telephoneNumber")
                        .get(0) : "";
                String mail = attrs.get("mail") != null ? (String) attrs.get("mail").get(0) : "";
                String department = attrs.get("department") != null ? (String) attrs.get("department").get(0) : "";
                String homePhone = attrs.get("homePhone") != null ? (String) attrs.get("homePhone").get(0) : "";

                Map<String, String> userAttrs = new HashMap<String, String>();
                userAttrs.put("mobile", mobile);
                userAttrs.put("fullname", fullname);
                userAttrs.put("officePhone", officePhone);
                userAttrs.put("mail", mail);
                userAttrs.put("department", department);
                userAttrs.put("homePhone", homePhone);
                userAttrs.put("username", username);

                // 设置已存在标记
                if (userService.getUserByUsername(username) != null) {
                    log.debug("用户：{} 已存在，设置标记", username);
                    userAttrs.put("exists", "true");
                }

                log.debug(
                        "attrs username: {}, fullname: {}, mobile: {}, officePhone: {}, homePhone: {}, mail: {}, depart: {}",
                        new Object[]{username, fullname, mobile, officePhone, homePhone, mail, department});

                result.put(username, userAttrs);
            }
            ctx.close();
        } catch (NamingException e) {
            if (e.getCause() != null) {
                log.debug("LDAP Cause Exception errormsg: {}", e.getCause().getMessage());
            }
            log.debug("LDAP Exception errormsg: {}", e.getMessage());

            if (e.getCause() != null && e.getCause().getMessage().contains("timed out")) {
                throw new LDAPException("LDAP服务器连接超时，请检查服务器");
            } else if (e.getCause() != null && e.getCause().getMessage().contains("52e")) {
                throw new LDAPException("LDAP服务器管理员密码错误");
            } else if (e.getMessage().contains("52e")) {
                throw new LDAPException("LDAP服务器管理员密码错误");
            } else if (e.getMessage().contains("525")) {
                throw new LDAPException("LDAP服务器管理员用户名错误或LDAP域错误");
            } else if (e.getMessage().contains(
                    "In order to perform this operation a successful bind must be completed on the connection")) {
                throw new LDAPException("请填写LDAP服务器管理员密码");
            }

            throw new LDAPException(ZDStringEscapeUtils.escapeJavaScript(e.getMessage()));
        }

        return result;
    }

    @Override
    public void importLDAPUsers(String adminUsername, String adminPassword, String[] usernames) throws LDAPException, UserAmountExceededException {
        log.debug("importLDAPUsers: {}", usernames.length);

        Map<String, Map<String, String>> users = loadLDAPUsers(adminUsername, adminPassword);

        if (usernames == null || usernames.length == 0) {
            log.debug("null usernames");
            return;
        }

        for (String username : usernames) {
            log.debug("准备创建LDAP用户：{}", username);
            Map<String, String> attrs = users.get(username);
            if (attrs == null) {
                log.debug("ldapUser: {} 不存在，跳过", username);
                continue;
            }

            Group g = groupService.getGroupByName(attrs.get("department"));
            if (g == null) {
                g = groupService.getTopGroups().get(0);
                log.debug("部门 {} 不存在，使用默认顶级部门", attrs.get("department"));
            }

            String fullname = attrs.get("fullname");
            String mobilePhone = attrs.get("mobile");
            String officePhone = attrs.get("officePhone");
            String homePhone = attrs.get("homePhone");
            String email = attrs.get("mail");
            User updateUser = userService.getUserByUsername(username);
            if (updateUser != null) {
                // 更新用户信息
                updateUser.setFullname(fullname);
                updateUser.setMobilePhone(mobilePhone);
                updateUser.setOfficePhone(officePhone);
                updateUser.setHomePhone(homePhone);
                updateUser.setEmail(email);
                userService.updateUser(updateUser);
                log.debug("更新LDAP用户信息：{}", username);
            } else {
                // 创建新用户
                User newUser = new User();
                newUser.setUsername(username);
                newUser.setFullname(fullname);
                String password = ZDUtils.generateUserPassword(username, mobilePhone, officePhone, homePhone);
                newUser.setPassword(PasswordUtils.genPassHash(password));
                newUser.setMobilePhone(mobilePhone);
                newUser.setOfficePhone(officePhone);
                newUser.setHomePhone(homePhone);
                newUser.setEmail(email);
                try {
                    userService.createUser(newUser);

                    ZDJoinTableGroupUserUtils.insert(g.getId(), newUser.getId());
                    log.debug("创建LDAP用户 {} ({})", attrs.get("fullname"), username);
                } catch (EntityAlreadyExistsException e) {
                    log.debug("同名LDAP用户 {} 已存在，跳过", username);
                    continue;
                } catch (UserAmountExceededException e) {
                    log.debug("已达授权用户数上限，跳过");
                    throw new UserAmountExceededException("已达授权用户数上限");
                }
            }
        }
    }

    @Override
    public List<Map> loadLDAPGroups(String adminUsername, String adminPassword, String groupDn) throws LDAPException {
        LdapTools ldapTools = getLdapTools(adminUsername, adminPassword);

        if (StringUtils.isBlank(groupDn)) {
            groupDn = SystemConfigManager.getInstance().getProperty("ldap.root");
        }
        List<Map> orgunitList = ldapTools.getOrgUnit(groupDn);

        ldapTools.releaseCtx();

        return orgunitList;
    }

    @Override
    public Map<String, Map> loadLDAPGroupAllUsers(String adminUsername, String adminPassword, String groupDn) throws LDAPException {
        LdapTools ldapTools = getLdapTools(adminUsername, adminPassword);

        Map<String, Map> usersList = ldapTools.getGroupUsersList(groupDn, null);

        ldapTools.releaseCtx();

        return usersList;
    }

    @Override
    public void doImportLDAPGroups(String adminUsername, String adminPassword, String groupDn, Group selectGroup, String includeParentGroup, boolean importUsers, boolean ignoreSameUsername, boolean forbidUser, Set<User> ldapUserSet, List<String> ldapGuidlist) throws LDAPException, UserAmountExceededException {
        Group parentGroup = selectGroup;
        LdapTools ldapTools = getLdapTools(adminUsername, adminPassword);

        if (selectGroup == null) {
            log.error("doImportLDAPGroups 所要导入的位置不存在");
            throw new IllegalArgumentException("所要导入的位置不存在");
        }

        if ("include".endsWith(includeParentGroup)) {
            List<Map> groupList = ldapTools.getGroupList(groupDn, SearchControls.OBJECT_SCOPE);
            if (groupList.size() > 0) {
                Map<String, String> map = groupList.get(0);
                String groupName = parseNcpName(map);
                String fullname = ECMUtils.getStringFromRecordMap(map.get("name"));

                // 判断部门是否存在
                if (StringUtils.isBlank(groupName)) {
                    log.error("doImportLDAPGroups 部门名为空");
                    throw new IllegalArgumentException("部门名为空");
                }

                if (groupName.endsWith(selectGroup.getName())) {
                    log.error("doImportLDAPGroups 所选部门不能导入本部门的子级");
                    throw new IllegalArgumentException("所选部门不能导入本部门的子级");
                }

                String description = map.get("description") != null ? (String) map.get("description") : "";
                String ldapId = map.get("guid") != null ? (String) map.get("guid") : "";

                // 保存ldapId，删除部门时使用
                if (ldapGuidlist != null && StringUtils.isNotBlank(ldapId) && !ldapGuidlist.contains(ldapId)) {
                    ldapGuidlist.add(ldapId);
                }

                Group newGroup = getGroupByNameOrLdapId(groupName, ldapId);

                if (newGroup != null) {
                    newGroup.setName(groupName);
                    newGroup.setDescription(description);
                    newGroup.setLdapId(ldapId);
                    newGroup.setCode(ldapId);
                    if (newGroup.getParent() == null) {
                        log.error("doImportLDAPGroups 无法导入顶级部门 groupName:{}", groupName);
                        throw new IllegalArgumentException("无法导入顶级部门");
                    }

                    newGroup.setParentCode(selectGroup.getCode());
                    groupService.updateGroup(newGroup);
                    parentGroup = newGroup;
                } else {
                    newGroup = new Group();
                    newGroup.setName(groupName);
                    newGroup.setDescription(description);
                    newGroup.setLdapId(ldapId);
                    newGroup.setCode(ldapId);
                    newGroup.setParentCode(selectGroup.getCode());
                    groupService.createGroup(newGroup);
                    parentGroup = newGroup;
                }
            }
        }
        List<Map> loadLDAPGroups = this.loadLDAPGroups(adminUsername, adminPassword, groupDn);
        createLdapGroups(loadLDAPGroups, parentGroup, ldapGuidlist);

        // 同时导入用户
        if (importUsers) {
            this.importGroupsWithUsers(adminUsername, adminPassword, groupDn, ignoreSameUsername, forbidUser, ldapUserSet);
        }

        // 删除部门
        if (PropsUtils.getBoolProps("ldapSyncWithDeleteGroup", false)) {
            this.deleteLdapGroups(ldapGuidlist);
        }

        ldapTools.releaseCtx();
    }

    private String parseNcpName(Map map) {
        if (map == null) {
            return "";
        }

        boolean ldapUseSimpleGroupName = PropsUtils.getBoolProps("ldapUseSimpleGroupName");

        // 设置为true时，只用LDAP中最终部门名称，此时所有部门名称不能重复
        if (ldapUseSimpleGroupName) {
            String groupName = ECMUtils.getStringFromRecordMap(map.get("name"));
            if (StringUtils.isBlank(groupName)) {
                groupName = ECMUtils.getStringFromRecordMap(map.get("department"));
            }

            log.debug("parseNcpName ldapUseSimpleGroupName result={}", groupName);
            return groupName;
        }

        StringBuffer name = new StringBuffer();
        String ncpName = ECMUtils.getStringFromRecordMap(map.get("ncpName"));
        if (StringUtils.isBlank(ncpName)) {
            return name.toString();
        }

        String groupDn = SystemConfigManager.getInstance().getProperty("ldap.root");
        ncpName = ncpName.replace("," + groupDn, "").replace("DC=", "").replace("OU=", "").replace("CN=", "");

        String[] split = ncpName.split(",");
        for (int i = split.length - 1; i >= 0; i--) {
            name.append(split[i]);
            if (i != 0) {
                name.append("/");
            }
        }

        log.debug("parseNcpName result={}", name.toString());
        return name.toString();
    }

    /**
     * 递归创建部门
     *
     * @param loadLDAPGroups
     * @param parentGroup
     */
    private void createLdapGroups(List<Map> loadLDAPGroups, Group parentGroup, List<String> ldapGuidlist) {
        for (Map map : loadLDAPGroups) {
            String groupName = parseNcpName(map);
            String fullname = ECMUtils.getStringFromRecordMap(map.get("name"));

            // 判断部门是否存在
            if (StringUtils.isBlank(groupName)) {
                log.error("doImportLDAPGroups 部门名为空");
                throw new IllegalArgumentException("部门名为空");
            }

            String description = map.get("description") != null ? (String) map.get("description") : "";
            String ldapId = map.get("guid") != null ? (String) map.get("guid") : "";

            // 保存ldapId，删除部门时使用
            if (ldapGuidlist != null && StringUtils.isNotBlank(ldapId) && !ldapGuidlist.contains(ldapId)) {
                ldapGuidlist.add(ldapId);
            }

            Group newGroup = getGroupByNameOrLdapId(groupName, ldapId);

            if (newGroup != null) {
                newGroup.setName(groupName);
                newGroup.setDescription(description);
                newGroup.setLdapId(ldapId);
                newGroup.setCode(ldapId);
                if (newGroup.getParent() == null) {
                    log.error("doImportLDAPGroups 无法导入顶级部门 groupName:{}", groupName);
                    throw new IllegalArgumentException("无法导入顶级部门");
                }
                newGroup.setParentCode(parentGroup.getCode());
                groupService.updateGroup(newGroup);
            } else {
                newGroup = new Group();
                newGroup.setName(groupName);
                newGroup.setDescription(description);
                newGroup.setLdapId(ldapId);
                newGroup.setCode(ldapId);
                newGroup.setParentCode(parentGroup.getCode());
                groupService.createGroup(newGroup);
            }
            if (map.get("nodes") != null) {
                List<Map> childrenList = (List<Map>) map.get("nodes");
                createLdapGroups(childrenList, newGroup, ldapGuidlist);
            }
        }
    }

    private Group getGroupByNameOrLdapId(String groupName, String ldapId) {
        Group newGroup = null;
        Group groupByLdapId = groupService.getGroupByLdapId(ldapId);
        Group groupByName = groupService.getGroupByName(groupName);
        if (groupByLdapId == null) {
            // ldapId对应部门不存在
            if (groupByName != null) {
                // name对应部门存在，返回name对应部门
                newGroup = groupByName;
            }
        } else {
            // ldapId对应部门存在
            if (groupByName != null) {
                String ldapIdByName = groupByName.getLdapId();
                String nameByLdap = groupByLdapId.getName();
                nameByLdap = TextUtils.unescapeXml(nameByLdap);
                // name对应部门与ldap对应部门均存在，此时两者必须为同一部门，否则出现冲突
                if (StringUtils.isNotBlank(ldapIdByName) && ldapIdByName.equals(ldapId) && groupName.equals(nameByLdap)) {
                    newGroup = groupByLdapId;
                } else {
                    log.error(
                            "getGroupByNameOrLdapId 部门冲突 groupByName name:{}, ldapId:{}  groupByLdapId name:{}, ldapId:{}",
                            new Object[]{groupName, ldapIdByName, nameByLdap, ldapId});
                    throw new IllegalArgumentException("导入失败! 部门冲突。部门1:" + groupName + ", 部门2:" + nameByLdap);
                }
            }

            newGroup = groupByLdapId;
        }

        return newGroup;
    }

    /**
     * 导入部门时同时导入用户
     *
     * @param adminUsername
     * @param adminPassword
     * @param groupDn
     * @throws LDAPException
     * @throws UserAmountExceededException
     */
    private void importGroupsWithUsers(String adminUsername, String adminPassword, String groupDn,
                                       boolean ignoreSameUsername, boolean forbidUser, Set<User> ldapUserSet) throws LDAPException,
            UserAmountExceededException {

        // 单个LDAP部门导入时，如果启用禁用，则会出现将其他LDAP部门所有用户均禁用掉
        boolean isFromSingleGroupImort = false;
        if (ldapUserSet == null) {
            isFromSingleGroupImort = true;
            ldapUserSet = new HashSet<User>();
        }

        Map<String, Map> users = this.loadLDAPGroupAllUsers(adminUsername, adminPassword, groupDn);
        // ldap域中用户集合
        Set<Map.Entry<String, Map>> entrySet = users.entrySet();
        for (Map.Entry<String, Map> entry : entrySet) {
            Map attrs = entry.getValue();
            log.debug("importGroupsWithUsers attrs={}", attrs);
            Group g = groupService.getGroupByName(parseNcpName(attrs));
            if (g == null) {
                g = groupService.getTopGroups().get(0);
                log.debug("部门 {} 不存在，使用默认顶级部门", attrs.get("department"));
            }

            String username = (String) attrs.get("username");
            String userAccountControl = (String) attrs.get("userAccountControl");
            String fullname = (String) attrs.get("fullname");
            String mobilePhone = (String) attrs.get("mobile");
            String officePhone = (String) attrs.get("officePhone");
            String homePhone = (String) attrs.get("homePhone");
            String email = (String) attrs.get("mail");
            User updateUser = userService.getUserByUsername(username);
            if (updateUser != null) {
                if (ignoreSameUsername) {
                    continue;
                }
                // 更新用户信息
                updateUser.setFullname(fullname);
                updateUser.setMobilePhone(mobilePhone);
                updateUser.setOfficePhone(officePhone);
                updateUser.setHomePhone(homePhone);
                updateUser.setEmail(email);

                if (LdapUtils.isDisabledUser(userAccountControl)) {
                    updateUser.setEnabled(false);
                } else {
                    updateUser.setEnabled(true);
                }

                userService.updateUser(updateUser);
                if (forbidUser && ldapUserSet != null) {
                    ldapUserSet.add(updateUser);
                }

                // 移除用户现有的组
                String ldapSyncNotRemoveGroupNames = StringUtils.defaultIfBlank(
                        scm.getProperty("ldap.syncNotRemoveGroupNames"), "");

                ZDJoinTableGroupUserUtils.deleteByUserId(updateUser.getId());

                // 将用户添加到ldap设置的部门
                ZDJoinTableGroupUserUtils.insert(g.getId(), updateUser.getId());
                log.debug("更新LDAP用户信息：{}", username);
            } else {
                // 创建新用户
                User newUser = new User();
                newUser.setUsername(username);
                newUser.setFullname(fullname);
                String password = ZDUtils.generateUserPassword(username, mobilePhone, officePhone, homePhone);
                newUser.setPassword(PasswordUtils.genPassHash(password));
                newUser.setMobilePhone(mobilePhone);
                newUser.setOfficePhone(officePhone);
                newUser.setHomePhone(homePhone);
                newUser.setEmail(email);

                if (LdapUtils.isDisabledUser(userAccountControl)) {
                    newUser.setEnabled(false);
                }

                try {
                    userService.createUser(newUser);
                    if (forbidUser && ldapUserSet != null) {
                        ldapUserSet.add(newUser);
                    }

                    ZDJoinTableGroupUserUtils.insert(g.getId(), newUser.getId());
                    log.debug("创建LDAP用户 {} ({})", attrs.get("fullname"), username);
                } catch (EntityAlreadyExistsException e) {
                    log.debug("同名LDAP用户 {} 已存在，跳过", username);
                    continue;
                } catch (UserAmountExceededException e) {
                    log.debug("已达授权用户数上限，跳过");
                    throw new UserAmountExceededException("已达授权用户数上限");
                }
            }
        }

        // 单部门导入时，不要使用禁用用户功能
        if (forbidUser && CollectionUtils.isNotEmpty(ldapUserSet) && isFromSingleGroupImort) {
            // 禁用系统中多出来的用户
            List<User> needForbidUserList = userService.getAllUsers();
            needForbidUserList.removeAll(ldapUserSet);
            if (CollectionUtils.isEmpty(needForbidUserList)) {
                return;
            }

            for (User needForbidUser : needForbidUserList) {
                if (UserGroupUtils.ADMIN_USERNAME.equals(needForbidUser.getUsername())) {
                    continue;
                }

                needForbidUser.setEnabled(false);
                userService.updateUser(needForbidUser);
            }
        }
    }

    /**
     * 删除ldap中不存在部门
     *
     * @param ldapGuidlist
     */
    private void deleteLdapGroups(List<String> ldapGuidlist) {
        List<Group> groupForLdapDelete = groupService.getGroupForLdapDelete();
        List<Group> waitDelete = new ArrayList<Group>();

        for (Group group : groupForLdapDelete) {
            String ldapId = group.getLdapId();
            if (ldapGuidlist.contains(ldapId)) {
                continue;
            }

            if (group.getUsers().size() != 0) {
                continue;
            }

            if (group.getChildren().size() > 0) {
                if (isGroupCanDeleteWithChildren(group, ldapGuidlist)) {
                    waitDelete.add(group);
                }
            } else {
                groupService.deleteGroup(group);
            }
        }

        List<Long> deleteGroupId = new ArrayList<Long>();
        for (Group waitDeleteGroup : waitDelete) {
            try {
                deleteGroupWithChildren(waitDeleteGroup, ldapGuidlist, deleteGroupId);
            } catch (Exception e) {
                log.error("deleteLdapGroups e", e);
            }
        }
    }

    private boolean deleteGroupWithChildren(Group group, List<String> ldapGuidlist, List<Long> deleteGroupId) {
        if (group == null) {
            return true;
        }

        if (deleteGroupId.contains(group.getId())) {
            return true;
        }

        String ldapId = group.getLdapId();
        if (ldapGuidlist.contains(ldapId)) {
            return false;
        }

        if (group.getUsers().size() > 0) {
            return false;
        }

        List<Group> children = group.getChildren();
        if (children.size() > 0) {
            for (Group childGroup : children) {
                boolean deleteGroupWithChildren = deleteGroupWithChildren(childGroup, ldapGuidlist, deleteGroupId);
                if (!deleteGroupWithChildren) {
                    return false;
                }
            }
        }

        deleteGroupId.add(group.getId());
        groupService.deleteGroup(group);
        return true;
    }

    /**
     * 该部门是否可以被删除
     *
     * @param group
     * @param ldapGuidlist
     * @return
     */
    private boolean isGroupCanDeleteWithChildren(Group group, List<String> ldapGuidlist) {
        List<Group> children = group.getChildren();
        if (children.size() > 0) {
            for (Group childGroup : children) {
                String ldapId = childGroup.getLdapId();
                if (ldapGuidlist.contains(ldapId)) {
                    return false;
                }

                if (childGroup.getUsers().size() != 0) {
                    return false;
                }

                if (childGroup.getChildren().size() > 0) {
                    if (!isGroupCanDeleteWithChildren(childGroup, ldapGuidlist)) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    @Override
    public void importLDAPUsersWithGroups(String adminUsername, String adminPassword, String[] ldapUsernames, String groupDn) throws LDAPException, UserAmountExceededException {
        Map<String, Map> users = this.loadLDAPGroupAllUsers(adminUsername, adminPassword, groupDn);

        if (ldapUsernames == null || ldapUsernames.length == 0) {
            log.debug("null usernames");
            return;
        }

        for (String username : ldapUsernames) {
            log.debug("准备创建LDAP用户：{}", username);
            Map<String, String> attrs = users.get(username);
            if (attrs == null) {
                log.debug("ldapUser: {} 不存在，跳过", username);
                continue;
            }

            Group g = groupService.getGroupByName(attrs.get("department"));
            if (g == null) {
                g = groupService.getTopGroups().get(0);
                log.debug("部门 {} 不存在，使用默认顶级部门", attrs.get("department"));
            }

            String fullname = attrs.get("fullname");
            String mobilePhone = attrs.get("mobile");
            String officePhone = attrs.get("officePhone");
            String homePhone = attrs.get("homePhone");
            String email = attrs.get("mail");
            User updateUser = userService.getUserByUsername(username);
            if (updateUser != null) {
                // 更新用户信息
                updateUser.setFullname(fullname);
                updateUser.setMobilePhone(mobilePhone);
                updateUser.setOfficePhone(officePhone);
                updateUser.setHomePhone(homePhone);
                updateUser.setEmail(email);
                userService.updateUser(updateUser);
                log.debug("更新LDAP用户信息：{}", username);
            } else {
                // 创建新用户
                User newUser = new User();
                newUser.setUsername(username);
                newUser.setFullname(fullname);
                String password = ZDUtils.generateUserPassword(username, mobilePhone, officePhone, homePhone);
                newUser.setPassword(PasswordUtils.genPassHash(password));
                newUser.setMobilePhone(mobilePhone);
                newUser.setOfficePhone(officePhone);
                newUser.setHomePhone(homePhone);
                newUser.setEmail(email);
                try {
                    userService.createUser(newUser);
                    ZDJoinTableGroupUserUtils.insert(g.getId(), newUser.getId());
                    log.debug("创建LDAP用户 {} ({})", attrs.get("fullname"), username);
                } catch (EntityAlreadyExistsException e) {
                    log.debug("同名LDAP用户 {} 已存在，跳过", username);
                    continue;
                } catch (UserAmountExceededException e) {
                    log.debug("已达授权用户数上限，跳过");
                    throw new UserAmountExceededException("已达授权用户数上限");
                }
            }
        }
    }

    @Override
    public String getUsernameWithComputerName(String computerName) throws LDAPException {
        String adminUsername = blowfish.decryptString(SystemConfigManager.getInstance().getProperty("ldap.au"));
        String adminPassword = blowfish.decryptString(SystemConfigManager.getInstance().getProperty("ldap.ap"));
        String ldapRoot = SystemConfigManager.getInstance().getProperty("ldap.root");

        LdapTools ldapTools = getLdapTools(adminUsername, adminPassword);

        String userDn = ldapTools.getUserDnByComputerName(ldapRoot, computerName);
        Attributes attrs = ldapTools.getLdapResult("user", SearchControls.OBJECT_SCOPE, userDn, null);

        String username = null;
        try {
            username = (String) attrs.get("sAMAccountName").get(0);
        } catch (NamingException e) {
            log.error("getUsernameWithComputerName attrs获取用户名失败", e);
        }

        ldapTools.releaseCtx();

        return username;
    }

    @Override
    public void syncLDAPGroupsAndUsers(String adminUsername, String adminPassword, boolean immediatelySync) throws LDAPException, UserAmountExceededException {
        long selectGroupId = NumberUtils.toLong(scm.getProperty("ldapSyncGroupId"));
        Group selectGroup = groupService.getGroupById(selectGroupId);
        String includeParentGroup = StringUtils.defaultIfEmpty(scm.getProperty("ldapSyncIncludeParentGroup"), "");
        boolean importUsers = scm.getBooleanProperty("ldapSyncImportUsers");
        boolean ignoreSameUsername = scm.getBooleanProperty("ldapSyncIgnoreSameUsername");
        boolean forbidUser = scm.getBooleanProperty("ldapSyncForbidUser");
        String groupDns = StringUtils.defaultIfEmpty(scm.getProperty("ldapSyncGroupDns"), "");
        String ldapSyncHour = scm.getProperty("ldap.syncHour");
        String ldapSyncMinute = scm.getProperty("ldap.syncMinute");
        String currentHour = ZDDateUtils.getCurrentHour();
        String currentMinute = ZDDateUtils.getCurrentMinute();
        String ladpSyncType = scm.getProperty("ldap.syncType");

        Set<User> ldapUserSet = new HashSet<User>();
        List<String> ldapGuidlist = new ArrayList<String>();
        if (immediatelySync || LdapUtils.LDAPSYNCTYPE_CRON.equals(ladpSyncType)) {
            if (StringUtils.isBlank(groupDns)) {
                log.warn("syncLDAPGroupsAndUsers 未选择要同步的部门");
                return;
            }

            for (String groupDn : groupDns.split(";")) {
                doImportLDAPGroups(adminUsername, adminPassword, groupDn, selectGroup, includeParentGroup, importUsers,
                        ignoreSameUsername, forbidUser, ldapUserSet, ldapGuidlist);
            }

            log.info("syncLDAPGroupsAndUsers LDAP组织人员同步完成");
        } else {
            if (StringUtils.isBlank(groupDns)) {
                log.warn("syncLDAPGroupsAndUsers 未选择要同步的部门");
                return;
            }

            if (NumberUtils.isDigits(ldapSyncHour)) {
                // 时和分同时指定
                if (currentHour.equals(ldapSyncHour) && currentMinute.equals(ldapSyncMinute)) {
                    for (String groupDn : groupDns.split(";")) {
                        doImportLDAPGroups(adminUsername, adminPassword, groupDn, selectGroup, includeParentGroup,
                                importUsers, ignoreSameUsername, forbidUser, ldapUserSet, ldapGuidlist);
                    }

                    log.info("syncLDAPGroupsAndUsers LDAP组织人员同步完成");
                }
            } else {
                // 只指定分
                if (currentMinute.equals(ldapSyncMinute)) {
                    for (String groupDn : groupDns.split(";")) {
                        doImportLDAPGroups(adminUsername, adminPassword, groupDn, selectGroup, includeParentGroup,
                                importUsers, ignoreSameUsername, forbidUser, ldapUserSet, ldapGuidlist);
                    }

                    log.info("syncLDAPGroupsAndUsers LDAP组织人员同步完成");
                }
            }
        }

        if (forbidUser && CollectionUtils.isNotEmpty(ldapUserSet)) {
            // 禁用系统中多出来的用户
            List<User> needForbidUserList = userService.getAllUsers();
            needForbidUserList.removeAll(ldapUserSet);
            if (CollectionUtils.isEmpty(needForbidUserList)) {
                return;
            }

            for (User needForbidUser : needForbidUserList) {
                if (UserGroupUtils.ADMIN_USERNAME.equals(needForbidUser.getUsername())) {
                    continue;
                }

                needForbidUser.setEnabled(false);
                userService.updateUser(needForbidUser);
            }
        }

        if (CollectionUtils.isNotEmpty(ldapGuidlist)) {
            // 删除部门
            if (PropsUtils.getBoolProps("ldapSyncWithDeleteGroup", false)) {
                this.deleteLdapGroups(ldapGuidlist);
            }
        }
    }

    /**
     * 获得ldapTools类
     *
     * @param adminUsername
     * @param adminPassword
     * @return
     * @throws LDAPException
     */
    private LdapTools getLdapTools(String adminUsername, String adminPassword) throws LDAPException {
        String ldapDomain = SystemConfigManager.getInstance().getProperty("ldap.domain");
        String ldapHost = SystemConfigManager.getInstance().getProperty("ldap.host");
        String ldapPort = SystemConfigManager.getInstance().getProperty("ldap.port");
        String ldapRoot = SystemConfigManager.getInstance().getProperty("ldap.root");

        if (StringUtils.isBlank(ldapDomain) || StringUtils.isBlank(ldapHost) || StringUtils.isBlank(ldapPort) ||
                StringUtils.isBlank(ldapRoot) || StringUtils.isBlank(adminUsername)) {
            throw new LDAPException("LDAP配置不完整，无法连接");
        }

        if (StringUtils.isBlank(adminPassword)) {
            adminPassword = "";
        }

        LdapTools ldapTools = new LdapTools(ldapHost, ldapPort, adminUsername, adminPassword, ldapDomain);

        log.debug("初始化LDAP链接成功，管理员用户名：{}", adminUsername + "@" + ldapDomain);

        return ldapTools;
    }
}