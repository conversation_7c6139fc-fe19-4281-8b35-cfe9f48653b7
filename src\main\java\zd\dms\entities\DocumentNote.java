package zd.dms.entities;

import jakarta.persistence.*;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.base.utils.system.SpringUtils;
import zd.dms.services.document.DocumentService;
import zd.dms.utils.TextUtils;

import java.util.Date;

/**
 * DocumentNote Domain Object
 *
 * <AUTHOR>
 * @version $Revision$, $Date$
 */
@Entity
@Table(name = "docnote")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class DocumentNote extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = 7278484277354258018L;

    /**
     * 作者用户名
     */
    @Column(nullable = false)
    private String writer;

    /**
     * 作者名，使用全名
     */
    private String writerFullname;

    /**
     * 内容
     */
    @Column(length = Length.LOB_DEFAULT)
    private String note;

    @Index(name = "i_dnote_docId")
    private String docId;

    /**
     * 创建时间
     */
    @Index(name = "i_dnote_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 默认构造器
     */
    public DocumentNote() {
        super();
        creationDate = new Date();
    }

    /**
     * @see Object#equals(Object)
     */
    @Override
    public boolean equals(Object o) {
        if (!(o instanceof DocumentNote)) {
            return false;
        }

        final DocumentNote dn = (DocumentNote) o;

        return new EqualsBuilder().appendSuper(super.equals(dn)).append(writer, dn.getWriter())
                .append(note, dn.getNote()).append(docId, dn.getDocument()).isEquals();
    }

    /**
     * @see Object#hashCode()
     */
    @Override
    public int hashCode() {
        return new HashCodeBuilder().appendSuper(super.hashCode()).append(writer).append(note).append(docId)
                .toHashCode();
    }

    public Document getDocument() {
        return SpringUtils.getBean(DocumentService.class).getDocumentById(docId);
    }

    public String getWriter() {
        return writer;
    }

    public void setWriter(String writer) {
        this.writer = writer;
    }

    public String getNote() {
        return TextUtils.escapeXml(note);
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getWriterFullname() {
        return writerFullname;
    }

    public void setWriterFullname(String writerFullname) {
        this.writerFullname = writerFullname;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
