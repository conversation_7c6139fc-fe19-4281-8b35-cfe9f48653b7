package zd.dms.repositories.workflow.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.User;
import zd.dms.repositories.workflow.CarbonCopyRepository;
import zd.dms.utils.repositories.PredicateUtils;
import zd.dms.utils.repositories.SpecTools;
import zd.dms.workflow.entities.SSCarbonCopy;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class CarbonCopyRepositoryDaoImpl extends BaseRepositoryDaoImpl<SSCarbonCopy, String> implements CarbonCopyRepository {

    public CarbonCopyRepositoryDaoImpl(Class<SSCarbonCopy> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public Page getCarbonCopyByPage(String keywords, String username, String resourceType, String pdType, int page, int pageSize) {

        Specification<SSCarbonCopy> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSCarbonCopy> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("assignee", username);
            specTools.eq("resourceType", resourceType);

            String tempRecPiType = StringUtils.trim(pdType);
            if (StringUtils.isNotBlank(tempRecPiType) && !"all".equals(tempRecPiType)) {
                if ("lend".equalsIgnoreCase(tempRecPiType)) {
                    specTools.or(PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendElectric"), PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendPaper"));
                } else {
                    specTools.equal("pdType", tempRecPiType);
                }
            }

            if (StringUtils.isNotBlank(keywords)) {
                Predicate resourceNameLike = PredicateUtils.like(root, criteriaBuilder, "resourceName", keywords);
                Predicate piNameLike = PredicateUtils.like(root, criteriaBuilder, "piName", keywords);
                Predicate piCreatorLike = PredicateUtils.like(root, criteriaBuilder, "piCreator", keywords);
                Predicate piCreatorFullnameLike = PredicateUtils.like(root, criteriaBuilder, "piCreatorFullname", keywords);

                specTools.or(resourceNameLike, piNameLike, piCreatorLike, piCreatorFullnameLike);
            }

            specTools.desc("endDate");

            return specTools.getRestriction();
        };

        return findAll(spec, page, pageSize);
    }

    @Override
    public int getUncompeletedCarbonCopyCount(String username) {
        String queryString = "select count(*) from wfcc where status != 100 and status != 4 and status != 5 and assignee='" +
                username + "'";

        return NumberUtils.toInt(String.valueOf(findObject(queryString)));
    }

    @Override
    public int getUncompeletedCarbonCopyCount(String username, String resTypeRec) {
        String queryString = "select count(*) from wfcc where status != 100 and status != 4 and assignee='" + username +
                "' and resourceType = '" + resTypeRec + "'";

        return NumberUtils.toInt(String.valueOf(findObject(queryString)));
    }

    @Override
    public List<SSCarbonCopy> searchCarbonCopy(String keywords, User currentUser, int status, String resType) {
        if (StringUtils.isBlank(keywords) || currentUser == null) {
            return new ArrayList<SSCarbonCopy>();
        }

        String hql = "from SSCarbonCopy p where assignee = '" + currentUser.getUsername() + "' and (resourceName like '%" +
                keywords + "%' OR resourceNameLong like '%" + keywords + "%') and status = " + status +
                " and resourceType='" + resType + "'";
        return findAll(hql);
    }
}
