package zd.dms.services.docsig;

import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.dms.entities.DocumentSig;
import zd.dms.entities.DocumentSignature;
import zd.dms.entities.User;

import java.io.File;
import java.io.IOException;
import java.util.List;

@Transactional
public interface DocumentSignatureService extends BaseJpaService<DocumentSignature, String> {

    DocumentSignature getDocSigsByBelongUsername(String username);

    List<DocumentSignature> getAllDocSigsByExt(String extension);

    void createDocSig(DocumentSignature docSig, File uploadFile) throws IOException;

    @Transactional(readOnly = true)
    DocumentSignature getSigByName(String name);

    @Transactional(readOnly = true)
    DocumentSignature getSigById(String id);

    @Transactional(readOnly = true)
    List<DocumentSignature> getAllDocSigs();

    void updateDocSig(DocumentSignature docSig, File uploadFile) throws IOException;

    void deleteDocSig(DocumentSignature docSig) throws IOException;

    void deleteDocSigById(String id) throws IOException;

    @Transactional(readOnly = true)
    List<DocumentSignature> getPermitedSigs(User user);

    @Transactional(readOnly = true)
    List<DocumentSignature> getPermitedSigs(User user, String extension);

    @Transactional(readOnly = true)
    boolean isValidSig(String filename);

    @Transactional(readOnly = true)
    boolean checkPassword(DocumentSignature sig, String password);

    @Transactional(readOnly = true)
    List<DocumentSig> getSigsByDocId(String docId);

    void deleteSigsByDocId(String docId);

    void createDocSig(DocumentSig sig);
}
