package zd.dms.entities;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.context.UserContextHolder;
import zd.base.entities.AbstractEntity;
import zd.base.entities.PropertyAware;
import zd.base.utils.system.SpringUtils;
import zd.dms.io.StorageUtils;
import zd.dms.io.ZDIOUtils;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.document.DocumentUtils;
import zd.dms.services.document.FolderService;
import zd.dms.services.pdf.PDFUtils;
import zd.dms.services.user.UserService;
import zd.dms.utils.DocPropUtils;
import zd.dms.utils.OfficeUtils;
import zd.dms.utils.PropertyUtils;
import zd.dms.utils.TextUtils;
import zd.dms.utils.did.DynamicDocIdUtils;
import zd.dms.workflow.engine.ProcessService;
import zd.dms.workflow.entities.SSProcessInstance;
import zd.record.entities.DataStruct;
import zd.record.entities.RecFolder;
import zd.record.service.datastruct.DataStructService;
import zd.record.service.folder.RecFolderService;
import zd.record.utils.RecordDBUtils;

import java.text.DecimalFormat;
import java.util.*;

/**
 * Document Domain Object
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "tdms_doc")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Document extends AbstractEntity implements PropertyAware {

    private static Logger log = LoggerFactory.getLogger(Document.class);

    public static final String[] parseJsonFieldName = new String[]{"filename"};

    public static final String[] parseJsonMethodName = new String[]{"getDisplayFolderWithoutLink"};

    public static final Map<String, String> DEFAULT_COL_MAP = new HashMap<String, String>();

    static {
        DEFAULT_COL_MAP.put("文件名", "filename");
        DEFAULT_COL_MAP.put("类型", "extension");
        DEFAULT_COL_MAP.put("大小", "fileSize");
        DEFAULT_COL_MAP.put("更新时间", "modifiedDate");
        DEFAULT_COL_MAP.put("创建时间", "creationDate");
        DEFAULT_COL_MAP.put("创建人", "creatorFullname");
        DEFAULT_COL_MAP.put("更新人", "updaterFullname");
        DEFAULT_COL_MAP.put("修订版", "newestVersion");
        DEFAULT_COL_MAP.put("版本", "docVersion");
        DEFAULT_COL_MAP.put("点击数", "clickCount");
        DEFAULT_COL_MAP.put("编号", "serialNumber");
        DEFAULT_COL_MAP.put("排序", "numIndex");
    }

    /**
     * serial
     */
    private static final long serialVersionUID = 7687773923069464873L;

    /**
     * Decimal Format
     */
    private static final DecimalFormat df = new DecimalFormat("#####0.00");

    /**
     * 描述
     */
    private String description;

    /**
     * 文件名
     */
    @Column(nullable = false)
    @Index(name = "i_doc_filename")
    private String filename;

    @Column(length = Length.LOB_DEFAULT)
    private String filenameBase64;

    @Transient
    private String filenameBase64Decoded;

    /**
     * 后缀
     */
    @Index(name = "i_doc_ext")
    private String extension;

    /**
     * 流水号
     */
    @Column(unique = true)
    private String serialNumber;

    /**
     * 版本号
     */
    private String docVersion;

    /**
     * 文件内容
     */
    @Column(length = Length.LOB_DEFAULT)
    private String content;

    /**
     * 文档属性
     */
    @Column(length = Length.LOB_DEFAULT)
    private String docProps;

    /**
     * file hash
     */
    private String hash;

    /**
     * 文件尺寸
     */
    private long fileSize;

    /**
     * 文档排序
     */
    @Index(name = "i_doc_numindex")
    private Integer numIndex;

    /**
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * 是否正在修改
     */
    @Index(name = "i_doc_editing")
    private boolean editing;

    private boolean fromScan;

    /**
     * 正在被谁编辑
     */
    @Index(name = "i_doc_editingby")
    private String editingBy;

    /**
     * 正在被谁编辑全名
     */
    private String editingByFullname;

    @Transient
    private Map<String, String> propertiesMap;

    @Column(length = Length.LOB_DEFAULT)
    private String properties;

    /**
     * 文档创建者
     */
    private String creatorFullname;

    /**
     * 更新者
     */
    private String updaterFullname;

    /**
     * 是否已删除
     */
    @Index(name = "i_doc_deleted")
    private boolean deleted;

    /**
     * 是否处于锁定状态
     */
    @Index(name = "i_doc_lock")
    private boolean locked;

    /**
     * 锁定时间
     */
    private Date lockDate;

    /**
     * 锁定者用户名
     */
    private String lockedBy;

    /**
     * 锁定者全名
     */
    private String lockedByFullname;

    /**
     * 删除人用户名
     */
    @Index(name = "i_doc_deletedby")
    private String deletedBy;

    /**
     * 删除人全名
     */
    private String deletedByFullname;

    /**
     * 删除时间
     */
    private Date deleteDate;

    /**
     * 所属流程
     */
    @Index(name = "i_doc_piid")
    private String piId;

    /**
     * 目录id
     */
    private Long folderId;

    /**
     * 文件编码
     */
    private String encoding;

    /**
     * 最新版本
     */
    private int newestVersion;

    /**
     * 点击数
     */
    private int clickCount;

    private String webOfficeRndKey;

    @Transient
    private SSProcessInstance processInstance;

    /**
     * 打印份数限制
     */
    private Integer printLimit;

    /**
     * 存储分区
     */
    private String storage;

    @Transient
    private String outLinkId;

    /**
     * 自定义时间1
     */
    protected Date customDate1;

    /**
     * 自定义时间2
     */
    protected Date customDate2;

    /**
     * 数据ID和表
     */
    @Column(length = Length.LOB_DEFAULT)
    private String recordIdAndTable;

    @Transient
    @Setter
    private boolean attachDoc;

    @Getter
    @Setter
    private long attachRecId;

    @Getter
    @Setter
    private String attachRecTableName;

    /**
     * 创建时间
     */
    @Index(name = "i_doc_cd")
    @Column(nullable = false)
    private Date creationDate;

    @Index(name = "i_doc_stg")
    private String docStage;

    // TODO ngcopy
    /*@Transient
    private DocPublishPlan docPublishPlan;

    @Transient
    private DocPublishVersion docPublishVersion;*/

    private String pubInitialVerNum;

    // 记录发布版号，用于培训计划文档列表展示对应发布版本
    @Transient
    private String publishVersionNumber;

    // 当流程设置自动归档后，会清除piId的值，tempPiId会记录流程的ID
    @Transient
    private String tempPiId;

    /**
     * 是否正在进行多人编辑
     */
    @Index(name = "i_doc_editingOOS")
    private Boolean editingOOS;

    /**
     * 开启多人协同编辑的用户名
     */
    @Index(name = "i_doc_editingOOSby")
    private String editingOOSBy;

    /**
     * 开启多人协同编辑的用户姓名
     */
    private String editingOOSByFullname;

    /**
     * 协同编辑中的所有人员用户名，用逗号分隔
     */
    @Column(length = Length.LOB_DEFAULT)
    private String editingOOSAllUsername;

    /**
     * 开启协同编辑时记录进入的officeserver的ip，保证协同编辑同一份文档时所有用户进入officeserver集群的同一个服务器
     */
    private String officeServerIp;

    @Transient
    private Map<String, String> docAdvPropMap;

    @Transient
    private List<String> editingFullnameAndUsernameList;

    @Transient
    private String finalMyPermissions;

    @Transient
    private Folder folder;

    /**
     * 默认构造器
     */
    public Document() {
        super();
        deleted = false;
        newestVersion = SystemConfigManager.getInstance().getDefaultDocRevision();
        editing = false;
        editingOOS = false;
        clickCount = 0;
        fromScan = false;
        printLimit = -1;
        storage = StorageUtils.getActiveStorageId();
        numIndex = 9999;
        creationDate = new Date();

        SystemConfigManager scm = SystemConfigManager.getInstance();
        docVersion = StringUtils.defaultIfEmpty(scm.getProperty("defaultDocVersion"), "1.0");

        docStage = SystemConfigManager.getInstance().getDefaultDocStage();
    }

    public Folder getFolder() {
        if (folder == null) {
            FolderService folderService = SpringUtils.getBean(FolderService.class);
            folder = folderService.getById(folderId);
        }

        return folder;
    }

    public boolean isAttachDoc() {
        if (!attachDoc) {
            return attachRecId > 0 || StringUtils.isNotBlank(attachRecTableName);
        }

        return attachDoc;
    }

    public String getEditingFullnameAndUsernameStr() {
        List<String> editingFullnameAndUsernameList = getEditingFullnameAndUsernameList();
        if (CollectionUtils.isEmpty(editingFullnameAndUsernameList)) {
            return "";
        }

        return StringUtils.join(editingFullnameAndUsernameList, ", ");
    }

    public List<String> getEditingFullnameAndUsernameList() {
        // TODO ngcopy
        /*if (editingFullnameAndUsernameList == null) {
            editingFullnameAndUsernameList = ZDWebOfficeUtils.getEditingFullnameAndUsernameList(id);
        }

        return editingFullnameAndUsernameList;*/

        return null;
    }

    public synchronized Map<String, String> getDocAdvPropMap() {
        if (docAdvPropMap == null) {
            docAdvPropMap = new HashMap<String, String>();
            List<Map<String, Object>> docProps;
            try {
                docProps = DocPropUtils.getDocPropsMap(this, null);
                docAdvPropMap = DocPropUtils.getDocPropValues(docProps);
            } catch (Exception e) {
            }
        }

        return docAdvPropMap;
    }

    public String getWebOfficeDocumentType() {
        /*return ZDWebOfficeUtils.getDocumentType(this);*/
        return null;
    }

    public String getSummaryEditingOOSUserFullname() {
        List<String> fullnameList = new ArrayList<String>();
        UserService userService = SpringUtils.getBean(UserService.class);

        int userCount = 0;
        if (StringUtils.isNotBlank(editingOOSAllUsername)) {
            String[] usernameArray = editingOOSAllUsername.split(",");

            for (String username : usernameArray) {
                User user = userService.getUserByUsername(username);
                if (user != null) {
                    fullnameList.add(user.getFullname());
                    userCount++;
                }

                if (userCount == 5) {
                    break;
                }
            }
        }

        if (userCount == 5) {
            return StringUtils.join(fullnameList, " ") + "等";
        } else {
            return StringUtils.join(fullnameList, " ");
        }
    }

    public String getAllEditingOOSUserFullname() {
        List<String> fullnameList = new ArrayList<String>();

        UserService userService = SpringUtils.getBean(UserService.class);
        if (StringUtils.isNotBlank(editingOOSAllUsername)) {
            String[] usernameArray = editingOOSAllUsername.split(",");
            for (String username : usernameArray) {
                User user = userService.getUserByUsername(username);
                if (user != null) {
                    fullnameList.add(user.getFullname());
                }
            }
        }

        return StringUtils.join(fullnameList, " ");
    }

    public List<String> getAllEditingOOSUsername() {
        List<String> result = new ArrayList<String>();
        if (StringUtils.isNotBlank(editingOOSAllUsername)) {
            String[] usernameArray = editingOOSAllUsername.split(",");
            for (String username : usernameArray) {
                result.add(username);
            }
        }

        return result;
    }

    /**
     * 获取文档打印限制数，包含目录中设置
     *
     * @return
     */
    public long getPrintLimitWithFolder() {
        int docPrintLimit = getPrintLimit();
        if (docPrintLimit == -1) {
            return DocumentUtils.getPrintLimitWithFolder(getFolder());
        }

        return docPrintLimit;
    }

    public String getMyPermissions() {
        if (finalMyPermissions != null) {
            return finalMyPermissions;
        }

        Folder folder = getFolder();
        if (folder != null) {
            finalMyPermissions = folder.getMyPermissionsWithInit();
        } else {
            finalMyPermissions = "";
        }

        return finalMyPermissions;
    }

    public boolean isBatchPrintable() {
        return "doc".equals(extension) || "xls".equals(extension) || "ppt".equals(extension) ||
                "docx".equals(extension) || "xlsx".equals(extension) || "pptx".equals(extension) ||
                "pdf".equals(extension) || "tif".equals(extension);
    }

    public List<String> getRecordIdAndTableList() {
        log.debug("recordIdAndTable: {}", recordIdAndTable);
        List<String> result = new LinkedList<String>();
        if (StringUtils.isNotBlank(recordIdAndTable)) {
            String[] ids = recordIdAndTable.split(":");
            CollectionUtils.addAll(result, ids);
        }

        return result;
    }

    public boolean isHasMaterial() {
        if (StringUtils.contains(recordIdAndTable, SystemConfigManager.getInstance().getProperty("sapBomTableName"))) {
            return true;
        }

        return false;
    }

    public List<Map<String, Object>> getMaterialRecords() {
        List<Map<String, Object>> results = new LinkedList<Map<String, Object>>();
        for (String s : getRecordIdAndTableList()) {
            if (StringUtils.isBlank(s) || s.indexOf(",") < 0) {
                continue;
            }

            String[] arr = s.split(",");
            if (arr == null || arr.length < 2) {
                continue;
            }

            String tableName = arr[1];
            if (!tableName.equals(SystemConfigManager.getInstance().getProperty("sapBomTableName"))) {
                continue;
            }

            List<String> cols = new ArrayList<String>();
            cols.add("编号");
            cols.add("物料号");
            cols.add("物料名称");
            cols.add("附件");
            Map<String, Object> recordData = RecordDBUtils.getRecordByTableName(NumberUtils.toLong(arr[0]), cols,
                    arr[1]);

            if (recordData == null || recordData.isEmpty() || recordData.size() == 0) {
                continue;
            }

            results.add(recordData);
        }

        return results;
    }

    public List<Map<String, Object>> getRecords() {
        List<Map<String, Object>> results = new LinkedList<Map<String, Object>>();

        DataStructService dataStructService = SpringUtils.getBean(DataStructService.class);
        RecFolderService recFolderService = SpringUtils.getBean(RecFolderService.class);

        for (String s : getRecordIdAndTableList()) {
            if (StringUtils.isBlank(s) || s.indexOf(",") < 0) {
                continue;
            }

            String[] arr = s.split(",");
            if (arr == null || arr.length < 2) {
                continue;
            }

            DataStruct ds = dataStructService.getDataStructByName(arr[1]);
            Map<String, Object> recordData = RecordDBUtils.getRecordByTableName(NumberUtils.toLong(arr[0]), null,
                    arr[1]);

            if (recordData == null || recordData.isEmpty() || recordData.size() == 0) {
                continue;
            }

            // 如果有主数据
            long parentId = NumberUtils.toLong(String.valueOf(recordData.get("主数据ID")), 0);
            if (parentId > 0) {
                long folderId = NumberUtils.toLong(String.valueOf(recordData.get("目录ID")));
                RecFolder folder = recFolderService.getById(folderId);
                if (folder != null && folder.isHasDataTypeParent()) {
                    List<String> cols2 = new ArrayList<String>();
                    cols2.add("编号");
                    Map<String, Object> parentData = RecordDBUtils.getRecord(parentId, cols2, folder.getParent());
                    if (parentData != null && !parentData.isEmpty()) {
                        recordData.put("parentData", parentData);
                        recordData.put("parentFolder", folder.getParent());
                    }
                }
            }

            if (recordData != null && recordData.size() > 0) {
                if (ds != null) {
                    recordData.put("ECMDataStruct", ds);
                }
                results.add(recordData);
            }
        }

        return results;
    }

    public String getFilename() {
        if (StringUtils.isNotBlank(filenameBase64Decoded)) {
            return filenameBase64Decoded;
        }

        if (StringUtils.isNotBlank(filenameBase64)) {
            filenameBase64Decoded = TextUtils.decodeBase64(filenameBase64);
            return filenameBase64Decoded;
        }

        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
        this.filenameBase64 = TextUtils.encodeBase64(filename);
        this.filenameBase64Decoded = "";
    }

    public String getFilenameIn30() {
        if (StringUtils.isBlank(FilenameUtils.getBaseName(getFilename())) ||
                FilenameUtils.getBaseName(getFilename()).length() < 30) {
            return getFilename();
        }

        String result = FilenameUtils.getBaseName(getFilename()).substring(0, 30);
        if (StringUtils.isNotBlank(extension)) {
            result = result + "." + extension;
        }

        return result;
    }

    public String getSummaryFilename15() {
        if (StringUtils.isBlank(getFilename()) || getFilename().length() < 15) {
            return getFilename();
        }

        return getFilename().substring(0, 8) + "..." + getFilename().substring(getFilename().length() - 8);
    }

    public String getSummaryFilename30() {
        if (StringUtils.isBlank(getFilename()) || getFilename().length() < 30) {
            return getFilename();
        }

        return getFilename().substring(0, 15) + "..." + getFilename().substring(getFilename().length() - 12);
    }

    public String getSummaryFilename() {
		/*
		if (StringUtils.isBlank(filename) || filename.length() < 50) {
			return filename;
		}

		return filename.substring(0, 25) + "..." + filename.substring(filename.length() - 22);
		*/
        int docListFilenameLength = SystemConfigManager.getInstance().getIntProperty("docListFilenameLength");
        if (docListFilenameLength < 50) {
            docListFilenameLength = 50;
        }

        return StringUtils.abbreviateMiddle(getFilename(), "...", docListFilenameLength);
    }

    public String getHtmlFilename() {
        if (StringUtils.isBlank(getFilename()) || getFilename().length() < 50) {
            return getFilename();
        }

        List<String> filenameArray = TextUtils.splitEqually(getFilename(), 50);
        return StringUtils.join(filenameArray, "<br/>");
    }

    public String getHtmlFilename30() {
        if (StringUtils.isBlank(getFilename()) || getFilename().length() < 30) {
            return getFilename();
        }

        List<String> filenameArray = TextUtils.splitEqually(getFilename(), 30);
        return StringUtils.join(filenameArray, "<br/>");
    }

    public Set<Map<String, String>> getDocPropList() {
        Map<String, Map<String, String>> result = getDocPropMap();

        Set<Map<String, String>> sortedDocPropList = new TreeSet<Map<String, String>>(
                new Comparator<Map<String, String>>() {
                    public int compare(Map<String, String> o1, Map<String, String> o2) {
                        int index1 = 0;
                        int index2 = 0;
                        if (o1.containsKey("index")) {
                            index1 = NumberUtils.toInt(o1.get("index"), 0);
                        }
                        if (o2.containsKey("index")) {
                            index2 = NumberUtils.toInt(o2.get("index"), 0);
                        }

                        if (index1 < index2) {
                            return -1;
                        }
                        return 1;
                    }
                });

        sortedDocPropList.addAll(result.values());

        return sortedDocPropList;
    }

    public Map<String, Map<String, String>> getDocPropMap() {
        Map<String, Map<String, String>> result = new LinkedHashMap<String, Map<String, String>>();
        if (StringUtils.isBlank(docProps)) {
            return result;
        }

        log.debug("getDocPropMap docProps: {}", docProps);
        JSONArray ja = JSON.parseArray(docProps);
        for (int i = 0; i < ja.size(); i++) {
            JSONObject jo = ja.getJSONObject(i);
            if (jo == null) {
                log.debug("getDocPropMap: null jo");
                continue;
            }
            // log.debug("jo: {}", jo);
            try {
                String mainKey = jo.getString("key");
                if (StringUtils.isBlank(mainKey)) {
                    log.debug("getDocPropMap: empty mainKey");
                    continue;
                }

                Map<String, String> keyValueMap = new LinkedHashMap<String, String>();
                for (Object keyObj : jo.keySet()) {
                    String key = (String) keyObj;
                    String value = TextUtils.escapeXml(jo.getString(key));
                    keyValueMap.put(key, value);
                }
                result.put(mainKey, keyValueMap);
            } catch (Throwable t) {
                log.debug("JSONObject parse ex", t);
            }
        }

        return result;
    }

    public void removeDocProp(String mainKey) {
        Map<String, Map<String, String>> docPropMap = getDocPropMap();
        if (docPropMap.containsKey(mainKey)) {
            docPropMap.remove(mainKey);
        }

        docProps = JSON.toJSONString(docPropMap.values());
        log.debug("docProps for {} : {}", getFilename(), docProps);
    }

    public void setDocProp(String mainKey, String key, String value, String index) {
        Map<String, Map<String, String>> docPropMap = getDocPropMap();
        if (docPropMap.containsKey(mainKey)) {
            docPropMap.get(mainKey).put("key", key);
            docPropMap.get(mainKey).put("value", value);
            docPropMap.get(mainKey).put("index", index);
        } else {
            Map<String, String> keyValueMap = new HashMap<String, String>();
            keyValueMap.put("key", key);
            keyValueMap.put("value", value);
            keyValueMap.put("index", index);
            docPropMap.put(mainKey, keyValueMap);
        }

        docProps = JSON.toJSONString(docPropMap.values());
        log.debug("docProps for {} : {}", getFilename(), docProps);
    }

    public String getDocProp(String mainKey, String key) {
        Map<String, Map<String, String>> docPropMap = getDocPropMap();
        if (docPropMap.containsKey(mainKey)) {
            return docPropMap.get(mainKey).get(key);
        }

        return null;
    }

    /**
     * 获取动态id
     *
     * @return 动态id
     */
    public String getDid() {
        return DynamicDocIdUtils.generateDocDynamicId(this.getRawId(), 0);
    }

    /**
     * 获取动态id，限时20秒
     *
     * @return 动态id，限时20秒
     */
    public String getDid10Secs() {
        return DynamicDocIdUtils.generateDocDynamicId(this.getRawId(), 20);
    }

    public boolean isHasPdfWatermark() {
        if ("text".equals(getProperty("watermarked")) || "image".equals(getProperty("watermarked"))) {
            return true;
        }

        return false;
    }

    public boolean isZip() {
        return "zip".equals(getExtension());
    }

    public boolean isPdf() {
        return "pdf".equals(getExtension());
    }

    public boolean isOfd() {
        return "ofd".equalsIgnoreCase(getExtension());
    }

    public boolean isWord() {
        return "doc".equals(getExtension()) || "docx".equals(getExtension());
    }

    public boolean isPicture() {
        boolean result = false;
        if ("jpg".equalsIgnoreCase(getExtension()) || "png".equalsIgnoreCase(getExtension()) ||
                "jpeg".equalsIgnoreCase(getExtension()) || "bmp".equalsIgnoreCase(getExtension()) ||
                "psd".equalsIgnoreCase(getExtension()) || "tif".equalsIgnoreCase(getExtension()) ||
                "gif".equalsIgnoreCase(getExtension()) || "cdr".equalsIgnoreCase(getExtension()) ||
                "ai".equalsIgnoreCase(getExtension()) || "raw".equalsIgnoreCase(getExtension())) {
            result = true;
        }
        return result;
    }

    /**
     * 判断word版本是否为03
     *
     * @return
     */
    public boolean isWord03() {
        return "doc".equals(getExtension());
    }

    /**
     * 判断word版本是否为07
     *
     * @return
     */
    public boolean isWord07() {
        return "docx".equals(getExtension());
    }

    public boolean isExcel() {
        return "xls".equals(getExtension()) || "xlsx".equals(getExtension());
    }

    public boolean isWordOrExcel() {
        return "doc".equals(getExtension()) || "docx".equals(getExtension()) || "xls".equals(getExtension()) ||
                "xlsx".equals(getExtension());
    }

    public boolean isPdfSwfEnabled() {
        return SystemConfigManager.getInstance().getBooleanProperty(SystemConfigManager.PDF_SWF_ENABLED_KEY);
    }

    public boolean isSwfExists() {
        if ("pdf".equals(getExtension())) {
            return PDFUtils.isSwfExists(this);
        }

        return false;
    }

    public boolean isOnWorkflow() {
        if (getProcessInstance() != null && !getProcessInstance().isEnded()) {
            return true;
        }

        return false;
    }

    public boolean isHasWorkflowEditPermission(String username) {
        return isHasWorkflowEditPermission(username, "");
    }

    public boolean isHasWorkflowEditPermission(String username, String from) {
        SSProcessInstance pi = getProcessInstance();
        if (pi != null) {
            // 如果流程已结束，就不再限制权限
            if (pi.isEnded()) {
                return true;
            }

          /*  if (pi.getCannotEditDoc() && (StringUtils.isBlank(from) || !"workflow".equals(from))) {
                return false;
            }*/

           /* SSTaskNode activeNode = pi.getActiveNode();
            if (activeNode != null && activeNode.getType() == SSTaskNode.TYPE_SHENPI) {
                if (activeNode.getAssigneeList().contains(username)) {
                    return true;
                }
            }*/

            return false;
        } else {
            return true;
        }
    }

    public ProcessService getProcessService() {
        return SpringUtils.getBean(ProcessService.class);
    }

    public SSProcessInstance getProcessInstance() {
        if (StringUtils.isBlank(piId)) {
            return null;
        }

        if (processInstance == null) {
            processInstance = getProcessService().getProcessInstance(piId);
        }

        return processInstance;
    }

    public boolean isLockedByMe() {
        if (!isLocked()) {
            return true;
        }

        User currentUser = UserContextHolder.getUser();
        if (currentUser == null) {
            log.debug("isLockedByMe: null context user");
            return false;
        }

        if (currentUser.getUsername().equals(lockedBy)) {
            return true;
        } else {
            return false;
        }
    }

    public boolean isHasThumbnail() {
        return BooleanUtils.toBoolean(getProperty("hasThumbnail")) &&
                ZDIOUtils.getThumbnailFile(this, this.getNewestVersion()) != null &&
                ZDIOUtils.getThumbnailFile(this, this.getNewestVersion()).exists();
    }

    public void setHasThumbnail(boolean value) {
        setProperty("hasThumbnail", String.valueOf(value));
    }

    public String getRawId() {
        if (id.startsWith("link_")) {
            return id.substring(5);
        } else {
            return id;
        }
    }

    public boolean isNeedThumbnail() {
        return true;
		/*
		return "eps".equalsIgnoreCase(extension) ||
				"ai".equalsIgnoreCase(extension) ||
				"cdr".equalsIgnoreCase(extension) ||
				"tif".equalsIgnoreCase(extension) ||
				"ma".equalsIgnoreCase(extension) ||
				"mb".equalsIgnoreCase(extension) ||
				"max".equalsIgnoreCase(extension) ||
				"exb".equalsIgnoreCase(extension) || // CAXA
				"psd".equalsIgnoreCase(extension);
		*/
    }

    public boolean isOfdFile() {
        return "ofd".equalsIgnoreCase(extension);
    }

    public boolean isOfficeFile() {
        return OfficeUtils.isOfficeFile(extension);
    }

    public boolean isOffice03Or07File() {
        return isOffice03File() || isOffice07File();
    }

    public boolean isOffice07File() {
        return "docx".equalsIgnoreCase(extension) || "xlsx".equalsIgnoreCase(extension) ||
                "pptx".equalsIgnoreCase(extension);
    }

    public boolean isOffice03File() {
        return "doc".equalsIgnoreCase(extension) || "xls".equalsIgnoreCase(extension) ||
                "ppt".equalsIgnoreCase(extension);
    }

    public boolean isOfficeFileOutLink() {
        return OfficeUtils.isOfficeFileOutLink(extension);
    }

    public boolean isTextFileWithoutTxt() {
        return OfficeUtils.isTxtFile(extension) && !"txt".equalsIgnoreCase(extension);
    }

    public boolean isTextFile() {
        return OfficeUtils.isTxtFile(extension);
    }

    public boolean isHtmlFile() {
        return "html".equals(extension) || "htm".equals(extension);
    }

    public boolean isEditable() {
        return "doc".equalsIgnoreCase(extension) || "docx".equalsIgnoreCase(extension) ||
                "xls".equalsIgnoreCase(extension) || "xlsx".equalsIgnoreCase(extension) ||
                "ppt".equalsIgnoreCase(extension) || "pptx".equalsIgnoreCase(extension) ||
                "wps".equalsIgnoreCase(extension) || "vsd".equalsIgnoreCase(extension) ||
                OfficeUtils.isTxtFile(extension);
    }

    public boolean isCadFile() {
        return OfficeUtils.isCadFile(getFilename(), extension);
    }

    public boolean isSolidWorksCadFile() {
        return OfficeUtils.isSolidWorksCadFile(getFilename(), extension);
    }

    public boolean isAutovueCadFile() {
        return OfficeUtils.isAutovueCadFile(getFilename(), extension);
    }

    public String getCadExtension() {
        if (getFilename().indexOf(".prt.") > 0) {
            return "prt";
        } else {
            return getExtension();
        }
    }

    public boolean isImageFile() {
        return OfficeUtils.isImageFile(extension);
    }

    public boolean isAudioFile() {
        return "mp3".equalsIgnoreCase(extension) || "wav".equalsIgnoreCase(extension) ||
                "wma".equalsIgnoreCase(extension);
    }

    public boolean isVideoFile() {
        return "flv".equalsIgnoreCase(extension) || "swf".equalsIgnoreCase(extension) ||
                "mpg".equalsIgnoreCase(extension) || "avi".equalsIgnoreCase(extension) ||
                "mp4".equalsIgnoreCase(extension) || "wmv".equalsIgnoreCase(extension) ||
                "mov".equalsIgnoreCase(extension);
    }

    public String getDocIcon() {
        return DocumentUtils.getDocIcon(getCadExtension());
    }

    public String getBigDocIcon() {
        return DocumentUtils.getBigDocIcon(getCadExtension());
    }

    public String getPreviewContent() {
        if (StringUtils.isBlank(content)) {
            return "";
        }

        return TextUtils.toHtml(TextUtils.escapeXml(StringUtils.substring(content, 0,
                DocumentUtils.docPreviewContentNumber)));
    }

    public String getHtmlContent() {
        return TextUtils.toHtml(content);
    }

    public String getBaseName() {
        return FilenameUtils.getBaseName(getFilename());
    }

    public String getDisplaySize() {
        double size = 0L;
        String fileSizeUnit = StringUtils.defaultIfEmpty(SystemConfigManager.getInstance().getProperty("fileSizeUnit"),
                "mb");
        if ("mb".equalsIgnoreCase(fileSizeUnit)) {
            size = ((double) fileSize) / (1024 * 1024);
            return df.format(size) + " MB";
        }

        size = ((double) fileSize) / 1024;
        return df.format(size) + " KB";
    }

    public List<Document> getLinkedDocuments() {
        return DocumentUtils.getLinkedDocuments(this);
    }

    public String getLinkUrl() {
        return DocumentUtils.getDocumentNoticeUrl(this);
    }

    public String getShortLinkUrl() {
        return DocumentUtils.getDocumentShortNoticeUrl(this);
    }

    public String getDisplayFolder() {
        return DocumentUtils.getDisplayDocumentPathWithLink(getFolder(), this);
    }

    public String getDisplayAreaFolderWithoutLink() {
        return DocumentUtils.getAreaDisplayDocumentPath(getFolder(), this);
    }

    public String getDisplayFolderWithoutLink() {
        return DocumentUtils.getDisplayDocumentPath(getFolder(), this);
    }

    public String getDisplayFullpath() {
        return DocumentUtils.getDisplayDocumentPath(getFolder(), this) + "/" + getFilename();
    }

    public String getDisplayAreaFullpath() {
        return DocumentUtils.getAreaDisplayDocumentPath(getFolder(), this) + "/" + getFilename();
    }

    public String getDisplayFullpathWithSession() {
        return DocumentUtils.getDisplayDocumentPathWithSession(getFolder(), this) + "/" + getFilename();
    }

    /**
     * 设置属性
     *
     * @param name  名称
     * @param value 值
     */
    public void setProperty(String name, String value) {
        initPropertiesMap();
        propertiesMap.put(name, value);
        PropertyUtils.updateProperties(this);
    }

    /**
     * 移除属性
     *
     * @param name 要移除的属性名称
     */
    public void removeProperty(String name) {
        initPropertiesMap();
        propertiesMap.remove(name);
    }

    /**
     * 获取属性
     *
     * @param name 名称
     * @return 值
     */
    public String getProperty(String name) {
        initPropertiesMap();
        return propertiesMap.get(name);
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public Date getModifiedDate() {
        return modifiedDate;
    }

    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    public boolean isEditing() {
        return editing;
    }

    public void setEditing(boolean editing) {
        this.editing = editing;
    }

    public Map<String, String> getPropertiesMap() {
        initPropertiesMap();
        return propertiesMap;
    }

    public void setPropertiesMap(Map<String, String> propertiesMap) {
        this.propertiesMap = propertiesMap;
    }

    public void initPropertiesMap() {
        if (this.propertiesMap == null) {
            this.propertiesMap = new HashMap<>();

            PropertyUtils.updatePropertiesMap(this);
        }
    }

    public String getProperties() {
        return properties;
    }

    public void setProperties(String properties) {
        this.properties = properties;
    }

    public String getCreatorFullname() {
        return creatorFullname;
    }

    public void setCreatorFullname(String creatorFullname) {
        this.creatorFullname = creatorFullname;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public String getDeletedBy() {
        return deletedBy;
    }

    public void setDeletedBy(String deletedBy) {
        this.deletedBy = deletedBy;
    }

    public int getNewestVersion() {
        return newestVersion;
    }

    public void setNewestVersion(int newestVersion) {
        this.newestVersion = newestVersion;
    }

    public String getEditingBy() {
        return editingBy;
    }

    public void setEditingBy(String editedBy) {
        this.editingBy = editedBy;
    }

    public int getClickCount() {
        return clickCount;
    }

    public void setClickCount(int clickCount) {
        this.clickCount = clickCount;
    }

    public String getDeletedByFullname() {
        return deletedByFullname;
    }

    public void setDeletedByFullname(String deletedByFullname) {
        this.deletedByFullname = deletedByFullname;
    }

    public Date getDeleteDate() {
        return deleteDate;
    }

    public void setDeleteDate(Date deleteDate) {
        this.deleteDate = deleteDate;
    }

    public String getEditingByFullname() {
        return editingByFullname;
    }

    public void setEditingByFullname(String editingByFullname) {
        this.editingByFullname = editingByFullname;
    }

    public Long getFolderId() {
        return folderId;
    }

    public void setFolderId(Long folderId) {
        this.folderId = folderId;
    }

    public String getEncoding() {
        return encoding;
    }

    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }

    public String getSerialNumber() {
        return TextUtils.escapeXml(serialNumber);
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public boolean isLocked() {
        return locked;
    }

    public void setLocked(boolean locked) {
        this.locked = locked;
    }

    public boolean isFromScan() {
        return fromScan;
    }

    public void setFromScan(boolean fromScan) {
        this.fromScan = fromScan;
    }

    public String getLockedBy() {
        return lockedBy;
    }

    public void setLockedBy(String lockedBy) {
        this.lockedBy = lockedBy;
    }

    public String getLockedByFullname() {
        if (StringUtils.isBlank(lockedByFullname)) {
            return "";
        }

        return lockedByFullname;
    }

    public void setLockedByFullname(String lockedByFullname) {
        this.lockedByFullname = lockedByFullname;
    }

    public Date getLockDate() {
        return lockDate;
    }

    public void setLockDate(Date lockDate) {
        this.lockDate = lockDate;
    }

    public String getPiId() {
        return piId;
    }

    public void setPiId(String piId) {
        this.piId = piId;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public String getDocVersion() {
        if (StringUtils.isBlank(docVersion)) {
            return "1.0";
        }

        return docVersion;
    }

    public void setDocVersion(String docVersion) {
        this.docVersion = docVersion;
    }

    public String getDocProps() {
        return docProps;
    }

    public void setDocProps(String docProps) {
        this.docProps = docProps;
    }

    public int getPrintLimit() {
        if (printLimit == null) {
            return -1;
        }
        return printLimit;
    }

    public void setPrintLimit(int printLimit) {
        this.printLimit = printLimit;
    }

    public String getStorage() {
        return storage;
    }

    public void setStorage(String storage) {
        this.storage = storage;
    }

    public String getOutLinkId() {
        return outLinkId;
    }

    public void setOutLinkId(String outLinkId) {
        this.outLinkId = outLinkId;
    }

    public Date getCustomDate1() {
        return customDate1;
    }

    public void setCustomDate1(Date customDate1) {
        this.customDate1 = customDate1;
    }

    public Date getCustomDate2() {
        return customDate2;
    }

    public void setCustomDate2(Date customDate2) {
        this.customDate2 = customDate2;
    }

    public String getRecordIdAndTable() {
        return recordIdAndTable;
    }

    public void setRecordIdAndTable(String recordIdAndTable) {
        this.recordIdAndTable = recordIdAndTable;
    }

    public Integer getNumIndex() {
        if (numIndex == null) {
            return 9999;
        }

        return numIndex;
    }

    public void setNumIndex(Integer numIndex) {
        this.numIndex = numIndex;
    }

    public String getUpdaterFullname() {
        return updaterFullname;
    }

    public void setUpdaterFullname(String updaterFullname) {
        this.updaterFullname = updaterFullname;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getDocStage() {
        return docStage;
    }

    public void setDocStage(String docStage) {
        this.docStage = docStage;
    }

    public String getDescription() {
        return TextUtils.escapeXml(description);
    }

    public void setDescription(String description) {
        this.description = description;
    }

    // TODO ngcopy
  /*  public DocPublishPlan getDocPublishPlan() {
        return docPublishPlan;
    }

    public void setDocPublishPlan(DocPublishPlan docPublishPlan) {
        this.docPublishPlan = docPublishPlan;
    }*/

    public String getPubInitialVerNum() {
        return pubInitialVerNum;
    }

    public void setPubInitialVerNum(String pubInitialVerNum) {
        this.pubInitialVerNum = pubInitialVerNum;
    }

    public String getPublishVersionNumber() {
        return publishVersionNumber;
    }

    public void setPublishVersionNumber(String publishVersionNumber) {
        this.publishVersionNumber = publishVersionNumber;
    }

    public String getTempPiId() {
        return tempPiId;
    }

    public void setTempPiId(String tempPiId) {
        this.tempPiId = tempPiId;
    }

    public boolean isEditingOOS() {
        if (editingOOS == null) {
            return false;
        }

        return editingOOS;
    }

    public void setEditingOOS(Boolean editingOOS) {
        this.editingOOS = editingOOS;
    }

    public String getEditingOOSBy() {
        return editingOOSBy;
    }

    public void setEditingOOSBy(String editingOOSBy) {
        this.editingOOSBy = editingOOSBy;
    }

    public String getEditingOOSByFullname() {
        return editingOOSByFullname;
    }

    public void setEditingOOSByFullname(String editingOOSByFullname) {
        this.editingOOSByFullname = editingOOSByFullname;
    }

    public String getEditingOOSAllUsername() {
        return editingOOSAllUsername;
    }

    public void setEditingOOSAllUsername(String editingOOSAllUsername) {
        this.editingOOSAllUsername = editingOOSAllUsername;
    }

    public String getOfficeServerIp() {
        return officeServerIp;
    }

    public void setOfficeServerIp(String officeServerIp) {
        this.officeServerIp = officeServerIp;
    }

    public String getFilenameBase64() {
        return filenameBase64;
    }

    public void setFilenameBase64(String filenameBase64) {
        this.filenameBase64 = filenameBase64;
    }

    public String getWebOfficeRndKey() {
        return webOfficeRndKey;
    }

    public void setWebOfficeRndKey(String webOfficeRndKey) {
        this.webOfficeRndKey = webOfficeRndKey;
    }
}
