package zd.dms.services.lendrequest.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.dms.entities.LendRequest;
import zd.dms.repositories.lendrequest.LendRequestRepository;
import zd.dms.services.lendrequest.LendRequestService;

import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class LendRequestServiceImpl extends BaseJpaServiceImpl<LendRequest, Long> implements LendRequestService {

    private final LendRequestRepository lendRequestRepository;

    @Override
    public BaseRepository<LendRequest, Long> getBaseRepository() {
        return lendRequestRepository;
    }

    @Override
    public int getUnapprovedCount(String username) {
        return lendRequestRepository.getUnapprovedCount(username);
    }

    @Override
    public LendRequest getById(long id) {
        return lendRequestRepository.get(id);
    }

    @Override
    public void create(LendRequest lendRequest) {
        lendRequestRepository.save(lendRequest);
    }

    @Override
    public void deleteById(long id) {
        lendRequestRepository.deleteById(id);
    }

    @Override
    public void update(LendRequest lendRequest) {
        lendRequestRepository.update(lendRequest);
    }

    @Override
    public Page getRequestsByCreator(int pageNumber, int pageSize, String creator) {
        return lendRequestRepository.getRequestsByCreator(pageNumber, pageSize, creator);
    }

    @Override
    public Page getApprovedRequestsByUsername(int pageNumber, int pageSize, String approver) {
        return lendRequestRepository.getApprovedRequestsByUsername(pageNumber, pageSize, approver);
    }

    @Override
    public List<LendRequest> getUnapprovedRequests(String username) {
        return lendRequestRepository.getUnapprovedRequests(username);
    }
}
