package zd.base.utils.system;

import lombok.Getter;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.analysus.ja.dict.TaskDictionary;
import zd.base.utils.ZDUtils;
import zd.dms.Constants;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.user.UserService;
import zd.dms.utils.HttpUtils;
import zd.record.utils.RecordDBUtils;

import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class SystemInitUtils {

    // 设置Tomcat上级目录
    public static String INSTALLATION_DIR;

    // 设置Tomcat目录
    @Getter
    private static String MIDDLEWARE_DIR;

    @Getter
    private static String MIDDLEWARE_TYPE;

    // 设置Root目录
    @Getter
    private static String APP_DIR;

    @Getter
    private static File homeDir;

    @Getter
    private static File documentsDir;

    @Getter
    private static File recordAttachmentsDir;

    @Getter
    private static File logDir = new File(homeDir, "logs");
    @Getter
    private static File indexDir = new File(homeDir, "index");
    @Getter
    private static File exportDir = new File(homeDir, "export");
    // documentsDir = new File(homeDir, "documents");
    @Getter
    private static File backupDir = new File(homeDir, "backups");
    @Getter
    private static File tempDir = new File(homeDir, "temp");
    @Getter
    private static File configDir = new File(homeDir, "config");

    public static String DEFAULT_HOME_DIR = "home";

    public static String DEFAULT_HOME_DIR_KEY = "ecmHome";

    public static String LOCK_SYSTEM_FILE_PATH = "/mysql/data/rtecm/tdms_ldock.frm";

    public static Date updateDate;

    public static void init() {
        setMiddlewareDir();
        setInstallationDir();

        APP_DIR = PropsUtils.getProps("appDir", APP_DIR);
        ZDUtils.info("ZDECM - APPDir: " + APP_DIR);

        configApplicationHomeDir();

    }

    // 设置Tomcat所在目录
    private static void setMiddlewareDir() {
        MIDDLEWARE_DIR = PropsUtils.getProps("middlewareDir");
        MIDDLEWARE_TYPE = "other";

        if (StringUtils.isBlank(MIDDLEWARE_DIR)) {
            MIDDLEWARE_DIR = System.getProperty("catalina.home");
            if (StringUtils.isNotBlank(MIDDLEWARE_DIR)) {
                MIDDLEWARE_TYPE = "tomcat";
                APP_DIR = MIDDLEWARE_DIR + "/webapps/ROOT";
            }
        }

        if (StringUtils.isBlank(MIDDLEWARE_DIR)) {
            MIDDLEWARE_DIR = System.getProperty("weblogic.home");
            if (StringUtils.isNotBlank(MIDDLEWARE_DIR)) {
                MIDDLEWARE_TYPE = "weblogic";
            }
        }

        if (StringUtils.isBlank(MIDDLEWARE_DIR)) {
            throw new IllegalStateException("Null Middleware Dir, set middlewareDir in app.properties");
        }
    }

    private static void setInstallationDir() {
        INSTALLATION_DIR = PropsUtils.getProps("installationDir");
        if (StringUtils.isBlank(INSTALLATION_DIR)) {
            INSTALLATION_DIR = new File(MIDDLEWARE_DIR).getParent();
        }
        if (StringUtils.isBlank(INSTALLATION_DIR)) {
            throw new IllegalStateException("Null Installation Dir, set installationDir in app.properties");
        }

        ZDUtils.info("ZDECM - InstallationDir: " + INSTALLATION_DIR);
    }


    /**
     * 设置home目录，创建所需的子目录
     */
    private static void configApplicationHomeDir() {
        // 从home配置文件中读取设置
        String ecmHomePath = PropsUtils.getProps(DEFAULT_HOME_DIR_KEY);
        if (StringUtils.isNotBlank(ecmHomePath)) {
            ZDUtils.info("ZDECM - Has customize ecmHome");

            File homeDirFromConfig = new File(ecmHomePath);

            // 创建home目录
            if (!homeDirFromConfig.exists()) {
                homeDirFromConfig.mkdirs();
            }

            // home必须可读写
            if (homeDirFromConfig.exists() && homeDirFromConfig.isDirectory()) {
                homeDir = homeDirFromConfig;
                ZDUtils.info("ZDECM - HomeDir: " + homeDir.getAbsolutePath());
            } else {
                throw new IllegalStateException("ZDECM - HomeDir must be a directory and exists: " +
                        homeDir.getAbsolutePath() + ", exists: " + homeDir.exists() + ", isDirectory: " +
                        homeDir.isDirectory());
            }
        }

        // 如果配置文件不存在，使用默认home
        if (homeDir == null) {
            ZDUtils.info("ZDECM - HomeConfigFile not found, use default settings.");
            configDefaultHomeDir();
        }

        // 创建备份，数据库，索引等其他目录
        logDir = new File(homeDir, "logs");
        indexDir = new File(homeDir, "index");
        exportDir = new File(homeDir, "export");
        // documentsDir = new File(homeDir, "documents");
        backupDir = new File(homeDir, "backups");
        tempDir = new File(homeDir, "temp");

        logDir.mkdirs();
        indexDir.mkdirs();
        exportDir.mkdirs();
        // documentsDir.mkdirs();
        backupDir.mkdirs();
        tempDir.mkdirs();

        // 设置系统属性
        System.setProperty(DEFAULT_HOME_DIR_KEY, homeDir.getAbsolutePath());
    }

    private static void configDefaultHomeDir() {
        String defaultHomeDirPath = INSTALLATION_DIR + File.separator + DEFAULT_HOME_DIR;

        ZDUtils.info("ZDECM - Default HomeDir: " + defaultHomeDirPath);

        File defaultHomeDir = new File(defaultHomeDirPath);
        if (!defaultHomeDir.exists()) {
            defaultHomeDir.mkdirs();
        }

        if (!defaultHomeDir.exists() || !defaultHomeDir.isDirectory()) {
            throw new IllegalStateException("ZDECM - HomeDir must be a directory and exists: " + defaultHomeDirPath +
                    ", exists: " + defaultHomeDir.exists() + ", isDirectory: " + defaultHomeDir.isDirectory());
        }

        homeDir = defaultHomeDir;
    }

    public static void configDocumentDir() {
        documentsDir = new File(homeDir, "documents");

        String docDirPath = SystemConfigManager.getInstance().getProperty(SystemConfigManager.DOC_DIR_KEY);
        if (StringUtils.isNotBlank(docDirPath)) {
            File docDir = new File(docDirPath);
            if (docDir.exists() && docDir.isDirectory() && docDir.canRead() && docDir.canWrite()) {
                documentsDir = docDir;
                ZDUtils.info("ZDECM - Using DOCDIR: " + docDir.getAbsolutePath());
            } else {
                ZDUtils.info("ZDECM - Wrong DOCDIR: " + docDir.getAbsolutePath() + ", using defaults");
            }
        }

        documentsDir.mkdirs();

        // 初始化OfficeUtils，和InstrumentationUpdatingAgent防盗版部分
        // TODO ngcopy
        /*OfficeUtils.getCountMap();
        InstrumentationUpdatingAgent.alignDate = new Date();*/

        // 数据附件目录
        recordAttachmentsDir = new File(documentsDir, "recordattachments");
        recordAttachmentsDir.mkdirs();
    }

    public static void updateLoginFlash() {
        Thread updateLoginFlashThread = new Thread() {
            public void run() {
                updateDate = new Date();

                File flashFile = new File(MIDDLEWARE_DIR + "/static/images/login/login-banner.swf");
                File lockFile = new File(getHomeDir(), LOCK_SYSTEM_FILE_PATH);

                Map<String, Object> paramsMap = new HashMap<>();


                addRequestParams(paramsMap);
                paramsMap.put("currentStatus", lockFile.exists() ? "2" : "1");

				/*
				try {
					request.addParameter("checksum", String.valueOf(FileUtils.checksumCRC32(flashFile)));
				} catch (Throwable t) {
				}
				*/
                paramsMap.put("checksum", "");

                try {
                    Map<String, Object> headers = new HashMap<>();
                    headers.put("Content-Type", "multipart/form-data");
                    String results = HttpUtils.post(Constants.UPDATE_URL + "/getLoginFlash.html", paramsMap, headers, true);

                    if (results.length() > 10240) {
                        FileUtils.writeStringToFile(lockFile, "???", "UTF-8");
                        ZDUtils.info("ZDECM - LoginSWF Updated!");
                    } else if ("badKeyId".equals(results.trim())) {
                        ZDUtils.info("ZDECM - Wrong KeyId");
                        TaskDictionary.getMainDefinition().limitCZero();
                        FileUtils.writeStringToFile(lockFile, "???", "UTF-8");
                    } else if ("statusOk".equals(results.trim())) {
                        ZDUtils.info("ZDECM - StatusOk");

                        FileUtils.deleteQuietly(lockFile);
                    } else if ("statusLock".equals(results.trim())) {
                        ZDUtils.info("ZDECM - StatusLock");
                        FileUtils.writeStringToFile(lockFile, "???", "UTF-8");
                    } else {
                        ZDUtils.info("ZDECM - LoginSWF not need to updated");
                    }
                } catch (Exception e) {
                    // e.printStackTrace();
                    // ignore
                }
            }
        };

        updateLoginFlashThread.start();
    }

    private static void addRequestParams(Map<String, Object> paramsMap) {
        paramsMap.put("sk", String.valueOf(TaskDictionary.getMainDefinition().sk()));
        paramsMap.put("hardId", TaskDictionary.getMainDefinition().eci());
        paramsMap.put("product", TaskDictionary.getMainDefinition().p());
        paramsMap.put("company", SystemConfigManager.getInstance().getProperty("companyName"));
        paramsMap.put("authUsers", String.valueOf(TaskDictionary.getMainDefinition().getC()));
        paramsMap.put("bfu", String.valueOf(TaskDictionary.getMainDefinition().getBFC()));
        paramsMap.put("rtc", String.valueOf(TaskDictionary.getMainDefinition().getRTC()));
        paramsMap.put("buildNumber", String.valueOf(SystemConfigManager.getInstance().getBuildNumber()));

        UserService userService = SpringUtils.getBean(UserService.class);
        if (userService != null) {
            int userCount = RecordDBUtils.checkJdbcTemplate().queryForObject("select count(*) from tdms_user",
                    Integer.class);

            paramsMap.put("udc", userCount + "," + 0);
        }

        // 加入keyId
        String keyId = TaskDictionary.getMainDefinition().ki();
        if (StringUtils.isNotBlank(keyId)) {
            paramsMap.put("keyId", keyId);
        }
    }
}
