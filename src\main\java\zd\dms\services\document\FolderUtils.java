package zd.dms.services.document;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.utils.system.SpringUtils;
import zd.dms.entities.Folder;
import zd.dms.entities.FolderArea;
import zd.dms.repositories.document.FolderRepository;
import zd.record.entities.RecFolder;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


/**
 * Property Utils
 *
 * <AUTHOR>
 */
public class FolderUtils {

    private static final Logger log = LoggerFactory.getLogger(FolderUtils.class);

    private static final Map<String, List<Long>> FOLDERS_WITH_PERMISSION_AND_COUNT_CACHE_MAP = new ConcurrentHashMap<String, List<Long>>();
    private static final List<String> FOLDER_DEFAULT_COLS = new LinkedList<String>();
    private static final List<String> FOLDER_CANDIDATE_COLS = new LinkedList<String>();
    private static final List<String> FOLDER_SORTABLE_COLS = new LinkedList<String>();

    public static List<String> getFolderDefaultCols() {
        if (FOLDER_DEFAULT_COLS.size() == 0) {
            synchronized (FolderUtils.class) {
                if (FOLDER_DEFAULT_COLS.size() == 0) {
                    FOLDER_DEFAULT_COLS.add("文件名");
                    FOLDER_DEFAULT_COLS.add("状态");
                    FOLDER_DEFAULT_COLS.add("类型");
                    FOLDER_DEFAULT_COLS.add("大小");
                    FOLDER_DEFAULT_COLS.add("更新时间");
                }
            }
        }

        return FOLDER_DEFAULT_COLS;
    }

    public static List<String> getFolderCandidateCols() {
        if (FOLDER_CANDIDATE_COLS.size() == 0) {
            FOLDER_CANDIDATE_COLS.add("排序");
            FOLDER_CANDIDATE_COLS.add("文件名");
            FOLDER_CANDIDATE_COLS.add("编号");
            FOLDER_CANDIDATE_COLS.add("状态");
            FOLDER_CANDIDATE_COLS.add("发布状态");
            FOLDER_CANDIDATE_COLS.add("发布版本");
            FOLDER_CANDIDATE_COLS.add("类型");
            FOLDER_CANDIDATE_COLS.add("大小");
            FOLDER_CANDIDATE_COLS.add("更新时间");
            FOLDER_CANDIDATE_COLS.add("创建时间");
            FOLDER_CANDIDATE_COLS.add("创建人");
            FOLDER_CANDIDATE_COLS.add("更新人");
            FOLDER_CANDIDATE_COLS.add("修订版");
            FOLDER_CANDIDATE_COLS.add("版本");
            FOLDER_CANDIDATE_COLS.add("点击数");
            FOLDER_CANDIDATE_COLS.add("目录ID");
        }

        return FOLDER_CANDIDATE_COLS;
    }

    public static List<String> getFolderSortableCols() {
        if (FOLDER_SORTABLE_COLS.size() == 0) {
            FOLDER_SORTABLE_COLS.add("排序");
            FOLDER_SORTABLE_COLS.add("文件名");
            FOLDER_SORTABLE_COLS.add("编号");
            FOLDER_SORTABLE_COLS.add("类型");
            FOLDER_SORTABLE_COLS.add("大小");
            FOLDER_SORTABLE_COLS.add("更新时间");
            FOLDER_SORTABLE_COLS.add("创建时间");
            FOLDER_SORTABLE_COLS.add("创建人");
            FOLDER_SORTABLE_COLS.add("更新人");
            FOLDER_SORTABLE_COLS.add("修订版");
            FOLDER_SORTABLE_COLS.add("版本");
            FOLDER_SORTABLE_COLS.add("点击数");
            FOLDER_SORTABLE_COLS.add("目录ID");
        }

        return FOLDER_SORTABLE_COLS;
    }

    public static boolean isCurrentFolderDiabledStartWorkflow(Folder folder) {
        String diabledStartWorkflowWithParent = getDiabledStartWorkflowWithParent(folder);
        if ("true".equals(diabledStartWorkflowWithParent)) {
            return true;
        }

        return false;
    }

    public static String getDiabledStartWorkflowWithParent(Folder folder) {
        if (folder == null) {
            return "false";
        }

        String diabledStartWorkflow = folder.getDiabledStartWorkflow();
        if ("true".equals(diabledStartWorkflow) || "false".equals(diabledStartWorkflow)) {
            return diabledStartWorkflow;
        }

        Folder parent = folder.getParent();
        while (parent != null) {
            diabledStartWorkflow = parent.getDiabledStartWorkflow();
            if ("true".equals(diabledStartWorkflow) || "false".equals(diabledStartWorkflow)) {
                return diabledStartWorkflow;
            }

            parent = parent.getParent();
        }

        return "false";
    }

    public static String getPdIdsWithParent(Folder folder) {
        if (folder == null) {
            return "";
        }

        String pdIds = folder.getPdIds();
        if (StringUtils.isNotBlank(pdIds)) {
            return pdIds;
        }

        Folder parent = folder.getParent();
        while (parent != null) {
            pdIds = parent.getPdIds();
            if (StringUtils.isNotBlank(pdIds)) {
                return pdIds;
            }

            parent = parent.getParent();
        }

        return "";
    }

    public static void clearFoldersWithListPermissionAndCountCacheMap() {
        log.debug("clearFoldersWithListPermissionAndCountCacheMap clear");
        FOLDERS_WITH_PERMISSION_AND_COUNT_CACHE_MAP.clear();
    }

    public static void removeFoldersWithListPermissionAndCountCacheMap(String username) {
        log.debug("removeFoldersWithListPermissionAndCountCacheMap remove: {}", username);
        FOLDERS_WITH_PERMISSION_AND_COUNT_CACHE_MAP.remove(username);
        FOLDERS_WITH_PERMISSION_AND_COUNT_CACHE_MAP.remove("[escapeISO]" + username);
    }

    /**
     * 修改目录及其子目录的treeName
     *
     * @param folder   要修改的目录
     * @param treeName 修改后的treeName
     * @return
     */
    public static void changeFolderAndChildrenTreeName(Folder folder, String treeName) {
        if (StringUtils.isBlank(treeName)) {
            treeName = null;
        }
        folder.setTreeName(treeName);

        List<Folder> children = folder.getChildren();
        if (children != null) {
            for (Folder child : children) {
                changeFolderAndChildrenTreeName(child, treeName);
            }
        }
    }

    public static String getFolderNodeTreeName(String treeName) {
        if (StringUtils.isNotBlank(treeName)) {
            return "area_" + treeName;
        } else {
            return FolderArea.TREENAME_DOC;
        }
    }

    public static String getTreeNameWithParent(Folder folder) {
        if (folder == null) {
            return "";
        }

        String treeName = folder.getTreeName();
        if (StringUtils.isNotBlank(treeName)) {
            return treeName;
        }

        Folder parent = folder.getParent();
        while (parent != null) {
            treeName = parent.getTreeName();
            if (StringUtils.isNotBlank(treeName)) {
                return treeName;
            }

            parent = parent.getParent();

        }

        return "";
    }

    public static String getPropertyFromAllLevel(Folder f, String propertyName) {
        if (f == null) {
            return null;
        }

        Folder toCheck = f;
        while (true) {
            if (StringUtils.isNotBlank(toCheck.getProperty(propertyName))) {
                return toCheck.getProperty(propertyName);
            } else {
                toCheck = toCheck.getParent();
                if (toCheck == null) {
                    return null;
                }
            }
        }
    }

    public static String getPropertyFromAllLevel(RecFolder f, String propertyName) {
        if (f == null) {
            return null;
        }

        RecFolder toCheck = f;
        while (true) {
            if (StringUtils.isNotBlank(toCheck.getProperty(propertyName))) {
                return toCheck.getProperty(propertyName);
            } else {
                toCheck = toCheck.getParent();
                if (toCheck == null) {
                    return null;
                }
            }
        }
    }

    public static List<Long> getAllSubFolderIds(String folderIdString) {
        List<Long> resultList = new LinkedList<Long>();

        FolderRepository folderRepository = SpringUtils.getBean(FolderRepository.class);
        if (StringUtils.isNotBlank(folderIdString)) {
            String[] arr = folderIdString.split(",|，");
            for (String a : arr) {
                if (StringUtils.isNotBlank(a)) {
                    Folder f = folderRepository.findObjectById(NumberUtils.toLong(a, -1));
                    if (f != null) {
                        resultList.add(f.getId());
                        resultList.addAll(getAllSubFolderIds(f));
                    }
                }
            }
        }

        return resultList;
    }

    public static List<Long> getAllSubFolderIds(Folder f) {
        List<Long> result = new LinkedList<Long>();
        Set<Folder> subFolders = getAllSubFolders(f);
        for (Folder subFolder : subFolders) {
            result.add(subFolder.getId());
        }
        return result;
    }

    public static Set<Folder> getAllSubFolders(Folder f) {
        Set<Folder> result = new LinkedHashSet<Folder>();
        getSubFolder(f, result);
        return result;
    }

    private static void getSubFolder(Folder f, Set<Folder> folders) {
        folders.addAll(f.getChildren());
        for (Folder subFolder : f.getChildren()) {
            getSubFolder(subFolder, folders);
        }
    }
}
