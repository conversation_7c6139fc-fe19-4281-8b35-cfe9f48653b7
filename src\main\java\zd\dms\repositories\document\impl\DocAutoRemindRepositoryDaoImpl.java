package zd.dms.repositories.document.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.DocAutoRemind;
import zd.dms.repositories.document.DocAutoRemindRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;

public class DocAutoRemindRepositoryDaoImpl extends BaseRepositoryDaoImpl<DocAutoRemind, Long> implements DocAutoRemindRepository {

    public DocAutoRemindRepositoryDaoImpl(Class<DocAutoRemind> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public void deleteById(long id) {
        String hql = "delete from DocAutoRemind where id = ?1";
        executeUpdate(hql, id);
    }

    @Override
    public void deleteAllByFolderId(long folderId) {
        String hql = "delete from DocAutoRemind where folderId = ?1";
        executeUpdate(hql, folderId);
    }

    @Override
    public List<DocAutoRemind> getRemindsByFolderId(long folderId) {
        Specification<DocAutoRemind> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocAutoRemind> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("folderId", folderId);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }
}
