//                    佛祖保佑       永无BUG
//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                           O\  =  /O
//                        ____/`---'\____
//                      .'  \\|     |//  `.
//                     /  \\|||  :  |||//  \
//                    /  _||||| -:- |||||-  \
//                    |   | \\\  -  /// |   |
//                    | \_|  ''\---/''  |   |
//                    \  .-\__  `-`  ___/-. /
//                  ___`. .'  /--.--\  `. . __
//               ."" '<  `.___\_<|>_/___.'  >'"".
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |
//              \  \ `-.   \_ __\ /__ _/   .-` /  /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
package zd.dms.services.config;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.ConstantsBuildNumber;
import zd.base.utils.ZDUtils;
import zd.base.utils.redis.ZDRedisUtils;
import zd.base.utils.system.BootStrapUtils;
import zd.base.utils.system.PropsUtils;
import zd.base.utils.system.SystemInitUtils;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.utils.admin.SettingsUtils;
import zd.dms.utils.db.ZDDBConfigUtils;

import java.util.*;

/**
 * 配置管理器，不使用spring管理，单例
 */
public class SystemConfigManager {

    private static final Logger log = LoggerFactory.getLogger(SystemConfigManager.class);

    private String marjorVersion_NGen = "V2.1";

    public static SystemConfigManager systemConfigManager = new SystemConfigManager();

    public static final String PDF_SWF_ENABLED_KEY = "pdfSwfEnabled";

    public static final String MYDOC_LIMIT_KEY = "myDocLimit";

    public static final String DOC_DIR_KEY = "docDirKey";

    public int getDefaultDocRevision() {
        int result = NumberUtils.toInt(getProperty("defaultDocRevision"), 1);

        if (result < 1) {
            return 1;
        }

        return result;
    }

    public String getDocStageDef() {
        String result = getProperty("docStageDef");
        if (StringUtils.isBlank(result)) {
            result = "草稿:blue\n定稿:green\n作废:red";
        }

        return result;
    }

    public List<DocStageDefItem> getDocStageDefItems() {
        List<DocStageDefItem> items = new LinkedList<DocStageDefItem>();
        String defStr = getDocStageDef();
        String[] defArr = defStr.split("\n");
        for (String defArrStr : defArr) {
            if (StringUtils.isNotBlank(defArrStr) && defArrStr.indexOf(":") > 0) {
                defArrStr = defArrStr.trim().replaceAll("\n", "");
                String[] defItemArr = defArrStr.split(":");
                if (defItemArr != null && defItemArr.length == 2) {
                    DocStageDefItem item = new DocStageDefItem(defItemArr[0], defItemArr[1]);
                    items.add(item);
                }
            }
        }

        return items;
    }

    public String getDocStageColor(String statusName) {
        String defStr = getDocStageDef();
        String[] defArr = defStr.split("\n");
        for (String defArrStr : defArr) {
            if (StringUtils.isNotBlank(defArrStr) && defArrStr.indexOf(":") > 0) {
                defArrStr = defArrStr.trim().replaceAll("\n", "");
                if (defArrStr.startsWith(statusName + ":")) {
                    return StringUtils.substringAfter(defArrStr, statusName + ":");
                }
            }
        }

        return "black";
    }

    public String getDefaultDocStage() {
        String defStr = getDocStageDef();
        String[] defArr = defStr.split("\n");
        for (String defArrStr : defArr) {
            if (StringUtils.isNotBlank(defArrStr) && defArrStr.indexOf(":") > 0) {
                return StringUtils.substringBefore(defArrStr, ":");
            }
        }

        return "草稿";
    }


    public String getStaticBuildNumber() {
        return ConstantsBuildNumber.getBuildNumber();
    }

    public static SystemConfigManager getInstance() {
        return systemConfigManager;
    }

    public boolean isUserCanEditEmail() {
        return getBooleanProperty("userCanEditEmail", true);
    }

    public boolean isUsingExternalHibernateConf() {
        return PropsUtils.getBoolProps("usingExternalHibConf", false);
    }

    public String getMarjorVersion() {
        return marjorVersion_NGen;
    }

    /**
     * 保存所有配置
     */
    public void save() {
        /*File configFile = new File(SystemInitUtils.getHomeDir(), CONFIG_FILE_NAME);
        File tmpFile = new File(SystemInitUtils.getHomeDir(), CONFIG_TMP_FILE_NAME);

        OutputStream out = null;
        try {
            out = ZDIOUtils.getDOStream(tmpFile);
            props.storeToXML(out, "ZDECM配置文件，一般情况下请勿手工修改!", "utf-8");

            FileUtils.copyFile(tmpFile, configFile);
        } catch (FileNotFoundException e) {
            throw new IllegalStateException(e);
        } catch (IOException e) {
            throw new IllegalStateException(e);
        } finally {
            IOUtils.closeQuietly(out);
        }*/
    }

    public void loadDefaultConfig() {
        ZDUtils.info("ZDECM - loadDefaultConfig.");
        Map<String, String> map = new HashMap<>();
        map.put("setupComplete", "false");
        map.put("maxVersion", "10");
        map.put("timeZone", "Asia/Shanghai");
        map.put("fileEncoding", "GBK");
        map.put("dailyBackupTime", "16");
        map.put("adminDeletePassword", DigestUtils.md5Hex(UserGroupUtils.ADMIN_USERNAME));
        map.put("companyName", "请在此填写您公司的全称");
        map.put("positions", "总经理,副总经理,部门经理,部门副经理,会计,出纳");
        map.put("wfreplys", "已阅!,!同意!,!同意，按规定办理!,!意见已写入文档!,!保留意见!,!交给相关部门处理!,!按流程办理");

        List<Map<String, Object>> listByKeys = ZDDBConfigUtils.getListByKeys(map.keySet());
        for (Map<String, Object> tempMap : listByKeys) {
            String key = MapUtils.getString(tempMap, "KEY", "");
            map.remove(key);
        }

        ZDDBConfigUtils.batchInsert(map);
    }

    public void setDefaultProperty(String key, String value) {
        String currentValue = getProperty(key);
        if (StringUtils.isBlank(currentValue)) {
            setProperty(key, value);
        }
    }

    public void setProperty(String key, String value) {
        if (StringUtils.isBlank(key)) {
            return;
        }

        ZDDBConfigUtils.insertOrUpdate(key, value);
    }

    public void setProperty(String key, int value) {
        setProperty(key, value + "");
    }

    public void setProperty(String key, boolean value) {
        if (value) {
            setProperty(key, "true");
        } else {
            setProperty(key, "false");
        }
    }

    public String getProperty(String key) {
        if (!BootStrapUtils.isInit) {
            return "";
        }

        String value = ZDRedisUtils.hgetString(ZDDBConfigUtils.REDIS_KEY, key, null);
        if (value == null) {
            value = ZDDBConfigUtils.getValue(key);
            if (StringUtils.isBlank(value)) {
                Object defaultValue = SettingsUtils.getDefaultValue(key);
                if (defaultValue != null) {
                    value = defaultValue + "";
                } else {
                    value = "";
                }
            }

            ZDRedisUtils.hset(ZDDBConfigUtils.REDIS_KEY, key, value);
        }

        return replaceHomeDir(value);
    }

    private String replaceHomeDir(String str) {
        if (StringUtils.isNotBlank(str)) {
            str = StringUtils.replace(str, "${tenwebHome}", StringUtils.replace(SystemInitUtils.getHomeDir().getAbsolutePath(), "\\", "/"));
        }

        return str;
    }

    public int getIntProperty(String key) {
        return NumberUtils.toInt(getProperty(key), 0);
    }

    public boolean getBooleanProperty(String key) {
        return BooleanUtils.toBoolean(getProperty(key));
    }

    public boolean getBooleanProperty(String key, boolean defaultValue) {
        if (StringUtils.isBlank(getProperty(key))) {
            return defaultValue;
        }
        return BooleanUtils.toBoolean(getProperty(key));
    }

    public void removeProperty(String key) {
        ZDDBConfigUtils.delete(key);
    }

    public Properties getPropertiesWithPrefix(String prefix) {
        Properties properties = new Properties();
        List<Map<String, Object>> byKeyPrefix = ZDDBConfigUtils.getByKeyPrefix(prefix);
        for (Map<String, Object> map : byKeyPrefix) {
            String key = MapUtils.getString(map, "KEY", "");
            if (StringUtils.isBlank(key)) {
                continue;
            }

            properties.put(key, replaceHomeDir(MapUtils.getString(map, "VALUE", "")));
        }

        return properties;
    }

    /**
     * 系统是否已经成功设置
     *
     * @return 成功返回true
     */
    public boolean isSetupComplete() {
        return getBooleanProperty("setupComplete");
    }

    /**
     * 设置编译版本号
     *
     * @param buildNumber 编译版本号
     */
    public void setBuildNumber(String buildNumber) {
        setProperty("buildNumber", buildNumber);
    }

    /**
     * 获取编译版本号
     *
     * @return 编译版本号
     */
    public int getBuildNumber() {
        return getIntProperty("buildNumber");
    }

}
