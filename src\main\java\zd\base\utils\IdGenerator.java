package zd.base.utils;

import cn.hutool.core.lang.generator.SnowflakeGenerator;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.IdentifierGenerator;

public class IdGenerator implements IdentifierGenerator {

  @Override
  public Object generate(SharedSessionContractImplementor sharedSessionContractImplementor, Object o) {
    SnowflakeGenerator snowflakeGenerator = new SnowflakeGenerator();
    return snowflakeGenerator.next();
  }
}
