package zd.dms.repositories.propdata.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.SystemConfigItem;
import zd.dms.repositories.propdata.SystemConfigItemRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;


public class SystemConfigItemRepositoryDaoImpl extends BaseRepositoryDaoImpl<SystemConfigItem, Long> implements SystemConfigItemRepository {

    public SystemConfigItemRepositoryDaoImpl(Class<SystemConfigItem> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public Page getItems(int pageNumber, int pageSize) {
        Specification<SystemConfigItem> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SystemConfigItem> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }

    @Override
    public SystemConfigItem getItemByName(String name) {
        Specification<SystemConfigItem> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SystemConfigItem> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("keyName", name);

            return specTools.getRestriction();
        };

        return findSingleObject(spec);
    }

    @Override
    public List<SystemConfigItem> getAllItems() {
        Specification<SystemConfigItem> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SystemConfigItem> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }
}
