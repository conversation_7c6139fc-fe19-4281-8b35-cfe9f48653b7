package zd.base.utils.es;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.client5.http.classic.methods.*;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.apache.hc.core5.util.Timeout;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
public class ESHttpUtils {

    public static String getHttpSSLResponse(String url) {
        CloseableHttpClient client = HttpClients.createDefault();


        HttpGet httpGet = new HttpGet(url);
        setRequestConfig(httpGet);
        setDefaultHeaders(httpGet);

        CloseableHttpResponse response = null;
        try {
            response = client.execute(httpGet);
            HttpEntity entity = response.getEntity();
            String result = EntityUtils.toString(entity);
            if (StringUtils.isNotEmpty(result)) {
                log.debug("getHttpSSLResponse url: {}, response: {}", url, result);
                return result;
            } else {
                log.debug("getHttpSSLResponse status code: {}", response.getCode());
            }
        } catch (Throwable t) {
            log.debug("getHttpSSLResponse ex2", t);
        } finally {
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                }
            }
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                }
            }
        }

        return null;
    }

    public static String postJsonHttpSSLResponse(String url, String json) {
        CloseableHttpClient client = HttpClients.createDefault();

        HttpPost httpPost = new HttpPost(url);
        setRequestConfig(httpPost);
        setDefaultHeaders(httpPost);

        // json entity
        if (StringUtils.isNotBlank(json)) {
            httpPost.setEntity(new StringEntity(json, StandardCharsets.UTF_8));
        }

        CloseableHttpResponse response = null;
        try {
            response = client.execute(httpPost);
            HttpEntity entity = response.getEntity();
            String result = EntityUtils.toString(entity);
            log.debug("postJsonHttpSSLResponse result: {}", result);
            if (StringUtils.isNotEmpty(result)) {
                log.debug("postJsonHttpSSLResponse url: {}", url);
                return result;
            } else {
                log.debug("postJsonHttpSSLResponse status code: {}", response.getCode());
            }
        } catch (Throwable t) {
            log.debug("postJsonHttpSSLResponse ex2", t);
        } finally {
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                }
            }
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                }
            }
        }

        return null;
    }

    public static String putHttpSSLResponse(String url, String json) {
        CloseableHttpClient client = HttpClients.createDefault();

        HttpPut httpPut = new HttpPut(url);
        log.debug("putHttpSSLResponse: {}", url);
        setRequestConfig(httpPut);
        setDefaultHeaders(httpPut);

        // json entity
        if (StringUtils.isNotBlank(json)) {
            httpPut.setEntity(new StringEntity(json, StandardCharsets.UTF_8));
        }

        CloseableHttpResponse response = null;
        try {
            response = client.execute(httpPut);
            HttpEntity entity = response.getEntity();
            String result = EntityUtils.toString(entity);
            if (StringUtils.isNotEmpty(result)) {
                log.debug("putHttpSSLResponse url: {}, response: {}", url, result);
                return result;
            } else {
                log.debug("putHttpSSLResponse status code: {}", response.getCode());
            }
        } catch (Throwable t) {
            log.debug("putHttpSSLResponse ex2", t);
        } finally {
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                }
            }
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                }
            }
        }

        return null;
    }


    public static String deleteHttpSSLResponse(String url) {
        CloseableHttpClient client = HttpClients.createDefault();

        HttpDelete httpDelete = new HttpDelete(url);
        log.debug("deleteHttpSSLResponse: {}", url);
        setRequestConfig(httpDelete);
        setDefaultHeaders(httpDelete);

        CloseableHttpResponse response = null;
        try {
            response = client.execute(httpDelete);
            HttpEntity entity = response.getEntity();
            String result = EntityUtils.toString(entity);
            if (StringUtils.isNotEmpty(result)) {
                log.debug("deleteHttpSSLResponse url: {}, response: {}", url, result);
                return result;
            } else {
                log.debug("deleteHttpSSLResponse status code: {}", response.getCode());
            }
        } catch (Throwable t) {
            log.debug("deleteHttpSSLResponse ex2", t);
        } finally {
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                }
            }
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                }
            }
        }

        return null;
    }

    private static void setRequestConfig(HttpUriRequestBase httpUriRequestBase) {
        RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(Timeout.ofSeconds(20000))
                .setConnectionRequestTimeout(Timeout.ofSeconds(20000))
                .setResponseTimeout(Timeout.ofSeconds(20000)).build();

        httpUriRequestBase.setConfig(requestConfig);
    }

    private static void setDefaultHeaders(HttpUriRequestBase httpUriRequestBase) {
        httpUriRequestBase.setHeader("Accept", "*/*");
        httpUriRequestBase.setHeader("Cache-Control", "gzip, deflate, br");
        httpUriRequestBase.setHeader("Content-Type", "application/json; charset=utf-8");
        httpUriRequestBase.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.150 Safari/537.36 Edg/88.0.705.68");
    }
}
