package zd.base.tasks.zdmq;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zd.base.tasks.AbstractTask;
import zd.dms.utils.zdmq.ZDMQConfigUtils;
import zd.dms.utils.zdmq.ZDMQDBUtils;

import java.util.List;
import java.util.Map;

/**
 * mq任务
 */
@Component
@Slf4j
public class ZDMQResetTask extends AbstractTask {

    @Scheduled(cron = "10 */1 * * * ?")
    @Scheduled(initialDelay = 60000)
    @Override
    public void work() {
        ZDMQDBUtils.resetAllTagsByTimes();
        ZDMQDBUtils.resetProcessMsgs();
    }
}
