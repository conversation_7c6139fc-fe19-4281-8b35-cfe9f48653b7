package zd.base.entities;

import java.io.Serializable;

import jakarta.persistence.*;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import zd.base.context.UserContextHolder;
import zd.dms.entities.User;

/**
 * Abstract Entity Object，包括所有实体对象中 相同的部分。
 */
@MappedSuperclass
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public abstract class AbstractSequenceEntity implements Serializable {

    /**
     * Identifier
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
//    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "HIBERNATE_SEQUENCE")
//    @SequenceGenerator(name = "HIBERNATE_SEQUENCE", initialValue = 1, allocationSize = 1, sequenceName = "HIBERNATE_SEQUENCE")
    protected Long id;

    /**
     * 创建者
     */
    protected String creator;

    /**
     * 默认构造器
     */
    public AbstractSequenceEntity() {
        // 从UserContextHolder中取得当前的用户名
        User currentUser = UserContextHolder.getUser();
        creator = currentUser != null ? currentUser.getUsername() : null;
    }

    /**
     * @return Returns the id.
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id The id to set.
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @see Object#equals(Object)
     */
    @Override
    public boolean equals(Object o) {
        if (!(o instanceof AbstractSequenceEntity)) {
            return false;
        }

        final AbstractSequenceEntity t = (AbstractSequenceEntity) o;

        return new EqualsBuilder().append(id, t.getId()).append(creator, t.getCreator()).isEquals();
    }

    /**
     * @see Object#hashCode()
     */
    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(id).append(creator).toHashCode();
    }

    /**
     * Get Creator
     *
     * @return Creator
     */
    public String getCreator() {
        return creator;
    }

    /**
     * Set Creator
     *
     * @param creator Creator name
     */
    public void setCreator(String creator) {
        this.creator = creator;
    }
}
