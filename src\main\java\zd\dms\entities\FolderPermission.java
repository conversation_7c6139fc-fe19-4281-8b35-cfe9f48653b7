package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.dms.services.security.FolderPermissionUtils;

import java.util.*;


/**
 * Permission Domain Object
 */

@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "folder_permission")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class FolderPermission extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = 4968351151515850346L;

    /**
     * 权限“所有权限”
     */
    public static final String ALL = "*";

    /**
     * 权限“创建”
     */
    public static final String CREATE = "C";

    /**
     * 权限“编辑”和“更新”
     */
    public static final String EDIT = "E";

    /**
     * 权限“读取”
     */
    public static final String READ = "R";

    /**
     * 权限“删除”
     */
    public static final String DELETE = "D";

    /**
     * 权限“打印”
     */
    public static final String PRINT = "P";

    /**
     * 权限“浏览”
     */
    public static final String LIST = "L";

    /**
     * 权限“下载”
     */
    public static final String DOWNLOAD = "X";

    /**
     * 权限“订阅”
     */
    public static final String SUBSCRIPTION = "S";

    /**
     * 权限“赋权”
     */
    public static final String AUTHORIZATION = "A";

    /**
     * 权限“高级”
     */
    public static final String ADVFILE = "G";

    /**
     * 权限“同步”
     */
    public static final String TONGBU = "T";

    /**
     * 权限“可见”
     */
    public static final String VISIBLE = "J";

    /**
     * 权限“导出”
     */
    public static final String EXPORT_FILES = "I";

    /**
     * 权限“外发”
     */
    public static final String OUT_LINK = "O";

    public static final String[] PERMISSIONS = new String[]{ALL, AUTHORIZATION, CREATE, EDIT, READ, DELETE, PRINT,
            LIST, DOWNLOAD, SUBSCRIPTION, ADVFILE, TONGBU, VISIBLE, EXPORT_FILES, OUT_LINK};

    public static final String PERMISSIONS_STR = StringUtils.join(PERMISSIONS, "");

    public static final String[] PERMISSIONSNAME = new String[]{"管理", "赋权", "创建", "编辑", "阅读", "删除", "打印", "浏览", "下载",
            "订阅", "高级", "同步", "可见", "导出", "外发"};

    // 三员管理员具有的目录权限： 可见、赋权， 对于一切目录具有：创建、编辑、删除权
    public static final String[] RECEPTIONPERMISSIONS = new String[]{VISIBLE, AUTHORIZATION};

    /**
     * 权限
     */
    private String permission;

    @Index(name = "i_fperm_username")
    private String username;

    @Index(name = "i_fperm_groupCode")
    private String groupCode;

    @Index(name = "i_fperm_recFolderId")
    private long folderId;

    @Index(name = "i_fperm_position")
    @Column(length = 1000)
    private String position;

    /**
     * 创建时间
     */
    @Index(name = "i_fperm_cd")
    @Column(nullable = false)
    private Date creationDate;

    private Date expirationDate;

    /**
     * 默认构造器
     */
    public FolderPermission() {
        super();
        creationDate = new Date();
    }

    public List<String> getPermissionList() {
        if (StringUtils.isBlank(permission)) {
            return Collections.emptyList();
        }

        List<String> permissionList = new ArrayList<>();
        permissionList.addAll(Arrays.asList(permission.split("")));

        return permissionList;
    }

    public String getDisplayName() {
        return FolderPermissionUtils.getDisplayName(this);
    }

}
