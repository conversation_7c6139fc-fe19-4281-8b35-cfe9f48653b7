package zd.dms.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.dms.entities.User;
import zd.dms.services.security.AuthenticationService;
import zd.dms.services.user.UserService;

import java.util.Map;

@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "LoginController", description = "登出Controller")
@RequestMapping("/")
public class LogoutController extends ControllerSupport {

    private final UserService userService;

    private final AuthenticationService authenticationService;

    @Operation(summary = "用户登出")
    @RequestMapping(value = "/logout", method = {RequestMethod.GET, RequestMethod.POST})
    @ZDLog("用户登出")
    public JSONResultUtils<Map<String, Object>> logout(HttpServletRequest request, HttpServletResponse response) {

        User currentUser = getCurrentUser();

        String currentUserFullName = currentUser == null ? "unknown" : currentUser.getFullname();

        authenticationService.logout(request, response);
        logInfo("用户 " + currentUserFullName + " 登出系统");

        return JSONResultUtils.success();
    }
}
