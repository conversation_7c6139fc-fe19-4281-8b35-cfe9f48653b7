package zd.dms.repositories.admin;

import zd.base.repositories.BaseRepository;
import zd.dms.entities.LogMessage;

import java.util.Date;
import java.util.List;

public interface LogMessageRepository  extends BaseRepository<LogMessage,Long> {

    List<LogMessage> getLogMessageByOperator(String operator);

    List<LogMessage> getLogMessageByLevel(int level);

    List<LogMessage> getLogsByDate(Date startDate, Date endDate);

    void clearAllLogMessage();
}
