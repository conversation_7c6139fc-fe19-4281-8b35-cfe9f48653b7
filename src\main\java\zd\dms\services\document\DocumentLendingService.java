package zd.dms.services.document;

import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.lang.Nullable;
import org.springframework.transaction.annotation.Transactional;
import zd.dms.dto.document.DocumentLendingDTO;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentLending;
import zd.dms.entities.User;

@Transactional(rollbackFor = Throwable.class)
public interface DocumentLendingService {

    void createLendings(List<String> documentIds, List<String> borrowerUsernames, int lendHours,
        List<String> permissions, User lender);

    void deleteByDocument(Document document);

    void delete(DocumentLending lending);

    void deleteById(long id);

    /**
     * 收回我借出的文档
     */
    void recallDocument(List<Long> ids);

    void deleteExpiredLendings();

    @Transactional(readOnly = true)
    DocumentLending getDocumentLendingById(long id);

    @Transactional(readOnly = true)
    List<DocumentLending> getDocumentLendingsByCreator(String creator);

    @Transactional(readOnly = true)
    List<DocumentLending> getDocumentLendings(Document document);

    @Transactional(readOnly = true)
    List<DocumentLending> getAllDocumentLendings();

    /**
     * 搜索我借出的文档
     *
     * @param currentUsername  当前用户用户名
     * @param fileName         文件名（可选）
     * @param borrowerFullName 借阅人姓名（可选）
     * @param pageable         分页参数
     */
    @Transactional(readOnly = true, rollbackFor = Throwable.class)
    Page<DocumentLendingDTO> searchLendFromMe(String currentUsername, @Nullable String fileName,
        @Nullable String borrowerFullName, Pageable pageable);

    /**
     * 搜索借给我的文档
     *
     * @param currentUsername 当前用户用户名
     * @param fileName        文件名
     * @param lenderFullName  借出人姓名
     * @param pageable        分页参数
     */
    @Transactional(readOnly = true, rollbackFor = Throwable.class)
    Page<DocumentLendingDTO> searchLendToMe(String currentUsername, @Nullable String fileName,
        @Nullable String lenderFullName, Pageable pageable);
}
