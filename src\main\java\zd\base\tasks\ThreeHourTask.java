package zd.base.tasks;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zd.base.utils.system.PropsUtils;
import zd.dms.services.mail.FolderMailService;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.utils.abbyy.AbbyyOcrUtils;

@Component
@Slf4j
public class ThreeHourTask extends AbstractTask {

    @Autowired
    private FolderMailService folderMailService;

    @Scheduled(cron = "0 0 0/3 * * ?")
    @Scheduled(initialDelay = 60000)
    @Override
    public void work() {
        folderMailService.receiveMails(UserGroupUtils.ADMIN_USERNAME, "");

        // 检测删除OCR临时文件
        AbbyyOcrUtils.deleteOcrTempFile(PropsUtils.getIntProps("delte3hTempBeforeDay", 3));

    }
}
