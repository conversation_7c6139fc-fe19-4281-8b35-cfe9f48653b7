package zd.dms.controllers.document;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import zd.base.context.UserContextHolder;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.validate.ValidateUtils;
import zd.dms.dto.document.DocumentLendingDTO;
import zd.dms.dto.request.DocumentLendRequest;
import zd.dms.entities.User;
import zd.dms.services.document.DocumentLendingService;

@Slf4j
@RequiredArgsConstructor
@RestController
@Tag(name = "DocumentLendingController", description = "文档借阅")
@RequestMapping("/document/lending")
public class DocumentLendingController {

    private final DocumentLendingService documentLendingService;

    @Operation(summary = "借出文档")
    @PostMapping("/lend")
    public JSONResultUtils<Void> lend(@RequestBody DocumentLendRequest lendRequest) {

        List<String> documentIds = ValidateUtils.notEmpty(lendRequest.getDocumentIds(), "请选择文档");
        List<String> borrowerUserNames = ValidateUtils.notEmpty(lendRequest.getBorrowerUserNames(), "请选择借出用户");
        int timeValue = ValidateUtils.inclusiveBetween(1, 20000, lendRequest.computeTimeValue(),
            "请输入借出时间，范围在1-20000小时之间");
        List<String> permissions = lendRequest.getPermissions();
        User user = UserContextHolder.getUser();

        documentLendingService.createLendings(documentIds, borrowerUserNames, timeValue, permissions, user);

        return JSONResultUtils.success();
    }

    @Operation(summary = "搜索我借阅的文档")
    @GetMapping("/searchLendToMe")
    public JSONResultUtils<Page<DocumentLendingDTO>> searchLendToMe(
        @RequestParam(name = "fileName", required = false) @Parameter(description = "文件名") String fileName,
        @RequestParam(name = "lenderFullName", required = false) @Parameter(description = "借出人姓名") String lenderFullName,
        @ParameterObject Pageable pageable
    ) {

        var page = documentLendingService.searchLendToMe(
            UserContextHolder.getUsername(),
            fileName,
            lenderFullName,
            pageable
        );

        return JSONResultUtils.successWithData(page);
    }

    @Operation(summary = "搜索我借出的文档")
    @GetMapping("/searchLendFromMe")
    public JSONResultUtils<Page<DocumentLendingDTO>> searchLendFromMe(
        @RequestParam(name = "fileName", required = false) @Parameter(description = "文件名") String fileName,
        @RequestParam(name = "borrowerFullName", required = false) @Parameter(description = "借阅人姓名") String borrowerFullName,
        @ParameterObject Pageable pageable
    ) {

        var page = documentLendingService.searchLendFromMe(
            UserContextHolder.getUsername(),
            fileName,
            borrowerFullName,
            pageable
        );

        return JSONResultUtils.successWithData(page);
    }

    @Operation(summary = "收回我借出的文档")
    @PostMapping("/recallDocument")
    public JSONResultUtils<Void> recallBorrowedDocument(
        @RequestParam(name = "ids") @Parameter(description = "借阅ID集合") List<Long> ids) {
        ValidateUtils.notEmpty(ids, "请选择要收回的文档");

        documentLendingService.recallDocument(ids);

        return JSONResultUtils.success();
    }
}
