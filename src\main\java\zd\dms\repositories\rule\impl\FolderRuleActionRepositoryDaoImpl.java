package zd.dms.repositories.rule.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.FolderRuleAction;
import zd.dms.repositories.rule.FolderRuleActionRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;

public class FolderRuleActionRepositoryDaoImpl extends BaseRepositoryDaoImpl<FolderRuleAction, String> implements FolderRuleActionRepository {

    public FolderRuleActionRepositoryDaoImpl(Class<FolderRuleAction> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<FolderRuleAction> getActionsByRule(String ruleId) {
        Specification<FolderRuleAction> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderRuleAction> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("ruleId", ruleId);
            specTools.asc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void deleteActionsByRule(String ruleId) {
        String hql = "delete from FolderRuleAction where ruleId = ?1";
        executeUpdate(hql, ruleId);
    }

    @Override
    public void deleteActionsByFolder(long folderId) {
        String hql = "delete from FolderRuleAction where folderId = ?1";
        executeUpdate(hql, folderId);
    }
}
