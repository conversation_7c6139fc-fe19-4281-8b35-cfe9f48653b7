package zd.dms.controllers.sso;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapTool;
import zd.dms.entities.User;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.security.SecurityInfo;
import zd.dms.services.user.UserService;
import zd.dms.utils.WXUtils;

import java.util.Map;

@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "WeChatController", description = "微信Controller")
@RequestMapping("/sso/wx")
public class WeChatController extends ControllerSupport {

    private final UserService userService;

    @Operation(summary = "微信用户登陆")
    @RequestMapping(value = "/login", method = {RequestMethod.GET, RequestMethod.POST})
    @ZDLog("微信用户登陆")
    public JSONResultUtils<Map<String, Object>> login(@RequestParam(required = false) String code, @RequestBody(required = false) Map<String, Object> condition,
                                                      @RequestHeader("User-Agent") String userAgent, HttpServletRequest request, HttpServletResponse response) {

        code = MapUtils.getString(condition, "password", code);
        log.debug("code -> {}", code);

        if (StringUtils.isBlank(code)) {
            return JSONResultUtils.error("code不能为空");
        }

        String token = WXUtils.getAccessToken();
        if (token == null) {
            return JSONResultUtils.error("未获取到令牌，请检查系统后台企业配置和网络情况");
        }

        String wxId = WXUtils.getMemberGuidByCode(token, code);
        if (StringUtils.isBlank(wxId)) {
            return JSONResultUtils.error("企业微信授权失败，请在系统设置中检查设置");
        }

        User u = userService.getUserByQywxEmployeeId(wxId);
        if (u == null) {
            SecurityInfo.setLoginErrorIP(getIpAddress());
            return JSONResultUtils.error("本用户没有设置企业微信账号");
        }

        // 是否可以使用移动端
        if (LoginUtils.isMobile(userAgent)) {
            if (!LoginUtils.isHasMobileAccess(u)) {
                return JSONResultUtils.error("禁止移动端登陆");
            }
        }

        String username = u.getUsername();
        LoginUtils.allowLogin(username);
        LoginUtils.allowAdminLogin(u);

        LoginUtils.login(username, "", false, false, request, response);

        logInfo("用户 " + getCurrentUser().getFullname() + " 通过钉钉登录系统");
        log.debug("用户 {} 通过钉钉登录成功", username);

        Map<String, Object> jsonData = LoginUtils.getUserData(u, request);
        return JSONResultUtils.successWithData(jsonData);
    }

    @Operation(summary = "获取微信AppId")
    @GetMapping("/appId")
    @ZDLog("获取微信AppId")
    public JSONResultUtils<Object> getAppId() {
        String qywxCorpId = SystemConfigManager.getInstance().getProperty("qywxCorpId");
        Map<String, Object> resultsMap = ZDMapTool.getInstance().put("appId", qywxCorpId).getMap();
        return JSONResultUtils.successWithData(resultsMap);
    }
}
