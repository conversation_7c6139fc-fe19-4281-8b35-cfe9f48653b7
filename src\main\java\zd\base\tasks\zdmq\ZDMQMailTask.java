package zd.base.tasks.zdmq;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zd.base.tasks.AbstractTask;
import zd.dms.utils.zdmq.ZDMQConfigUtils;
import zd.dms.utils.zdmq.ZDMQDBUtils;

import java.util.List;
import java.util.Map;

/**
 * mq任务
 */
@Component
@Slf4j
public class ZDMQMailTask extends AbstractTask {

    @Scheduled(cron = "20 */1 * * * ?")
    @Scheduled(initialDelay = 60000)
    @Override
    public void work() {
        String batchNo = ZDMQConfigUtils.getBatchNo();
        int batchSize = 1;
        while (true) {
            List<Map<String, Object>> nextMsgs = ZDMQDBUtils.getNextMsgs(ZDMQConfigUtils.TYPE_MAIL, batchNo, batchSize);
            if (CollectionUtils.isEmpty(nextMsgs)) {
                break;
            }

            nextMsgs.forEach(item -> {
                String id = MapUtils.getString(item, "ID", "");
                String msg = MapUtils.getString(item, "MSG", "");
                if (StringUtils.isBlank(msg)) {
                    ZDMQDBUtils.removeMsg(id);
                    return;
                }

                ZDMQDBUtils.updateFailedStatus(id, "处理失败", item);
            });

            if (nextMsgs.size() < batchSize) {
                break;
            }
        }
    }
}
