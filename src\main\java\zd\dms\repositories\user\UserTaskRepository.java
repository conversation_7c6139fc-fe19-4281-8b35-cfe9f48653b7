package zd.dms.repositories.user;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.UserTask;

import java.util.Date;

public interface UserTaskRepository extends BaseRepository<UserTask, String> {

    Page getUserTasks(String username, String type, String status, int pageNumber, int pageSize);

    Page getUserTasksBeforeDate(Date beforeDate, int pageNumber, int pageSize);

}
