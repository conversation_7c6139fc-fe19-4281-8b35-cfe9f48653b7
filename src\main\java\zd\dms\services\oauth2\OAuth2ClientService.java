package zd.dms.services.oauth2;

import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;
import zd.base.service.BaseJpaService;
import zd.base.utils.JSONResultUtils;
import zd.dms.entities.oauth2.OAuth2Client;

import java.util.List;
import java.util.Map;

/**
 * OAuth2客户端服务接口
 */
public interface OAuth2ClientService extends BaseJpaService<OAuth2Client, String> {

    JSONResultUtils<Object> save(Map<String, Object> params);

    JSONResultUtils<Object> update(Map<String, Object> params);

    Page list(@RequestBody Map<String, Object> params);

    /**
     * 根据客户端ID查找客户端
     *
     * @param clientId 客户端ID
     * @return 客户端
     */
    OAuth2Client findByClientId(String clientId);

    /**
     * 保存客户端
     *
     * @param client 客户端
     * @return 保存后的客户端
     */
    void save(OAuth2Client client);

    /**
     * 查找所有客户端
     *
     * @return 客户端列表
     */
    List<OAuth2Client> findAll();

    /**
     * 删除客户端
     *
     * @param id 客户端ID
     */
    void deleteById(String id);

    /**
     * 验证客户端凭证
     *
     * @param clientId     客户端ID
     * @param clientSecret 客户端密钥
     * @return 是否有效
     */
    boolean validateClientCredentials(String clientId, String clientSecret);
}
