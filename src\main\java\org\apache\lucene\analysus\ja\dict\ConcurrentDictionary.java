package org.apache.lucene.analysus.ja.dict;

import jakarta.servlet.http.HttpSession;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.utils.redis.ZDRedisUtils;
import zd.base.utils.system.SpringUtils;
import zd.dms.Constants;
import zd.dms.entities.OU;
import zd.dms.services.user.OuService;
import zd.dms.services.user.UserGroupUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;


public class ConcurrentDictionary {

    private static final Logger log = LoggerFactory.getLogger(ConcurrentDictionary.class);

    private static final OuService ouService = SpringUtils.getBean(OuService.class);

    private static final Object locker = new Object();

    public static final String ULR = "已达到最大并发用户数限制，请稍后再试";

    public static void updateLastAccessDate(String userId) {
        addRedisLastAccessDate(userId, System.currentTimeMillis());
    }

    public static boolean isOnline(String userId) {
        return hasRedisLastAccessDate(userId);
    }

    public static void checkOnlineUsers() {
        synchronized (ConcurrentDictionary.class) {
            Map<String, Object> redisLastAccessDateMap = getRedisLastAccessDateMap();
            Set<Map.Entry<String, Object>> entries = redisLastAccessDateMap.entrySet();

            List<String> removeUserIds = new ArrayList<>();
            for (Map.Entry<String, Object> entry : entries) {
                String userId = entry.getKey();
                long value = NumberUtils.toLong(entry.getValue() + "", 0L);
                if (value == 0) {
                    log.debug("remove ou userId:{}", userId);
                    ouService.removeOU(userId);
                    removeUserIds.add(userId);
                }

                if ((System.currentTimeMillis() - value) > 41 * 1000) {
                    log.debug("remove ou userId:{} lastAccessTime:{}", userId, value);
                    ouService.removeOU(userId);
                    removeUserIds.add(userId);
                }
            }

            for (String userId : removeUserIds) {
                removeRedisLastAccessDate(userId);
            }
        }
    }

    public static boolean vou(HttpSession s) {
        if (s == null) {
            return false;
        }

        String userId = (String) s.getAttribute(Constants.SESSION_KEY);
        String randomToken = (String) s.getAttribute(Constants.SESSION_RANDOM_TOKEN);
        return vou(userId, randomToken);
    }

    /**
     * 验证用户token是否正确
     */
    public static boolean vou(String userId, String randomToken) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(randomToken)) {
            return false;
        }

        String currentRandomToken = getRedisUserIdAndRandom(userId);
        if (randomToken.equals(currentRandomToken)) {
            return true;
        }

        OU ou = ouService.getOUByUserId(userId);
        if (ou == null) {
            return false;
        }

        String sessionId = ou.getSessionId();
        if (!randomToken.equals(sessionId)) {
            return false;
        }

        addRedisUserIdAndRandom(userId, randomToken);

        return true;
    }

    /**
     * 将用户加入在线列表
     *
     * @param s
     * @param username
     */
    public static boolean au(HttpSession s, String username, boolean force) {
        synchronized (locker) {
            try {
                String userId = (String) s.getAttribute(Constants.SESSION_KEY);

                String randomToken = (String) s.getAttribute(Constants.SESSION_RANDOM_TOKEN);
                if (StringUtils.isBlank(randomToken)) {
                    log.error("au randomToken is null");
                    return false;
                }

                if (StringUtils.isNotBlank(userId)) {
                    log.debug("AddUserToOnlineUser: {}", userId);


                    if (!force) {
                        OU ou = ouService.getOUByUserId(userId);
                        if (ou != null) {
                            String sessionId = ou.getSessionId();
                            if (!randomToken.equals(sessionId)) {
                                return false;
                            }
                        }
                    }

                    ouService.addOU(userId, username, randomToken);
                    addRedisLastAccessDate(userId, System.currentTimeMillis());
                    addRedisUserIdAndRandom(userId, randomToken);
                    return true;
                }

                return false;
            } catch (Throwable t) {
                log.error("ex in au", t);
            }

            return false;
        }
    }

    /**
     * 将用户从在线列表移除
     */
    public static void ru(HttpSession s) {
        String userId = (String) s.getAttribute(Constants.SESSION_KEY);
        ru(userId);
        s.setAttribute(Constants.SESSION_KEY, null);
    }

    public static void ru(String userId) {
        synchronized (locker) {
            try {
                if (StringUtils.isNotBlank(userId)) {
                    ouService.removeOU(userId);
                    removeRedisLastAccessDate(userId);
                    removeRedisUserIdAndRandom(userId);
                }
            } catch (Throwable t) {
                log.error("ex in ru", t);
            }
        }
    }

    /**
     * 检测用户是否已经达到上限
     *
     * @return 用户是否已经达到上限
     */
    public static boolean isr() {
        if (ouService.getOUCount() >= TaskDictionary.getMainDefinition().getC()) {
            log.debug("已达到最大用户数db：{}", TaskDictionary.getMainDefinition().getC());
            return true;
        }

        return false;
    }

    /**
     * 并发授权模式下，检测用户是否已经达到上限
     *
     * @param userId
     * @param username
     * @return 并发用户是否已经达到上限
     */
    public static boolean isrbf(String userId, String username) {
        int onlineCountDB = ouService.getOUCount();

        // 如果自己已在线，在线数-1
        OU ou = ouService.getOUByUserId(userId);
        if (ou != null) {
            log.debug("isrbf UserDB: {} already exists, minus one");
            onlineCountDB = onlineCountDB - 1;
        }

        if (onlineCountDB >= TaskDictionary.getMainDefinition().getBFC() && !UserGroupUtils.ADMIN_USERNAME.equals(username)) {
            log.debug("已达到最大并发用户数db：{}", TaskDictionary.getMainDefinition().getBFC());
            return true;
        }

        return false;
    }

    public static void lout() {
        lout(5);
    }

    public static void lout(int count) {


        Map<String, Object> redisLastAccessDateMap = getRedisLastAccessDateMap();
        Set<String> keys = redisLastAccessDateMap.keySet();

        if (keys.size() > count) {
            List<String> needRemoveUserIds = new ArrayList<>();
            int i = 1;
            for (String userId : keys) {
                if (i > count) {
                    needRemoveUserIds.add(userId);
                }

                i++;
            }

            for (String userId : needRemoveUserIds) {
                ru(userId);
            }
        }
    }

    private static void addRedisUserIdAndRandom(String userId, String randomStr) {
        ZDRedisUtils.hset("OU_ONLINE_USERS", userId, randomStr);
    }

    private static String getRedisUserIdAndRandom(String userId) {
        return ZDRedisUtils.hget("OU_ONLINE_USERS", userId) + "";
    }

    private static void removeRedisUserIdAndRandom(String userId) {
        ZDRedisUtils.hdel("OU_ONLINE_USERS", userId);
    }

    private static void addRedisLastAccessDate(String userId, Long time) {
        ZDRedisUtils.hset("USER_LAST_ACCESS_DATE", userId, time);
    }

    private static long getRedisLastAccessDate(String userId) {
        return NumberUtils.toLong(ZDRedisUtils.hget("USER_LAST_ACCESS_DATE", userId) + "", 0L);
    }

    private static void removeRedisLastAccessDate(String userId) {
        ZDRedisUtils.hdel("USER_LAST_ACCESS_DATE", userId);
    }

    private static boolean hasRedisLastAccessDate(String userId) {
        return ZDRedisUtils.hHasKey("USER_LAST_ACCESS_DATE", userId);
    }

    private static Map<String, Object> getRedisLastAccessDateMap() {
        return ZDRedisUtils.hmget("USER_LAST_ACCESS_DATE");
    }
}
