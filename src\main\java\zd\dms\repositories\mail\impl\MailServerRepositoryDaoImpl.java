package zd.dms.repositories.mail.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.MailServerInfo;
import zd.dms.entities.PropDataItem;
import zd.dms.repositories.mail.MailServerRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;

public class MailServerRepositoryDaoImpl extends BaseRepositoryDaoImpl<MailServerInfo, String> implements MailServerRepository {

    public MailServerRepositoryDaoImpl(Class<MailServerInfo> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<MailServerInfo> getEnabledServer() {
        Specification<MailServerInfo> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<MailServerInfo> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("enabled", true);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }
}
