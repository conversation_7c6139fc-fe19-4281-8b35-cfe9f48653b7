package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import zd.base.entities.AbstractSequenceEntity;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "sysconf")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class SystemConfigItem extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = 8720941523775931509L;

    /**
     * 来自zdecm.cfg的配置项，暂时未使用
     */
    public static final int TYPE_CONF = 1;

    /**
     * 来自ecm.properties的配置项
     */
    public static final int TYPE_ECM_PROP = 2;

    @Column(unique = true)
    private String keyName;

    @Column(length = Length.LOB_DEFAULT)
    private String keyValue;

    /**
     * 默认构造器
     */
    public SystemConfigItem() {
        super();
    }

    public List<String> getValueList() {
        List<String> result = new LinkedList<String>();

        if (StringUtils.isBlank(keyValue)) {
            return result;
        }

        String[] strArray = keyValue.split("\n");
        for (String s : strArray) {
            if (StringUtils.isNotBlank(s)) {
                result.add(s.trim());
            }
        }

        return result;
    }

    public String getKeyName() {
        return keyName;
    }

    public void setKeyName(String keyName) {
        this.keyName = keyName;
    }

    public String getKeyValue() {
        return keyValue;
    }

    public void setKeyValue(String keyValues) {
        this.keyValue = keyValues;
    }
}
