package zd.dms.repositories.document;

import zd.base.repositories.BaseRepository;
import zd.dms.entities.Folder;
import zd.dms.entities.FolderSubscribe;
import zd.dms.entities.User;

import java.util.List;

/**
 * <AUTHOR>
 * @version $Revision$
 */
public interface FolderSubscribeRepository extends BaseRepository<FolderSubscribe, Long> {

    FolderSubscribe getSubscribe(Folder folder, User user);

    void deleteSubscribe(Folder folder, User user);

    void deleteAllSubscribesByUser(User user);

    void deleteAllSubscribesByFolder(Folder folder);

    List<FolderSubscribe> getSubscribes(Folder folder);

    // docs
    FolderSubscribe getSubscribe(String docId, User user);

    void deleteAllSubscribesByDocId(String docId);

    List<FolderSubscribe> getSubscribes(String docId);

    void deleteSubscribe(String docId, User user);
}
