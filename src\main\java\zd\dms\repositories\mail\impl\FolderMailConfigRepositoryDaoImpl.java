package zd.dms.repositories.mail.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.FolderMailConfig;
import zd.dms.entities.MailServerInfo;
import zd.dms.repositories.mail.FolderMailConfigRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;

public class FolderMailConfigRepositoryDaoImpl extends BaseRepositoryDaoImpl<FolderMailConfig, String> implements FolderMailConfigRepository {

    public FolderMailConfigRepositoryDaoImpl(Class<FolderMailConfig> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<FolderMailConfig> getMailConfigsById(long folderId) {
        Specification<FolderMailConfig> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderMailConfig> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("folderId", folderId);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void deleteByFolderId(long folderId) {
        String hql = "delete from FolderMailConfig where folderId = ?1";
        executeUpdate(hql, folderId);
    }
}
