package zd.base.tasks;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zd.base.utils.system.PropsUtils;
import zd.dms.services.mail.FolderMailService;
import zd.dms.utils.abbyy.AbbyyOcrUtils;
import zd.dms.utils.ldap.LdapWorkUtils;

@Component
@Slf4j
public class SyncLDAPGroupsAndUsersTask extends AbstractTask {

    @Scheduled(cron = "0 0 22 * * ?")
    @Scheduled(initialDelay = 60000)
    @Override
    public void work() {
        LdapWorkUtils.startLdapImport();
    }
}
