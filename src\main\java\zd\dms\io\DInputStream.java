//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                           O\  =  /O
//                        ____/`---'\____
//                      .'  \\|     |//  `.
//                     /  \\|||  :  |||//  \
//                    /  _||||| -:- |||||-  \
//                    |   | \\\  -  /// |   |
//                    | \_|  ''\---/''  |   |
//                    \  .-\__  `-`  ___/-. /
//                  ___`. .'  /--.--\  `. . __
//               ."" '<  `.___\_<|>_/___.'  >'"".
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |
//              \  \ `-.   \_ __\ /__ _/   .-` /  /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
//                    佛祖保佑       永无BUG
package zd.dms.io;

import java.io.FilterInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * XorInputStream is an inputstream that Xor all input bytes with the byte mask
 * given in the constructor. Use to decode output of a XorOutputStream by giving
 * it the XorInputStream the same byte mask as XorOutputStream. XorInputStream
 * and XorOutputStream can be used for a very weak form of encryption.
 * 
 * @version 1.0 14 Feb 1998
 * <AUTHOR> Whitney (<a
 *         href=mailto:<EMAIL>><EMAIL></a>)
 */
public class DInputStream extends FilterInputStream {
	byte codeMask = 0;

	/**
	 * Create an XorInputStream on the given input stream.
	 * 
	 * @param mask the bit pattern with which all input bytes will be Xored
	 */
	public DInputStream(InputStream in, byte mask) {
		super(in);
		codeMask = mask;
	}

	/**
	 * Reads the next byte of data and Xors it with the mask given in the
	 * constructor. If no byte is available because the end of the stream has
	 * been reached, the value <code>-1</code> is returned. This method blocks
	 * until input data is available, the end of the stream is detected, or an
	 * exception is thrown.
	 * 
	 * @return the next byte of data, or <code>-1</code> if the end of the
	 *         stream is reached.
	 * @exception IOException if an I/O error occurs.
	 */
	public int read() throws IOException {
		int input = in.read();
		if (input < 0)
			return input;
		else
			return (byte) input ^ codeMask;
	}

	/**
	 * Reads up to <code>length</code> bytes of data and Xor each byte. This
	 * method blocks until some input is available.
	 * 
	 * @param inputBuffer the buffer into which the data is read.
	 * @param offset the start offset of the data.
	 * @param length the maximum number of bytes read.
	 * @return the total number of bytes read into the buffer, or
	 *         <code>-1</code> if there is no more data because the end of the
	 *         stream has been reached.
	 * @exception IOException if an I/O error occurs.
	 */
	public int read(byte inputBuffer[], int offset, int length)
			throws IOException {
		byte[] codedInput = new byte[length];
		int bytesRead = in.read(codedInput);

		for (int k = 0; k < bytesRead; k++)
			inputBuffer[offset + k] = (byte) (codedInput[k] ^ codeMask);
		return bytesRead;
	}

}
