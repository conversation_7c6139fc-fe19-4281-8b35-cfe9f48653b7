package zd.dms.services.document;

import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.dms.entities.OutLink;

import java.util.List;

@Transactional
public interface OutLinkService extends BaseJpaService<OutLink, String> {

    @Transactional(readOnly = true)
    OutLink getOutLinkByUrl(String url);

    @Transactional(readOnly = true)
    List<OutLink> getOutLinksByUser(String username);

    Page getOutLinksPageByUser(String username, int pageNumber, int pageSize);

    Page getOutLinksPage(int pageNumber, int pageSize);

    @Transactional(readOnly = true)
    List<OutLink> getExpired();

    @Transactional(readOnly = true)
    List<OutLink> getReachedLimit();

    void deleteUseless();

    void createOutLink(OutLink outLink);

    void deleteOutLinkById(String id);

    void updateOutLink(OutLink outLink);
}
