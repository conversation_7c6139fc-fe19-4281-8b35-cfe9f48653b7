package zd.dms.controllers.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.model.ObjectMapperUtils;
import zd.base.utils.validate.ValidateUtils;
import zd.dms.Constants;
import zd.dms.entities.MailQueueItem;
import zd.dms.entities.MailServerInfo;
import zd.dms.entities.Role;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.mail.MailService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "MailController", description = "邮件设置Controller")
@RequestMapping("/admin/mail")
public class MailController extends ControllerSupport {

    private final MailService mailService;

    private final SystemConfigManager scm = SystemConfigManager.getInstance();

    @Operation(summary = "获取邮件服务器")
    @GetMapping("/getServerById/{id}")
    @ZDLog("获取邮件服务器")
    public JSONResultUtils<Object> getServer(@PathVariable String id) {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);
        MailServerInfo serverInfo = mailService.getServerById(id);
        return successData(ObjectMapperUtils.toMap(serverInfo));
    }

    @Operation(summary = "获取邮件服务器列表")
    @GetMapping("/listServer")
    @ZDLog("获取邮件服务器列表")
    public JSONResultUtils<Object> listServer() {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);

        boolean mailMerge = scm.getBooleanProperty("mailMerge", true);

        Map<String, Object> props = new HashMap<>();
        props.put("mailMerge", mailMerge);

        List<MailServerInfo> mailServers = mailService.getMailServers();
        props.put("mailServers", ObjectMapperUtils.toMapList(mailServers));

        Page pageMailQueue = mailService.getPageMailQueue(1, 100);
        if (pageMailQueue != null) {
            props.put("mailQueue", ObjectMapperUtils.toMapList(pageMailQueue.getContent()));
        }

        return successData(props);
    }

    @Operation(summary = "创建邮件服务器")
    @PostMapping("/createServer")
    @ZDLog("创建邮件服务器")
    public JSONResultUtils<Object> createServer(@RequestBody Map<String, Object> params) {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);

        String host = MapUtils.getString(params, "host", "");
        ValidateUtils.isTrue(StringUtils.isNotBlank(host), "请填写主机");
        ValidateUtils.isTrue(host.length() <= 100, "邮件主机最多100个字");

        int port = MapUtils.getIntValue(params, "port", 0);
        ValidateUtils.isTrue(port > 0 && port < 60000, "请填写正确的端口");

        String username = MapUtils.getString(params, "username", "");
        String password = MapUtils.getString(params, "password", "");
        String fromAddr = MapUtils.getString(params, "fromAddr", "");
        boolean useSsl = MapUtils.getBooleanValue(params, "useSsl", false);
        boolean useTls = MapUtils.getBooleanValue(params, "useTls", false);
        int maxSendPerMin = MapUtils.getIntValue(params, "maxSendPerMin", 0);

        MailServerInfo serverInfo = new MailServerInfo();
        serverInfo.setHost(host);
        serverInfo.setPort(port);
        serverInfo.setUsername(username);
        serverInfo.setPassword(password);
        serverInfo.setFromAddr(fromAddr);
        serverInfo.setUseSsl(useSsl);
        serverInfo.setUseTls(useTls);
        serverInfo.setMaxSendPerMin(maxSendPerMin);
        mailService.addServer(serverInfo);

        return success();
    }

    @Operation(summary = "更新邮件服务器")
    @PostMapping("/updateServer")
    @ZDLog("更新邮件服务器")
    public JSONResultUtils<Object> updateServer(@RequestBody Map<String, Object> params) {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);

        String host = MapUtils.getString(params, "host", "");
        ValidateUtils.isTrue(StringUtils.isNotBlank(host), "请填写主机");
        ValidateUtils.isTrue(host.length() <= 100, "邮件主机最多100个字");

        int port = MapUtils.getIntValue(params, "port", 0);
        ValidateUtils.isTrue(port > 0 && port < 60000, "请填写正确的端口");

        String username = MapUtils.getString(params, "username", "");
        String password = MapUtils.getString(params, "password", "");
        String fromAddr = MapUtils.getString(params, "fromAddr", "");
        boolean useSsl = MapUtils.getBooleanValue(params, "useSsl", false);
        boolean useTls = MapUtils.getBooleanValue(params, "useTls", false);
        int maxSendPerMin = MapUtils.getIntValue(params, "maxSendPerMin", 0);
        boolean enabled = MapUtils.getBooleanValue(params, "enabled", false);

        MailServerInfo serverInfo = mailService.getServerById(MapUtils.getString(params, "id"));
        serverInfo.setHost(host);
        serverInfo.setPort(port);
        serverInfo.setUsername(username);
        serverInfo.setPassword(password);
        serverInfo.setFromAddr(fromAddr);
        serverInfo.setUseSsl(useSsl);
        serverInfo.setUseTls(useTls);
        serverInfo.setMaxSendPerMin(maxSendPerMin);
        serverInfo.setEnabled(enabled);
        mailService.updateServer(serverInfo);

        return success();
    }

    @Operation(summary = "删除邮件服务器")
    @PostMapping("/deleteServer/{id}")
    @ZDLog("删除邮件服务器")
    public JSONResultUtils<Object> deleteServer(@PathVariable String id) {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);
        mailService.deleteServer(id);
        return success();
    }

    @Operation(summary = "批量删除邮件服务器")
    @PostMapping("/deleteServers")
    @ZDLog("批量删除邮件服务器")
    public JSONResultUtils<Object> deleteServer(@RequestBody List<String> ids) {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);
        mailService.deleteServers(ids);
        return success();
    }

    @Operation(summary = "从邮件队列中批量删除邮件")
    @PostMapping("/deleteMails")
    @ZDLog("从邮件队列中批量删除邮件")
    public JSONResultUtils<Object> deleteMails(@RequestBody List<String> ids) {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);
        mailService.deleteMailQueueItems(ids);
        return success();
    }

    @Operation(summary = "清空邮件发送队列")
    @PostMapping("/clearQueue")
    @ZDLog("清空邮件发送队列")
    public JSONResultUtils<Object> clearQueue() {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);
        mailService.deleteAllMailQueueItem();
        return success();
    }

    @Operation(summary = "测试邮件服务器")
    @PostMapping("/testServer/{id}")
    @ZDLog("测试邮件服务器")
    public JSONResultUtils<Object> testServer(@PathVariable String id) {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);

        ValidateUtils.isTrue(StringUtils.isNotBlank(getCurrentUser().getEmail()), "请先在个人设置中设置您的电子邮件地址后再进行测试");

        MailServerInfo server = mailService.getServerById(id);
        ValidateUtils.isTrue(server != null, "指定邮件服务器不存在");

        boolean result = mailService.doSendMail(server, getCurrentUser().getEmail(), getCurrentUser().getEmail(), "这是一封来自" +
                Constants.getProductShortName() + "的测试邮件", "测试邮件正文", false, null);

        String resultMsg = result ? "邮件服务器测试成功" : "邮件服务器测试失败，请检查设置";

        return JSONResultUtils.successWithMsgWhole(resultMsg);
    }

    @Operation(summary = "重发全部邮件")
    @PostMapping("/resetAllMail")
    @ZDLog("重发全部邮件")
    public JSONResultUtils<Object> resetAllMail() {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);

        int count = mailService.resetAllMail();

        return JSONResultUtils.successWithMsgWhole(count + " 封邮件已进入重发队列");
    }

    @Operation(summary = "重发邮件")
    @PostMapping("/resetMail/{id}")
    @ZDLog("重发邮件")
    public JSONResultUtils<Object> resetMail(@PathVariable String id) {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);

        MailQueueItem item = mailService.getMail(id);
        ValidateUtils.isTrue(item != null, "指定邮件不存在");

        ValidateUtils.isTrue(MailQueueItem.STATUS_FAILED == item.getStatus(), "邮件正在等待发送");

        item.setStatus(MailQueueItem.STATUS_WAIT);
        mailService.updateMailQueueItem(item);

        return success();
    }

    @Operation(summary = "更改邮件发送模式")
    @PostMapping("/mergeMail")
    @ZDLog("更改邮件发送模式")
    public JSONResultUtils<Object> mergeMail(@RequestBody Map<String, Object> params) {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);

        boolean mailMerge = MapUtils.getBooleanValue(params, "mailMerge", true);

        scm.setProperty("mailMerge", mailMerge);
        scm.save();

        String displayMailMerge = mailMerge ? "合并发送" : "逐条发送";

        return JSONResultUtils.successWithMsgWhole("邮件发送模式更改为：" + displayMailMerge);
    }
}
