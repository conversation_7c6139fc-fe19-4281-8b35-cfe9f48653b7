package zd.base.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import zd.base.aspect.OperationLoggerAspect;

@Configuration
@EnableAspectJAutoProxy
public class AspectConfig {

    @Bean
    public OperationLoggerAspect operationLoggerAspect() {
        return new OperationLoggerAspect();
    }
}
