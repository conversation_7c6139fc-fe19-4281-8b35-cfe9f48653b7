package zd.base.utils.constant;

import java.util.HashMap;
import java.util.Map;

public class ErrorCodeDefine {

    // 第1位（固定, 标识错误出于哪个系统）;第2位（错误级别，1为非系统bug,
    // 2为系统bug需要改代码）;第3-4位（功能模块）;第5-7位（错误编码，从001开始，依次顺延）
    // 例如：x101000

    public static final Map<String, String> ERROR_CODE_MSG = new HashMap<>();

    public static String SUCCESS = "0";

    public static String UNDEFINED_ERROR = "u100000";
    public static String SYSTEM_ERROR = "u200000";

    static {
        /******* S 系统级别模块 00 ************/
        ERROR_CODE_MSG.put(SUCCESS, "成功");
        ERROR_CODE_MSG.put(UNDEFINED_ERROR, "未定义的错误类型");
        ERROR_CODE_MSG.put("u100001", "请求方式错误");
        ERROR_CODE_MSG.put(SYSTEM_ERROR, "操作失败，请稍后重试");
        ERROR_CODE_MSG.put("u100002", "错误的请求参数");
        ERROR_CODE_MSG.put("u100003", "访问的数据不存在");
        ERROR_CODE_MSG.put("u100004", "访问次数受限制");
        ERROR_CODE_MSG.put("u100005", "访问拒绝，无效token");
        /******* E 系统级别模块 00 ************/
        ERROR_CODE_MSG.put("u141007", "权限不足，无法访问此功能");
        /******* S 用户模块 01 ************/
        ERROR_CODE_MSG.put("u101001", "用户已经存在");
        ERROR_CODE_MSG.put("u101002", "用户信息不能为空");
        ERROR_CODE_MSG.put("u101003", "username 不能为空");
        ERROR_CODE_MSG.put("u101004", "password 不能为空");
        ERROR_CODE_MSG.put("u101005", "所属部门不能为空");
        ERROR_CODE_MSG.put("u101006", "用户不存在");
        ERROR_CODE_MSG.put("u101007", "roleIds 参数不能为空");
        ERROR_CODE_MSG.put("u101008", "name 不能为空");
        ERROR_CODE_MSG.put("u101009", "permissionIds 与 permissionCodes 不能同时为空");
        ERROR_CODE_MSG.put("u101010", "角色不存在");
        ERROR_CODE_MSG.put("u101011", "权限码不能为空");
        ERROR_CODE_MSG.put("u101012", "权限码不能重复");
        ERROR_CODE_MSG.put("u101013", "role不能为空");
        ERROR_CODE_MSG.put("u101014", "权限有子权限,禁止删除");
        ERROR_CODE_MSG.put("u101015", "顶级权限禁止删除");
        ERROR_CODE_MSG.put("u101016", "无效的corpId");
        ERROR_CODE_MSG.put("u101017", "无效的userCode");
        ERROR_CODE_MSG.put("u101018", "企业已停用");
        ERROR_CODE_MSG.put("u101019", "获取userId失败");
        ERROR_CODE_MSG.put("u101020", "dingUserId对应的用户不存在");
        ERROR_CODE_MSG.put("u101021", "dingUserIds不能为空");
        ERROR_CODE_MSG.put("u101022", "msg参数不能为空");
        ERROR_CODE_MSG.put("u101023", "发送消息失败");
        ERROR_CODE_MSG.put("u101024", "用户编号不能为空");
        ERROR_CODE_MSG.put("u101025", "禁用参数不能为空");
        ERROR_CODE_MSG.put("u101026", "请先选择授权用户");
        ERROR_CODE_MSG.put("u101027", "部门不存在");
        ERROR_CODE_MSG.put("u101028", "目标类型不能为空");
        ERROR_CODE_MSG.put("u101029", "目标架构不能为空");
        /******* E 用户模块 01 ************/

        /******* S 部门模块 02 ************/
        ERROR_CODE_MSG.put("u102001", "部门名称不能为空");
        ERROR_CODE_MSG.put("u102002", "部门编码不能重复");
        ERROR_CODE_MSG.put("u102003", "同级部门名称不能重复");
        ERROR_CODE_MSG.put("u102004", "部门新增失败");
        ERROR_CODE_MSG.put("u102005", "部门修改失败");
        ERROR_CODE_MSG.put("u102006", "部门唯一编码不能为空");
        ERROR_CODE_MSG.put("u102007", "该部门下有用户无法删除");
        ERROR_CODE_MSG.put("u102008", "id 不能为空");
        ERROR_CODE_MSG.put("u102009", "父部门不能为其子部门");
        ERROR_CODE_MSG.put("u102010", "顶级部门无法删除");
        /******* E 部门模块 02 ************/

        /******* S 公司模块 03 ************/
        ERROR_CODE_MSG.put("u103001", "公司名称不能为空");
        ERROR_CODE_MSG.put("u103002", "购买时长不能小于0");
        ERROR_CODE_MSG.put("u103003", "客户数量不能为空");
        ERROR_CODE_MSG.put("u103004", "服务版本不能为空");
        ERROR_CODE_MSG.put("u103005", "id 不能为空");
        ERROR_CODE_MSG.put("u103006", "公司修改失败");
        ERROR_CODE_MSG.put("u103007", "公司唯一编码不能为空");
        ERROR_CODE_MSG.put("u103008", "插入续约信息失败");
        ERROR_CODE_MSG.put("u103009", "自定义编码不能重复");
        ERROR_CODE_MSG.put("u103010", "自定义编码不能为空");
        /******* E 公司模块 03 ************/

        /******* S 数据字典模块 04 ************/
        ERROR_CODE_MSG.put("u104001", "字典名称不能为空");
        ERROR_CODE_MSG.put("u104002", "字典类型不能为空");
        ERROR_CODE_MSG.put("u104003", "同类型字典值不能重复");
        ERROR_CODE_MSG.put("u104004", "id 不能为空");
        ERROR_CODE_MSG.put("u104005", "启用状态不能为空");
        /******* E 数据字典模块 04 ************/

        /******* S 公司续期模块 05 ************/
        ERROR_CODE_MSG.put("u105001", "公司唯一编码不能为空");
        ERROR_CODE_MSG.put("u105002", "增加次数值不能为空");
        ERROR_CODE_MSG.put("u105003", "续期天数值不能为空");
        ERROR_CODE_MSG.put("u105004", "版本不能为空");
        ERROR_CODE_MSG.put("u105005", "来源不能为空");
        /******* E 公司续期模块 05 ************/

        /******* S 降级设置模块 05 ************/
        ERROR_CODE_MSG.put("u106001", "功能描述不能为空");
        ERROR_CODE_MSG.put("u106002", "功能路径不能为空");
        ERROR_CODE_MSG.put("u106003", "功能状态不能为空");
        /******* E 降级设置模块 05 ************/

        /******* S 公有云模块 06 ************/
        ERROR_CODE_MSG.put("u107001", "注册类型不能为空");
        ERROR_CODE_MSG.put("u107002", "手机号不能为空");
        ERROR_CODE_MSG.put("u107003", "公司名称不能为空");
        ERROR_CODE_MSG.put("u107004", "密码不能为空");
        ERROR_CODE_MSG.put("u107005", "验证码错误");
        ERROR_CODE_MSG.put("u107006", "注册类型错误");
        ERROR_CODE_MSG.put("u107007", "您最多只能注册3个公司");
        ERROR_CODE_MSG.put("u107008", "姓名不能为空");
        ERROR_CODE_MSG.put("u107009", "邀请码不能为空");
        ERROR_CODE_MSG.put("u107010", "用户状态不能为空");
        ERROR_CODE_MSG.put("u107011", "公司唯一编码不能为空");
        ERROR_CODE_MSG.put("u107012", "登录类型不能为空");
        ERROR_CODE_MSG.put("u107013", "数据库名称不能为空");
        ERROR_CODE_MSG.put("u107014", "服务器路径不能为空");
        ERROR_CODE_MSG.put("u107015", "您已加入此公司");
        /******* E 公有云模块 06 ************/

        /******* S 应用目录模块 08 ************/
        ERROR_CODE_MSG.put("u108001", "目录名称不能为空");
        ERROR_CODE_MSG.put("u108002", "父目录编号不能为空");
        ERROR_CODE_MSG.put("u108003", "目录类型不能为空");
        ERROR_CODE_MSG.put("u108004", "目录类型错误");
        ERROR_CODE_MSG.put("u108005", "应用编号不能为空");
        ERROR_CODE_MSG.put("u108006", "应用名称不能为空");
        ERROR_CODE_MSG.put("u108007", "目录编号不能为空");
        ERROR_CODE_MSG.put("u108008", "目标目录编号不能为空");
        ERROR_CODE_MSG.put("u108009", "拖动类型不能为空");
        ERROR_CODE_MSG.put("u108010", "未查询到对应应用");
        ERROR_CODE_MSG.put("u108011", "目录不存在");

        /******* E 应用目录模块 08 ************/

        /******* S 表单模块 09 ************/
        ERROR_CODE_MSG.put("u109001", "表单名称不能为空");
        ERROR_CODE_MSG.put("u109002", "表单编号不能为空");
        ERROR_CODE_MSG.put("u109003", "表单不能为空");
        /******* E 表单模块 09 ************/

        /******* S 字段模块 10 ************/
        ERROR_CODE_MSG.put("u110001", "字段名称不能为空");
        ERROR_CODE_MSG.put("u110002", "排序号不能为空");
        ERROR_CODE_MSG.put("u110003", "标签类型不能为空");
        ERROR_CODE_MSG.put("u110004", "标签宽度级别不能为空");
        ERROR_CODE_MSG.put("u110005", "标签宽度级别错误");
        ERROR_CODE_MSG.put("u110006", "是否显示标签名称不能为空");
        ERROR_CODE_MSG.put("u110007", "排序号必须在1-999之间");
        ERROR_CODE_MSG.put("u110008", "字段编号不能为空");
        /******* E 字段模块 10 ************/

        /******* S 属性模块 11 ************/
        ERROR_CODE_MSG.put("u111001", "属性名称不能为空");
        ERROR_CODE_MSG.put("u111002", "属性值不能为空");
        /******* E 属性模块 11 ************/

        /******* S 数据模块 12 ************/
        ERROR_CODE_MSG.put("u112001", "数据ID不能为空");
        ERROR_CODE_MSG.put("u112002", "数据内容不能为空");
        ERROR_CODE_MSG.put("u112003", "主数据ID不能为空");
        ERROR_CODE_MSG.put("u112004", "数据编号不能为空");
        ERROR_CODE_MSG.put("u112005", "数据不存在");
        ERROR_CODE_MSG.put("u112006", "数据在流程中，无法删除");
        ERROR_CODE_MSG.put("u112007", "数据必填校验失败");
        ERROR_CODE_MSG.put("u112008", "数据唯一校验失败");
        /******* E 数据模块 12 ************/

        /******* S 角色模块 13 ************/
        ERROR_CODE_MSG.put("u113001", "角色名称不能为空");
        ERROR_CODE_MSG.put("u113002", "角色名称已存在");
        ERROR_CODE_MSG.put("u113003", "角色编号不能为空");
        ERROR_CODE_MSG.put("u113004", "该角色下有用户绑定不能删除");
        /******* E 角色模块 13 ************/

        /******* S 权限模块 14 ************/
        ERROR_CODE_MSG.put("u114001", "应用权限设置失败");
        ERROR_CODE_MSG.put("u114002", "应用权限类型不能为空");
        ERROR_CODE_MSG.put("u114003", "应用权限类型不存在");
        ERROR_CODE_MSG.put("u114004", "表单权限不能为空");
        ERROR_CODE_MSG.put("u114005", "权限不足,无法访问此功能");
        ERROR_CODE_MSG.put("u114006", "请求权限错误");
        ERROR_CODE_MSG.put("u114007", "权限不足，无法访问此功能");
        /******* E 权限模块 14 ************/

        /******* S 流程模块 15 ************/
        ERROR_CODE_MSG.put("u115001", "流程结构不能为空");
        ERROR_CODE_MSG.put("u115002", "流程更新失败");
        ERROR_CODE_MSG.put("u115003", "流程保存失败");
        ERROR_CODE_MSG.put("u115004", "流程编号不能为空");
        ERROR_CODE_MSG.put("u115005", "流程删除失败");
        ERROR_CODE_MSG.put("u115006", "流程启动失败，存在节点暂未设置办理人");
        ERROR_CODE_MSG.put("u115007", "流程启动失败");
        ERROR_CODE_MSG.put("u115008", "流程定义编号不能为空");
        ERROR_CODE_MSG.put("u115009", "流程定义不存在或流程未启动");
        ERROR_CODE_MSG.put("u115010", "发起流程失败，数据不存在");
        ERROR_CODE_MSG.put("u115011", "任务办理失败");
        ERROR_CODE_MSG.put("u115012", "流程定义不存在");
        ERROR_CODE_MSG.put("u115013", "任务退回失败");
        ERROR_CODE_MSG.put("u115014", "流程撤销失败");
        ERROR_CODE_MSG.put("u115015", "流程加签失败");
        ERROR_CODE_MSG.put("u115016", "流程转办失败");
        /******* E 流程模块 15 ************/

        /******* S 用户字段模块 16 ************/
        ERROR_CODE_MSG.put("u116001", "用户编码不能为空");
        ERROR_CODE_MSG.put("u116002", "表单编码不能为空");
        /******* E 用户字段模块 16 ************/

        /******* S 映射表单模块 17 ************/
        ERROR_CODE_MSG.put("u117001", "连接地址不能为空");
        ERROR_CODE_MSG.put("u117002", "连接用户不能为空");
        ERROR_CODE_MSG.put("u117003", "连接密码不能为空");
        ERROR_CODE_MSG.put("u117004", "数据表名不能为空");
        ERROR_CODE_MSG.put("u117005", "列信息不能为空");
        ERROR_CODE_MSG.put("u117006", "数据库名称不能为空");
        ERROR_CODE_MSG.put("u117007", "唯一字段不能为空");
        ERROR_CODE_MSG.put("u117008", "数据库类型不能为空");
        /******* E 映射表单模块 17 ************/

        /******* S 字典模块 18 ************/
        ERROR_CODE_MSG.put("u118001", "元组名称不能为空");
        ERROR_CODE_MSG.put("u118002", "英文名称不能为空");
        ERROR_CODE_MSG.put("u118003", "中文名称不能为空");
        ERROR_CODE_MSG.put("u118004", "元组唯一编码不能为空");
        ERROR_CODE_MSG.put("u118005", "单位类型不能为空");
        ERROR_CODE_MSG.put("u118006", "字典唯一编码不能为空");
        ERROR_CODE_MSG.put("u118007", "单位类型不匹配");
        /******* E 字典模块 18 ************/

        /******* S 模板模块 19 ************/
        ERROR_CODE_MSG.put("u119001", "模板名称不能为空");
        ERROR_CODE_MSG.put("u119002", "模板关联表单编码不能为空");
        ERROR_CODE_MSG.put("u119003", "模板内容不能为空");
        ERROR_CODE_MSG.put("u119004", "模板宽度必须大于0");
        ERROR_CODE_MSG.put("u119005", "模板高度必须大于0");
        ERROR_CODE_MSG.put("u119006", "模板状态不能为空");

        /******* E 模板模块 19 ************/

    }
}
