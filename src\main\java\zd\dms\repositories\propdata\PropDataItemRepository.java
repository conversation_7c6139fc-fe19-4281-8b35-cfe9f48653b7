package zd.dms.repositories.propdata;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.PropDataItem;

import java.util.List;

public interface PropDataItemRepository extends BaseRepository<PropDataItem, Long> {

    Page getItems(int pageNumber, int pageSize);

    PropDataItem getItemByName(String name);

    List<PropDataItem> getAllItems();
}
