package zd.base.controllers;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.transaction.Transactional;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.service.BaseJpaService;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.model.ObjectMapperUtils;
import zd.base.utils.validate.ValidateUtils;

import java.util.List;

@RestController
public abstract class BaseController<T, ID> extends ControllerSupport {

    public abstract BaseJpaService<T, ID> getBaseJpaService();

//    @Transactional
//    @Operation(summary = "通用数据获取")
//    @GetMapping("/get/{id}")
//    @ZDLog("通用数据获取")
//    public JSONResultUtils<Object> getById(@PathVariable ID id) {
//        T t = getBaseJpaService().findById(id);
//        if (t == null) {
//            return error("数据不存在");
//        }
//
//        return successData(ObjectMapperUtils.toMap(t, "get"));
//    }
//
//    @Operation(summary = "通用数据删除")
//    @PostMapping("/delete/{id}")
//    @ZDLog("通用数据删除")
//    public JSONResultUtils<Object> delete(@PathVariable ID id, HttpServletRequest request) {
//        getBaseJpaService().deleteById(id);
//
//        return successMsg("删除成功");
//    }
//
//    @Operation(summary = "通用数据批量删除")
//    @PostMapping("/delete")
//    @ZDLog("通用数据批量删除")
//    public JSONResultUtils<Object> delete(@RequestBody List<ID> ids, HttpServletRequest request) {
//        ValidateUtils.isTrue(CollectionUtils.isNotEmpty(ids), "u100002");
//        getBaseJpaService().deleteAllById(ids);
//
//        return successMsg("批量删除成功");
//    }

}
