package zd.dms.repositories.document;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.Document;
import zd.dms.entities.Folder;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $Revision$
 */
public interface DocumentRepository extends BaseRepository<Document, String> {

    /**
     * 批量获取一个文档目录下的所有的文档
     *
     * @param conn       数据库连接
     * @param docSql     获取文档的sql语句
     * @param folderId   文档目录id
     * @param startIndex 数据的起始位置
     * @param endIndex   数据的终止位置
     * @return 文档集合
     * @throws SQLException
     */
    List<Document> getDocumentsByFolderWithoutDeletedByJDBC(Connection conn, String docSql, Long folderId,
                                                            int startIndex, int endIndex) throws SQLException;

    List<Document> getDocumentByFolderAndSql(long folderId, String extraWhere);

    /**
     * 批量获取所有的文档
     *
     * @param conn       数据库连接
     * @param docSql     获取文档的sql语句
     * @param startIndex 数据的起始位置
     * @param endIndex   数据的终止位置
     * @return 文档集合
     * @throws SQLException
     */
    List<Document> getAllDocumentsWithoutDeletedByJDBC(Connection conn, String docSql,
                                                       int startIndex, int endIndex) throws SQLException;

    Document getDocumentByFolder(String filename, Folder folder);

    List<Document> getDocumentsByFolders(List<Long> folders);

    List<Document> getDocumentsByExtension(String extension);

    List<Document> getMostReadDocuments();

    Page<Document> getDocumentsWithoutAttach(int pageNumber, int pageSize);

    List<Document> getDocumentsByFolderWithoutDeleted(Folder folder, String sortField, boolean asc);

    Page<Document> getDocumentsByFolder(long folderId, String deleteByUsername, boolean isTrash, String keywords, Map<String, Object> advSearchKeywords, String sortField, boolean asc, int pageNumber, int pageSize);

    List<Document> getDeletedDocumentsByFolder(Folder folder);

    List<Document> getAllDocumentsByFolder(Folder folder);

    List<Document> getAllDocumentsByIndex(int startIndex, int pageSize);

    List<Document> getDocumentsInTrash(String username);

    List<Document> getDocumentsInTrash(String username, int type);

    Page getAllTrash(int pageNumber, int pageSize);

    int getDocumentCountByFolder(Folder folder);

    int getDocumentCountByFolderWithDeleted(Folder folder);

    boolean isDocumentInFolder(String filename, Folder folder, Document oriDoc);

    int getDCWithDeleted();

    int getDCWithoutDeleted();

    void incrementDocumentClickCount(Document doc);

    void resetEditingDocument(String username);

    void resetAllEditingDocument();

    List<Document> getDocumentInEditing(String username);

    List<Document> getDocumentLogByUser(String username, int logType, Date afterDate);

    Document getDocumentBySerial(String serial);

    List<Document> getUploadDocumentLogByUser(String username, Date afterDate);

    Page searchDocsByPage(int pageNumber, int pageSize, String content, String filename, String creatorFullname,
                          String sn, Date creationStartDate, Date creationEndDate, Date updateStartDate, Date updateEndDate,
                          Date customDate1Start, Date customDate1End, Date customDate2Start, Date customDate2End, String docProps,
                          List<Long> searchFolderIds, boolean withAnd);

    List<String> getImageList(long folderId);

    List<Document> getDocumentByDocProp(String sql);
}
