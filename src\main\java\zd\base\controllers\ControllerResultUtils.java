package zd.base.controllers;

import zd.base.utils.JSONResultUtils;

public class ControllerResultUtils {

    public JSONResultUtils<Object> success() {
        return JSONResultUtils.success();
    }

    public JSONResultUtils<Object> successMsg(String msg) {
        return JSONResultUtils.successWithMsg(msg);
    }

    public JSONResultUtils<Object> successData(Object data) {
        return JSONResultUtils.successWithData(data);
    }

    public JSONResultUtils<Object> error(String codeOrMsg) {
        return JSONResultUtils.error(codeOrMsg);
    }

    public JSONResultUtils<Object> error(String codeOrMsg, Object data) {
        return JSONResultUtils.errorWithData(codeOrMsg, data);
    }
}
