package zd.base.utils.es;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

public class ESJSONUtils {

	public static boolean isEmpty(JSONArray jsonArray) {
		return jsonArray == null || jsonArray.size() == 0;
	}

	public static boolean isNotEmpty(JSONArray jsonArray) {
		return jsonArray != null && jsonArray.size() > 0;
	}

	public static JSONArray parseJSONArray(String json) {
		if (StringUtils.isBlank(json)) {
			return null;
		}

		try {
			return JSON.parseArray(json);
		} catch (Exception e) {
		}

		return null;
	}

	public static JSONObject parseJSONObject(String json) {
		if (StringUtils.isBlank(json)) {
			return null;
		}

		try {
			return JSON.parseObject(json);
		} catch (Exception e) {
		}

		return null;
	}

	public static JSONObject parseJSONObject(Object obj) {
		if (obj == null) {
			return null;
		}

		if (obj instanceof JSONObject) {
			try {
				return (JSONObject) obj;
			} catch (Exception e) {
			}
		}

		return null;
	}

	public static JSONObject getJSONObject(JSONObject jsonObject, String key) {
		if (StringUtils.isBlank(key)) {
			return null;
		}

		Object obj = jsonObject.get(key);
		if (obj == null) {
			return null;
		}

		if (obj instanceof JSONObject) {
			return (JSONObject) obj;
		}

		return null;
	}

	public static JSONArray getJSONArray(JSONObject jsonObject, String key) {
		if (StringUtils.isBlank(key)) {
			return null;
		}

		Object obj = jsonObject.get(key);
		if (obj == null) {
			return null;
		}

		if (obj instanceof JSONArray) {
			return (JSONArray) obj;
		}

		return null;
	}

	public static String getJSONString(JSONObject jsonObject, String key) {
		return getJSONString(jsonObject, key, null);
	}

	public static String getJSONString(JSONObject jsonObject, String key, String defaultValue) {
		if (jsonObject == null) {
			return defaultValue;
		}

		if (StringUtils.isBlank(key)) {
			return defaultValue;
		}

		Object obj = jsonObject.get(key);
		if (obj == null) {
			return defaultValue;
		}

		return obj + "";
	}

	public static String toJsonString(Map<String, Object> props) {
		return JSON.toJSONString(props, SerializerFeature.WriteMapNullValue);
	}

}
