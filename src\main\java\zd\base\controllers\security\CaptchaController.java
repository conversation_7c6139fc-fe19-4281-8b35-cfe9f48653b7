package zd.base.controllers.security;

import com.google.code.kaptcha.Producer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zd.base.annotation.ZDLog;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;

/**
 * 验证码控制器
 */
@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "CaptchaController", description = "验证码Controller")
@RequestMapping("/opening/captcha")
public class CaptchaController {

    private final Producer captchaProducer;

    /**
     * 验证码常量
     */
    public static final String CAPTCHA_SESSION_KEY = "KAPTCHA_SESSION_KEY";

    /**
     * 生成验证码
     */
    @Operation(summary = "获取验证码")
    @GetMapping("/generate")
    @ZDLog("获取验证码")
    public void generateCaptcha(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setDateHeader("Expires", 0);
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
        response.addHeader("Cache-Control", "post-check=0, pre-check=0");
        response.setHeader("Pragma", "no-cache");
        response.setContentType("image/jpeg");

        // 生成验证码文本
        String capText = captchaProducer.createText();
        log.debug("生成验证码: {}", capText);

        // 将验证码存入session
        HttpSession session = request.getSession();
        session.setAttribute(CAPTCHA_SESSION_KEY, capText);

        // 创建验证码图片
        BufferedImage bi = captchaProducer.createImage(capText);
        ServletOutputStream out = response.getOutputStream();

        // 输出图片
        ImageIO.write(bi, "jpg", out);
        try {
            out.flush();
        } finally {
            out.close();
        }
    }
}
