package zd.base.tasks.zdmq;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.redisson.api.RLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zd.base.tasks.AbstractTask;
import zd.base.utils.ZDUtils;
import zd.base.utils.es.ESDataUtils;
import zd.base.utils.es.ESQueueUtils;
import zd.base.utils.es.ESUtils;
import zd.base.utils.redis.ZDRedisLockUtils;
import zd.dms.entities.Document;
import zd.dms.services.document.DocumentService;
import zd.dms.utils.zdmq.ZDMQConfigUtils;
import zd.dms.utils.zdmq.ZDMQDBUtils;
import zd.record.utils.RecordDBUtils;

import java.util.*;

/**
 * mq任务
 */
@Component
@Slf4j
public class ESQueueTask extends AbstractTask {

    @Scheduled(cron = "20 */1 * * * ?")
    @Scheduled(initialDelay = 60000)
    @Override
    public void work() {
        String batchNo = ZDMQConfigUtils.getBatchNo();
        int batchSize = 100;
        while (true) {
            RLock lock = ZDRedisLockUtils.getLock("ZDMQESQueueTask");
            boolean lockResult = ZDRedisLockUtils.tryLock(lock, 10 * 60);
            if (!lockResult) {
                break;
            }

            try {
                List<Map<String, Object>> nextMsgs = ZDMQDBUtils.getNextMsgs(ZDMQConfigUtils.TYPE_ES, batchNo, batchSize);
                if (CollectionUtils.isEmpty(nextMsgs)) {
                    break;
                }

                Map<String, Map<String, Object>> msgIdAndMsgs = new HashMap<>();

                List<String> insertRecordIds = new ArrayList<>();
                List<String> deleteRecordIds = new ArrayList<>();

                List<String> insertDocIds = new ArrayList<>();
                List<String> deleteDocIds = new ArrayList<>();

                Date endDate = null;
                for (Map<String, Object> item : nextMsgs) {
                    String id = MapUtils.getString(item, "ID", "");
                    String msgId = MapUtils.getString(item, "MSG_ID", "");
                    String msgType = MapUtils.getString(item, "MSG_TYPE", "");
                    if (StringUtils.isBlank(msgId)) {
                        ZDMQDBUtils.removeMsg(id);
                        continue;
                    }
                    msgIdAndMsgs.put(msgId, item);

                    putMsgIds(insertRecordIds, deleteRecordIds, insertDocIds, deleteDocIds, id, msgId, msgType);

                    Object creationDate = item.get("CREATION_DATE");
                    endDate = (Date) creationDate;
                }

                putEsRecord(insertRecordIds, deleteRecordIds, insertDocIds, deleteDocIds, msgIdAndMsgs, endDate);

                if (nextMsgs.size() < batchSize) {
                    break;
                }
            } catch (Exception e) {
                log.debug("error", e);
            } finally {
                ZDRedisLockUtils.unlock(lock);
            }
        }
    }

    private void putMsgIds(List<String> insertRecordIds, List<String> deleteRecordIds, List<String> insertDocIds, List<String> deleteDocIds, String id, String msgId, String msgType) {
        if (ESQueueUtils.TYPE_REC_INSERT.equals(msgType)) {
            if (!insertRecordIds.contains(msgId)) {
                insertRecordIds.add(msgId);
            }

            deleteRecordIds.remove(msgId);
        } else if (ESQueueUtils.TYPE_REC_DELETE.equals(msgType)) {
            if (!deleteRecordIds.contains(msgId)) {
                deleteRecordIds.add(msgId);
            }

            insertRecordIds.remove(msgId);
        } else if (ESQueueUtils.TYPE_DOC_INSERT.equals(msgType)) {
            if (!insertDocIds.contains(msgId)) {
                insertDocIds.add(msgId);
            }

            deleteDocIds.remove(msgId);
        } else if (ESQueueUtils.TYPE_DOC_DELETE.equals(msgType)) {
            if (!deleteDocIds.contains(msgId)) {
                deleteDocIds.add(msgId);
            }

            insertDocIds.remove(msgId);
        } else {
            ZDMQDBUtils.removeMsg(id);
        }
    }

    private void putEsRecord(List<String> insertRecordIds, List<String> deleteRecordIds, List<String> insertDocIds, List<String> deleteDocIds, Map<String, Map<String, Object>> msgIdAndMsgs, Date endDate) {
        if (CollectionUtils.isNotEmpty(insertRecordIds)) {
            List<Map<String, Object>> esRecords = new ArrayList<>();
            insertRecordIds.forEach(item -> {
                Map<String, Object> esRecordData = getEsRecordData(item);
                if (MapUtils.isEmpty(esRecordData)) {
                    // remove
                    ZDMQDBUtils.deleteMqMsgId(ZDMQConfigUtils.TYPE_ES, item, endDate, false);
                    return;
                }

                esRecords.add(esRecordData);
            });

            boolean results = ESUtils.batchCreateDocument(ESUtils.RECORD_ES_INDEX, esRecords);
            if (results) {
                ZDMQDBUtils.deleteMqMsgIds(ZDMQConfigUtils.TYPE_ES, insertRecordIds, endDate, true);
            } else {
                insertRecordIds.forEach(item -> {
                    Map<String, Object> msgMap = msgIdAndMsgs.get(item);
                    if (MapUtils.isEmpty(msgMap)) {
                        return;
                    }

                    String id = MapUtils.getString(msgMap, "ID", "");
                    ZDMQDBUtils.updateFailedStatus(id, "处理失败", msgMap);
                    ZDMQDBUtils.deleteMqMsgId(ZDMQConfigUtils.TYPE_ES, item, endDate, false);
                });
            }
        }

        if (CollectionUtils.isNotEmpty(deleteRecordIds)) {
            deleteRecordIds.forEach(item -> {
                boolean results = ESUtils.deleteDocument(item, ESUtils.RECORD_ES_INDEX);
                if (results) {
                    ZDMQDBUtils.deleteMqMsgId(ZDMQConfigUtils.TYPE_ES, item, endDate, true);
                } else {
                    Map<String, Object> msgMap = msgIdAndMsgs.get(item);
                    if (MapUtils.isEmpty(msgMap)) {
                        return;
                    }

                    String id = MapUtils.getString(msgMap, "ID", "");
                    ZDMQDBUtils.updateFailedStatus(id, "处理失败", msgMap);
                    ZDMQDBUtils.deleteMqMsgId(ZDMQConfigUtils.TYPE_ES, item, endDate, false);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(insertDocIds)) {
            List<Map<String, Object>> esDocuments = new ArrayList<>();
            insertDocIds.forEach(item -> {
                Map<String, Object> esDocumentData = getEsDocumentData(item);
                if (MapUtils.isEmpty(esDocumentData)) {
                    // remove
                    ZDMQDBUtils.deleteMqMsgId(ZDMQConfigUtils.TYPE_ES, item, endDate, false);
                    return;
                }

                esDocuments.add(esDocumentData);
            });

            boolean results = ESUtils.batchCreateDocument(ESUtils.RECORD_ES_INDEX, esDocuments);
            if (results) {
                ZDMQDBUtils.deleteMqMsgIds(ZDMQConfigUtils.TYPE_ES, insertDocIds, endDate, true);
            } else {
                insertDocIds.forEach(item -> {
                    Map<String, Object> msgMap = msgIdAndMsgs.get(item);
                    if (MapUtils.isEmpty(msgMap)) {
                        return;
                    }

                    String id = MapUtils.getString(msgMap, "ID", "");
                    ZDMQDBUtils.updateFailedStatus(id, "处理失败", msgMap);
                    ZDMQDBUtils.deleteMqMsgId(ZDMQConfigUtils.TYPE_ES, item, endDate, false);
                });
            }
        }

        if (CollectionUtils.isNotEmpty(deleteDocIds)) {
            deleteDocIds.forEach(item -> {
                boolean results = ESUtils.deleteDocument(item, ESUtils.RECORD_ES_INDEX);
                if (results) {
                    ZDMQDBUtils.deleteMqMsgId(ZDMQConfigUtils.TYPE_ES, item, endDate, true);
                } else {
                    Map<String, Object> msgMap = msgIdAndMsgs.get(item);
                    if (MapUtils.isEmpty(msgMap)) {
                        return;
                    }

                    String id = MapUtils.getString(msgMap, "ID", "");
                    ZDMQDBUtils.updateFailedStatus(id, "处理失败", msgMap);
                    ZDMQDBUtils.deleteMqMsgId(ZDMQConfigUtils.TYPE_ES, item, endDate, false);
                }
            });
        }
    }

    private Map<String, Object> getEsRecordData(String tableNameAndRecId) {
        if (StringUtils.isBlank(tableNameAndRecId)) {
            return null;
        }

        int index = tableNameAndRecId.lastIndexOf("_");
        if (index <= 0) {
            return null;
        }

        long recId = NumberUtils.toLong(tableNameAndRecId.substring(index + 1));
        String tableName = tableNameAndRecId.substring(0, index);

        Map<String, Object> recordData = RecordDBUtils.getRecordByTableName(recId, null, tableName);
        if (MapUtils.isEmpty(recordData)) {
            return null;
        }

        return ESDataUtils.getEsRecordData(tableName, tableNameAndRecId, recordData);
    }

    private Map<String, Object> getEsDocumentData(String docIndexId) {
        if (StringUtils.isBlank(docIndexId)) {
            return null;
        }

        int index = docIndexId.lastIndexOf("_");
        if (index <= 0) {
            return null;
        }

        String docId = docIndexId.substring(index + 1);
        DocumentService documentService = ZDUtils.getBean(DocumentService.class);
        Document document = documentService.getDocumentById(docId);
        if (document == null) {
            return null;
        }

        return ESDataUtils.getEsDocumentData(document, docIndexId);
    }
}
