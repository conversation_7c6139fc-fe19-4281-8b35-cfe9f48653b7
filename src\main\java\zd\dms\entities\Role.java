package zd.dms.entities;

import jakarta.persistence.Column;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class Role {

    private static final Map<String, String> DISPLAY_NAMES = new HashMap<String, String>();

    static {
        DISPLAY_NAMES.put("admin", "系统管理员");
        DISPLAY_NAMES.put("limitedAdmin", "后台管理员");
        DISPLAY_NAMES.put("userAdmin", "用户管理员");
        DISPLAY_NAMES.put("groupAdmin", "组管理员");
        DISPLAY_NAMES.put("workflowAdmin", "流程管理员");
        DISPLAY_NAMES.put("noticeAdmin", "通知公告管理员");
        DISPLAY_NAMES.put("ecmAdmin", "档案管理员");
        DISPLAY_NAMES.put("dccAdmin", "DCC管理员");
        DISPLAY_NAMES.put("receptionAdmin", "前台管理员");
        DISPLAY_NAMES.put("logsAdmin", "安全审计员");
    }

    /**
     * 角色：系统管理员
     */
    public static final String SYSTEM_ADMIN = "admin";

    /**
     * 角色：后台管理员
     */
    public static final String LIMITED_ADMIN = "limitedAdmin";

    /**
     * 角色：用户管理员
     */
    public static final String USER_ADMIN = "userAdmin";

    /**
     * 角色：组管理员
     */
    public static final String GROUP_ADMIN = "groupAdmin";

    /**
     * 角色：流程管理员
     */
    public static final String WORKFLOW_ADMIN = "workflowAdmin";

    /**
     * 角色：通知公告管理员
     */
    public static final String NOTICE_ADMIN = "noticeAdmin";

    /**
     * 角色：通知公告管理员
     */
    public static final String ECM_ADMIN = "ecmAdmin";

    /**
     * 角色：dcc文控管理员
     */
    public static final String DCC_ADMIN = "dccAdmin";

    /**
     * 角色：前台管理员
     */
    public static final String RECEPTION_ADMIN = "receptionAdmin";

    /**
     * 角色：安全审计员
     */
    public static final String LOGS_ADMIN = "logsAdmin";
}
