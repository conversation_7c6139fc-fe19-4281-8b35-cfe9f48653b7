package zd.dms.repositories.document.impl;

import jakarta.persistence.EntityManager;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentVersion;
import zd.dms.repositories.document.DocumentVersionRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;

public class DocumentVersionRepositoryDaoImpl extends BaseRepositoryDaoImpl<DocumentVersion, Long> implements DocumentVersionRepository {

    public DocumentVersionRepositoryDaoImpl(Class<DocumentVersion> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public DocumentVersion getOldestDocumentVersion(Document doc) {
        List<DocumentVersion> versions = getDocumentVersionByDocument(doc, false, true);

        for (DocumentVersion dv : versions) {
            if (dv.getVersionNumber() == 0) {
                continue;
            }

            return dv;
        }

        return null;
    }

    @Override
    public DocumentVersion getNewestDocumentVersion(Document doc) {
        List<DocumentVersion> versions = getDocumentVersionByDocument(doc, false, true);

        if (versions.size() <= 0) {
            return null;
        }
        return versions.get(versions.size() - 1);
    }

    @Override
    public int getVersionCount(Document document) {
        return NumberUtils.toInt(String.valueOf(findObject(
                "select count(*) from DocumentVersion as d where d.versionNumber <> 0 and d.docId = ?1",
                new Object[]{document.getId()})));
    }

    @Override
    public DocumentVersion getDocumentVersion(Document doc, int versionNumber) {
        Specification<DocumentVersion> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentVersion> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("docId", doc.getId());
            specTools.eq("versionNumber", versionNumber);

            return specTools.getRestriction();
        };

        return findSingleObject(spec);

    }

    @Override
    public List<DocumentVersion> getDocumentVersionByDocument(Document doc, boolean withTempVersion, boolean asc) {
        Specification<DocumentVersion> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentVersion> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("docId", doc.getId());

            if (!withTempVersion) {
                specTools.ne("versionNumber", 0);
            }

            if (asc) {
                specTools.asc("versionNumber");
            } else {
                specTools.desc("versionNumber");
            }

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void deleteAllVersionsByDocument(Document doc) {
        String hql = "delete from DocumentVersion where docId = ?1";
        executeUpdate(hql, doc.getId());
    }

    @Override
    public void deleteVersionByDocument(Document doc, int versionNumber) {
        String hql = "delete from DocumentVersion where docId = ?1 and versionNumber = ?2";
        executeUpdate(hql, doc.getId(), versionNumber);
    }
}
