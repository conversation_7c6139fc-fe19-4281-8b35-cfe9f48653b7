package zd.dms.repositories.id.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.OutLink;
import zd.dms.repositories.id.OutLinkRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.Date;
import java.util.List;

public class OutLinkRepositoryDaoImpl extends BaseRepositoryDaoImpl<OutLink, String> implements OutLinkRepository {

    public OutLinkRepositoryDaoImpl(Class<OutLink> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<OutLink> getExpired() {
        Specification<OutLink> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<OutLink> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.lt("expirationDate", new Date());

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<OutLink> getReachedLimit() {
        Specification<OutLink> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<OutLink> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.gt("visitCountLimit", 0);
            specTools.addPredicates(criteriaBuilder.greaterThanOrEqualTo(root.get("visitCount"), root.get("visitCountLimit")));

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<OutLink> getOutLinksByUser(String username) {
        Specification<OutLink> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<OutLink> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("creator", username);
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public OutLink getOutLinkByUrl(String url) {
        Specification<OutLink> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<OutLink> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("url", url);

            return specTools.getRestriction();
        };

        return findSingleObject(spec);
    }

    @Override
    public Page getOutLinksPageByUser(String username, int pageNumber, int pageSize) {
        Specification<OutLink> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<OutLink> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("creator", username);
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }

    @Override
    public Page getOutLinksPage(int pageNumber, int pageSize) {
        Specification<OutLink> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<OutLink> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }
}
