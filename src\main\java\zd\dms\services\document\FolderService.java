package zd.dms.services.document;

import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.base.utils.JSONResultUtils;
import zd.dms.entities.Folder;
import zd.dms.entities.FolderLog;
import zd.dms.entities.FolderSubscribe;
import zd.dms.entities.User;
import zd.record.entities.RecFolder;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Transactional
public interface FolderService extends BaseJpaService<Folder, Long> {

    JSONResultUtils<Object> create(Map<String, Object> params, User user);

    JSONResultUtils<Object> update(Map<String, Object> params, User user);

    JSONResultUtils<Object> delete(long id,User user);

    @Transactional(readOnly = true)
    Map<Long, Long> getChildCount(List<Long> parentIds);

    @Transactional(readOnly = true)
    List<Folder> getTopFolderListByFolderAreaId(long folderAreaId);

    List<Folder> findFolders(String keywords);

    @Transactional(readOnly = true)
    List<Folder> getChildren(long parentId);

    @Transactional(readOnly = true)
    List<Long> getFolderIdsByLimit(int limit);

    @Transactional(readOnly = true)
    List<Long> getSubFolderIds(List<Long> parentIds);

    @Transactional(readOnly = true)
    List<Folder> getFoldersByNameAndParent(Folder parentFolder, String name);

    @Transactional(readOnly = true)
    List<Folder> getPubTopFoldersByTreeName(String treeName);

    /**
     * 根据目录名称，模糊查询出相应的目录
     *
     * @param folderNameKeyWordArray 条件数组
     * @return
     */
    List<Folder> findFoldersByName(String[] folderNameKeyWordArray);

    List<Folder> getFoldersByName(String folderName);

    FolderLog addLog(Folder folder, int type, String msg, String operator, String fullname, String ipAddress,
                     boolean logToParent);

    void addLogFromAdmin(Folder folder, int type, String msg, boolean logToParent);

    @Transactional(readOnly = true)
    List<FolderLog> getLogs(Folder folder, int type);

    @Transactional(readOnly = true)
    List<FolderLog> getTop100Logs(Folder folder);

    void deleteLogsBeforeDays(int days);

    @Transactional(readOnly = true)
    Page getLogs(int pageNumber, int pageSize, int type, String username, Date startDate, Date endDate);

    @Transactional(readOnly = true)
    Page getLogsByFolder(int pageNumber, int pageSize, int type, Folder folder);

    void copySubFolderStructure(Folder sourceFolder, Folder destFolder, boolean copyPermission,
                                boolean includeParentFolder);

    void updateNullNumIndex(int numIndex);

    void createDefaultMyFolders(User user);

    void createDefaultFolders();

    /**
     * 根据组id获取指定分类
     *
     * @param id 要获取的组的id
     * @return 对应的Folder对象
     */
    @Transactional(readOnly = true)
    Folder getById(long id);

    @Transactional(readOnly = true)
    List<Long> getTopFolderIds();

    @Transactional(readOnly = true)
    List<Folder> getTopFolders();

    @Transactional(readOnly = true)
    List<Folder> getTopMyFolders(String username);

    @Transactional(readOnly = true)
    List<Folder> getFoldersByParent(Folder folder);

    @Transactional(readOnly = true)
    List<Folder> getAllFolders();

    void create(Folder folder);

    void update(Folder folder);

    void delete(Folder folder);

    void deleteAllContents(Folder folder);

    void deleteAllMyFolders(String username);

    void updateOldMyDoc();

    /**
     * 获取有角色的组列表
     *
     * @return 有角色的组列表
     */
    @Transactional(readOnly = true)
    List<Folder> getFoldersInRole();

    @Transactional(readOnly = true)
    boolean isChildren(Folder folder, Folder folder2);

    int isFolderNameInFolder(String name, Folder parent, Folder oriFolder, int folderType, String creator);

    @Transactional(readOnly = true)
    List<Folder> searchFoldersByNameAndDescription(String name, String description);

    // subscribes
    void subscribe(Folder folder, User user);

    void subscribe(String docId, User user);

    @Transactional(readOnly = true)
    FolderSubscribe getSubscribe(Folder folder, User user);

    @Transactional(readOnly = true)
    FolderSubscribe getSubscribe(String docId, User user);

    void deleteSubscribe(Folder folder, User user);

    void deleteSubscribe(String docId, User user);

    void deleteAllSubscribesByUser(User user);

    @Transactional(readOnly = true)
    List<FolderSubscribe> getSubscribes(Folder folder);

    @Transactional(readOnly = true)
    List<FolderSubscribe> getSubscribes(String docId);

    String importFolder(Folder importFolder, Folder parentFolder);

    void doCreateDefaultAttachFolder();
}
