package zd.dms.repositories.log;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.LogMessage;

import java.util.Date;
import java.util.List;

public interface LogRepository extends BaseRepository<LogMessage, Long> {

    /**
     * 根据时间和日志级别获取日志列表
     *
     * @param pageNumber    page number
     * @param pageSize      page size
     * @param level         日志级别
     * @param startDate     开始时间
     * @param endDate       结束时间
     * @param orderProperty 排序字段
     * @param asc           是否为升序
     * @return 日志列表
     */
    Page getLogs(int pageNumber, int pageSize, int level, Date startDate,
                 Date endDate, String orderProperty, boolean asc);

    List<LogMessage> getLogsByDate(Date startDate, Date endDate);

    void clearLogs();
}
