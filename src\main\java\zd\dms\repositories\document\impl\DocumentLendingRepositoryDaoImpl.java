package zd.dms.repositories.document.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentLending;
import zd.dms.repositories.document.DocumentLendingRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.Date;
import java.util.List;

public class DocumentLendingRepositoryDaoImpl extends BaseRepositoryDaoImpl<DocumentLending, Long> implements DocumentLendingRepository {

    public DocumentLendingRepositoryDaoImpl(Class<DocumentLending> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public void deleteByDocument(Document document) {
        String hql = "delete from DocumentLending where docId = ?1";
        executeUpdate(hql, document.getId());
    }

    @Override
    public List<DocumentLending> getAllDocumentLendings() {
        Specification<DocumentLending> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLending> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<DocumentLending> getDocumentLendings(String username) {
        Specification<DocumentLending> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLending> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.like("usernames", "%**" + username + "**%");
            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<DocumentLending> getDocumentLendingsByCreator(String creator) {
        Specification<DocumentLending> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLending> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("creator", creator);
            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<DocumentLending> getDocumentLendings(Document document) {
        Specification<DocumentLending> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLending> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("docId", document.getId());
            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void deleteById(long id) {
        String hql = "delete from DocumentLending where id = ?1";
        executeUpdate(hql, id);
    }

    @Override
    public void deleteExpiredLendings() {
        Date now = new Date();
        String hql = "delete from DocumentLending where endDate <= ?1";
        executeUpdate(hql, now);
    }

    @Override
    public List<DocumentLending> getExpiredLendings() {
        Specification<DocumentLending> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLending> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.le("endDate", new Date());

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public boolean isDocumentLended(Document document, String username) {
        Specification<DocumentLending> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLending> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("docId", document.getId());
            specTools.like("usernames", "%**" + username + "**%");

            return specTools.getRestriction();
        };

        return count(spec) > 0;
    }

    @Override
    public List<DocumentLending> searchMylendingsDoc(String fileName, String lendBy, String currentUser) {
        String whereClause = " 1=1";
        if (StringUtils.isNotBlank(fileName)) {
            whereClause += " and t1.filename like '%" + fileName + "%'";
        }

        if (StringUtils.isNotBlank(lendBy)) {
            whereClause += " and t1.creatorfullname like '%" + lendBy + "%'";
        }

        if (StringUtils.isBlank(currentUser)) {
            return null;
        }

        final String sql = "select t3.* from(" +
                " select t1.* from (" +
                "select dl.docId as docId ,dl.creatorfullname  as creatorfullname ,td.filename as filename from " +
                "(select * from document_lending where usernames like '%**" + currentUser +
                "**%') dl left join tdms_doc td on dl.docId = td.id)t1" + " where" + whereClause +
                ")t2 left join (select * from document_lending where usernames like '%**" + currentUser +
                "**%') t3 on t2.docId = t3.docId order by t3.creationdate desc";

        Query nativeQuery = this.entityManager.createNativeQuery(sql, DocumentLending.class);
        return nativeQuery.getResultList();
    }

    @Override
    public List<DocumentLending> searchMylendsDoc(String fileName, String lendTo, String currentUser) {
        String whereClause = " 1=1";
        if (StringUtils.isNotBlank(fileName)) {
            whereClause += " and t1.filename like '%" + fileName + "%'";
        }

        if (StringUtils.isNotBlank(lendTo)) {
            whereClause += " and t1.fullnames like '%" + lendTo + "%'";
        }

        if (StringUtils.isBlank(currentUser)) {
            return null;
        }

        final String sql = "select t3.* from(" + " select t1.* from (" +
                "select dl.docId as docId ,dl.fullnames as fullnames ,td.filename as filename from " +
                "(select * from document_lending where creator = '" + currentUser +
                "') dl left join tdms_doc td on dl.docId = td.id)t1" + " where" + whereClause +
                ")t2 left join (select * from document_lending where creator = '" + currentUser +
                "') t3 on t2.docId = t3.docId order by t3.creationdate desc";

        Query nativeQuery = this.entityManager.createNativeQuery(sql, DocumentLending.class);
        return nativeQuery.getResultList();
    }
}
