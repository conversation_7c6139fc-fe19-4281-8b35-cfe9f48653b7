package zd.dms.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.base.entities.PropertyAware;
import zd.base.utils.system.SpringUtils;
import zd.dms.services.security.FolderPermissionUtils;
import zd.dms.services.security.RoleUtils;
import zd.dms.services.user.GroupService;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.services.user.UserService;
import zd.dms.utils.PropertyUtils;
import zd.dms.utils.TextUtils;

import java.util.*;

/**
 * Group Domain Object
 *
 * <AUTHOR>
 * @version $Revision$
 */
@Entity
@Table(name = "tdms_group")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Group extends AbstractSequenceEntity implements PropertyAware {

    /**
     * serial
     */
    private static final long serialVersionUID = 8655423795683869558L;

    /**
     * 组名，unique
     */
    @Index(name = "i_group_name")
    private String name;

    /**
     * 编码
     */
    @Index(name = "i_group_code")
    private String code;

    @Getter
    @Setter
    @Index(name = "i_group_parentCode")
    private String parentCode;

    /**
     * ldapId
     */
    @Index(name = "i_group_ldapId")
    private String ldapId;

    /**
     * 组描述
     */
    private String description;

    /**
     * 本组是否可用
     */
    @Column(nullable = false)
    @Index(name = "i_group_enabled")
    private boolean enabled;


    @Transient
    private Map<String, String> propertiesMap;

    @Column(length = Length.LOB_DEFAULT)
    private String properties;

    /**
     * 从钉钉同步回来的组的ID
     */
    private String syncDingGroupId;

    /**
     * 从企业微信同步回来的组的ID
     */
    private String syncQywxGroupId;

    @Column(length = Length.LOB_DEFAULT)
    @Getter
    @Setter
    private String roles;

    @Transient
    private List<String> roleList;

    /**
     * 排序号
     */
    private int numIndex;

    /**
     * 创建时间
     */
    @Index(name = "i_group_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 权限到期时间
     */
    @Transient
    private Date expirationDate;

    /**
     * 预留的groupIdCode，用于部门系统对接使用
     */
    private String groupIdCode;

    @Transient
    private String oldName;

    @Setter
    @Getter
    @Transient
    private long childCount;

    @Setter
    @Getter
    @Transient
    private long parentId;

    @Transient
    private List<User> users;

    /**
     * 默认构造器
     */
    public Group() {
        super();
        enabled = true;
        numIndex = 999;
        creationDate = new Date();
    }

    public Group(String name) {
        this();
        this.name = name;
    }

    /**
     * 构造器
     *
     * @param name        组名
     * @param description 组描述
     */
    public Group(String name, String description) {
        this();
        this.name = name;
        this.description = description;
    }

    public List<User> getUsers() {
        if (users == null) {
            UserService userService = SpringUtils.getBean(UserService.class);
            users = userService.getUsersByGroupId(this.id);
        }

        return users;
    }

    public Group getParent() {
        return SpringUtils.getBean(GroupService.class).getGroupByCode(parentCode);
    }

    public List<Group> getChildren() {
        return SpringUtils.getBean(GroupService.class).getChildren(code);
    }

    /**
     * 获取部门显示名
     *
     * @return
     */
    public String getDisplayFullname() {
        return name;
    }

    public int getAllMemberCount() {
        return UserGroupUtils.getAllMemberCount(this);
    }

    public String getNameAndMemberCount() {
        return getDisplayFullname() + "(" + getAllMemberCount() + ")";
    }

    public String getType() {
        return "Group";
    }

    public String getDisplayName() {
        String result = "/" + getDisplayFullname();

        Group tempParent = getParent();
        while (tempParent != null) {
            if (!"所有部门".equals(tempParent.getName())) {
                result = "/" + tempParent.getDisplayFullname() + result;
            }
            tempParent = tempParent.getParent();
        }

        return result;
    }

    /**
     * 设置属性
     *
     * @param name  名称
     * @param value 值
     */
    public void setProperty(String name, String value) {
        initPropertiesMap();
        propertiesMap.put(name, value);
        PropertyUtils.updateProperties(this);
    }

    /**
     * 移除属性
     *
     * @param name 要移除的属性名称
     */
    public void removeProperty(String name) {
        initPropertiesMap();
        propertiesMap.remove(name);
    }

    /**
     * 获取属性
     *
     * @param name 名称
     * @return 值
     */
    public String getProperty(String name) {
        initPropertiesMap();
        return propertiesMap.get(name);
    }

    public List<String> getRoleList() {
        if (roleList == null) {
            roleList = new ArrayList<>();
            if (StringUtils.isBlank(roles)) {
                return roleList;
            }

            String[] split = roles.split(",");
            for (String s : split) {
                if (StringUtils.isBlank(s)) {
                    continue;
                }

                s = s.replace("**", "");
                if (StringUtils.isBlank(s)) {
                    continue;
                }

                if (roleList.contains(s)) {
                    continue;
                }

                roleList.add(s);
            }
        }

        return roleList;
    }

    /**
     * 为本用户添加角色
     *
     * @param role 要添加的角色
     */
    public void addRole(String role) {
        if (StringUtils.isBlank(role)) {
            return;
        }

        String tempRoles = getRoles();
        if (StringUtils.isBlank(tempRoles)) {
            setRoles("**" + role + "**");
            return;
        }

        if (tempRoles.contains("**" + role + "**")) {
            return;
        }

        setRoles(tempRoles + ",**" + role + "**");
    }

    /**
     * 删除本用户的指定角色
     *
     * @param role 要删除的角色
     */
    public void removeRole(String role) {
        if (StringUtils.isBlank(role)) {
            return;
        }

        List<String> tempRoleList = getRoleList();
        tempRoleList.remove(role);

        RoleUtils.setGroupRoles(this, tempRoleList);
    }

    /**
     * @return Returns the description.
     */
    public String getDescription() {
        return TextUtils.escapeXml(description);
    }

    /**
     * @param description The description to set.
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * @return Returns the name.
     */
    public String getName() {
        return TextUtils.escapeXml(name);
    }

    /**
     * @param name The name to set.
     */
    public void setName(String name) {
        // 当修改name或初次赋值时，修改oldName为旧的name
        if (StringUtils.isNotBlank(name) && StringUtils.isBlank(oldName)) {
            this.oldName = this.name;
        }

        this.name = name;
    }

    /**
     * @return Returns the enabled.
     */
    public boolean getEnabled() {
        return enabled;
    }

    /**
     * @param enabled The enabled to set.
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    /**
     * @see Object#equals(Object)
     */
    @Override
    public boolean equals(Object o) {
        if (!(o instanceof Group)) {
            return false;
        }

        final Group g = (Group) o;

        return new EqualsBuilder().appendSuper(super.equals(g)).append(name, g.getName()).isEquals();
    }

    /**
     * @see Object#hashCode()
     */
    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(name).toHashCode();
    }

    public Map<String, String> getPropertiesMap() {
        initPropertiesMap();
        return propertiesMap;
    }

    public void setPropertiesMap(Map<String, String> propertiesMap) {
        this.propertiesMap = propertiesMap;
    }

    public void initPropertiesMap() {
        if (this.propertiesMap == null) {
            this.propertiesMap = new HashMap<>();

            PropertyUtils.updatePropertiesMap(this);
        }
    }

    public String getProperties() {
        return properties;
    }

    public void setProperties(String properties) {
        this.properties = properties;
    }

    public int getNumIndex() {
        return numIndex;
    }

    public void setNumIndex(int numIndex) {
        this.numIndex = numIndex;
    }

    public String getCode() {
        return TextUtils.escapeXml(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getGroupIdCode() {
        return TextUtils.escapeXml(groupIdCode);
    }

    public void setGroupIdCode(String groupIdCode) {
        this.groupIdCode = groupIdCode;
    }

    public String getSyncDingGroupId() {
        return syncDingGroupId;
    }

    public void setSyncDingGroupId(String syncDingGroupId) {
        this.syncDingGroupId = syncDingGroupId;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getOldName() {
        // 当未修改name时，即未设置oldName，则返回name
        if (StringUtils.isBlank(oldName)) {
            this.oldName = this.name;
        }

        return oldName;
    }

    public void setOldName(String oldName) {
        this.oldName = oldName;
    }

    public String getLdapId() {
        return ldapId;
    }

    public void setLdapId(String ldapId) {
        this.ldapId = ldapId;
    }

    public String getSyncQywxGroupId() {
        return syncQywxGroupId;
    }

    public void setSyncQywxGroupId(String syncQywxGroupId) {
        this.syncQywxGroupId = syncQywxGroupId;
    }
}
