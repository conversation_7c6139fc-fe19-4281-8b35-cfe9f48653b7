package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "threeAdminlog")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class ThreeAdminLog extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = 8417104449109409644L;

    public static final int TYPE_CREATE = 1;

    public static final int TYPE_UPDATE = 2;

    public static final int TYPE_DELETE = 3;

    public static final int TYPE_PERM = 4;

    public static final int TYPE_SUBSCRIPTION = 5;

    public static final int TYPE_EXPORT = 6;

    public static final int TARGETTYPE_RECEPTION = 1;
    public static final int TARGETTYPE_USER = 2;

    /**
     * 操作者用户名
     */
    @Column(nullable = false)
    @Index(name = "i_talog_operator")
    private String operator;

    /**
     * 操作类型
     */
    @Index(name = "i_talog_operatetype")
    private int operateType;

    /**
     * 操作者全名
     */
    private String operatorFullname;

    /**
     * 内容
     */
    @Column(length = Length.LOB_DEFAULT)
    private String msg;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 操作者角色
     */
    private String role;

    /**
     * 目标对象： 前台目录、用户
     */
    private String target;

    /**
     * 目录对象类型
     */
    private int targetType;

    /**
     * 创建时间
     */
    @Index(name = "i_talog_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 默认构造器
     */
    public ThreeAdminLog() {
        super();
        creationDate = new Date();
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getOperatorFullname() {
        return operatorFullname;
    }

    public void setOperatorFullname(String fullname) {
        this.operatorFullname = fullname;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public int getOperateType() {
        return operateType;
    }

    public void setOperateType(int operateType) {
        this.operateType = operateType;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public int getTargetType() {
        return targetType;
    }

    public void setTargetType(int targetType) {
        this.targetType = targetType;
    }
}
