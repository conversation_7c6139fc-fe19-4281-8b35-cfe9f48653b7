package zd.dms.controllers.api.v2;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import zd.base.utils.JSONResultUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * POST http://localhost:8081/oauth2/token
 * Content-Type: application/x-www-form-urlencoded
 * Authorization: Basic ZGVtby1jbGllbnQ6ZGVtby1zZWNyZXQ=  (demo-client:demo-secret的Base64编码)
 * grant_type=client_credentials&scope=api
 * <p>
 * GET http://localhost:8081/api/v2/test
 * Authorization: Bearer
 */
@RestController
@RequestMapping("/api/v2/test")
@RequiredArgsConstructor
@Slf4j
public class ApiTestController {

    /**
     * 测试API
     */
    @PostMapping("/testPost")
    public JSONResultUtils<Object> testPost(@RequestBody Map<String, Object> params) {
        log.info("API测试端点被调用 params:{}", params);

        Map<String, Object> result = new HashMap<>();
        result.put("params", params);
        result.put("message", "API测试成功");

        return JSONResultUtils.successWithData(result);
    }

    /**
     * 测试API
     */
    @GetMapping("/testGet/{id}")
    public JSONResultUtils<Object> testGet(@PathVariable String id, @RequestParam("value") String value) {
        log.info("API测试端点被调用 id:{} value:{}", id, value);

        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        result.put("value", value);
        result.put("message", "API测试成功");


        return JSONResultUtils.successWithData(result);
    }
}
