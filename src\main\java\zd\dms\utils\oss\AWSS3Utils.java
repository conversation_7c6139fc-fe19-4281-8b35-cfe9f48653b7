package zd.dms.utils.oss;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import zd.base.config.oss.AWSS3Config;
import zd.base.utils.system.SpringUtils;
import zd.base.utils.system.SystemInitUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class AWSS3Utils {

    private static final AWSS3Config s3Config = SpringUtils.getBean(AWSS3Config.class);
    private static final S3Client s3Client = SpringUtils.getBean(S3Client.class);

    public static boolean doUploadToOSS(File uploadFile, String objectKey, int count) {
        if (count > 10) {
            log.error("上传文件时，重试次数超过十次 objectKey ：{}", objectKey);
            return false;
        }

        try (FileInputStream fileInputStream = new FileInputStream(uploadFile)) {
            // 创建上传请求
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(s3Config.getBucketName())
                    .key(objectKey)
                    .build();

            // 上传文件
            s3Client.putObject(putObjectRequest, RequestBody.fromFile(uploadFile));
        } catch (Throwable e) {
            log.error("doUploadToOSS error", e);
            return false;
        }

        return true;
    }

    public static InputStream downloadInputStreamFromOSS(String objectKey) {
        try {
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(s3Config.getBucketName())
                    .key(objectKey)
                    .build();

            return s3Client.getObject(getObjectRequest);
        } catch (Throwable e) {
            log.error("downloadInputStreamFromOSS error", e);
        }

        return null;
    }

    public static boolean exists(String objectKey) {
        HeadObjectResponse headObjectResponse = getStat(objectKey);
        return headObjectResponse != null;
    }

    public static HeadObjectResponse getStat(String objectKey) {
        if (StringUtils.isBlank(objectKey)) {
            return null;
        }

        try {
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(s3Config.getBucketName())
                    .key(objectKey)
                    .build();

            return s3Client.headObject(headObjectRequest);
        } catch (NoSuchKeyException e) {
            // 对象不存在，返回null
            return null;
        } catch (Throwable e) {
            log.error("getStat error", e);
        }

        return null;
    }

    public static File downloadFileFromOSS(String objectKey) {
        File tempFile = new File(SystemInitUtils.getTempDir(), RandomStringUtils.randomAlphabetic(15));
        InputStream input = null;
        FileOutputStream fileOutputStream = null;
        try {
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(s3Config.getBucketName())
                    .key(objectKey)
                    .build();

            ResponseInputStream<GetObjectResponse> responseInputStream = s3Client.getObject(getObjectRequest);
            input = responseInputStream;

            fileOutputStream = new FileOutputStream(tempFile);
            IOUtils.copy(input, fileOutputStream);
        } catch (Throwable e) {
            log.error("downloadFileFromOSS error", e);
        } finally {
            IOUtils.closeQuietly(fileOutputStream);
            IOUtils.closeQuietly(input);
        }

        return tempFile;
    }

    public static boolean deleteFileFromOSS(String objectKey) {
        try {
            List<S3Object> objects = listFileFromOSS(objectKey);
            if (objects == null || objects.isEmpty()) {
                return true;
            }

            for (S3Object object : objects) {
                String tempObjectName = object.key();
                if (StringUtils.isBlank(tempObjectName)) {
                    continue;
                }

                // 单文件删除
                deleteSingleFileFromOSS(tempObjectName);
            }
        } catch (Throwable e) {
            log.error("deleteFileFromOSS error", e);
            return false;
        }

        return true;
    }

    /**
     * 列出指定前缀的所有对象
     */
    public static List<S3Object> listFileFromOSS(String objectKey) {
        if (StringUtils.isBlank(objectKey)) {
            return null;
        }

        try {
            ListObjectsV2Request listObjectsRequest = ListObjectsV2Request.builder()
                    .bucket(s3Config.getBucketName())
                    .prefix(objectKey)
                    .build();

            ListObjectsV2Response listObjectsResponse = s3Client.listObjectsV2(listObjectsRequest);
            return listObjectsResponse.contents();
        } catch (Exception e) {
            log.error("listFileFromOSS error", e);
        }

        return new ArrayList<>();
    }

    public static boolean deleteSingleFileFromOSS(String objectKey) {
        if (StringUtils.isBlank(objectKey)) {
            return true;
        }

        try {
            DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
                    .bucket(s3Config.getBucketName())
                    .key(objectKey)
                    .build();

            s3Client.deleteObject(deleteObjectRequest);
            return true;
        } catch (Exception e) {
            log.error("deleteSingleFileFromOSS error", e);
        }

        return false;
    }
}
