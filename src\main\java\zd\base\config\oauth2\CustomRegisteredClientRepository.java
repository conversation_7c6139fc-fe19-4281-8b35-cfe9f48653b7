package zd.base.config.oauth2;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;
import zd.dms.entities.oauth2.OAuth2Client;
import zd.dms.services.oauth2.OAuth2ClientService;

import java.time.Duration;
import java.util.Arrays;

/**
 * 自定义RegisteredClientRepository实现
 */
@RequiredArgsConstructor
@Slf4j
public class CustomRegisteredClientRepository implements RegisteredClientRepository {

    private final OAuth2ClientService oAuth2ClientService;

    @Override
    public void save(RegisteredClient registeredClient) {
        // 不实现保存方法，因为我们使用自己的服务来管理客户端
        throw new UnsupportedOperationException("Client registration is not supported");
    }

    @Override
    public RegisteredClient findById(String id) {
        // 不实现按ID查找方法，因为我们只需要按客户端ID查找
        throw new UnsupportedOperationException("Finding client by id is not supported");
    }

    @Override
    public RegisteredClient findByClientId(String clientId) {
        OAuth2Client client = oAuth2ClientService.findByClientId(clientId);
        if (client == null || !client.getEnabled()) {
            log.debug("findByClientId RegisteredClient null clientId:{}", clientId);
            return null;
        }

        log.debug("findByClientId RegisteredClient clientId:{} clientName:{} enabled:{}", clientId, client.getClientName(), client.getEnabled());

        // 构建RegisteredClient
        RegisteredClient.Builder builder = RegisteredClient.withId(client.getId())
                .clientId(client.getClientId())
                .clientSecret(client.getClientSecret())  // 直接使用数据库中已经加密的客户端密钥
                .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
                .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_POST);

        // 添加授权类型
        if (client.getGrantTypes() != null) {
            Arrays.stream(client.getGrantTypes().split(","))
                    .map(String::trim)
                    .forEach(grantType -> {
                        switch (grantType) {
                            case "authorization_code":
                                builder.authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE);
                                break;
                            case "refresh_token":
                                builder.authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN);
                                break;
                            case "client_credentials":
                                builder.authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS);
                                break;
                            // 不再支持密码授权类型，跳过
                            case "password":
                                // 密码授权类型已被弃用，不再添加
                                break;
                        }
                    });
        } else {
            // 默认支持客户端凭证授权类型
            builder.authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS);
        }

        // 添加重定向URI
        if (client.getRedirectUris() != null) {
            Arrays.stream(client.getRedirectUris().split(","))
                    .map(String::trim)
                    .forEach(builder::redirectUri);
        }

        // 添加作用域
        if (client.getScopes() != null) {
            Arrays.stream(client.getScopes().split(","))
                    .map(String::trim)
                    .forEach(builder::scope);
        } else {
            // 默认作用域
            builder.scope("api");
        }

        // 设置令牌有效期
        TokenSettings tokenSettings = TokenSettings.builder()
                .accessTokenTimeToLive(Duration.ofSeconds(client.getAccessTokenValidity()))
                .refreshTokenTimeToLive(Duration.ofSeconds(client.getRefreshTokenValidity()))
                .build();

        // 设置客户端设置
        ClientSettings clientSettings = ClientSettings.builder()
                .requireAuthorizationConsent(false)
                .requireProofKey(false)
                .build();

        return builder
                .tokenSettings(tokenSettings)
                .clientSettings(clientSettings)
                .build();
    }
}
