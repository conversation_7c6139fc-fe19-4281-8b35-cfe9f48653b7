package zd.dms.repositories.log.impl;

import jakarta.persistence.EntityManager;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.Folder;
import zd.dms.entities.FolderLog;
import zd.dms.entities.User;
import zd.dms.repositories.log.FolderLogRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.Date;
import java.util.List;

public class FolderLogRepositoryDaoImpl extends BaseRepositoryDaoImpl<FolderLog, Long> implements FolderLogRepository {

    public FolderLogRepositoryDaoImpl(Class<FolderLog> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<FolderLog> getLogsByFolder(Folder folder, int type) {
        Specification<FolderLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("folderId", folder.getId());

            if (type > 0) {
                specTools.eq("operateType", type);
            }

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<FolderLog> getTop100LogsByFolder(Folder folder) {
        Specification<FolderLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("folderId", folder.getId());

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void deleteAllLogsByFolder(Folder folder) {
        String hql = "delete from FolderLog where folderId = ?1";
        executeUpdate(hql, folder.getId());
    }

    @Override
    public void deleteLogsBeforeDays(int days) {
        String hql = "delete from FolderLog where creationDate < ?1";

        Date d = DateUtils.addDays(new Date(), -days);
        executeUpdate(hql, d);
    }

    @Override
    public Page getLogs(int pageNumber, int pageSize, int type, String username, Date startDate, Date endDate) {
        Specification<FolderLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            if (startDate != null) {
                specTools.ge("creationDate", startDate);
            }
            if (endDate != null) {
                specTools.le("creationDate", endDate);
            }

            if (type > 0) {
                specTools.eq("operateType", type);
            }

            if (StringUtils.isNotBlank(username)) {
                specTools.eq("operator", username);
            }

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }

    public Page getLogsByFolder(int pageNumber, int pageSize, int type, Folder folder) {
        Specification<FolderLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("folderId", folder.getId());

            if (type > 0) {
                specTools.eq("operateType", type);
            }

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }

    @Override
    public List<FolderLog> getLogsByDate(Date startDate, Date endDate, int type, String username) {
        Specification<FolderLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            if (startDate != null) {
                specTools.ge("creationDate", startDate);
            }
            if (endDate != null) {
                specTools.le("creationDate", endDate);
            }

            if (type > 0) {
                specTools.eq("operateType", type);
            }

            if (StringUtils.isNotBlank(username)) {
                specTools.eq("operator", username);
            }

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public int getCount() {
        return NumberUtils.toInt(String
                .valueOf(findObject("select count(*) from FolderLog")));
    }

    @Override
    public int getUserDocLogCountByTypeAndDate(User user, int type, Date startDate, Date endDate) {
        if (user == null) {
            return 0;
        }

        Specification<FolderLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("operateType", type);
            specTools.eq("operator", user.getUsername());

            if (startDate != null) {
                specTools.ge("creationDate", startDate);
            }

            if (endDate != null) {
                specTools.le("creationDate", endDate);
            }

            return specTools.getRestriction();
        };

        long count = count(spec);
        return (int) count;
    }
}
