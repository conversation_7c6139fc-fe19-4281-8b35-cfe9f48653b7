package zd.dms.entities;

import jakarta.persistence.*;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.entities.AbstractEntity;
import zd.base.entities.PropertyAware;
import zd.dms.services.rule.action.RuleActionUtils;
import zd.dms.utils.PropertyUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "folder_rule_action")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class FolderRuleAction extends AbstractEntity implements PropertyAware {

	private static final Logger log = LoggerFactory.getLogger(FolderRuleAction.class);

	/**
	 * Serial
	 */
	private static final long serialVersionUID = 1L;

	public static final String TYPE_LOCK = "LockAction";

	public static final String TYPE_CLEARPI = "ClearPiAction";

	public static final String TYPE_MOVE = "MoveAction";

	public static final String TYPE_RENAME = "RenameAction";

	public static final String TYPE_SN = "SnAction";

	public static final String TYPE_MSG = "MsgAction";

	public static final String TYPE_START_WORKFLOW = "StartWorkflowAction";

	public static final String TYPE_REMIND = "RemindAction";

	public static final String TYPE_COPY = "CopyAction";

	public static final String TYPE_COPY_DOC_PROPS = "CopyDocPropsAction";

	public static final String TYPE_PUBLISH_DOC = "PublishDocAction";

	public static final String TYPE_MERGE_DOC = "MergeDocAction";

	public static final String TYPE_SYNCUPDATE = "SyncUpdateAction";

	public static final String TYPE_CTRL_DOC = "ControlledDocAction";

	public static final String TYPE_INSE_VERSION = "InsertVersionAction";

	@Index(name = "i_fra_ruleid")
	private String ruleId;

	@Index(name = "i_fra_folder")
	private long folderId;

	/**
	 * 条件类型
	 */
	private String actionType;

	@Transient
	private Map<String, String> propertiesMap;

	@Column(length = Length.LOB_DEFAULT)
	private String properties;

	/**
	 * 创建时间
	 */
	@Index(name = "i_fra_cd")
	@Column(nullable = false)
	private Date creationDate;

	/**
	 * 默认构造器
	 */
	public FolderRuleAction() {
		super();
		creationDate = new Date();
	}

	public String getDescription() {
		return RuleActionUtils.getDescrption(this);
	}

	/**
	 * 设置属性
	 * 
	 * @param name 名称
	 * @param value 值
	 */
	public void setProperty(String name, String value) {
		initPropertiesMap();
		propertiesMap.put(name, value);
		PropertyUtils.updateProperties(this);
	}

	/**
	 * 移除属性
	 * 
	 * @param name 要移除的属性名称
	 */
	public void removeProperty(String name) {
		initPropertiesMap();
		propertiesMap.remove(name);
	}

	/**
	 * 获取属性
	 * 
	 * @param name 名称
	 * @return 值
	 */
	public String getProperty(String name) {
		initPropertiesMap();
		return propertiesMap.get(name);
	}

	public Map<String, String> getPropertiesMap() {
		initPropertiesMap();
		return propertiesMap;
	}

	public void setPropertiesMap(Map<String, String> propertiesMap) {
		this.propertiesMap = propertiesMap;
	}

	public void initPropertiesMap() {
		if (this.propertiesMap == null) {
			this.propertiesMap = new HashMap<>();

			PropertyUtils.updatePropertiesMap(this);
		}
	}

	public String getProperties() {
		return properties;
	}

	public void setProperties(String properties) {
		this.properties = properties;
	}

	public String getActionType() {
		return actionType;
	}

	public void setActionType(String conditionType) {
		this.actionType = conditionType;
	}

	public String getRuleId() {
		return ruleId;
	}

	public void setRuleId(String ruleId) {
		this.ruleId = ruleId;
	}

	public long getFolderId() {
		return folderId;
	}

	public void setFolderId(long folderId) {
		this.folderId = folderId;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}
}
