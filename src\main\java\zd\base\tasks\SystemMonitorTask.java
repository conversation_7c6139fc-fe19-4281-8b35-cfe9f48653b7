package zd.base.tasks;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zd.base.utils.ZDSystemMonitorUtils;

/**
 * 每分钟执行任务类
 */
@Component
@Slf4j
public class SystemMonitorTask extends AbstractTask {

    @Scheduled(cron = "*/20 * * * * ?")
    @Scheduled(initialDelay = 60000)
    @Override
    public void work() {
        ZDSystemMonitorUtils.startCheck();
    }

}
