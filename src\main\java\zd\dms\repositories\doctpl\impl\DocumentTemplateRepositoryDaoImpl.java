package zd.dms.repositories.doctpl.impl;

import jakarta.persistence.EntityManager;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.DocumentTemplate;
import zd.dms.repositories.doctpl.DocumentTemplateRepository;

public class DocumentTemplateRepositoryDaoImpl extends BaseRepositoryDaoImpl<DocumentTemplate, String> implements DocumentTemplateRepository {

    public DocumentTemplateRepositoryDaoImpl(Class<DocumentTemplate> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }
}
