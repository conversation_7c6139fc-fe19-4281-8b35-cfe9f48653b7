package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import zd.base.entities.AbstractSequenceEntity;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "docindexqueue")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class IndexDocQueueItem extends AbstractSequenceEntity {

	/**
	 * serial
	 */
	private static final long serialVersionUID = 5059949321187842788L;

	public static final int TYPE_UPDATE = 1;
	public static final int TYPE_DELETE = 2;

	private String indexServerName;

	private String docId;
	private String sn;
	private String filename;

	@Column(length = Length.LOB_DEFAULT)
	private String content;

	@Column(length = Length.LOB_DEFAULT)
	private String docProps;

	private String creatorFullname;
	private Date creationDate;
	private Date updateDate;
	private boolean deleted;
	private long folderId;
	private Date customDate1;
	private Date customDate2;

	private int opType;

	public void setPropsByDocument(Document doc) {
		this.docId = doc.getId();
		this.sn = doc.getSerialNumber();
		this.filename = doc.getFilename();
		this.content = doc.getContent();
		this.docProps = doc.getDocProps();
		this.creatorFullname = doc.getCreatorFullname();
		this.creationDate = doc.getCreationDate();
		this.updateDate = doc.getModifiedDate();
		this.deleted = doc.isDeleted();
		this.folderId = doc.getFolderId();
		this.customDate1 = doc.getCustomDate1();
		this.customDate2 = doc.getCustomDate2();
	}

	public Document getTempDocObject() {
		Document doc = new Document();
		doc.setId(docId);
		doc.setSerialNumber(sn);
		doc.setFilename(filename);
		doc.setContent(content);
		doc.setDocProps(docProps);
		doc.setCreatorFullname(creatorFullname);
		doc.setCreationDate(creationDate);
		doc.setModifiedDate(updateDate);
		doc.setDeleted(deleted);
		doc.setFolderId(folderId);
		doc.setCustomDate1(customDate1);
		doc.setCustomDate2(customDate2);

		return doc;
	}

	public String getDocId() {
		return docId;
	}

	public void setDocId(String docId) {
		this.docId = docId;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getFilename() {
		return filename;
	}

	public void setFilename(String filename) {
		this.filename = filename;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getDocProps() {
		return docProps;
	}

	public void setDocProps(String docProps) {
		this.docProps = docProps;
	}

	public String getCreatorFullname() {
		return creatorFullname;
	}

	public void setCreatorFullname(String creatorFullname) {
		this.creatorFullname = creatorFullname;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public boolean isDeleted() {
		return deleted;
	}

	public void setDeleted(boolean deleted) {
		this.deleted = deleted;
	}

	public long getFolderId() {
		return folderId;
	}

	public void setFolderId(long folderId) {
		this.folderId = folderId;
	}

	public Date getCustomDate1() {
		return customDate1;
	}

	public void setCustomDate1(Date customDate1) {
		this.customDate1 = customDate1;
	}

	public Date getCustomDate2() {
		return customDate2;
	}

	public void setCustomDate2(Date customDate2) {
		this.customDate2 = customDate2;
	}

	public String getIndexServerName() {
		return indexServerName;
	}

	public void setIndexServerName(String indexServerName) {
		this.indexServerName = indexServerName;
	}

	public int getOpType() {
		return opType;
	}

	public void setOpType(int opType) {
		this.opType = opType;
	}
}
