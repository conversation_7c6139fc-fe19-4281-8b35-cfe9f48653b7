//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                           O\  =  /O
//                        ____/`---'\____
//                      .'  \\|     |//  `.
//                     /  \\|||  :  |||//  \
//                    /  _||||| -:- |||||-  \
//                    |   | \\\  -  /// |   |
//                    | \_|  ''\---/''  |   |
//                    \  .-\__  `-`  ___/-. /
//                  ___`. .'  /--.--\  `. . __
//               ."" '<  `.___\_<|>_/___.'  >'"".
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |
//              \  \ `-.   \_ __\ /__ _/   .-` /  /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
//                    佛祖保佑       永无BUG
package zd.dms.io;

import org.apache.commons.lang3.StringUtils;
import zd.dms.services.config.SystemConfigManager;

import java.util.Properties;

public class StorageUtils {

    public static String getLastStorageId() {
        if (StringUtils.isNotBlank(SystemConfigManager.getInstance()
                .getProperty("lastStorage"))) {
            return SystemConfigManager.getInstance().getProperty("lastStorage");
        }

        return "0";
    }

    public static void setLastStorage(int storageId) {
        SystemConfigManager.getInstance().setProperty("lastStorage", storageId);
        SystemConfigManager.getInstance().save();
    }

    public static String getActiveStorageId() {
        if (StringUtils.isNotBlank(SystemConfigManager.getInstance()
                .getProperty("activeStorage"))) {
            return SystemConfigManager.getInstance().getProperty(
                    "activeStorage");
        }

        return "0";
    }

    public static void setActiveStorage(String storageId) {
        SystemConfigManager.getInstance().setProperty("activeStorage",
                storageId);
        SystemConfigManager.getInstance().save();
    }

    public static void setStorage(String storageId, String dirPath) {
        SystemConfigManager.getInstance().setProperty(
                "storagePath" + storageId, dirPath);
        SystemConfigManager.getInstance().save();
    }

    public static Properties getStorages() {
        return SystemConfigManager.getInstance().getPropertiesWithPrefix(
                "storagePath");
    }

    public static String getStorageDirPath(String storageId) {
        return SystemConfigManager.getInstance().getProperty(
                "storagePath" + storageId);
    }
}
