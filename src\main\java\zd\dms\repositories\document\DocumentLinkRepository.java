package zd.dms.repositories.document;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentLink;
import zd.dms.entities.Folder;
import zd.dms.entities.User;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Revision$
 */
public interface DocumentLinkRepository extends BaseRepository<DocumentLink, Long> {

    /**
     * 批量获取一个文档目录下的所有文档链接
     *
     * @param conn       数据库连接
     * @param docLinkSql 获取文档链接的sql语句
     * @param type       文档链接的类型
     * @param folderId   文档目录id
     * @return 文档链接集合
     * @throws SQLException
     */
    List<DocumentLink> getDocumentLinksByJDBC(Connection conn, String docLinkSql, int type, Long folderId)
            throws SQLException;

    void deleteDocumentLink(User user);

    void deleteDocumentLink(Folder folder);

    void deleteDocumentLink(Document document);

    List<DocumentLink> getDocumentLinks(int type, Folder folder);

    List<DocumentLink> getDocumentLinks(int type, Document document);

    List<DocumentLink> getDocumentLinks(int type, User user);

    List<DocumentLink> getDocumentLinks(int type);

    void deleteLinkById(long id);

    /**
     * 根据文件名，更新时间对我常用的文档搜索
     *
     * @param typeMyCommon
     * @param username
     * @param fileNames
     * @param advUpdateStartDate
     * @param advUpdateEndDate
     * @return
     */
    List<DocumentLink> searchDocumentLinks(int typeMyCommon, String username, String fileNames,
                                           String advUpdateStartDate, String advUpdateEndDate);
}
