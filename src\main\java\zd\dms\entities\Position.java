package zd.dms.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractEntity;
import zd.base.entities.PropertyAware;
import zd.base.utils.system.SpringUtils;
import zd.dms.services.security.FolderPermissionUtils;
import zd.dms.services.security.RoleUtils;
import zd.dms.services.user.GroupService;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.services.user.UserService;
import zd.dms.utils.PropertyUtils;
import zd.dms.utils.TextUtils;

import java.util.*;

@Entity
@Getter
@Setter
@Table(name = "tdms_position")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Position extends AbstractEntity {

    @Index(name = "i_position_username")
    private String username;

    private String userFullname;

    @Index(name = "i_position_groupcode")
    private String groupCode;

    private String groupName;

    @Index(name = "i_position_position")
    private String position;

    @Index(name = "i_position_enabled")
    private boolean enabled;

    @Index(name = "i_position_numIndex")
    private int numIndex;

    @Index(name = "i_position_enabled")
    private Date creationDate;

    /**
     * 默认构造器
     */
    public Position() {
        super();
        enabled = true;
        numIndex = 9999;
        creationDate = new Date();
    }

}
