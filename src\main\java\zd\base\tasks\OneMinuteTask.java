package zd.base.tasks;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zd.dms.exception.UserAmountExceededException;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.ldap.LDAPException;
import zd.dms.services.ldap.LDAPService;
import zd.dms.services.mail.MailService;
import zd.dms.utils.DocStorageUtils;
import zd.dms.utils.ldap.LdapUtils;

/**
 * 每分钟执行任务类
 */
@Component
@Slf4j
public class OneMinuteTask extends AbstractTask {

    private SystemConfigManager scm = SystemConfigManager.getInstance();

    @Autowired
    private MailService mailService;

    @Autowired
    private LDAPService ldapService;

    @Scheduled(cron = "0 */1 * * * ?")
    @Scheduled(initialDelay = 60000)
    @Override
    public void work() {
        // 分区剩余容量提醒
        DocStorageUtils.remindStorageFreeSpace();

        // 检查是否有失败邮件
        mailService.remindFailedMails();

        syncLDAPGroupsAndUsers();
    }

    private void syncLDAPGroupsAndUsers() {
        boolean ldapAutoSync = scm.getBooleanProperty("ldap.autoSync", false);
        String ladpSyncType = StringUtils.defaultIfEmpty(scm.getProperty("ldap.syncType"), LdapUtils.LDAPSYNCTYPE_TIME);
        if (ldapAutoSync && LdapUtils.LDAPSYNCTYPE_TIME.equals(ladpSyncType)) {
            String adminUsername = LdapUtils.blowfish.decryptString(scm.getProperty("ldap.au"));
            String adminPassword = LdapUtils.blowfish.decryptString(scm.getProperty("ldap.ap"));
            if (!LdapUtils.checkLadpConnectionSettings(adminUsername, adminPassword)) {
                log.error("OneMinJob syncLDAPGroupsAndUsers LDAP配置不完整，无法连接");
                return;
            }

            try {
                ldapService.syncLDAPGroupsAndUsers(adminUsername, adminPassword, false);
            } catch (LDAPException e) {
                log.error("OneMinJob syncLDAPGroupsAndUsers LDAP错误", e);
            } catch (UserAmountExceededException e) {
                log.error("OneMinJob syncLDAPGroupsAndUsers LDAP同步错误：已达最大用户数");
            }
        }
    }
}
