package zd.dms.controllers.user;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.BaseController;
import zd.base.service.BaseJpaService;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapUtils;
import zd.base.utils.model.ObjectMapperUtils;
import zd.base.utils.validate.ValidateUtils;
import zd.dms.dto.PageResponse;
import zd.dms.entities.Role;
import zd.dms.entities.User;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.position.PositionService;
import zd.dms.services.user.UserService;

import java.util.*;

@RequiredArgsConstructor
@RestController
@Tag(name = "UserController", description = "用户Controller")
@RequestMapping("/user")
public class UserController extends BaseController<User, String> {

    private final UserService userService;

    private final PositionService positionService;

    @Override
    public BaseJpaService<User, String> getBaseJpaService() {
        return userService;
    }

    @Operation(summary = "获取分页用户")
    @GetMapping("/page/{groupId}")
    @ZDLog("获取分页用户")
    public JSONResultUtils<Object> listUsers(@PathVariable long groupId, @RequestParam int pageNumber, @RequestParam int pageSize) {
        Page page = userService.getPageUserByGroupId(groupId, pageNumber, pageSize);

        return successData(PageResponse.of(page, "list"));
    }

    @Operation(summary = "根据用户名、部门id获取分页用户")
    @GetMapping("/getUserPage")
    @ZDLog("根据用户名、部门id获取分页用户")
    public JSONResultUtils<Object> getUserPage(@RequestParam long groupId, @RequestParam int pageNumber, @RequestParam int pageSize, @RequestParam(required = false) String username) {
        Page page = userService.searchUserPage(groupId, pageNumber, pageSize, username);

        return successData(PageResponse.of(page, ""));
    }

    @Operation(summary = "创建用户")
    @PostMapping("/create")
    @ZDLog("创建用户")
    public JSONResultUtils<Object> create(@RequestBody Map<String, Object> params) {
        String username = MapUtils.getString(params, "username", "");
        ValidateUtils.isTrue(StringUtils.isNotBlank(username), "请输入用户名");

        String fullname = MapUtils.getString(params, "fullname", "");
        ValidateUtils.isTrue(StringUtils.isNotBlank(fullname), "请输入用户姓名");

        String userp = MapUtils.getString(params, "userp", "");
        ValidateUtils.isTrue(StringUtils.isNotBlank(userp), "请输入密码");

        return userService.createUser(params);
    }

    @Operation(summary = "更新用户")
    @PostMapping("/update")
    @ZDLog("更新用户")
    public JSONResultUtils<Object> update(@RequestBody Map<String, Object> params) {
        String id = MapUtils.getString(params, "id", "");
        ValidateUtils.isTrue(StringUtils.isNotBlank(id), "请选择用户");

        return userService.update(params);
    }

    @Operation(summary = "更新用户")
    @PostMapping("/updateUserInfo")
    @ZDLog("更新用户")
    public JSONResultUtils<Object> updateUserInfo(@RequestBody Map<String, Object> params) {
        String username = MapUtils.getString(params, "username", "");
        ValidateUtils.isTrue(StringUtils.isNotBlank(username), "用户名为空");

        return userService.updateUserInfo(params);
    }

    @Operation(summary = "删除用户")
    @PostMapping("/delete/{id}")
    @ZDLog("删除用户")
    public JSONResultUtils<Object> delete(@PathVariable String id, HttpServletRequest request) {
        ValidateUtils.isTrue(StringUtils.isNotBlank(id), "请选择用户");

        userService.deleteUser(id);

        return success();
    }

    @Operation(summary = "批量删除用户")
    @PostMapping("/delete")
    @ZDLog("批量删除用户")
    public JSONResultUtils<Object> delete(@RequestBody List<String> ids, HttpServletRequest request) {
        ValidateUtils.isTrue(CollectionUtils.isNotEmpty(ids), "u100002");

        ids.stream().forEach(id -> {
            userService.deleteUser(id);
        });

        return successMsg("批量删除成功");
    }

    @Operation(summary = "根据用户名获取用户")
    @GetMapping("/getUserByUsername/{username}")
    @ZDLog("根据用户名获取用户")
    public JSONResultUtils<Object> getUserByUsername(@PathVariable String username) {
        ValidateUtils.isTrue(StringUtils.isNotBlank(username), "u100002");
        User user = userService.getUserByUsername(username);
        String property = user.getProperty("web-theme");
        System.out.println("property1 " + property);
        return successData(ObjectMapperUtils.toMap(user, "info"));
    }

    @Operation(summary = "保存主题")
    @PostMapping("/saveTheme")
    @ZDLog("保存主题")
    public JSONResultUtils<Object> saveTheme(@RequestBody Map<String, Object> params) {
        String color = MapUtils.getString(params, "color", "");
        User currentUser = getCurrentUser();

        if (currentUser != null) {
            User userByUsername = userService.getUserByUsername(currentUser.getUsername());
            if (userByUsername != null) {
                userByUsername.setProperty("web-theme", color);
                userService.updateUser(userByUsername);
            }
        }

        return success();
    }

    @Operation(summary = "获取属性")
    @PostMapping("/getProperty")
    @ZDLog("获取属性")
    public JSONResultUtils<Object> getProperty(@RequestBody Map<String, Object> params) {
        String propName = MapUtils.getString(params, "propName", "");
        if (StringUtils.isBlank(propName)) {
            return successData("");
        }

        User currentUser = getCurrentUser();

        if (currentUser != null) {
            String property = currentUser.getProperty(propName);
            if (StringUtils.isBlank(property)) {
                property = "";
            }

            return successData(property);
        }

        return successData("");
    }

    @Operation(summary = "获取属性")
    @PostMapping("/getProperties")
    @ZDLog("获取属性")
    public JSONResultUtils<Object> getProperties(@RequestBody Map<String, Object> params) {
        List<String> propNames = ZDMapUtils.getListStringValue(params, "propNames");
        if (CollectionUtils.isEmpty(propNames)) {
            return successData(new HashMap<>());
        }

        User currentUser = getCurrentUser();

        if (currentUser != null) {
            Map<String, Object> propertyMap = new HashMap<>();
            for (String propName : propNames) {
                String property = currentUser.getProperty(propName);
                if (StringUtils.isBlank(property)) {
                    property = "";
                }

                propertyMap.put(propName, property);
            }

            return successData(propertyMap);
        }

        return successData(new HashMap<>());
    }

    @Operation(summary = "保存属性")
    @PostMapping("/saveProperty")
    @ZDLog("保存属性")
    public JSONResultUtils<Object> saveProperty(@RequestBody Map<String, Object> params) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return error("用户未找到");
        }

        List<Map<String, Object>> propMaps = ZDMapUtils.getListMapValue(params, "propMaps");
        if (CollectionUtils.isEmpty(propMaps)) {
            return success();
        }

        User userByUsername = userService.getUserByUsername(currentUser.getUsername());
        for (Map<String, Object> propMap : propMaps) {
            String propName = MapUtils.getString(propMap, "propName", "");
            String propValue = MapUtils.getString(propMap, "propValue", "");
            if (StringUtils.isBlank(propName)) {
                continue;
            }

            if (StringUtils.isNotBlank(propValue)) {
                userByUsername.setProperty(propName, propValue);
            } else {
                userByUsername.removeProperty(propName);
            }
        }

        userService.updateUser(userByUsername);

        return success();
    }

    @Operation(summary = "获取岗位")
    @PostMapping("/positions")
    @ZDLog("获取岗位")
    public JSONResultUtils<Object> positions() {
        Set<String> positions = positionService.getPositionSettings();
        return successData(positions);
    }

    @Operation(summary = "保存岗位")
    @PostMapping("/savePositions")
    @ZDLog("保存岗位")
    public JSONResultUtils<Object> savePositions(@RequestBody Map<String, Object> params) {
        List<String> positions = ZDMapUtils.getListStringValue(params, "positions");
        positionService.savePositionSettings(StringUtils.join(positions, ","));
        return success();
    }

    @Operation(summary = "获取角色")
    @PostMapping("/roles")
    @ZDLog("获取角色")
    public JSONResultUtils<Object> roles() {
        checkRoles(Role.SYSTEM_ADMIN);
        // 根据是否是ECM版，展示不同的角色 hasWDTRecord || hasWDTDA
        Map<String, String> roleMap = new LinkedHashMap<String, String>();
        roleMap.put("admin", "系统管理员");
        roleMap.put("limitedAdmin", "后台管理员");
        roleMap.put("userAdmin", "用户管理员");
        roleMap.put("workflowAdmin", "流程管理员");

        if (!isHasWDTDA()) {
            roleMap.put("noticeAdmin", "通知公告管理员");
        }

        roleMap.put("receptionAdmin", "前台管理员");

        if (isHasWDTRecord() || isHasWDTDA()) {
            roleMap.put("ecmAdmin", "ECM管理员");
        }

        if (isHasDCC()) {
            roleMap.put("dccAdmin", "DCC管理员");
        }

        boolean threeMemberManage = SystemConfigManager.getInstance().getBooleanProperty("threeMemberManage");
        if (threeMemberManage) {
            roleMap.put("logsAdmin", "安全审计员");
        }

        return successData(roleMap);
    }
}
