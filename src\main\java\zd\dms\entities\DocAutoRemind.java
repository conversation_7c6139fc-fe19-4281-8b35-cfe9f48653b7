package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.dms.utils.TextUtils;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "doc_autoremind")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class DocAutoRemind extends AbstractSequenceEntity {

	/**
	 * serial
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 提醒设定者全名
	 */
	private String creatorFullname;

	@Column(length = Length.LOB_DEFAULT)
	private String remindUserGroups;

	public static final int TYPE_ONCE = 1;
	public static final int TYPE_EVERY_MONTH = 2;
	public static final int TYPE_EVERY_YEAR = 3;

	/**
	 * 提醒类型
	 */
	private int remindType;

	/**
	 * 提醒标题
	 */
	private String title;

	/**
	 * 提醒内容
	 */
	@Column(length = Length.LOB_DEFAULT)
	private String content;

	@Index(name = "i_rar_recfolderid")
	private long folderId;

	private String colName;

	/**
	 * 提取提醒天数
	 */
	private Integer advanceDay;

	/**
	 * 创建时间
	 */
	@Index(name = "i_rar_cd")
	@Column(nullable = false)
	private Date creationDate;

	/**
	 * 默认构造器
	 */
	public DocAutoRemind() {
		super();
		creationDate = new Date();
	}

	public String getCreatorFullname() {
		return creatorFullname;
	}

	public void setCreatorFullname(String creatorFullname) {
		this.creatorFullname = creatorFullname;
	}

	public String getRemindUserGroups() {
		return remindUserGroups;
	}

	public void setRemindUserGroups(String remindUserGroups) {
		this.remindUserGroups = remindUserGroups;
	}

	public String getTitle() {
		return TextUtils.escapeXml(title);
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public int getRemindType() {
		return remindType;
	}

	public void setRemindType(int remindType) {
		this.remindType = remindType;
	}

	public String getContent() {
		return TextUtils.escapeXml(content);
	}

	public void setContent(String content) {
		this.content = content;
	}

	public long getFolderId() {
		return folderId;
	}

	public void setFolderId(long folderId) {
		this.folderId = folderId;
	}

	public String getColName() {
		return colName;
	}

	public void setColName(String colName) {
		this.colName = colName;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}

	public Integer getAdvanceDay() {
		if (advanceDay == null) {
			return 0;
		}

		return advanceDay;
	}

	public void setAdvanceDay(Integer advanceDay) {
		this.advanceDay = advanceDay;
	}
}
