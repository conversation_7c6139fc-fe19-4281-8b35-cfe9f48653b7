package zd.dms.repositories.im.impl;

import jakarta.persistence.EntityManager;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.InstantMessage;
import zd.dms.repositories.im.InstantMessageRepository;
import zd.dms.utils.PageableUtils;
import zd.dms.utils.repositories.PredicateUtils;
import zd.dms.utils.repositories.SpecTools;
import zd.record.utils.RecordDBUtils;

import java.util.Date;
import java.util.List;

public class InstantMessageRepositoryDaoImpl extends BaseRepositoryDaoImpl<InstantMessage, Long> implements InstantMessageRepository {

    public InstantMessageRepositoryDaoImpl(Class<InstantMessage> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public void deleteMessagesByUsername(String username) {
        String hql = "delete from InstantMessage where sender = ?1";
        executeUpdate(hql, username);
    }

    @Override
    public void deleteMessagesBeforeDate(Date date) {
        String hql = "delete from InstantMessage where creationDate < ?1";
        executeUpdate(hql, date);
    }

    @Override
    public List<InstantMessage> getMessagesUnread(String username) {
        Specification<InstantMessage> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<InstantMessage> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("target", username);
            specTools.eq("received", false);

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void setRecevied(InstantMessage im) {
        im.setReceived(true);
        update(im);
    }

    @Override
    public List<InstantMessage> searchMessages(String keyword, String type, String username) {
        Specification<InstantMessage> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<InstantMessage> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("target", username);
            if (StringUtils.isNotBlank(type)) {
                specTools.eq("type", type);
            }

            specTools.or(PredicateUtils.like(root, criteriaBuilder, "title",
                    "%" + keyword + "%"), PredicateUtils.like(root, criteriaBuilder, "msg", "%" + keyword + "%"));
            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public Page getMessages(String type, String username, int pageNumber, int pageSize) {
        Specification<InstantMessage> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<InstantMessage> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.or(PredicateUtils.equal(root, criteriaBuilder, "target", username), PredicateUtils.equal(root, criteriaBuilder, "sender", username));
            if (StringUtils.isNotBlank(type)) {
                specTools.eq("type", type);
            }

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }

    @Override
    public Page getReceviedMessages(String type, String username, int pageNumber, int pageSize) {
        Specification<InstantMessage> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<InstantMessage> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("target", username);
            if (StringUtils.isNotBlank(type)) {
                specTools.eq("type", type);
            }

            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec, pageNumber, pageSize);
    }

    @Override
    public List<InstantMessage> getLatestMessages(int count, String type, String target, String sender) {
        Specification<InstantMessage> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<InstantMessage> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            if (StringUtils.isNotBlank(type)) {
                specTools.eq("type", type);
            }

            if (StringUtils.isNotBlank(target)) {
                specTools.eq("target", target);
            }

            if (StringUtils.isNotBlank(sender)) {
                specTools.eq("sender", sender);
            }

            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        int pageNumber = PageableUtils.getPageNumberByStartIndex(0, count);

        return findAll(spec, pageNumber, count).getContent();
    }

    @Override
    public void deleteMsgById(long id) {
        String hql = "delete from InstantMessage where id = ?1";
        executeUpdate(hql, id);
    }

    @Override
    public int getCount() {
        return NumberUtils.toInt(String.valueOf(findObject("select count(*) from InstantMessage")));

    }

    @Override
    public int getUnreadCount(String username) {
        String queryString = "select count(*) from instant_message where target='" + username + "' and received=0";
        if (RecordDBUtils.isPostgreSQLOrUXOrHighgoOrKingbase()) {
            queryString = "select count(*) from instant_message where target='" + username + "' and received = true";
        }

        return RecordDBUtils.checkJdbcTemplate().queryForObject(queryString, Integer.class);
    }

    @Override
    public void receiveAllMsg(String username) {
        String hql = "update InstantMessage set received = true where target = ?1 and received = false";
        executeUpdate(hql, username);
    }
}
