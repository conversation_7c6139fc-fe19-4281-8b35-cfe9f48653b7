package zd.dms.controllers.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.ResponseEntityUtils;
import zd.base.utils.ZDDateUtils;
import zd.dms.workflow.ProcessInstanceExportService;
import zd.record.service.folder.RecFolderService;

import java.io.InputStream;
import java.util.Date;

@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "ReportController", description = "报表Controller")
@RequestMapping("/admin")
public class ReportController extends ControllerSupport {

    /*private final ProcessInstanceExportService processInstanceExportService;*/

    private final RecFolderService recFolderService;

    @Operation(summary = "导出流程报表")
    @GetMapping("/exportPiXls")
    @ZDLog("导出流程报表")
    public ResponseEntity<byte[]> exportPiXls(@RequestParam String startDate, @RequestParam String endDate, @RequestHeader("User" +
            "-Agent") String userAgent) {
        Date startDateObj = ZDDateUtils.getStartDateByString(startDate);
        Date endDateObj = ZDDateUtils.getEndDateByString(endDate);

        try {
            /*InputStream in = processInstanceExportService.getExcelPi(startDateObj, endDateObj);
            return ResponseEntityUtils.fileResult("审批流程-" + startDate + "至" + endDate + ".xlsx", in);*/
        } catch (Exception e) {
            log.error("exportPiXls error", e);
        }

        return ResponseEntityUtils.nullResult();
    }
}
