<script setup lang="ts">

import { Column, DOCUMENT_LENDING_COLUMNS } from 'config/columns';
import { onMounted, ref } from 'vue';
import SimplePage from '@/components/common/SimplePage.vue';
import CommonTableLayout from '@/components/common/CommonTableLayout.vue';
import { documentLendingApi } from 'utils/api/document/document-lending-api';
import type { DocumentLendingDTO, SearchLendToMeRequest } from '@/types/document/document-lending-api-types';
import { ZDNKOfficeUtils } from '@/utils/document/ZDNKOfficeUtils';
import { ZDDocPermUtils } from '@/utils/document/ZDDocPermUtils';
import { DF_PERM_MAP } from '@/config/document/folder/columns-perm';
import { QNotify } from 'utils/common/qusar-notify-utils';
import { ZDDownloadUtils } from 'utils/system/ZDDownloadUtils';
import { ZDDocUtils } from 'utils/document/ZDDocUtils';
import TableColumnButton from '@/components/common/TableColumnButton.vue';
import TableColumnMenuList from '@/components/common/TableColumnMenuList.vue';
import TableColumnMenuItem from '@/components/common/TableColumnMenuItem.vue';
import { mapNameArrayToColumnArray } from 'utils/common/column-utils';
import usePage from 'utils/hooks/page-hook';

// 搜索
const searchButtonLoading = ref(false);
const lenderFullName = ref('');
const fileName = ref('');

// 表格
const tableLoading = ref(false);
const lendings = ref<DocumentLendingDTO[]>([]);
const selectedLendings = ref<DocumentLendingDTO[]>([]);

const columnNames: Array<keyof DocumentLendingDTO> = ['fileName', 'path', 'fileExtension', 'fileSize', 'displayLender', 'returnTime', 'remainTime',];
const columns: Array<Column<DocumentLendingDTO>> = mapNameArrayToColumnArray(DOCUMENT_LENDING_COLUMNS, columnNames);

// nk
const nkDocUrl = ref('');

// 加载数据
const searchLending = async (page: number, size: number) => {
  const params: SearchLendToMeRequest = {
    page: page - 1,
    size,
    sort: ['creationDate,desc'],
    fileName: fileName.value,
    lenderFullName: lenderFullName.value,
  }

  const response = await documentLendingApi.searchLendToMe(params);
  lendings.value = response.content;
  total.value = response.totalElements;
}

// 分页
const pageDisabled = ref(false);
const handlePageChange = async (page: number, size: number) => {
  try {
    tableLoading.value = true;
    pageDisabled.value = true;
    await searchLending(page, size);
  } finally {
    tableLoading.value = false;
    pageDisabled.value = false;
  }
}
const { currentPage, pageSize, total, refresh } = usePage(handlePageChange);
const handleSearchButtonClick = async () => {
  try {
    searchButtonLoading.value = true;
    refresh();
  } finally {
    searchButtonLoading.value = false;
  }
}

const canRead = (row: DocumentLendingDTO) => {
  return ZDNKOfficeUtils.isCanRead(row.fileExtension) && ZDDocPermUtils.hasPerm(row.permissions, DF_PERM_MAP.READ)
}

const readDocument = (row: DocumentLendingDTO) => {
  const docExt = row.fileExtension;
  const docId = row.documentId;
  const from = 'document-lending';
  const info = `folderId=${row.folderId}`;

  if (ZDNKOfficeUtils.isCanRead(docExt)) {
    nkDocUrl.value = ZDNKOfficeUtils.getIframeUrl(docId, from, info, docExt);
  } else {
    QNotify.negative('不支持的文件格式');
  }
};

const canEdit = (row: DocumentLendingDTO) => ZDNKOfficeUtils.isCanRead(row.fileExtension) && ZDDocPermUtils.hasPerm(row.permissions, DF_PERM_MAP.EDIT)

const editDocument = (row: DocumentLendingDTO) => {
  const docExt = row.fileExtension;
  const docId = row.documentId;
  const from = 'document-lending';
  const info = `folderId=${row.folderId}`;

  if (ZDNKOfficeUtils.isCanRead(docExt)) {
    nkDocUrl.value = ZDNKOfficeUtils.getEditIframeUrl(docId, from, info, docExt);
  } else {
    QNotify.negative('不支持的文件格式');
  }
};

const canDownload = (row: DocumentLendingDTO) => ZDDocPermUtils.hasPerm(row.permissions, DF_PERM_MAP.DOWNLOAD)
const downloadDocument = (row: DocumentLendingDTO) => {
  const downloadUrl = ZDDocUtils.getDownloadUrl(row.documentId, row.folderId + '', row.fileExtension);
  ZDDownloadUtils.downloadFile(downloadUrl);
}

const handleSelectionChange = (selection: DocumentLendingDTO[]) => {
  selectedLendings.value = selection;
};

// 页面加载时初始化
onMounted(() => {
  refresh();
})
</script>

<template>
  <CommonTableLayout>
    <template #toolbar>
      <div class="row q-gutter-x-sm">
        <!-- <q-btn class="text-main-btn-theme" label="续借" color="main-btn-theme" unelevated @click="startWorkflow" /> -->
      </div>
    </template>
    <template #search>
      <div class="row items-center q-gutter-md">
        <div class="row items-center q-gutter-sm">
          <div class="col-auto">借出人：</div>
          <div class="col-auto">
            <q-input v-model="lenderFullName" bottom-slots hide-bottom-space outlined dense class="search-input" />
          </div>
        </div>

        <div class="row items-center q-gutter-x-sm">
          <div class="col-auto">文件名：</div>
          <div class="col-auto">
            <q-input v-model="fileName" bottom-slots hide-bottom-space outlined dense class="search-input" />
          </div>
        </div>

        <div class="col-auto">
          <q-btn color="primary" label="搜索" @click="handleSearchButtonClick" :loading="searchButtonLoading" />
        </div>
      </div>
    </template>
    <template #default="{ height }">
      <el-table row-key="id" :height="height" :data="lendings" highlight-current-row
        @selection-change="handleSelectionChange" v-loading="tableLoading">
        <template :key="col.name" v-for="col in columns">
          <el-table-column :prop="col.name" :label="col.label" align="left" show-overflow-tooltip />
        </template>

        <el-table-column fixed="right" label="操作" width="140px">
          <template #default="{ row }: { row: DocumentLendingDTO }">
            <div class="flex justify-start items-center q-gutter-sm">
              <TableColumnButton label="阅读" :show="canRead(row)" @click="readDocument(row)" />
              <TableColumnButton label="编辑" :show="canEdit(row)" @click="editDocument(row)" />
              <TableColumnMenuList :show="canDownload(row)">
                <TableColumnMenuItem v-if="canDownload(row)" label="下载" @click="downloadDocument(row)" />
              </TableColumnMenuList>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <iframe id="iframe" :src="nkDocUrl" frameborder="0" width="0" height="0"></iframe>
    </template>

    <template #pagination>
      <div class="q-pl-md q-pr-md">
        <SimplePage :total="total" v-model:current-page="currentPage" v-model:page-size="pageSize" :disabled="pageDisabled" />
      </div>
    </template>
  </CommonTableLayout>
</template>

<style scoped lang="scss">
.search-input {
  width: 200px;
}
</style>
