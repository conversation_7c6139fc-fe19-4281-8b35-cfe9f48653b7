package zd.base.utils.model;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import zd.base.utils.ClassUtils;
import zd.base.utils.ZDDateUtils;
import zd.dms.entities.User;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

@Slf4j
public class BeanUtils {

    private static List<String> enableParamTypes = null;

    /**
     * 内省进行数据转换-javaBean转map
     *
     * @param obj 需要转换的bean
     * @return 转换完成的map
     * @throws Exception
     */
    /*public static <T> Map<String, Object> beanToMap(T obj, boolean putIfNull, String... removeKeys) {
        try {
            Map<String, Object> map = new HashMap<>();
            if (removeKeys == null) {
                removeKeys = new String[]{};
            }

            // 获取javaBean的BeanInfo对象
            BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass(), Object.class);
            // 获取属性描述器
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
                // 获取属性名
                String key = propertyDescriptor.getName();
                System.out.println("--------------" + key);
                if (ArrayUtils.contains(removeKeys, key)) {
                    continue;
                }

                if (!isCanMap(propertyDescriptor)) {
                    continue;
                }

                // 获取该属性的值
                Method readMethod = propertyDescriptor.getReadMethod();
                if (readMethod == null) {
                    continue;
                }

                // 通过反射来调用javaBean定义的getName()方法
                Object value = readMethod.invoke(obj);
                if (value == null && !putIfNull) {
                    continue;
                }

                if (value instanceof Date) {
                    value = ZDDateUtils.format((Date) value, ObjectMapperUtils.dateFormatStr);
                }

                map.put(key, value);
            }

            return map;
        } catch (Exception e) {
            log.error("beanToMap error", e);
        }

        return new HashMap<String, Object>();
    }*/

    /**
     * 内省进行数据转换-javaBean转map
     *
     * @param obj 需要转换的bean
     * @return 转换完成的map
     * @throws Exception
     */
    public static <T> Map<String, Object> beanToMap(T obj, boolean putIfNull, String... removeKeys) {
        Map<String, Object> results = beanToMap(obj, null, putIfNull, removeKeys);
        Class<?> superclass = obj.getClass().getSuperclass();
        if (superclass == null) {
            return results;
        }

        String name = superclass.getName();
        if (!"zd.base.entities.AbstractEntity".equals(name) && !"zd.base.entities.AbstractSequenceEntity".equals(name)) {
            return results;
        }

        Map<String, Object> superResults = beanToMap(obj, superclass, putIfNull, removeKeys);
        superResults.putAll(results);

        return superResults;
    }

    private static <T> Map<String, Object> beanToMap(T obj, Class<?> clz, boolean putIfNull, String... removeKeys) {
        try {
            Map<String, Object> map = new HashMap<>();
            if (removeKeys == null) {
                removeKeys = new String[]{};
            }

            if (clz == null) {
                clz = obj.getClass();
            }

            Field[] declaredFields = clz.getDeclaredFields();
            for (Field field : declaredFields) {
                String name = field.getName();
                if (ArrayUtils.contains(removeKeys, name)) {
                    continue;
                }

                if (!isCanMap(field)) {
                    continue;
                }

                Method method = null;
                String getMethodName = getMethodName(name, "get");
                try {
                    method = clz.getMethod(getMethodName);
                } catch (Exception e) {
                }

                if (method == null) {
                    Class<?> type = field.getType();
                    String className = type.getName();
                    if ("java.lang.Boolean".equals(className) || "boolean".equals(className)) {
                        getMethodName = getMethodName(name, "is");
                        try {
                            method = clz.getMethod(getMethodName);
                        } catch (Exception e) {
                        }
                    }
                }

                if (method == null) {
                    continue;
                }
                method.setAccessible(true);

                Object value = method.invoke(obj);
                if (value == null && !putIfNull) {
                    continue;
                }

                if (value instanceof Date) {
                    value = ZDDateUtils.format((Date) value, ObjectMapperUtils.dateFormatStr);
                }

                map.put(name, value);
            }

            return map;
        } catch (Exception e) {
            log.error("beanToMap error", e);
        }

        return new HashMap<String, Object>();
    }

    public static <T> List<Map<String, Object>> beansToMaps(List<T> objs, boolean putIfNull) throws Exception {
        return beansToMaps(objs, putIfNull, false);
    }

    private static boolean isCanMap(PropertyDescriptor propertyDescriptor) {
        getEnableParamTypes();

        Class<?> propertyType = propertyDescriptor.getPropertyType();
        if (propertyType == null) {
            return false;
        }

        String className = propertyType.getName();
        return enableParamTypes.contains(className);
    }

    private static boolean isCanMap(Field field) {
        getEnableParamTypes();

        Class<?> type = field.getType();
        String className = type.getName();
        return enableParamTypes.contains(className);
    }

    private static List<String> getEnableParamTypes() {
        if (enableParamTypes == null) {
            synchronized (BeanUtils.class) {
                if (enableParamTypes == null) {
                    enableParamTypes = new ArrayList<>();

                    enableParamTypes.add("boolean");
                    enableParamTypes.add("java.lang.Boolean");

                    enableParamTypes.add("int");
                    enableParamTypes.add("java.lang.Integer");

                    enableParamTypes.add("long");
                    enableParamTypes.add("java.lang.Long");

                    enableParamTypes.add("double");
                    enableParamTypes.add("java.lang.Double");

                    enableParamTypes.add("float");
                    enableParamTypes.add("java.lang.Float");

                    enableParamTypes.add("java.lang.String");
                    enableParamTypes.add("java.util.Date");
                }
            }
        }

        return enableParamTypes;
    }

    public static <T> List<Map<String, Object>> beansToMaps(List<T> objs, boolean putIfNull, boolean addIndex)
            throws Exception {

        List<Map<String, Object>> result = new ArrayList<>();

        Map<String, Object> beanToMap = null;
        int index = 0;
        for (Object obj : objs) {
            beanToMap = beanToMap(obj, putIfNull);
            if (addIndex) {
                beanToMap.put("index", ++index);
            }

            result.add(beanToMap);
        }

        return result;
    }

    /**
     * Map转bean
     *
     * @param map map
     * @param clz 被转换的类字节码对象
     * @return
     * @throws Exception
     */
	/*public static <T> T map2Bean(Map<String, Object> map, Class<T> clz) throws Exception {
		// new 出一个对象
		T obj = clz.newInstance();
		// 获取person类的BeanInfo对象
		BeanInfo beanInfo = Introspector.getBeanInfo(clz, Object.class);
		// 获取属性描述器
		PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
		for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
			// 获取属性名
			String key = propertyDescriptor.getName();
			Object value = map.get(key);

			// 通过反射来调用Person的定义的setName()方法
			Method writeMethod = propertyDescriptor.getWriteMethod();
			writeMethod.invoke(obj, value);
		}
		return obj;
	}*/
    public static <T> T map2Bean(Map<String, Object> map, Class<T> clz) {
        // new 出一个对象
        try {
            T obj = ClassUtils.newInstance(clz);
            if (MapUtils.isEmpty(map)) {
                return obj;
            }

            Map<String, Object> results = removeFields(map, clz);
            Set<Map.Entry<String, Object>> entries = results.entrySet();
            for (Map.Entry<String, Object> entry : entries) {
                String fieldName = entry.getKey();
                if (StringUtils.isBlank(fieldName)) {
                    continue;
                }

                Field field = null;
                try {
                    field = clz.getDeclaredField(fieldName);
                } catch (Exception e) {
                    log.debug("noSuchField :{}", fieldName);
                }

                if (field == null) {
                    continue;
                }

                field.setAccessible(true);
                field.set(obj, entry.getValue());
            }

            return obj;
        } catch (Exception e) {
            log.error("map2Bean error", e);
        }

        return null;
    }

    public static <T> T map2Bean(Map<String, Object> map, T obj) {
        if (obj == null) {
            return obj;
        }
        // new 出一个对象
        try {
            if (MapUtils.isEmpty(map)) {
                return obj;
            }

            Class<?> clz = obj.getClass();
            Map<String, Object> results = removeFields(map, clz);

            Set<Map.Entry<String, Object>> entries = results.entrySet();
            for (Map.Entry<String, Object> entry : entries) {
                String fieldName = entry.getKey();
                if (StringUtils.isBlank(fieldName)) {
                    continue;
                }

                Field field = null;
                try {
                    field = clz.getDeclaredField(fieldName);
                } catch (Exception e) {
                    log.debug("noSuchField :{}", fieldName);
                }

                if (field == null) {
                    continue;
                }

                field.setAccessible(true);
                field.set(obj, entry.getValue());
            }

            return obj;
        } catch (Exception e) {
            log.error("map2Bean error", e);
        }

        return null;
    }

    private static <T> Map<String, Object> removeFields(Map<String, Object> map, Class<T> clz) {
        Map<String, Object> results = new HashMap<>();
        results.putAll(map);
        if (clz == User.class) {
            results.remove("roles");
            results.remove("groupIds");
            results.remove("groups");
            results.remove("password");
            results.remove("userp");
            results.remove("groupIdsList");
            results.remove("creationDate");
            results.remove("web-theme");
            results.remove("positions");
        }

        return results;
    }

    private static String getMethodName(String field, String prefix) {
        if (StringUtils.isBlank(field)) {
            return prefix;
        }

        String c = field.charAt(0) + "";
        return prefix + c.toUpperCase() + field.substring(1);
    }

    public static <T> List<T> maps2Beans(List<Map<String, Object>> maps, Class<T> clz) throws Exception {
        List<T> result = new ArrayList<>();
        for (Map<String, Object> map : maps) {
            result.add(map2Bean(map, clz));
        }

        return result;
    }

    /**
     * 复制origin的值到dest上
     *
     * @param dest              目标对象
     * @param origin            元对象
     * @param setNull           如果源对象属性为null是否覆盖
     * @param excludeFieldNames 排除的属性
     */
    public static <T> void copyProperties(T dest, T origin, boolean setNull, String[] excludeFieldNames) {
        try {
            // 获取person类的BeanInfo对象
            BeanInfo destBeanInfo = Introspector.getBeanInfo(dest.getClass(), Object.class);

            // 获取目标属性描述器
            PropertyDescriptor[] destBeanInfoPropertyDescriptors = destBeanInfo.getPropertyDescriptors();

            for (PropertyDescriptor propertyDescriptor : destBeanInfoPropertyDescriptors) {
                // 获取属性名
                String key = propertyDescriptor.getName();
                if (ArrayUtils.contains(excludeFieldNames, key)) {
                    continue;
                }

                // 获取该属性的值
                Method readMethod = propertyDescriptor.getReadMethod();

                // 如果源对象没有对应属性就跳过
                Object srcValue = null;
                try {
                    srcValue = readMethod.invoke(origin);
                } catch (Exception ignored) {
                    // ignored
                    continue;
                }

                // 如果源对象的值null且null不设置的时候跳过
                if (srcValue == null && !setNull) {
                    continue;
                }

                // 获取setter方法修改属性
                Method writeMethod = propertyDescriptor.getWriteMethod();
                if (writeMethod == null) {
                    continue;
                }
                writeMethod.invoke(dest, srcValue);
            }
        } catch (Exception ignored) {
            // ignored
        }
    }

    public static <T> void copyProperties(T dest, T origin) {
        copyProperties(dest, origin, false, null);
    }

    public static <T> Object getProperty(T object, String proeprty) {
        // 获取javaBean的BeanInfo对象
        BeanInfo beanInfo;
        try {
            beanInfo = Introspector.getBeanInfo(object.getClass(), Object.class);
        } catch (IntrospectionException ignore) {
            return new Object();
        }

        // 获取属性描述器
        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            // 获取属性名
            String key = propertyDescriptor.getName();
            if (proeprty.equals(key)) {
                // 获取该属性的值
                Method readMethod = propertyDescriptor.getReadMethod();
                // 通过反射来调用javaBean定义的getName()方法
                try {
                    Object value = readMethod.invoke(object);
                    return value;
                } catch (Exception ignore) {
                    return new Object();
                }
            }
        }

        return new Object();
    }
}
