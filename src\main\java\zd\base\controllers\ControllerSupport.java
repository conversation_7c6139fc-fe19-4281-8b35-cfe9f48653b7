package zd.base.controllers;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.lucene.analysus.ja.dict.ModeDefinition;
import org.apache.lucene.analysus.ja.dict.TaskDictionary;
import zd.base.context.UserContextHolder;
import zd.base.utils.system.SpringUtils;
import zd.dms.entities.Folder;
import zd.dms.entities.LogMessage;
import zd.dms.entities.User;
import zd.dms.io.ZDIOUtils;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.log.LogService;
import zd.dms.services.security.FolderPermissionUtils;
import zd.dms.services.security.RoleService;
import zd.dms.services.security.RoleUtils;
import zd.dms.services.security.UnauthorizedException;
import zd.dms.utils.WebUtils;
import zd.record.entities.RecFolder;
import zd.record.service.security.RecFolderPermissionUtils;

import java.util.Date;

@Slf4j
public class ControllerSupport extends ControllerResultUtils {

    private final LogService logService = SpringUtils.getBean(LogService.class);

    public boolean isServiceExpired() {
        String serviceExpireDateStr = TaskDictionary.getMainDefinition().sed();
        if (StringUtils.isNotBlank(serviceExpireDateStr)) {
            Date expiredDate = null;
            try {
                expiredDate = DateUtils.parseDate(serviceExpireDateStr, "yyyy-MM-dd");
            } catch (Throwable e) {
                return false;
            }

            if (expiredDate != null) {
                if (new Date().getTime() > expiredDate.getTime()) {
                    return true;
                }
            }
        }

        return false;
    }

    public boolean isSk() {
        return ModeDefinition.sk();
    }

    public boolean isHasSync() {
        return ZDIOUtils.FH();
    }

    public boolean isHasWDTStd() {
        return ModeDefinition.s();
    }

    public boolean isHasWDTPro() {
        return ModeDefinition.p2();
    }

    public boolean isHasWDTCad() {
        return ModeDefinition.w();
    }

    public boolean isHasWDTUltimate() {
        return ModeDefinition.u();
    }

    public boolean isHasWDTRecord() {
        return ModeDefinition.r();
    }

    public boolean isHasWDTDA() {
        return ModeDefinition.d();
    }

    public boolean isHasWDTTrial() {
        return ModeDefinition.t();
    }

    public boolean isHasDCC() {
        return ModeDefinition.dc();
    }

    public boolean isHasSDA() {
        return ModeDefinition.sd();
    }

    /**
     * 是否有高级office模块
     */
    public boolean isHasAdvOffice() {
        return ModeDefinition.mgo();
    }

    /**
     * 是否有单点登录模块
     */
    public boolean isHasSso() {
        if (StringUtils.isBlank(SystemConfigManager.getInstance().getProperty("apiEnabled"))) {
            return true;
        } else {
            return SystemConfigManager.getInstance().getBooleanProperty("apiEnabled");
        }
        // return true;
        // return ObjectsUtils.msso();
    }

    /**
     * 是否有短信模块
     */
    public boolean isHasSms() {
        return ModeDefinition.msms();
    }

    /**
     * 是否有高级文档操作模块
     */
    public boolean isHasAdvFile() {
        return ModeDefinition.maf();
    }

    /**
     * 是否有目录邮箱集成
     */
    public boolean isHasFolderMail() {
        return isHasWDTPro();
    }

    /**
     * 是否有NK签章
     */
    public boolean isHasNkSign() {
        return ModeDefinition.mns();
    }

    /**
     * 是否有外发
     */
    public boolean isHasOutLink() {
        return ModeDefinition.mol();
    }

    /**
     * 是否有移动模块
     */
    public boolean isHasMobileModule() {
        return TaskDictionary.getMainDefinition().mcl() > 0;
    }

    /**
     * 短信功能是否已经启用
     */
    public boolean isSmsEnabled() {
        return isHasSms() && SystemConfigManager.getInstance().getBooleanProperty("smsEnabled");
    }

    /**
     * 是否有SAP集成模块
     */
    public boolean isHasSAPModule() {
        return TaskDictionary.getMainDefinition().hm("sap");
    }

    /**
     * 是否有PDF水印模块
     */
    public boolean isHasPDFWaterMarkModule() {
        return TaskDictionary.getMainDefinition().hm("pdfwm");
    }

    /**
     * 是否有word水印模块
     */
    public boolean isHasWordWaterMarkModule() {
        return TaskDictionary.getMainDefinition().hm("docwm");
    }

    /**
     * 是否有Word打印
     */
    public boolean isHasDocPrintModule() {
        return TaskDictionary.getMainDefinition().hm("docprint");
    }

    /**
     * 是否有OCR引擎
     */
    public boolean isHasOcrEngineModule() {
        return TaskDictionary.getMainDefinition().hm("ocrEngine");
    }

    /**
     * 是否有虚拟库房
     */
    public boolean isHasWarehouseModule() {
        return TaskDictionary.getMainDefinition().hm("warehouse");
    }

    /**
     * 是否有可视化档案馆
     */
    public boolean isHasVizArc() {
        return TaskDictionary.getMainDefinition().hm("vizArc");
    }

    /**
     * 是否有扫描模块
     */
    public boolean isHasAdvScanModule() {
        return TaskDictionary.getMainDefinition().hm("advScan");
    }

    /**
     * 是否有业务场景化模块
     */
    public boolean isHasBusinessSceneModule() {
        return TaskDictionary.getMainDefinition().hm("bizScene");
    }

    /**
     * 是否有手写签名模块
     */
    public boolean isHasHandwrittenSignatureModule() {
        return TaskDictionary.getMainDefinition().hm("hwSig");
    }

    /**
     * 是否有影像模块
     */
    public boolean isHasVideoModule() {
        return TaskDictionary.getMainDefinition().hm("video");
    }

    /**
     * 是否有客户端绑定模块
     */
    public boolean isHasBindingClient() {
        return TaskDictionary.getMainDefinition().hm("bdc");
    }

    /**
     * 脱敏模块
     */
    public boolean isHasClassified() {
        return TaskDictionary.getMainDefinition().hm("clfd");
    }

    /**
     * 协同编辑
     */
    public boolean isHasOOS() {
        return TaskDictionary.getMainDefinition().hm("oos");
    }

    public String getIpAddress() {
        return WebUtils.getIPAddress();
    }

    public User getCurrentUser() {
        return UserContextHolder.getUser();
    }

    public String getCurrentUsername() {
        if (getCurrentUser() != null) {
            return getCurrentUser().getUsername();
        }

        return "";
    }

    public String getCurrentFullname() {
        if (getCurrentUser() != null) {
            return getCurrentUser().getFullname();
        }

        return "";
    }

    protected void logInfo(String msg) {
        doLog(LogMessage.LEVEL_INFO, msg);
    }

    protected void logError(String msg) {
        doLog(LogMessage.LEVEL_ERROR, msg);
    }

    private void doLog(int level, String msg) {
        String username = "";
        User user = UserContextHolder.getUser();
        if (user != null) {
            username = user.getUsername();
        } else {
            username = "system";
        }

        log.debug("doLog username {}", username);

        String ip = getIpAddress();
        logService.addMessage(level, username, ip, msg);
    }

    protected void checkRoles(String... roles) {
        if (!RoleUtils.isInRole(RoleService.IF_ANY_GRANTED, roles)) {
            throw new UnauthorizedException();
        }
    }

    protected void checkRolesWithRule(String rule, String... roles) {
        if (!RoleUtils.isInRole(rule, roles)) {
            throw new UnauthorizedException();
        }
    }

    protected void checkPermission(RecFolder recfolder, String perm) {
        if (recfolder == null || !RecFolderPermissionUtils.checkPermission(getCurrentUser(), recfolder, perm)) {
            throw new UnauthorizedException();
        }
    }

    protected void checkPermission(Folder folder, String perm) {
        if (folder == null || !FolderPermissionUtils.checkPermission(getCurrentUser(), folder, perm)) {
            throw new UnauthorizedException();
        }
    }
}
