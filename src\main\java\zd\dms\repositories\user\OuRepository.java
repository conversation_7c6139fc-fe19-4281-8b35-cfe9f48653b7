package zd.dms.repositories.user;

import zd.base.repositories.BaseRepository;
import zd.dms.entities.OU;

import java.util.List;

public interface OuRepository extends BaseRepository<OU, String> {

    OU getOUByUserId(String userId);

    OU getOUBySessionId(String sessionId);

    void deleteAll();

    void deleteByUserId(String userId);

    int getOUCount();

    void deleteByUserIdAndSessionId(String userId, String sessionId);

}
