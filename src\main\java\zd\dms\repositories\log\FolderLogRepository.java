package zd.dms.repositories.log;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.Folder;
import zd.dms.entities.FolderLog;
import zd.dms.entities.User;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Revision$
 */
public interface FolderLogRepository extends BaseRepository<FolderLog, Long> {

    List<FolderLog> getLogsByFolder(Folder folder, int type);

    List<FolderLog> getTop100LogsByFolder(Folder folder);

    void deleteAllLogsByFolder(Folder folder);

    void deleteLogsBeforeDays(int days);

    Page getLogs(int pageNumber, int pageSize, int type, String username,
                 Date startDate, Date endDate);

    Page getLogsByFolder(int pageNumber, int pageSize, int type, Folder folder);

    List<FolderLog> getLogsByDate(Date startDate, Date endDate, int type,
                                  String username);

    int getCount();

    int getUserDocLogCountByTypeAndDate(User user, int type, Date startDate,
                                        Date endDate);
}
