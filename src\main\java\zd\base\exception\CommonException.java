package zd.base.exception;

import lombok.Data;

/**
 * 参数验证错误
 *
 */
@Data
public class CommonException extends RuntimeException {

    private static final long serialVersionUID = -7753381263400662738L;

    /**
     * 错误code。参考：
     */
    private String errorCode;

    /**
     * 是否打印日志
     */
    private boolean writeLog = true;

    public CommonException(String errorCode, String msg) {
        super(msg);
        this.errorCode = errorCode;
    }

    public CommonException(String errorCode, String msg, boolean writeLog) {
        super(msg);
        this.errorCode = errorCode;
        this.writeLog = writeLog;
    }
}
