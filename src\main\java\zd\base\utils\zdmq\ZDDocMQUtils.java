package zd.base.utils.zdmq;

import zd.dms.utils.JSONUtils;
import zd.dms.utils.zdmq.ZDMQConfigUtils;
import zd.dms.utils.zdmq.ZDMQDBUtils;

import java.util.HashMap;
import java.util.Map;

public class ZDDocMQUtils {

    public static void addDocExtractMq(String docId, int version, String username) {
        Map<String, Object> msgMap = new HashMap<>();
        msgMap.put("docId", docId);
        msgMap.put("version", version);
        msgMap.put("username", username);

        ZDMQDBUtils.addMsg(ZDMQConfigUtils.TYPE_DOC_EXTRACT, JSONUtils.toJSONString(msgMap, ""));
    }
    public static void addDocExtractMq(long recId, long recFolderId, String docId, int version, String username) {
        Map<String, Object> msgMap = new HashMap<>();
        msgMap.put("recId", recId);
        msgMap.put("recFolderId", recFolderId);
        msgMap.put("docId", docId);
        msgMap.put("version", version);
        msgMap.put("username", username);

        ZDMQDBUtils.addMsg(ZDMQConfigUtils.TYPE_DOC_EXTRACT, JSONUtils.toJSONString(msgMap, ""));
    }
}
