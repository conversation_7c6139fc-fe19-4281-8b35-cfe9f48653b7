package zd.base.utils;

import java.util.HashMap;
import java.util.Map;

public class ZDMapTool {

    private final Map<String, Object> map = new HashMap<>();

    public static ZDMapTool getInstance() {
        return new ZDMapTool();
    }

    public Map<String, Object> getMap() {
        return map;
    }

    public ZDMapTool put(String key, Object value) {
        map.put(key, value);
        return this;
    }

    private Object get(String key) {
        return map.get(key);
    }
}
