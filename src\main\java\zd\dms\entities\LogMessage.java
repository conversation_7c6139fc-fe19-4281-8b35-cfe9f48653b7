package zd.dms.entities;

import java.util.Date;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.Length;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import org.hibernate.annotations.Type;

import zd.base.entities.AbstractSequenceEntity;
import zd.dms.utils.TextUtils;


/**
 * Log Message Domain Object
 *
 * <AUTHOR>
 * @version $Revision$, $Date$
 */
@Entity
@Table(name = "tdms_systemlog")
@BatchSize(size = 50)
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class LogMessage extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = -2912649451492406800L;

    /**
     * 日志级别，信息，值1
     */
    public static final int LEVEL_INFO = 1;

    /**
     * 日志级别，错误，值2
     */
    public static final int LEVEL_ERROR = 2;

    /**
     * 信息
     */
    @Column(nullable = false, length = Length.LOB_DEFAULT)
    private String msg;

    /**
     * 操作者用户名
     */
    private String operator;

    /**
     * 操作者的IP地址
     */
    @Column(nullable = false)
    private String ip;

    /**
     * 日志级别
     */
    @Index(name = "i_syslog_level")
    @Column(name = "level1", nullable = false)
    private int level;

    /**
     * 创建时间
     */
    @Index(name = "i_syslog_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 默认构造器
     */
    public LogMessage() {
        super();
        creationDate = new Date();
    }

    /**
     * @see Object#equals(Object)
     */
    @Override
    public boolean equals(Object o) {
        if (!(o instanceof LogMessage)) {
            return false;
        }

        final LogMessage l = (LogMessage) o;

        return new EqualsBuilder().appendSuper(super.equals(l)).append(msg,
                l.getMsg()).append(operator, l.getOperator()).append(ip,
                l.getIp()).append(level, l.getLevel()).isEquals();
    }

    /**
     * @see Object#hashCode()
     */
    @Override
    public int hashCode() {
        return new HashCodeBuilder().appendSuper(super.hashCode()).append(msg)
                .append(operator).append(ip).append(level).toHashCode();
    }

    public String getEscapeXmlMsg() {
        return TextUtils.escapeXml(msg);
    }

    public String getEscapeXmlIp() {
        return TextUtils.escapeXml(ip);
    }

    public String getDisplayLevel() {
        if (level == LEVEL_INFO) {
            return "信息";
        } else {
            return "错误";
        }
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
