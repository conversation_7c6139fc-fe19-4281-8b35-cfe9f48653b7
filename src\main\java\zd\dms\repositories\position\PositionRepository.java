package zd.dms.repositories.position;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.Position;

import java.util.List;

public interface PositionRepository extends BaseRepository<Position, String> {

    List<Position> getPositions(String username, String groupCode, String position);

    List<Position> getPositionsByPosition(String position);

    List<Position> getPositionsByUsername(String username);

    Page getPositionsPage(String userKeywords, String groupKeywords, String posKeywords, int pageNumber, int pageSize);

    List<Position> getPositionsByUserAndGroup(String username, String groupCode);

    List<Position> getPositionsByPositionAndGroupCode(String position, String groupCode);
}
