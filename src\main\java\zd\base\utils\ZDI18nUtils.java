package zd.base.utils;

import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.lang.Nullable;
import zd.base.utils.system.SpringUtils;

import java.util.Locale;

public class ZDI18nUtils {

    private static final MessageSource messageSource = SpringUtils.getBean(MessageSource.class);

    // 请求头添加 'Accept-Language': 'zh-cn' 或 'en-us'
    public static String getMsg(String code) {
        return getMsg(code, null, LocaleContextHolder.getLocale());
    }

    public static String getMsg(String code, @Nullable Object[] args, Locale locale) {
        try {
            return messageSource.getMessage(code, args, locale);
        } catch (Exception ignored) {
        }

        return code;
    }
}
