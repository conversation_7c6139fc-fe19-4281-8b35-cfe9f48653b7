package zd.dms.repositories.document.impl;

import jakarta.persistence.EntityManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.*;
import zd.dms.repositories.document.DocumentControlledVersionRepository;
import zd.dms.repositories.document.DocumentNoteRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.Date;
import java.util.List;

public class DocumentNoteRepositoryDaoImpl extends BaseRepositoryDaoImpl<DocumentNote, Long> implements DocumentNoteRepository {

    public DocumentNoteRepositoryDaoImpl(Class<DocumentNote> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<DocumentNote> getDocumentNoteByDocument(Document doc) {
        Specification<DocumentNote> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentNote> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("docId", doc.getId());
            specTools.desc("id");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void deleteAllNotesByDocument(Document doc) {
        String hql = "delete from DocumentNote where docId = ?1";
        executeUpdate(hql, doc.getId());
    }

    @Override
    public void deleteNoteById(long id) {
        String hql = "delete from DocumentNote where id = ?1";
        executeUpdate(hql, id);
    }

    @Override
    public int getUserNoteCountByDate(User user, Date startDate, Date endDate) {
        Specification<DocumentNote> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentNote> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("creator", user.getUsername());

            if (startDate != null) {
                specTools.ge("creationDate", startDate);
            }

            if (endDate != null) {
                specTools.le("creationDate", endDate);
            }

            return specTools.getRestriction();
        };

        return (int) count(spec);
    }
}
