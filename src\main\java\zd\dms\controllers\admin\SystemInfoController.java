package zd.dms.controllers.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.ServletContext;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.math3.util.Precision;
import org.apache.lucene.analysus.ja.dict.ModeDefinition;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "SystemInfoController", description = "系统信息Controller")
@RequestMapping("/admin/systemInfo")
public class SystemInfoController extends ControllerSupport {

    private final ServletContext servletContext;

    @Operation(summary = "获取系统信息")
    @GetMapping("/getSystemInfos")
    @ZDLog("获取系统信息")
    public JSONResultUtils<Object> getSystemInfos() {
        Map<String, Object> results = new HashMap<>();
        ModeDefinition.csi(results);

        results.put("操作系统", SystemUtils.OS_NAME + " " + SystemUtils.OS_VERSION + " " + SystemUtils.OS_ARCH);
        results.put("Java", SystemUtils.JAVA_VM_NAME + " " + SystemUtils.JAVA_VM_VERSION + " " + SystemUtils.JAVA_VM_VENDOR);
        if (servletContext != null) {
            results.put("应用服务器", servletContext.getServerInfo() + " (Servlet API " + servletContext.getMajorVersion() + "." + servletContext.getMinorVersion() + ")");
        }

        // 获取物理内存
        double jvmTotalMemory = Runtime.getRuntime().totalMemory() / (1024 * 1024);
        double jvmMaxMemory = Runtime.getRuntime().maxMemory() / (1024 * 1024);
        double jvmFreeMemory = Runtime.getRuntime().freeMemory() / (1024 * 1024);
        double usedJvmMem = jvmTotalMemory - jvmFreeMemory;
        double jvmMemUsedRatio1 = Precision.round((usedJvmMem / jvmTotalMemory) * 100, 2, BigDecimal.ROUND_HALF_UP);
        double jvmMemUsedRatio2 = Precision.round((jvmTotalMemory / jvmMaxMemory) * 100, 2, BigDecimal.ROUND_HALF_UP);
        double jvmMemUsedRatio3 = Precision.round((usedJvmMem / jvmMaxMemory) * 100, 2, BigDecimal.ROUND_HALF_UP);
        results.put("jvmTotalMemory", jvmTotalMemory);
        results.put("jvmMaxMemory", jvmMaxMemory);
        results.put("jvmFreeMemory", jvmFreeMemory);
        results.put("usedJvmMem", usedJvmMem);
        results.put("jvmMemUsedRatio1", jvmMemUsedRatio1);
        results.put("jvmMemUsedRatio2", jvmMemUsedRatio2);
        results.put("jvmMemUsedRatio3", jvmMemUsedRatio3);

        return successData(results);
    }
}
