package org.apache.lucene.analysus.ja.dict;

import org.apache.commons.codec.binary.Base64;
import zd.base.utils.ZDUtils;

import javax.crypto.Cipher;
import java.io.*;
import java.security.*;
import java.security.spec.EncodedKeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * This is a utility class which provides
 * <p>
 * convenient method for security. This
 * <p>
 * class provides the way where you can
 * <p>
 * encrypt and decrypt the String having
 * <p>
 * more than 117 bytes for RSA algorithm
 * <p>
 * which is an asymmetric one.
 *
 * <AUTHOR>
 */
public class SecDictionary {

    /**
     * String variable which denotes the algorithm
     */
    private static final String ALGORITHM = "RSA";

    public static String PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC5iIG51OesJSPSetl4YaFGwPM7lrodRdH2k7GQ"
            + "BoFkvRmmwZtBOKX0oScj90qCA5sP0NFGXxep4yiM25Cl6A2jAvGZiCzoP4KiEMOC05x6FoOU5Rdc"
            + "QxlLRYTekpMhviTamJQbSwgj7X2W65XYt65Sub0dRd/hDge6okHjQmYp4QIDAQAB";

    /**
     * varibale for the keysize
     */
    private static final int KEYSIZE = 1024;

    public static final SecDictionary INSTANCE = new SecDictionary();

    /**
     * Default constructor
     */
    public SecDictionary() {
        super();
        Security.addProvider(Security.getProvider("SunJCE"));
    }

    /**
     * This method is used to obtain the String representation of the PublicKey.
     *
     * @param publicKey of type {@link PublicKey}
     * @return PublicKey as a String
     */
    public String getPublicKeyString(PublicKey publicKey) {
        return Base64.encodeBase64String(publicKey.getEncoded());
    }

    /**
     * This method is used to obtain the String representation of the
     * PrivateKey.
     *
     * @param privateKey of type {@link PrivateKey}
     * @return PrivateKey as a String
     */

    public String getPrivateKeyString(PrivateKey privateKey) {
        return Base64.encodeBase64String(privateKey.getEncoded());
    }

    /**
     * This method is used to obtain the {@link PrivateKey} object from the
     * String representation.
     *
     * @param key of type String
     * @return {@link PrivateKey}
     * @throws Exception
     */

    public PrivateKey getPrivateKeyFromString(String key) throws Exception {
        PrivateKey privateKey = null;
        try {
            KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);

            EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(key));
            privateKey = keyFactory.generatePrivate(privateKeySpec);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return privateKey;
    }

    /**
     * This method is used to obtain the {@link PublicKey} from the String
     * representation of the Public Key.
     *
     * @param key of type String
     * @return {@link PublicKey}
     * @throws Exception
     */

    public PublicKey getPublicKeyFromString(String key) throws Exception {
        PublicKey publicKey = null;

        try {
            KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
            EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(Base64.decodeBase64(key));
            publicKey = keyFactory.generatePublic(publicKeySpec);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return publicKey;
    }

    /**
     * This method is used to obtain the
     * <p>
     * encrypted contents from the original
     * <p>
     * contents by passing the {@link PublicKey}.
     * <p>
     * This method is useful when the byte is more
     * <p>
     * than 117.
     *
     * @param text      of type String
     * @param keyString of type {@link PublicKey}
     * @return encrypted value as a String
     * @throws Exception
     */
    public String getEncryptedValue(String text, String keyString) throws Exception {

        Key key = getPrivateKeyFromString(keyString);
        String encryptedText;

        try {
            byte[] textBytes = text.getBytes("UTF8");
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, key);
            int textBytesChunkLen = 100;
            int encryptedChunkNum = (textBytes.length - 1) / textBytesChunkLen + 1;
            // RSA returns 128 bytes as output for 100 text bytes
            int encryptedBytesChunkLen = 128;
            int encryptedBytesLen = encryptedChunkNum * encryptedBytesChunkLen;
            ZDUtils.info("Encrypted bytes length——-" + encryptedBytesChunkLen);

            // Define the Output array.
            byte[] encryptedBytes = new byte[encryptedBytesLen];
            int textBytesChunkIndex = 0;
            int encryptedBytesChunkIndex = 0;

            for (int i = 0; i < encryptedChunkNum; i++) {
                if (i < encryptedChunkNum - 1) {
                    encryptedBytesChunkIndex = encryptedBytesChunkIndex +
                            cipher.doFinal(textBytes, textBytesChunkIndex, textBytesChunkLen, encryptedBytes,
                                    encryptedBytesChunkIndex);
                    textBytesChunkIndex = textBytesChunkIndex + textBytesChunkLen;
                } else {
                    cipher.doFinal(textBytes, textBytesChunkIndex, textBytes.length - textBytesChunkIndex,
                            encryptedBytes, encryptedBytesChunkIndex);
                }
            }


            encryptedText = Base64.encodeBase64String(encryptedBytes);
        } catch (Exception e) {
            throw e;
        }
        return encryptedText;
    }

    /**
     * This method is used to decrypt the contents. This method is useful when
     * the size of the bytes is more than 117.
     *
     * @param text      of type String indicating the encrypted contents.
     * @param keyString of type {@link PrivateKey}
     * @return decrypted value as a String
     */
    public String getDecryptedValue(String text, String keyString) throws Exception {

        Key key = getPublicKeyFromString(keyString);
        String result = null;

        try {
            byte[] encryptedBytes = Base64.decodeBase64(text);
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.DECRYPT_MODE, key);
            int encryptedByteChunkLen = 128;
            int encryptedChunkNum = encryptedBytes.length / encryptedByteChunkLen;
            int decryptedByteLen = encryptedChunkNum * encryptedByteChunkLen;
            byte[] decryptedBytes = new byte[decryptedByteLen];
            int decryptedIndex = 0;
            int encryptedIndex = 0;

            for (int i = 0; i < encryptedChunkNum; i++) {

                if (i < encryptedChunkNum - 1) {
                    decryptedIndex = decryptedIndex +
                            cipher.doFinal(encryptedBytes, encryptedIndex, encryptedByteChunkLen, decryptedBytes,
                                    decryptedIndex);
                    encryptedIndex = encryptedIndex + encryptedByteChunkLen;
                } else {
                    decryptedIndex = decryptedIndex +
                            cipher.doFinal(encryptedBytes, encryptedIndex, encryptedBytes.length - encryptedIndex,
                                    decryptedBytes, decryptedIndex);
                }

            }

            result = new String(decryptedBytes, "UTF-8").trim();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;

    }

    public static byte[] decryptBASE64(String key) throws Exception {
        return Base64.decodeBase64(key);
    }

    /** */
    /**
     * 对 byte[] 类型数据进行加密
     *
     * @param rawData 原始数据
     * @return cipherData 加密后的数据
     * @throws Exception
     */
    public byte[] encryptData(byte[] rawData, String keyString) throws Exception {

        Key key = getPrivateKeyFromString(keyString);

        if (key == null) {
            System.err.println("加密失败，原因：加密密钥不存在！");
            return null;
        }
        try {
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, key);

            int blockSize = cipher.getBlockSize(); // 获取加密数据块的大小
            int outputSize = cipher.getOutputSize(rawData.length); // 获取加密后数据块的大小
            int leavedSize = rawData.length % blockSize;
            int blocksNum = (leavedSize == 0) ? (rawData.length / blockSize) : (rawData.length / blockSize + 1); // 获取加密块数
            byte[] cipherData = new byte[outputSize * blocksNum];
            cipherData = cipher.doFinal(rawData);
            return cipherData;
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception(e.getMessage());
        }
    }

    /** */
    /**
     * 解密 cipherData 为明文
     *
     * @param cipherData 原始密文
     * @return 解密后的明文
     * @throws Exception
     */
    public byte[] decryptData(byte[] cipherData, String keyString) throws Exception {
        Key key = getPublicKeyFromString(keyString);

        ZDUtils.info("正在解密数据...");
        if (key == null) {
            System.err.println("解密失败，原因：解密密钥不存在！");
            return null;
        }
        try {
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(cipher.DECRYPT_MODE, key);
            int blockSize = cipher.getBlockSize();
            ByteArrayOutputStream bout = new ByteArrayOutputStream(64);
            int j = 0;

            // 分别对各块数据进行解密
            while ((cipherData.length - j * blockSize) > 0) {
                bout.write(cipher.doFinal(cipherData, j * blockSize, blockSize));
                j++;
            }
            return bout.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception(e.getMessage());
        }
    }

    /**
     * 文件file进行加密并保存目标文件destFile中
     *
     * @param srcFileName  要加密的文件 如c:/test/srcFile.txt
     * @param destFileName 加密后存放的文件名 如c:/加密后文件.txt
     */

    public void encryptFile(String srcFileName, String destFileName, String keyString) throws Exception {

        Key key = getPrivateKeyFromString(keyString);

        OutputStream outputWriter = null;
        InputStream inputReader = null;

        try {

            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            byte[] buf = new byte[100];
            int bufl;

            cipher.init(Cipher.ENCRYPT_MODE, key);

            outputWriter = new FileOutputStream(destFileName);

            inputReader = new FileInputStream(srcFileName);

            while ((bufl = inputReader.read(buf)) != -1) {

                byte[] encText = null;

                byte[] newArr = null;

                if (buf.length == bufl) {

                    newArr = buf;

                } else {

                    newArr = new byte[bufl];

                    for (int i = 0; i < bufl; i++) {
                        newArr = buf;
                    }

                }

                encText = cipher.doFinal(newArr);

                outputWriter.write(encText);

            }

            outputWriter.flush();

        } catch (Exception e) {

            throw e;

        } finally {

            try {

                if (outputWriter != null) {

                    outputWriter.close();

                }

                if (inputReader != null) {

                    inputReader.close();

                }

            } catch (Exception e) {

            }

        }

    }

    /**
     * 文件file进行加密并保存目标文件destFile中
     *
     * @param srcFileName  已加密的文件 如c:/加密后文件.txt
     * @param destFileName 解密后存放的文件名 如c:/ test/解密后文件.txt
     */

    public void decryptFile(String srcFileName, String destFileName, String keyString) throws Exception {

        Key key = getPublicKeyFromString(keyString);

        OutputStream outputWriter = null;

        InputStream inputReader = null;

        try {

            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");

            byte[] buf = new byte[128];

            int bufl;

            cipher.init(Cipher.DECRYPT_MODE, key);

            outputWriter = new FileOutputStream(destFileName);

            inputReader = new FileInputStream(srcFileName);

            while ((bufl = inputReader.read(buf)) != -1) {

                byte[] encText = null;

                byte[] newArr = null;

                if (buf.length == bufl) {
                    newArr = buf;
                } else {
                    newArr = new byte[bufl];
                    for (int i = 0; i < bufl; i++) {
                        newArr = buf;
                    }
                }

                encText = cipher.doFinal(newArr);

                outputWriter.write(encText);

            }

            outputWriter.flush();

        } catch (Exception e) {

            throw e;

        } finally {

            try {

                if (outputWriter != null) {

                    outputWriter.close();

                }

                if (inputReader != null) {

                    inputReader.close();

                }

            } catch (Exception e) {

            }

        }

    }

}
