import {RouteRecordRaw} from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    redirect: 'portal',
    children: [
      {
        name: 'instantMessage',
        path: 'instantMessage',
        component: () => import('pages/im/InstantMessagePage.vue'),
        meta: {
          label: '提示消息',
        }
      },
      {
        name: 'lendCenter',
        path: 'lendCenter',
        component: () => import('pages/lendCenter/LendCenterPage.vue'),
        meta: {
          label: '借阅中心',
        }
      },
      {
        name: 'approval',
        path: 'approval',
        component: () => import('pages/ApprovalPage.vue'),
        meta: {
          label: '审批中心',
        },
      },
      {
        name: 'usertask',
        path: 'usertask',
        component: () => import('pages/usertask/UsertaskIndexPage.vue'),
        meta: {
          label: '审批中心',
        },
      },
      {
        name: 'startWorkflow',
        path: 'workflow/startWorkflow/:type',
        component: () => import('pages/workflow/StartWorkflow.vue'),
        meta: {
          label: '发起流程',
        },
      },
      {
        name: 'tempStartWorkflow',
        path: 'workflow/tempStartWorkflow',
        component: () => import('pages/workflow/StartWorkflow.vue'),
        meta: {
          label: '发起流程',
        },
      },
      {
        name: 'handleTask',
        path: 'workflow/HandleTask/:taskId',
        component: () => import('pages/workflow/HandleTask.vue'),
        meta: {
          label: '办理',
        },
      },
      {
        name: 'viewWorkflow',
        path: 'workflow/viewWorkflow/:piId',
        component: () => import('pages/workflow/ViewWorkflow.vue'),
        meta: {
          label: '查看流程',
        },
      },
      {
        name: 'showWorkflow',
        path: 'workflow/showWorkflow/:piId',
        component: () => import('pages/workflow/ShowWorkflowPage.vue'),
        meta: {
          label: '办理',
        },
      },
      {
        name: 'reportTemplates',
        path: 'reportTemplates',
        component: () => import('pages/reports/template/ReportTemplatesPage.vue'),
        meta: {
          label: '审批中心',
        },
      },
      {
        name: 'portal',
        path: 'portal',
        component: () => import('pages/PortalPage.vue'),
        meta: {
          label: '档案门户',
        },
      },
      {
        name: 'search',
        path: 'search',
        component: () => import('pages/record/RecSearchPage.vue'),
        meta: {
          label: '档案搜索',
        },
      },
      {
        name: 'record',
        path: 'record/:type',
        component: () => import('pages/record/RecordListPage.vue'),
        meta: {
          label: '档案管理',
        },
      },
      {
        name: 'document',
        path: 'document/:type',
        component: () => import('pages/document/DocumentListPage.vue'),
        meta: {
          label: '文档管理',
        },
      },
      {
        name: 'viewRecord',
        path: 'record/viewRecord/:recFolderId/:recId',
        component: () => import('pages/record/ViewRecordPage.vue'),
        meta: {
          label: '数据阅读',
        },
      },
      {
        name: 'viewRecordSimple',
        path: 'record/viewRecordSimple/:recFolderId/:recId',
        component: () => import('pages/record/ViewRecordSimplePage.vue'),
        meta: {
          label: '数据阅读',
        },
      },
      {
        name: 'newRecord',
        path: 'record/newRecord/:recFolderId',
        component: () => import('pages/record/NewRecordPage.vue'),
        meta: {
          label: '数据创建',
        },
      },
      {
        name: 'newRecordWithParent',
        path: 'record/newRecord/:recFolderId/:parentRecId/:parentRecFolderId',
        component: () => import('pages/record/NewRecordPage.vue'),
        meta: {
          label: '数据创建',
        },
      },
      {
        name: 'editRecord',
        path: 'record/editRecord/:recFolderId/:recId',
        component: () => import('pages/record/EditRecordPage.vue'),
        meta: {
          label: '数据编辑',
        },
      },
      {
        name: 'checkEEP',
        path: 'record/checkEEP/:recFolderId',
        component: () => import('pages/record/RecordCheckEEP.vue'),
        meta: {
          label: '校验EEP',
        },
      },
      {
        name: 'admin',
        path: 'admin',
        component: () => import('pages/AdminIndexPage.vue'),
        redirect: '/admin/system-info',
        meta: {
          label: '系统设置',
        },
        children: [
          {
            name: 'system-info',
            path: 'system-info',
            component: () => import('pages/SystemInfoPage.vue'),
            meta: {
              label: '系统信息',
            },
          },
          {
            name: 'settings',
            path: 'settings',
            component: () => import('pages/admin/settings/SettingsPage.vue'),
            meta: {
              label: '系统设置',
            },
          },
          {
            name: 'organization',
            path: 'organization',
            component: () => import('pages/admin/user/UserGroupPage.vue'),
            meta: {
              label: '组织架构',
            },
          },
          {
            name: 'position',
            path: 'position',
            component: () => import('pages/admin/user/PositionManagerPage.vue'),
            meta: {
              label: '岗位管理',
            },
          },
          {
            name: 'dataStructManager',
            path: 'dataStructManager',
            component: () =>
              import('pages/admin/record/DataStructManagerPage.vue'),
            meta: {
              label: '档案表管理',
            },
          },
          {
            name: 'printTplManager',
            path: 'printTplManager',
            component: () =>
              import('pages/admin/record/printTalManagerPage.vue'),
            meta: {
              label: '打印模板',
            },
          },
          {
            name: 'ZDMQManager',
            path: 'ZDMQManager',
            component: () =>
              import('pages/admin/mq/ZDMQManagerPage.vue'),
            meta: {
              label: '消息队列',
            },
          },
          {
            name: 'redisCache',
            path: 'redisCache',
            component: () =>
              import('pages/admin/system/RedisCacheManagerPage.vue'),
            meta: {
              label: '缓存管理',
            },
          },
          {
            name: 'esManager',
            path: 'esManager',
            component: () =>
              import('pages/admin/system/ESManagerPage.vue'),
            meta: {
              label: '索引管理',
            },
          },
          {
            name: 'workflowManager',
            path: 'workflowManager',
            component: () => import('pages/admin/workflow/WorkflowManagerPage.vue'),
            meta: {
              label: '流程管理',
            },
          },
          {
            name: 'oauth2Client',
            path: 'oauth2Client',
            component: () => import('pages/admin/oauth2/Oauth2ClientManagerPage.vue'),
            meta: {
              label: 'OAuth2管理',
            },
          },
          {
            name: 'folderArea',
            path: 'folderArea',
            component: () => import('pages/admin/FolderAreaPage.vue'),
            meta: {
              label: '目录分区',
            },
          },
          {
            name: 'mailSettings',
            path: 'mailSettings',
            component: () => import('pages/admin/mail/MailSettingsPage.vue'),
            meta: {
              label: '邮件设置',
            },
          },
          {
            name: 'systemMonitor',
            path: 'systemMonitor',
            component: () => import('pages/admin/system/SystemMonitorManagerPage.vue'),
            meta: {
              label: '系统监控',
            },
          },
        ],
      },
    ],
  },
  {
    name: 'form-design',
    path: '/form-design',
    component: () => import('pages/form/FormDesigner.vue'),
  },
  {
    name: 'workflow-design',
    path: '/workflow-design',
    component: () => import('pages/workflow/WorkFlowDesigner.vue'),
  },
  {
    name: 'printTpl-design',
    path: '/printTpl-design/:id?',
    component: () => import('pages/printTpl/PrintTplDesigner.vue'),
  },
  {
    name: 'printTpl-preview',
    path: '/printTpl-preview/:id?',
    component: () => import('pages/printTpl/PrintTplPreview.vue'),
  },
  {
    name: 'process-design',
    path: '/process-design',
    component: () => import('pages/workflow/WorkFlowDesigner.vue'),
  },
  {
    name: 'login',
    path: '/login',
    component: () => import('pages/LoginPage.vue'),
  },
  {
    name: 'loginToken',
    path: '/loginToken',
    component: () => import('pages/LoginTokenPage.vue'),
  },
  // {
  //   name: 'logout',
  //   path: '/logout',
  //   component: () => import('pages/LogoutPage.vue'),
  // },
  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

export default routes;
