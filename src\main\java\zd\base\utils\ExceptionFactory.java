package zd.base.utils;

import zd.base.exception.IllegalArgumentException;

public class ExceptionFactory {

    public static IllegalArgumentException illegalArgument(String message) {
        return new IllegalArgumentException(message);
    }

    public static IllegalArgumentException illegalArgument(String code, String message) {
        return new IllegalArgumentException(code, message);
    }
}
