package zd.dms.repositories.oauth2;

import org.springframework.data.domain.Page;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.FolderMailConfig;
import zd.dms.entities.oauth2.OAuth2Client;

import java.util.Optional;

/**
 * OAuth2客户端仓库
 */
@Repository
public interface OAuth2ClientRepository extends BaseRepository<OAuth2Client, String> {

    /**
     * 根据客户端ID查找客户端
     *
     * @param clientId 客户端ID
     * @return 客户端
     */
    OAuth2Client findByClientId(String clientId);

    Page getOAuth2ClientPage(int pageNumber, int pageSize);
}
