package zd.dms.repositories.user;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.Group;
import zd.dms.entities.User;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface GroupRepository extends BaseRepository<Group, Long> {

    List<Group> getGroupsByUserId(String userId);

    List<Map<String, Object>> getChildCount(List<String> parentCodes);

    List<Group> getChildren(String parentCode);

    int getGC();

    List<Group> findGroups(String keywords);

    List<Group> getGroupForLdapDelete();

    Group getGroupByLdapId(String ldapId);

    /**
     * 根据指定名称获取组
     *
     * @param name 名称
     * @return 名称对应的组
     */
    public Group getGroup(String name);

    List<Group> getGroups();

    List<Group> getTopGroups();

    /**
     * 获取有角色的用户列表
     *
     * @return 有角色的用户列表
     */
    List<Group> getGroupsInRole();

    /**
     * 根据指定名称进行模糊查询获取组
     *
     * @param pageNumber    当前页数
     * @param pageSize      每页显示条数
     * @param groupName     组名,当为空或null时不进行过滤
     * @param orderProperty 排序字段
     * @param asc           排序方式
     * @return
     */
    Page findGroupsByGroupName(int pageNumber, int pageSize, String groupName, String orderProperty, boolean asc);

    /**
     * 根据名称模糊查询组,按照部门编码排序，包含所有部门
     *
     * @param keywords
     * @param hasAllGroup
     * @return
     */
    List<Group> findGroups(String keywords, boolean hasAllGroup);

    /**
     * 按照排序获取部门
     *
     * @param orderProperty
     * @param asc
     * @return
     */
    List<Group> getGroupsHasOrder(String orderProperty, boolean asc);

    /**
     * 根据编码获得部门
     *
     * @param code 部门编码
     * @return
     */
    Group getGroupByCode(String code);

    /**
     * 根据部门预留字段获取部门
     *
     * @param groupIdCode
     * @return
     */
    Group getGroupByGroupIdCode(String groupIdCode);

    /**
     * 根据钉钉同步回来的部门ID查询部门
     *
     * @param syncDingGroupId
     * @return
     */
    Group getGroupBySyncDingGroupId(String syncDingGroupId);

    /**
     * 根据企业微信同步回来的部门ID查询部门
     *
     * @param qywxGroupId
     * @return
     */
    Group getGroupBySyncQywxGroupId(String qywxGroupId);

    /**
     * 获取所有用户的userIdCode
     *
     * @return
     */
    Set<String> getSyncDingGroupIds();

    List<Group> findGroups(String keywords, String separator);
}
