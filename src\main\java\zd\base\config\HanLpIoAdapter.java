package zd.base.config;

import com.hankcs.hanlp.corpus.io.IIOAdapter;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.*;

/**
 * <AUTHOR>
 * @since 2024-10-14
 */
@Component
public class HanLpIoAdapter implements IIOAdapter {
    @Override
    public InputStream open(String path) throws IOException {
        ClassPathResource resource = new ClassPathResource("static/hlp/" + path);
        InputStream is = new FileInputStream(resource.getFile());
        return is;
    }

    @Override
    public OutputStream create(String path) throws IOException {
        ClassPathResource resource = new ClassPathResource("static/hlp/" + path);
        OutputStream os = new FileOutputStream(resource.getFile());
        return os;
    }
}
