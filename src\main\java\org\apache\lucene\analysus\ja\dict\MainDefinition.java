package org.apache.lucene.analysus.ja.dict;

//import RY3jni.CRY3;
//import RY3jni.IRY3;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Node;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.utils.ZDUtils;
import zd.base.utils.system.PropsUtils;
import zd.dms.services.config.SystemConfigManager;

import java.io.File;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.*;

public class MainDefinition {

    private static final Logger log = LoggerFactory.getLogger(MainDefinition.class);

    /**
     * 当前授权用户数限制
     */
    private static int coc = 5;

    /**
     * 并发用户数
     */
    private static int bfc = 0;

    /**
     * 同步用户数限制
     */
    private static int suc = 0;

    /**
     * 数据表限制
     */
    private static int rtc = 0;

    /**
     * 移动用户数
     */
    private static int muc = 0;

    /**
     * 是否是集群版
     */
    private boolean cls = false;

    /**
     * 是否找到加密锁
     */
    private static boolean fk = false;

    /**
     * 是否找到体验版
     */
    private static boolean sk = false;

    /**
     * 是否找到软加密
     */
    private static boolean sk1 = false;

    /**
     * 是否已经初始化
     */
    private static boolean itd = false;

    /**
     * 加密锁厂商号
     */
    private static final String vi = "4DF483C4";

    /**
     * 硬件ID，当前使用CPU ID
     */
    private static String hid = null;

    private static String kiStr = null;

//    private static IRY3 ry3 = null;

    private static MyRY3 ry3 = null;

    /**
     * XML内容
     */
    private static String XC = "";

    private static final Map<String, String> mch = new HashMap<String, String>();

    public class MyRY3 {

        public void RY3_Find(char[] charArray, int[] count) {
        }

        public void RY3_Open(int i) {
        }

        public void RY3_Close(boolean b) {
        }

        public void RY3_GetHardID(char[] hardIDArray) {
        }

        public void RY3_PublicEncrypt(int i, byte[] data, int length) {
        }

        public void RY3_PrivateDecrypt(int i, byte[] data, int length) {
        }

        public void RY3_Read(int i, byte[] result, int length) {
        }

        public void RY3_Write(int i, byte[] blankArray, int length) {
        }
    }

    public MainDefinition() {
        try {
//            ry3 = new CRY3();
        } catch (Throwable t) {
            log.warn("MainDefinition Init Warning", t);
        }
    }

    /**
     * 数据源URL
     */
    public String ddu() {
        String ddu = gvfx("/rtecm/ddu");
        if (StringUtils.isNotBlank(ddu)) {
            cls = true;
        }
        return ddu;
    }

    /**
     * 数据源用户
     */
    public String ddur() {
        return gvfx("/rtecm/ddur");
    }

    /**
     * 数据源URL
     */
    public String edu() {
        String results = gvfx("/rtecm/edu");
        if (StringUtils.isBlank(results)) {
            return ddu();
        }

        return results;
    }

    /**
     * ECM数据源用户
     */
    public String edur() {
        String results = gvfx("/rtecm/edur");
        if (StringUtils.isBlank(results)) {
            return ddur();
        }

        return results;
    }

    /**
     * 服务过期时间
     */
    public String sed() {
        return gvfx("/rtecm/sed");
    }

    /**
     * 过期时间
     */
    public String ed() {
        return gvfx("/rtecm/ed");
    }

    public int getC() {
        return coc;
    }

    public int getBFC() {
        return bfc;
    }

    public void limitCZero() {
        coc = 0;
    }

    public void limitCFive() {
        coc = 5;
    }

    public int getSUC() {
        return suc;
    }

    public int getRTC() {
        return rtc;
    }

	/*
	public void setSUCC(int c) {
		suc = c;
	}
	*/

    public int getMUC() {
        return muc;
    }

    public boolean isCls() {
        return cls;
    }

    /**
     * 获取试用时间，单位小时
     *
     * @return 试用时间
     */
    public int tt() {
        return NumberUtils.toInt(gvfx("/rtecm/tt"), 0);
    }

    public boolean skiphw() {
        return "zdignoreipcheck".equals(gvfx("/rtecm/skips")) && "zdignorehardcheck".equals(gvfx("/rtecm/hid"));
    }

    public String skorg() {
        return gvfx("/rtecm/skorg");
    }

    /**
     * 清除软加密信息
     */
    public void csk() {
        resetXC();
        fk = false;
        sk = false;
        coc = 5;
        suc = 0;
        rtc = 0;
        muc = 0;
        bfc = 0;
    }

    /**
     * 获取本机IP
     *
     * @return
     */
    private static Set<String> i2() {
        Set<String> set = new HashSet<String>();
        try {
            for (Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces(); en.hasMoreElements(); ) {
                NetworkInterface intf = en.nextElement();
                for (Enumeration<InetAddress> enumIpAddr = intf.getInetAddresses(); enumIpAddr.hasMoreElements(); ) {
                    InetAddress inetAddress = enumIpAddr.nextElement();

                    if (!"127.0.0.1".equals(inetAddress.getHostAddress()) &&
                            !"localhost".equals(inetAddress.getHostAddress()) &&
                            !StringUtils.contains(inetAddress.getHostAddress(), ":")) {
                        set.add(inetAddress.getHostAddress().toString());
                    }
                }
            }
        } catch (Throwable t) {
        }
        return set;
    }

    /**
     * 比对本机IP和软加密中的IP
     */
    private boolean i3() {
        String ips = gvfx("/rtecm/skips");

        // 忽略IP监测
        if ("zdignoreipcheck".equals(ips)) {
            ZDUtils.info("ZDECM - SKIPS: true");
            return true;
        }

        if (StringUtils.isNotBlank(ips)) {
            // System.out.println("i3 ips: " + ips);
            String[] ipsFromXML = StringUtils.split(ips, ",");
            Set<String> svrIps = i2();
            int matchCount = 0;
            for (String ip : ipsFromXML) {
                // System.out.println("i3 keyip: " + ip);
                if (svrIps.contains(StringUtils.trim(ip))) {
                    matchCount++;
                }
            }

            // System.out.println("i3 matchCount: " + matchCount +
// ", ipsFromXml: " + ipsFromXML.length);
            if (matchCount == ipsFromXML.length) {
                return true;
            }
        }

        XC = "";
        return false;
    }

    /**
     * 从软加密中载入XML
     */
    public int lskxc() {
        // 软加密
        String licFile = PropsUtils.getProps("lic");
        // System.out.println("lskxc licprop: " + licFile);
        if (StringUtils.isNotBlank(licFile)) {
            File lic = new File(licFile);
            if (lic != null && lic.exists() && lic.canRead()) {
                try {
                    String licContent = FileUtils.readFileToString(lic, "UTF-8");
                    // System.out.println("lskxc lic: " + licContent);
                    if (StringUtils.isNotBlank(licContent)) {
                        XC = SecDictionary.INSTANCE.getDecryptedValue(StringUtils.trim(licContent),
                                SecDictionary.PUBLIC_KEY);

                        // System.out.println("lskxc lic xml: " + XC);

                        fk = true;
                        itd = true;

                        // 检查IP限制
                        if (i3()) {
                            // 设置变量
                            sk1 = true;

                            // 设置软加密锁号
                            kiStr = gvfx("/rtecm/skhid");

                            ZDUtils.info("ZDECM - lskxc i3");
                            return 1;
                        } else {
                            ZDUtils.info("ZDECM - lskxc i3.");
                        }
                    } else {
                        ZDUtils.info("ZDECM - lskxc i");
                    }
                } catch (Throwable t) {
                    log.debug("ex", t);
                }
            } else {
                ZDUtils.info("ZDECM - lskxc i.");
            }
        }

        // 体验版
        String encString = SystemConfigManager.getInstance().getProperty("sk");
        if (StringUtils.isNotBlank(encString)) {
            try {
                String trimed = encString.trim();
                XC = SecDictionary.INSTANCE.getDecryptedValue(trimed, SecDictionary.PUBLIC_KEY);

                // 设置变量
                itd = true;
                fk = true;
                sk = true;
                return 2;
            } catch (Throwable t) {
            }
        }

        fk = false;
        itd = true;
        return -1;
    }

    public boolean sk() {
        return sk;
    }

    public boolean sk1() {
        return sk1;
    }

    /**
     * 搜索加密锁
     *
     * @return
     */
    public boolean fk() {
        if (itd) {
            return fk;
        }

        int[] count = new int[4];

        if (ry3 != null) {
            try {
                // System.out.println("start findkey");
                ry3.RY3_Find(vi.toCharArray(), count);
                // System.out.println("found key");

                if (count[0] != 0) {
                    od();
                    fk = true;
                    return fk;
                }
            } catch (Throwable t) {
                fk = false;
                return fk;
            } finally {
                itd = true;
            }
        }

        fk = false;
        itd = true;

        return fk;
    }

    /**
     * 验证硬件ID
     *
     * @return
     */
    private boolean vhi() {
        if (!fk()) {
            return false;
        }

        String hardIdFromKey = gvfx("/rtecm/hid");
        String hardid = eci();

        // 忽略硬件信息监测
        if ("zdignorehardcheck".equals(hardIdFromKey)) {
            ZDUtils.info("ZDECM - SKHW: true");
            return true;
        }

        // 锁里获得的硬件ID不可以为空
        if (StringUtils.isBlank(hardIdFromKey)) {
            ZDUtils.info("SysMsg: 000004");
            return false;
        }

        String hardidDecoded = "";
        String hardIdFromKeyDecoded = "";
        try {
            hardidDecoded = new String(Base64.decodeBase64(hardid.getBytes()));
            hardIdFromKeyDecoded = new String(Base64.decodeBase64(hardIdFromKey.getBytes()));

            if (StringUtils.isBlank(hardidDecoded) || StringUtils.isBlank(hardIdFromKeyDecoded)) {
                ZDUtils.info("SysMsg: 000005");
                return false;
            }
        } catch (Throwable t) {
            return false;
        }

        String[] hardidArray = hardidDecoded.split(",");

        boolean biosOk = false;
        boolean macOk = false;
        for (String s : hardidArray) {
            if (s.startsWith("BI:") && hardIdFromKeyDecoded.indexOf(s.trim()) > -1) {
                biosOk = true;
            }

            if (s.startsWith("M:") && hardIdFromKeyDecoded.indexOf(s.trim()) > -1) {
                macOk = true;
            }
        }

        if (!biosOk) {
            ZDUtils.info("SysMsg: 000006, Content1: " + hardIdFromKeyDecoded + ", Content2: " + hardidDecoded);
        }

        if (!macOk) {
            ZDUtils.info("SysMsg: 000007, Content1: " + hardIdFromKeyDecoded + ", Content2: " + hardidDecoded);
        }

        return biosOk && macOk;
    }

    /**
     * 验证锁号
     *
     * @return
     */
    private boolean vki() {
        if (!fk()) {
            return false;
        }

        // 如果是软加密，验证锁号返回true
        if (sk()) {
            return true;
        }

        String keyIdFromKey = gvfx("/rtecm/kid");
        String keyid = ki();

        if (!keyid.equals(keyIdFromKey)) {
            ZDUtils.info("SysMsg: 000002, Content1: " + keyIdFromKey + ", Content2: " + keyid);
            return false;
        }

        // 如果没有加密锁并且用户数大于5，退出
        if (StringUtils.isBlank(ki()) && cl() > 5) {
            ZDUtils.info("SysMsg: 000003");
            System.exit(0);
        }

        return true;
    }

    /**
     * 获取授权用户数
     *
     * @return
     */
    public int cl() {
        if (!dv()) {
            coc = 5;
            return coc;
        }

        int result = 5;
        if (hm("clum") || hm("clus")) {
            result = NumberUtils.toInt(gvfx("/rtecm/cuc"), 5);
        } else {
            result = NumberUtils.toInt(gvfx("/rtecm/cc"), 5);
        }
        coc = result;
        return result;
    }

    /**
     * 获取并发用户数
     *
     * @return
     */
    public int bfl() {
        if (!dv()) {
            bfc = 0;
            return bfc;
        }

        bfc = NumberUtils.toInt(gvfx("/rtecm/bfc"), 0);
        return bfc;
    }

    /**
     * 获取同步用户数
     *
     * @return
     */
    public int scl() {
        if (!fk()) {
            return suc;
        }

        int result = NumberUtils.toInt(gvfx("/rtecm/suc"), 0);
        suc = result;
        return result;
    }

    /**
     * 获取数据表数
     *
     * @return
     */
    public int rtcl() {
        if (!fk()) {
            return rtc;
        }

        int result = NumberUtils.toInt(gvfx("/rtecm/rtc"), 0);
        rtc = result;
        return result;
    }

    /**
     * 获取移动用户数
     *
     * @return
     */
    public int mcl() {
        if (!fk()) {
            return muc;
        }

        int result = NumberUtils.toInt(gvfx("/rtecm/muc"), 0);
        muc = result;
        return result;
    }

    /**
     * 获取产品名称
     *
     * @return
     */
    public String p() {
        if (!fk()) {
            return "wdt";
        }

        return gvfx("/rtecm/pd");
    }

    /**
     * 加密锁是否含有指定组件
     *
     * @param pkey
     * @return bool value
     */
    public boolean hp(String pkey) {
        String[] ps = p().split(",");

        for (String s : ps) {
            if (StringUtils.isBlank(s)) {
                continue;
            }

            if (s.trim().equals(pkey.trim())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 从xml的制定路径中获取值
     *
     * @param path
     * @return
     */
    private String gvfx(String path) {
        if (!fk()) {
            return "";
        }

        if (StringUtils.isBlank(XC)) {
            lxc();
        }

        String finalResult = mch.get(path);
        if (StringUtils.isBlank(finalResult)) {
            try {
                Document document = DocumentHelper.parseText(XC);
                Node n = document.selectSingleNode(path);
                if (n != null) {
                    String result = n.getText();
                    mch.put(path, n.getText());
                    return result;
                } else {
                    return "";
                }
            } catch (Throwable t) {
                return "";
            }
        }

        return finalResult;
    }

    /**
     * 从加密锁中载入XML
     */
    private void lxc() {
        mch.clear();
        try {
            byte[] readFrom = r(2048);
            String trimed = new String(readFrom).trim();
            XC = SecDictionary.INSTANCE.getDecryptedValue(trimed, SecDictionary.PUBLIC_KEY);
        } catch (Throwable t) {
        }
    }

    /**
     * 打开加密锁
     */
    public void od() {
        if (ry3 != null) {
            // System.out.println("start openkey");
            ry3.RY3_Open(1);
            // System.out.println("key opened");
        }
    }

    /**
     * 关闭加密锁
     */
    public void cld() {
        if (ry3 != null) {
            ry3.RY3_Close(true);
        }
    }

    /**
     * 获取加密锁号
     *
     * @return
     */
    public String ki() {
        if (StringUtils.isNotBlank(kiStr)) {
            return kiStr;
        }

        if (ry3 != null) {
            try {
                char[] hardIDArray = new char[32];
                ry3.RY3_GetHardID(hardIDArray);
                kiStr = new String(hardIDArray);
                kiStr = StringUtils.trim(kiStr);
                return kiStr;
            } catch (Throwable e) {
                return "";
            }
        }

        return "";
    }

    /**
     * public encrypt
     *
     * @param numberKey
     * @param toEncrypt
     * @return
     */
    public byte[] pe(int numberKey, byte[] toEncrypt) {
        int length = (int) (Math.ceil(toEncrypt.length / 128.0) * 128);
        byte[] data = toEncrypt.clone();
        ry3.RY3_PublicEncrypt(1, data, length);

        return data;
    }

    /**
     * private decrypt
     *
     * @param numberKey
     * @param toDecrypt
     * @return
     */
    public byte[] pd(int numberKey, byte[] toDecrypt) {
        int length = (int) (Math.ceil(toDecrypt.length / 128.0) * 128);
        byte[] data = toDecrypt.clone();
        ry3.RY3_PrivateDecrypt(1, data, length);

        return data;
    }

    /**
     * 从加密锁中读取数据
     *
     * @param length
     * @return
     */
    public byte[] r(int length) {
        byte[] result = new byte[length];
        try {
            ry3.RY3_Read(0, result, result.length);
            return result;
        } catch (Throwable e) {
            e.printStackTrace();
            return result;
        }
    }

    /**
     * 写入加密锁
     *
     * @param data
     */
    public void w2(byte[] data) {
        try {
            // 先清空
            byte[] blankArray = new byte[2048];
            for (int i = 0; i < 2048; i++) {
                blankArray[i] = 0;
            }
            ry3.RY3_Write(0, blankArray, blankArray.length);

            // 写入正式数据
            ry3.RY3_Write(0, data.clone(), data.length);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * 检查是否存在加密狗
     *
     * @return
     */
    public boolean cd() {
        try {
            // 如果没有插狗或者检测错误，限制为试用版
            if (!dv()) {
                ZDUtils.info("SysMsg: 000009");
                coc = 5;
                bfc = 0;
                suc = 0;
                rtc = 0;
                muc = 0;
                ConcurrentDictionary.lout();
                return false;
            } else {
                // 根据狗的内容设置授权用户数
                ZDUtils.info("SysMsg: 000010");
                coc = cl();
                suc = scl();
                rtc = rtcl();
                muc = mcl();
                bfc = bfl();
                ConcurrentDictionary.lout(coc);
                return true;
            }
        } catch (Throwable t) {
            t.printStackTrace();
            // 抛出异常当作没有插狗
            coc = 5;
            suc = 0;
            rtc = 0;
            muc = 0;
            bfc = 0;
            ConcurrentDictionary.lout();
            return false;
        }
    }

    /**
     * 检查加密狗是否合法
     *
     * @return
     */
    public boolean dv() {
        // 检查狗和硬件指纹
        if (!fk()) {
            ZDUtils.info("SysMsg: 000001, Content: " + fk);
            return false;
        }

        if (!vki()) {
            return false;
        }

        if (!vhi()) {
            return false;
        }

        return true;
    }

    /**
     * 获取CPU ID
     *
     * @return
     */
    public String eci() {
        if (StringUtils.isNotBlank(hid)) {
            return hid;
        }

        // String cpuId = HI.INSTANCE.getCPUInfomation(1);
        // String cpuId = MHUtils.getCPUId();
        String biosSN = "BI:" + HWDefinition.getBSN();
        String macs = StringUtils.join(HWDefinition.getMA(), ",");
        String hardId = macs + "," + biosSN;
        byte[] encoded = Base64.encodeBase64(hardId.getBytes());
        hid = new String(encoded);
        ZDUtils.info("SysMsg: 000008");

        return hid;
    }

    /**
     * 是否含有E6标准版组件
     *
     * @return
     */
    public boolean s() {
        return TaskDictionary.getMainDefinition().hp("wdt");
    }

    /**
     * 是否含有E6专业版
     *
     * @return
     */
    public boolean p2() {
        return TaskDictionary.getMainDefinition().hp("wdp");
    }

    /**
     * 是否含有E6图文版
     *
     * @return
     */
    public boolean w() {
        return TaskDictionary.getMainDefinition().hp("wdw");
    }

    /**
     * 是否含有E6旗舰版
     *
     * @return
     */
    public boolean u() {
        return TaskDictionary.getMainDefinition().hp("wdu") || TaskDictionary.getMainDefinition().hp("grp") ||
                TaskDictionary.getMainDefinition().hp("ent") || TaskDictionary.getMainDefinition().hp("gov");
    }

    public boolean r() {
        return TaskDictionary.getMainDefinition().hp("wdr");
    }

    public boolean d() {
        return TaskDictionary.getMainDefinition().hp("wdd");
    }

    public boolean sda() {
        return TaskDictionary.getMainDefinition().hp("sda");
    }

    public boolean dc() {
        return TaskDictionary.getMainDefinition().hp("dcc");
    }

    /**
     * 获取模块n
     *
     * @param n
     * @return
     */
    public String m(int n) {
        if (!fk()) {
            return "";
        }

        return gvfx("/rtecm/m" + n);
    }

    /**
     * 加密锁是否含有指定模块
     *
     * @param mkey
     * @return
     */
    public boolean hm(String mkey) {
        for (int i = 1; i <= 12; i++) {
            if (m(i).equals(mkey)) {
                return true;
            }
        }

        return false;
    }

    public static void resetXC() {
        XC = "";
        mch.clear();
    }
}
