package zd.base.exception;

import lombok.Data;
import zd.base.utils.constant.ErrorCodeDefine;
import zd.base.utils.constant.ErrorCodeUtils;

/**
 * 参数验证错误
 */
@Data
public class IllegalArgumentException extends RuntimeException {

    /**
     * 错误code。参考：
     */
    private String errorCode;

    /**
     * 是否打印日志
     */
    private boolean writeLog = true;

    public IllegalArgumentException(String msg) {
        super(msg);
        this.errorCode = ErrorCodeDefine.UNDEFINED_ERROR;
    }

    public IllegalArgumentException(String errorCode, String msg) {
        super(msg);
        this.errorCode = errorCode;
    }

    public IllegalArgumentException(String errorCode, String msg, boolean writeLog) {
        super(msg);
        this.errorCode = errorCode;
        this.writeLog = writeLog;
    }
}
