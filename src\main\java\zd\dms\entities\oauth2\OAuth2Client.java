package zd.dms.entities.oauth2;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import zd.base.context.UserContextHolder;
import zd.base.entities.AbstractEntity;

import java.util.Date;

/**
 * OAuth2客户端实体
 */
@Entity
@Data
@Table(name = "oauth2_client")
public class OAuth2Client extends AbstractEntity {

    /**
     * 客户端ID
     */
    @org.hibernate.annotations.Index(name = "i_oauth2_client_cId")
    private String clientId;

    /**
     * 客户端密钥
     */
    @org.hibernate.annotations.Index(name = "i_oauth2_client_cs")
    private String clientSecret;

    /**
     * 客户端名称
     */
    @org.hibernate.annotations.Index(name = "i_oauth2_client_cname")
    private String clientName;

    /**
     * 授权类型（逗号分隔）
     */
    private String grantTypes;

    /**
     * 重定向URI（逗号分隔）
     */
    private String redirectUris;

    /**
     * 作用域（逗号分隔）
     */
    private String scopes;

    /**
     * 是否启用
     */
    @org.hibernate.annotations.Index(name = "i_oauth2_client_enabled")
    private Boolean enabled;

    /**
     * 访问令牌有效期（秒）
     */
    private Integer accessTokenValidity = 3600;

    /**
     * 刷新令牌有效期（秒）
     */
    private Integer refreshTokenValidity = 86400;

    private String creatorFullname;

    /**
     * 创建时间
     */
    @org.hibernate.annotations.Index(name = "i_oauth2_client_cd")
    private Date creationDate;

    @org.hibernate.annotations.Index(name = "i_oauth2_client_numIndex")
    private int numIndex;

    public OAuth2Client() {
        super();
        creationDate = new Date();
        numIndex = 999;
        enabled = true;
        creatorFullname = UserContextHolder.getUser() != null ? UserContextHolder.getUser().getFullname() : "";
    }
}
