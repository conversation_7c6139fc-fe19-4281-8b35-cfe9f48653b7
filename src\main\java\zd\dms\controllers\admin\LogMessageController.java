package zd.dms.controllers.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDDateUtils;
import zd.base.utils.model.ObjectMapperUtils;
import zd.base.utils.validate.ValidateUtils;
import zd.dms.dto.PageResponse;
import zd.dms.entities.FolderArea;
import zd.dms.entities.LogMessage;
import zd.dms.entities.Role;
import zd.dms.services.admin.LogMessageService;
import zd.dms.services.document.FolderAreaService;
import zd.dms.services.log.LogService;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "LogMessageController", description = "系统日志Controller")
@RequestMapping("/admin/logMessage")
public class LogMessageController extends ControllerSupport {

    private final LogService logMessageService;

    @Operation(summary = "获取分页系统日志")
    @PostMapping("/getPage")
    @ZDLog("获取系统日志")
    public JSONResultUtils<Object> listLogMessage(@RequestBody Map<String, Object> params) throws ParseException {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);

        int pageNumber = MapUtils.getIntValue(params,"pageNumber",1);
        int pageSize = MapUtils.getIntValue(params,"pageSize",10);
        int level = MapUtils.getIntValue(params,"level",0);
        String startDateStr = MapUtils.getString(params,"startDate");
        String endDateStr = MapUtils.getString(params,"endDate");

        Date startDate=null;
        Date endDate=null;
        if(StringUtils.isNotBlank(startDateStr)){
            startDate= ZDDateUtils.parseDate(startDateStr);
        }
        if(StringUtils.isNotBlank(endDateStr)){
            endDate= ZDDateUtils.parseDate(endDateStr);
        }
        Page page = logMessageService.getLogs(pageNumber,  pageSize,  level,  startDate,
                 endDate,  "",  true);

        return successData(PageResponse.of(page, ""));
    }

    @Operation(summary = "获取系统日志")
    @GetMapping("/get/{id}")
    @ZDLog("获取系统日志")
    public JSONResultUtils<Object> getLogMessages(@PathVariable String id) {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);

        LogMessage logMessage = logMessageService.getById(NumberUtils.toLong(id));

        return successData(logMessage);
    }

    @Operation(summary = "删除系统日志")
    @PostMapping("/delete/{id}")
    @ZDLog("删除系统日志")
    public JSONResultUtils<Object> delete(@PathVariable String id) {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);
        ValidateUtils.isTrue(StringUtils.isNotBlank(id), "请选择系统日志");

        LogMessage logMessage = logMessageService.getById(NumberUtils.toLong(id));
        if(logMessage == null){
            return error("没有找到对应系统日志");
        }

        logMessageService.deleteById(NumberUtils.toLong(id));

        return success();
    }

    @Operation(summary = "清空系统日志")
    @PostMapping("/clearLogMessage")
    @ZDLog("清空系统日志")
    public JSONResultUtils<Object> clearLogMessage() {
        checkRoles(Role.SYSTEM_ADMIN, Role.LIMITED_ADMIN);
        logMessageService.clearLogs();
        return success();
    }
}
