package zd.base.controllers.security;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import zd.base.annotation.ZDLog;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.security.JasyptUtils;
import zd.base.utils.validate.ValidateUtils;

import java.util.HashMap;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "EncryptController", description = "加密Controller")
@RequestMapping("/opening/encrypt")
public class EncryptController {

    @Operation(summary = "获取加密串")
    @RequestMapping(value = "/encryptStr", method = {RequestMethod.GET, RequestMethod.POST})
    @ZDLog("获取加密串")
    public JSONResultUtils<Map<String, Object>> encryptStr(@RequestParam(required = false) String str) {
        ValidateUtils.isTrue(StringUtils.isNotBlank(str), "str不能为空");
        String encrypt = JasyptUtils.encrypt(str);

        Map<String, Object> map = new HashMap<>();
        map.put("sourceStr", str);
        map.put("resultStr", encrypt);
        map.put("ymlSetting", "ENC(" + encrypt + ")");


        return JSONResultUtils.successWithData(map);
    }

    @Operation(summary = "获取解密串")
    @RequestMapping(value = "/decryptStr", method = {RequestMethod.GET, RequestMethod.POST})
    @ZDLog("获取解密串")
    public JSONResultUtils<Map<String, Object>> decryptStr(@RequestParam(required = false) String str) {
        ValidateUtils.isTrue(StringUtils.isNotBlank(str), "str不能为空");
        String decrypt = JasyptUtils.decrypt(str);

        Map<String, Object> map = new HashMap<>();
        map.put("sourceStr", str);
        map.put("resultStr", decrypt);

        return JSONResultUtils.successWithData(map);
    }
}
