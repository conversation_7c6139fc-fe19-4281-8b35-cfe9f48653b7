package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Length;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractEntity;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "sms_sendlog")
@BatchSize(size = 20)
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class SmsSendLog extends AbstractEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = 6338962806276800943L;

    /**
     * 发送成功
     */
    public static final int STATUS_SUCCESS = 1;

    /**
     * 发送失败
     */
    public static final int STATUS_FAILED = 2;

    /**
     * 本次发送手机号码数量
     */
    private int mobilePhoneCount;

    @Column(length = Length.LOB_DEFAULT)
    private String phoneNumbers;

    @Column(length = Length.LOB_DEFAULT)
    private String smsContent;

    /**
     * 发送状态
     */
    private int sendStatus;

    /**
     * 操作者用户名
     */
    private String creatorFullname;

    /**
     * 部门
     */
    @Column(name = "groups1")
    private String groups;

    /**
     * 操作者的IP地址
     */
    private String ip;

    /**
     * 创建时间
     */
    @Index(name = "i_smslog_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 默认构造器
     */
    public SmsSendLog() {
        super();
        sendStatus = STATUS_SUCCESS;
        creationDate = new Date();
    }

    public String getPhoneNumberSummary() {
        return StringUtils.abbreviate(phoneNumbers, 50);
    }

    public String getSmsContentSummary() {
        return StringUtils.abbreviate(smsContent, 50);
    }

    public int getMobilePhoneCount() {
        return mobilePhoneCount;
    }

    public void setMobilePhoneCount(int mobilePhoneCount) {
        this.mobilePhoneCount = mobilePhoneCount;
    }

    public int getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(int sendStatus) {
        this.sendStatus = sendStatus;
    }

    public String getCreatorFullname() {
        return creatorFullname;
    }

    public void setCreatorFullname(String creatorFullname) {
        this.creatorFullname = creatorFullname;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getPhoneNumbers() {
        return phoneNumbers;
    }

    public void setPhoneNumbers(String phoneNumbers) {
        this.phoneNumbers = phoneNumbers;
    }

    public String getSmsContent() {
        return smsContent;
    }

    public void setSmsContent(String smsContent) {
        this.smsContent = smsContent;
    }

    public String getGroups() {
        return groups;
    }

    public void setGroups(String groups) {
        this.groups = groups;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
