package zd.dms.repositories.sms;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.SmsSendLog;

import java.util.Date;
import java.util.List;

public interface SendLogRepository extends BaseRepository<SmsSendLog, String> {

    Page<SmsSendLog> getSendLogs(int page, int pageSize);

    void clearSendLogs();

    List<SmsSendLog> getSmsSendLogByDate(Date startDate, Date endDate);
}
