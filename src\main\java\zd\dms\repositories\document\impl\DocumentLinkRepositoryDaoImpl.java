package zd.dms.repositories.document.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.support.JdbcUtils;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.Document;
import zd.dms.entities.DocumentLink;
import zd.dms.entities.Folder;
import zd.dms.entities.User;
import zd.dms.repositories.document.DocumentLinkRepository;
import zd.dms.utils.DocDBUtils;
import zd.dms.utils.repositories.SpecTools;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class DocumentLinkRepositoryDaoImpl extends BaseRepositoryDaoImpl<DocumentLink, Long> implements DocumentLinkRepository {

    public DocumentLinkRepositoryDaoImpl(Class<DocumentLink> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<DocumentLink> getDocumentLinksByJDBC(Connection conn, String docLinkSql, int type, Long folderId) throws SQLException {
        PreparedStatement docLinkPstmt = null;
        ResultSet docLinkRs = null;

        List<DocumentLink> docLinkList = new ArrayList<DocumentLink>();
        if (conn != null) {
            try {
                docLinkPstmt = conn.prepareStatement(docLinkSql);
                docLinkPstmt.setLong(1, folderId);
                docLinkPstmt.setInt(2, type);

                docLinkRs = docLinkPstmt.executeQuery();
                while (docLinkRs.next()) {
                    DocumentLink docLink = new DocumentLink();
                    // 通过文档链接中的文档id：DOCUMENT_ID，获取文档信息，并封装到文档链接中
                    Document doc = getDocumentByIdWithJDBC(conn, docLinkRs.getString("DOCUMENT_ID"));
                    docLink.setDocument(doc);
                    docLinkList.add(docLink);
                }
            } catch (SQLException e) {
                log.error("getDocumentLinksByJDBC", e);
                throw e;
            } finally {
                JdbcUtils.closeResultSet(docLinkRs);
                JdbcUtils.closeStatement(docLinkPstmt);
                JdbcUtils.closeConnection(conn);
            }
        }

        return docLinkList;
    }

    /**
     * 通过文档id获取对象
     *
     * @param conn
     * @param docId
     * @return
     * @throws SQLException
     */
    private Document getDocumentByIdWithJDBC(Connection conn, String docId) throws SQLException {
        PreparedStatement docPstmt = null;
        ResultSet docRs = null;
        String docSql = "SELECT * FROM tdms_doc where ID = ?";
        log.debug("getDocumentByIdWithJDBC docSql: {}, docId", docSql, docId);

        Document doc = new Document();
        try {
            docPstmt = conn.prepareStatement(docSql);
            docPstmt.setString(1, docId);
            docRs = docPstmt.executeQuery();

            while (docRs.next()) {
                doc.setSerialNumber(docRs.getString("serialNumber"));
                doc.setFilename(docRs.getString("filename"));
                doc.setExtension(docRs.getString("extension"));
                doc.setDocVersion(docRs.getString("docVersion"));
                doc.setNewestVersion(docRs.getInt("newestVersion"));
                doc.setFileSize(docRs.getLong("fileSize"));
                doc.setCreatorFullname(docRs.getString("creatorFullname"));
                doc.setCreator(docRs.getString("creator"));
                doc.setCreationDate(docRs.getTimestamp("creationDate"));
                doc.setModifiedDate(docRs.getTimestamp("modifiedDate"));
                doc.setFromScan(docRs.getBoolean("fromScan"));
                doc.setLocked(docRs.getBoolean("locked"));
                doc.setLockDate(docRs.getDate("lockDate"));
                doc.setLockedByFullname(docRs.getString("lockedByFullname"));
                doc.setPiId(docRs.getString("piId"));
                doc.setHash(docRs.getString("hash"));
                doc.setClickCount(docRs.getInt("clickCount"));
                doc.setDocProps(docRs.getString("docProps"));
                doc.setCustomDate1(docRs.getTimestamp("customDate1"));
                doc.setCustomDate2(docRs.getTimestamp("customDate2"));
                doc.setFolderId(docRs.getLong("folderId"));
            }
        } catch (SQLException e) {
            log.error("getDocumentByIdWithJDBC", e);
            throw e;
        } finally {
            JdbcUtils.closeResultSet(docRs);
            JdbcUtils.closeStatement(docPstmt);
        }
        return doc;
    }

    @Override
    public void deleteDocumentLink(User user) {
        String hql = "delete from DocumentLink where username = ?1";
        executeUpdate(hql, user.getUsername());
    }

    @Override
    public void deleteDocumentLink(Folder folder) {
        String hql = "delete from DocumentLink where folderId = ?1";
        executeUpdate(hql, folder.getId());
    }

    @Override
    public void deleteDocumentLink(Document document) {
        String hql = "delete from DocumentLink where docId = ?1";
        executeUpdate(hql, document.getId());
    }

    @Override
    public List<DocumentLink> getDocumentLinks(int type, Folder folder) {
        Specification<DocumentLink> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLink> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("folder", folder);
            specTools.eq("linkType", type);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<DocumentLink> getDocumentLinks(int type, Document document) {
        Specification<DocumentLink> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLink> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("docId", document.getId());
            specTools.eq("linkType", type);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<DocumentLink> getDocumentLinks(int type, User user) {
        Specification<DocumentLink> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLink> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("username", user.getUsername());
            specTools.eq("linkType", type);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<DocumentLink> getDocumentLinks(int type) {
        Specification<DocumentLink> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentLink> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("linkType", type);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void deleteLinkById(long id) {
        String hql = "delete from DocumentLink where id = ?";
        executeUpdate(hql, id);
    }

    @Override
    public List<DocumentLink> searchDocumentLinks(int typeMyCommon, String username, String fileNames, String advUpdateStartDate, String advUpdateEndDate) {
        if (StringUtils.isBlank(username)) {
            return null;
        }

        String whereClause = " 1=1";
        if (StringUtils.isNotBlank(fileNames)) {
            whereClause += " and t1.filename like '%" + fileNames + "%'";
        }

        if (StringUtils.isNotBlank(advUpdateStartDate)) {
            whereClause += " and t1.modifieddate >= " + DocDBUtils.parseDateSQL(advUpdateStartDate + " 00:00:00");
        }

        if (StringUtils.isNotBlank(advUpdateEndDate)) {
            whereClause += " and t1.modifieddate <= " + DocDBUtils.parseDateSQL(advUpdateEndDate + " 23:59:59");
        }

        final String sql = "select t3.* from(" +
                "select t1.* from(select dl.docId as docId ,td.filename as filename,td.modifieddate as modifieddate from (" +
                "select * from document_link where creator = '" + username + "' and linktype = '" + typeMyCommon +
                "') dl left join tdms_doc td on dl.docId = td.id)t1 " + "where " + whereClause + ")t2 " +
                "left join (select * from document_link where creator = '" + username + "' and linktype = '" +
                typeMyCommon + "')t3 on t2.docId = t3.docId order by t3.creationdate desc";
        log.debug("sql :{}", sql);

        Query nativeQuery = this.entityManager.createNativeQuery(sql, DocumentLink.class);
        return nativeQuery.getResultList();
    }
}
