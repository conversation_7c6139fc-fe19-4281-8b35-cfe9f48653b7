package zd.base.utils.system;

import lombok.Getter;
import zd.dms.services.config.SystemConfigManager;

public class SystemConfigUtils {

    @Getter
    private static SystemConfigManager scm = SystemConfigManager.getInstance();


    public static String get(String key) {
        return scm.getProperty(key);
    }

    public void setProperty(String key, String value) {
        scm.setProperty(key, value);
    }

    public void setProperty(String key, int value) {
        scm.setProperty(key, value);
    }

    public void setProperty(String key, boolean value) {
        scm.setProperty(key, value);
    }

    public int getIntProperty(String key) {
        return scm.getIntProperty(key);
    }

    public boolean getBooleanProperty(String key) {
        return scm.getBooleanProperty(key);
    }

    public boolean getBooleanProperty(String key, boolean defaultValue) {
        return scm.getBooleanProperty(key, defaultValue);
    }

    public void removeProperty(String key) {
        scm.removeProperty(key);
    }
}
