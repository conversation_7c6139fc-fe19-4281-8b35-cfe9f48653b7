package zd.base.config.ftl;

import freemarker.cache.TemplateLoader;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;
import org.springframework.web.servlet.view.freemarker.FreeMarkerViewResolver;

import java.util.Properties;

@Configuration
public class FTLConfig {

    @Bean
    public FreeMarkerViewResolver freeMarkerViewResolver() {
        FreeMarkerViewResolver resolver = new FreeMarkerViewResolver();
        resolver.setCache(false);
        resolver.setPrefix("");
        resolver.setSuffix(".ftl");
        resolver.setContentType("text/html; charset=UTF-8");
        resolver.setOrder(1);
        return resolver;
    }

    @Bean
    public FreeMarkerConfigurer freeMarkerConfigurer() {
        FreeMarkerConfigurer configurer = new FreeMarkerConfigurer();
//        configurer.setTemplateLoaderPath("classpath:/templates/");
        configurer.setPreTemplateLoaders(new TemplateLoader[]{
//                new LocalFileTemplateLoader("d:/dev/nghome/templates/")
                new CommonFileTemplateLoader()
        });
        configurer.setDefaultEncoding("UTF-8");

        // 不进行国际化检查
        Properties freemarkerSettings = new Properties();
        freemarkerSettings.setProperty("localized_lookup", "false");
        configurer.setFreemarkerSettings(freemarkerSettings);

        return configurer;
    }
}