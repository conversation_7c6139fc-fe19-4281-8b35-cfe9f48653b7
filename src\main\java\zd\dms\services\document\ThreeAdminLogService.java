package zd.dms.services.document;

import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.dms.entities.Folder;
import zd.dms.entities.ThreeAdminLog;
import zd.record.entities.RecFolder;

import java.util.Date;

@Transactional
public interface ThreeAdminLogService extends BaseJpaService<ThreeAdminLog, Long> {

    Page getLogs(int pageNumber, int pageSize, int type, int targetType, String username, Date startDate, Date endDate);

    ThreeAdminLog addLog(Folder folder, RecFolder recFolder, String targetUserGroup, int type, String msg,
                         String operator, String fullname, String ipAddress, boolean logToParent);
}
