package zd.dms.controllers.document;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.exception.IllegalArgumentException;
import zd.base.utils.ResponseEntityUtils;
import zd.dms.entities.Document;
import zd.dms.entities.Folder;
import zd.dms.entities.FolderPermission;
import zd.dms.entities.User;
import zd.dms.io.ZDIOUtils;
import zd.dms.services.document.DocumentService;
import zd.dms.services.document.DocumentUtils;
import zd.dms.services.document.ImportExportService;
import zd.dms.services.security.FolderPermissionUtils;
import zd.dms.utils.ZDAuthUtils;
import zd.dms.utils.document.DownloadUtils;
import zd.record.entities.RecFolder;
import zd.record.entities.RecFolderPermission;
import zd.record.service.folder.RecFolderService;
import zd.record.service.security.RecFolderPermissionUtils;
import zd.record.utils.record.RecLendingPermissionUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "DownloadController", description = "文档下载Controller")
@RequestMapping("/document")
@Slf4j
public class DownloadController extends ControllerSupport {

    private final DocumentService documentService;

    private final ImportExportService importExportService;

    private final RecFolderService recFolderService;

    @Operation(summary = "获取文件")
    @GetMapping("/read/{docId}/{version}/{from}/{info}/{filename}")
    @ZDLog("获取文件")
    public ResponseEntity<byte[]> read(@PathVariable String docId, @PathVariable int version, @PathVariable String from,
            @PathVariable String info, @PathVariable String filename) throws Exception {
        return getDocStream(docId, version, from, info);
    }

    @Operation(summary = "获取文件")
    @GetMapping("/read2/{docId}/{version}/{from}/{info}/{token}/{filename}")
    @ZDLog("获取文件")
    public ResponseEntity<byte[]> read2(@PathVariable String docId, @PathVariable int version,
            @PathVariable String from, @PathVariable String info, @PathVariable String token,
            @PathVariable String filename, HttpServletRequest request, HttpServletResponse response) throws Exception {
        boolean auth = ZDAuthUtils.isAuth(request, response, token);
        if (!auth) {
            return ResponseEntityUtils.nullResult();
        }

        return getDocStream(docId, version, from, info);
    }

    private ResponseEntity<byte[]> getDocStream(String docId, int version, String from, String info) throws Exception {
        User currentUser = getCurrentUser();

        Document document = documentService.getDocumentById(docId);
        if (document == null) {
            return ResponseEntityUtils.nullResult();
        }

        Map<String, Object> infoMap = getInfoMap(info);
        String opType = MapUtils.getString(infoMap, "opType", "");
        if ("rec".equals(from)) {
            long recFolderId = MapUtils.getLongValue(infoMap, "recFolderId", 0L);

            RecFolder recFolder = recFolderService.getById(recFolderId);
            String needPerm = RecFolderPermission.READ_ATTACHMENT;
            if ("edit".equals(opType)) {
                needPerm = RecFolderPermission.EDIT;
            }

            if (!RecFolderPermissionUtils.checkPermission(getCurrentUser(), recFolder, needPerm)) {
                throw new IllegalArgumentException("无权进行此操作");
            }
        } else if ("recLending".equals(from)) {
            long recId = MapUtils.getLongValue(infoMap, "recId", 0L);
            if (recId <= 0) {
                throw new IllegalArgumentException("无权进行此操作");
            }

            long recFolderId = MapUtils.getLongValue(infoMap, "recFolderId", 0L);
            RecFolder recFolder = recFolderService.getById(recFolderId);
            if (recFolder == null) {
                throw new IllegalArgumentException("无权进行此操作");
            }
            String tableName = recFolder.getTableName();

            long recLendingId = MapUtils.getLongValue(infoMap, "recLendingId", 0L);
            if (!RecLendingPermissionUtils.checkRecLendingPermissions(recLendingId, currentUser, "Y", recId,
                    tableName)) {
                throw new IllegalArgumentException("无权进行此操作");
            }
        } else {
            String needPerm = FolderPermission.READ;
            if ("edit".equals(opType)) {
                needPerm = FolderPermission.EDIT;
            }

            Folder folder = document.getFolder();
            if (!FolderPermissionUtils.checkPermission(getCurrentUser(), folder, needPerm)) {
                throw new IllegalArgumentException("无权进行此操作");
            }
        }

        if (version == -1) {
            version = document.getNewestVersion();
        }

        InputStream downloadStream;
        if (document.isPdf()) {
            downloadStream = DownloadUtils.getDocPdfInForRead(document, version, currentUser);
        } else if (DocumentUtils.isPicture(document)) {
            downloadStream = DownloadUtils.getDocPictureInForRead(document, version, currentUser);
        } else {
            downloadStream = ZDIOUtils.getDocInputStream(document, version);
        }

        if (downloadStream == null) {
            return ResponseEntityUtils.nullResult();
        }

        return ResponseEntityUtils.fileResult(document.getFilename(), downloadStream);
    }

    private Map<String, Object> getInfoMap(String info) {
        if (StringUtils.isBlank(info) || "info".equals(info)) {
            return new HashMap<>();
        }

        Map<String, Object> results = new HashMap<>();
        String[] split = info.split("--");
        for (String str : split) {
            if (StringUtils.isBlank(str)) {
                continue;
            }

            String[] split1 = str.split("=");
            if (split1.length != 2) {
                continue;
            }

            results.put(split1[0], split1[1]);
        }

        return results;
    }

    @Operation(summary = "批量下载文档")
    @PostMapping("/batchDownload")
    @ZDLog("批量下载文档")
    public ResponseEntity<byte[]> batchDownload(@RequestBody List<String> docIds) throws Exception {
        if (docIds == null || docIds.isEmpty()) {
            throw new IllegalArgumentException("请选择要下载的文档");
        }

        User currentUser = getCurrentUser();
        String zipFilename = currentUser.getUsername() + "-批量下载-" +
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmm")) + ".zip";

        File zipExportFile = new File(System.getProperty("java.io.tmpdir"), zipFilename);
        if (zipExportFile.exists()) {
            Files.delete(zipExportFile.toPath());
        }

        List<String> validDocIds = new ArrayList<>();
        Map<String, String> docsPrefix = new HashMap<>();
        for (String docId : docIds) {
            Document document = documentService.getDocumentById(docId);
            if (document != null) {
                Folder folder = document.getFolder();
                if (FolderPermissionUtils.checkPermission(currentUser, folder, FolderPermission.DOWNLOAD)
                        && !document.isOnWorkflow()) {
                    validDocIds.add(docId);
                    docsPrefix.put(docId, folder.getName() + "/");
                }
            }
        }

        if (validDocIds.isEmpty()) {
            throw new IllegalArgumentException("没有选择任何文档，或者无下载文档的权限");
        }

        try {
            long start = System.currentTimeMillis();
            importExportService.batchDownloadZipDocs(zipExportFile, validDocIds.toArray(new String[0]), null,
                    currentUser, getIpAddress());
            log.debug("{} 批量下载，文件数：{}，尺寸：{}，耗时: {} ms",
                    currentUser.getUsername(), validDocIds.size(),
                    zipExportFile.length(), (System.currentTimeMillis() - start));
        } catch (Exception e) {
            log.error("压缩文档时出错", e);
            throw new RuntimeException("压缩文档时出错", e);
        }

        if (!zipExportFile.exists()) {
            throw new RuntimeException("压缩文档时出错");
        }

        try (InputStream inputStream = new FileInputStream(zipExportFile)) {
            return ResponseEntityUtils.fileResult(zipFilename, inputStream);
        } finally {
            Files.deleteIfExists(zipExportFile.toPath());
        }
    }
}
