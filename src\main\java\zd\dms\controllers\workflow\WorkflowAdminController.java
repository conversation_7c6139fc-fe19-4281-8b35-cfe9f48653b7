package zd.dms.controllers.workflow;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapUtils;
import zd.base.utils.model.ObjectMapperUtils;
import zd.dms.dto.PageResponse;
import zd.dms.workflow.engine.ProcessService;
import zd.dms.workflow.entities.SSProcessDefinition;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "WorkflowAdminController", description = "流程定义Controller")
@RequestMapping("/admin/workflow")
public class WorkflowAdminController extends ControllerSupport {

    private final ProcessService processService;

    @Operation(summary = "保存或更新流程定义")
    @PostMapping("/saveOrUpdatePd")
    @ZDLog("保存或更新流程定义")
    public JSONResultUtils<Object> saveOrUpdatePd(@RequestBody Map<String, Object> params) {

        String name = MapUtils.getString(params, "name", "");
        if (StringUtils.isBlank(name)) {
            return error("请填写流程名称");
        }

        if (name.length() > 50) {
            return error("流程名称最多50字");
        }

        /*if (StringUtils.isBlank(permissions) || StringUtils.isBlank(permissionsDisplay)) {
            return error("请先选择授权人员及部门");
        }*/

        int remindEveryHour = MapUtils.getIntValue(params, "remindEveryHour", 0);
        if (remindEveryHour < 0 || remindEveryHour > 200) {
            return error("自动催办时间不可以小于0或大于200");
        }

        String nodeJson = MapUtils.getString(params, "nodeJson", "");
        if (StringUtils.isBlank(nodeJson)) {
            return error("请设置流程节点");
        }

        String pdType = MapUtils.getString(params, "pdType", "normal");

        String pdId = MapUtils.getString(params, "pdId", "");
        SSProcessDefinition pd = null;
        if (StringUtils.isNotBlank(pdId)) {
            pd = processService.getProcessDefinition(pdId);
            if (pd == null) {
                return error("未找到对应流程");
            }
        } else {
            pd = new SSProcessDefinition();
            pd.setPdType(pdType);
        }

        pd.setName(name);
        pd.setRemindEveryHour(remindEveryHour);
        pd.setNodeJson(nodeJson);

        String relationTableName = MapUtils.getString(params, "relationTableName", "");
        pd.setRelationTableName(relationTableName);

        if (StringUtils.isNotBlank(pdId)) {
            processService.updateProcessDefinition(pd);
        } else {
            processService.createProcessDefinition(pd);
            pd.setCreatorFullname(getCurrentUser().getFullname());
        }

        return JSONResultUtils.success().whole();
    }

    @Operation(summary = "获取流程定义")
    @GetMapping("/getPd/{id}")
    @ZDLog("获取流程定义")
    public JSONResultUtils<Object> getPd(@PathVariable String id) {
        SSProcessDefinition pd = processService.getProcessDefinition(id);
        return JSONResultUtils.successWithData(ObjectMapperUtils.toMap(pd));
    }

    @Operation(summary = "流程定义列表")
    @PostMapping("/listPds")
    @ZDLog("流程定义列表")
    public JSONResultUtils<Object> listPds(@RequestBody Map<String, Object> params) {
        String pdType = MapUtils.getString(params, "pdType", "");
        List<SSProcessDefinition> pds = processService.getProcessDefinitions(pdType);
        return JSONResultUtils.successWithData(ObjectMapperUtils.toMapList(pds));
    }

    @Operation(summary = "删除流程定义")
    @PostMapping("/batchDelete")
    @ZDLog("删除流程定义")
    public JSONResultUtils<Object> batchDelete(@RequestBody Map<String, Object> params) {
        List<String> ids = ZDMapUtils.getListStringValue(params, "ids");
        ids.forEach(processService::deleteProcessDefinition);

        return success();
    }


    @Operation(summary = "流程定义分页列表")
    @PostMapping("/getPdsPage")
    @ZDLog("流程定义分页列表")
    public JSONResultUtils<Object> getPdsPage(@RequestBody Map<String, Object> params) {
        String pdType = MapUtils.getString(params, "pdType", "");
        int pageNumber = MapUtils.getIntValue(params, "pageNumber", 1);
        int pageSize = MapUtils.getIntValue(params, "pageSize", 10);
        Page pds = processService.getProcessDefinitionsPage(pageNumber, pageSize, pdType);
        return successData(PageResponse.of(pds));
    }
}
