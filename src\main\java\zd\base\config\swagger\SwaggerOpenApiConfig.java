package zd.base.config.swagger;

import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.servers.Server;
import org.springframework.boot.SpringBootConfiguration;

@SpringBootConfiguration
@OpenAPIDefinition(
        // ## API的基本信息，包括标题、版本号、描述、联系人等
        info = @Info(
                title = "ECM接口文档",       // Api接口文档标题（必填）
                description = "ECM接口所有Controller信息文档",      // Api接口文档描述
                version = "1.0.0"                                // Api接口版本
        ),
        // ## 表示服务器地址或者URL模板列表，多个服务地址随时切换（只不过是有多台IP有当前的服务API）
        servers = {
                @Server(url = "http://*********:8081/", description = "本地服务器一服务"),
                @Server(url = "https://www.baidu.com/demo/", description = "本地服务器二服务"),
        },
        externalDocs = @ExternalDocumentation(description = "更多内容暂无", url = "http://*********:8081/"))
public class SwaggerOpenApiConfig {

}
