package zd.dms.repositories.workflow.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.repositories.workflow.ProcessDefinitionRepository;
import zd.dms.utils.repositories.PredicateUtils;
import zd.dms.utils.repositories.QueryParamsUtils;
import zd.dms.utils.repositories.SpecTools;
import zd.dms.workflow.entities.SSProcessDefinition;

import java.util.ArrayList;
import java.util.List;

public class ProcessDefinitionRepositoryDaoImpl extends BaseRepositoryDaoImpl<SSProcessDefinition, String> implements ProcessDefinitionRepository {

    public ProcessDefinitionRepositoryDaoImpl(Class<SSProcessDefinition> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<SSProcessDefinition> listByPdTypeAndSecondType(String pdType, String[] secondType, String tagName) {
        Specification<SSProcessDefinition> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(criteriaBuilder.equal(root.get("pdType"), pdType));
            if (StringUtils.isNotBlank(tagName)) {
                PredicateUtils.like(root, criteriaBuilder, "tags", tagName, predicates);
            }

            PredicateUtils.in(root, criteriaBuilder, "secondType", secondType, predicates);

            Order orderPropertyOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "priority", false);
            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, orderPropertyOrder);
        };

        return findAll(spec);
    }

    @Override
    public List<SSProcessDefinition> listByTagAndPdType(String tag, final String pdType) {
        Specification<SSProcessDefinition> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.isNotBlank(tag)) {
                PredicateUtils.like(root, criteriaBuilder, "tags", tag, predicates);
            }

            String tempPdType = pdType;
            if (StringUtils.isBlank(tempPdType)) {
                tempPdType = "normal";
            }

            if ("normal".equals(tempPdType)) {
                predicates.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("pdType"), pdType), criteriaBuilder.isNull(root.get("pdType"))));
            } else {
                predicates.add(criteriaBuilder.equal(root.get("pdType"), pdType));
            }

            Order orderPropertyOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "priority", false);
            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, orderPropertyOrder);
        };

        return findAll(spec);
    }

    @Override
    public List<SSProcessDefinition> getProcessDefinitionsByType(String... pdType) {
        Specification<SSProcessDefinition> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSProcessDefinition> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);
            if (pdType != null && pdType.length != 0) {
                if (pdType.length > 1 || StringUtils.isNotBlank(pdType[0])) {
                    specTools.in("pdType", pdType);
                }
            }

            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public Page getProcessDefinitionsPage(int pageNumber, int pageSize, String pdType) {
        Specification<SSProcessDefinition> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSProcessDefinition> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);
            if (StringUtils.isNotBlank(pdType)) {
                    specTools.eq("pdType", pdType);
            }

            specTools.desc("creationDate");

            return specTools.getRestriction();
        };
        return findAll(spec,pageNumber,pageSize);
    }

    @Override
    public List<SSProcessDefinition> list() {
        Specification<SSProcessDefinition> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSProcessDefinition> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<SSProcessDefinition> listByTag(String tag) {
        Specification<SSProcessDefinition> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSProcessDefinition> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);
            specTools.like("tags", tag);
            specTools.desc("priority");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<String> getAllTags() {
        TypedQuery<String> query = this.entityManager.createQuery("select tags from SSProcessDefinition", String.class);
        return query.getResultList();
    }
}
