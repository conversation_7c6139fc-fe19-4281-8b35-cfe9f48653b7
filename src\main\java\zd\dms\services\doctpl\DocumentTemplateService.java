package zd.dms.services.doctpl;

import org.springframework.transaction.annotation.Transactional;
import zd.base.service.BaseJpaService;
import zd.dms.entities.DocumentTemplate;
import zd.dms.entities.User;

import java.io.File;
import java.io.IOException;
import java.util.List;

@Transactional
public interface DocumentTemplateService extends BaseJpaService<DocumentTemplate, String> {

    void createDocTpl(DocumentTemplate docTpl, File uploadFile)
            throws IOException;

    @Transactional(readOnly = true)
    DocumentTemplate getDocTplById(String id);

    @Transactional(readOnly = true)
    List<DocumentTemplate> getAllDocTpls();

    void updateDocTpl(DocumentTemplate docTpl, File uploadFile)
            throws IOException;

    void deleteDocTpl(DocumentTemplate docTpl) throws IOException;

    @Transactional(readOnly = true)
    List<DocumentTemplate> getPermitedTpls(User user, String docType);

    boolean isValidTpl(String filename);
}
