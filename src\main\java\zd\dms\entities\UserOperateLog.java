package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.dms.services.document.DocumentUtils;
import zd.dms.utils.TextUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "useroplog")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class UserOperateLog extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = 8417104449109409644L;

    public static final int TYPE_CREATE = 1;

    public static final int TYPE_UPDATE = 2;

    public static final int TYPE_READ = 3;

    public static final int TYPE_SEND_TO_ME = 4;

    /**
     * 操作者用户名
     */
    @Column(nullable = false)
    @Index(name = "i_u_operator")
    private String operator;

    /**
     * 操作类型
     */
    @Index(name = "i_u_operatetype")
    private int operateType;

    /**
     * 操作者全名
     */
    private String fullname;

    /**
     * 文件名
     */
    private String filename;

    /**
     * 目录
     */
    private String folderPath;

    /**
     * 文档id
     */
    @Index(name = "i_uol_docid")
    private String docId;

    /**
     * 目录id
     */
    private long folderId;

    /**
     * 创建时间
     */
    @Index(name = "i_uol_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 默认构造器
     */
    public UserOperateLog() {
        super();
        creationDate = new Date();
    }

    public String getSummaryFilename30() {
        if (StringUtils.isBlank(filename) || filename.length() < 30) {
            return filename;
        }

        return filename.substring(0, 15) + "..." + filename.substring(filename.length() - 12);
    }

    public String getSummaryFilename() {
        if (StringUtils.isBlank(filename) || filename.length() < 50) {
            return filename;
        }

        return filename.substring(0, 25) + "..." + filename.substring(filename.length() - 22);
    }

    public String getHtmlFilename() {
        if (StringUtils.isBlank(filename) || filename.length() < 50) {
            return filename;
        }

        List<String> filenameArray = TextUtils.splitEqually(filename, 50);
        return StringUtils.join(filenameArray, "<br/>");
    }

    public String getHtmlFilename30() {
        if (StringUtils.isBlank(filename) || filename.length() < 30) {
            return filename;
        }

        List<String> filenameArray = TextUtils.splitEqually(filename, 30);
        return StringUtils.join(filenameArray, "<br/>");
    }

    public String getDocIcon() {
        return DocumentUtils.getDocIcon(FilenameUtils.getExtension(filename));
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public int getOperateType() {
        return operateType;
    }

    public void setOperateType(int operateType) {
        this.operateType = operateType;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getFolderPath() {
        return folderPath;
    }

    public void setFolderPath(String folderPath) {
        this.folderPath = folderPath;
    }

    public long getFolderId() {
        return folderId;
    }

    public void setFolderId(long folderId) {
        this.folderId = folderId;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
