//                    佛祖保佑       永无BUG
//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                           O\  =  /O
//                        ____/`---'\____
//                      .'  \\|     |//  `.
//                     /  \\|||  :  |||//  \
//                    /  _||||| -:- |||||-  \
//                    |   | \\\  -  /// |   |
//                    | \_|  ''\---/''  |   |
//                    \  .-\__  `-`  ___/-. /
//                  ___`. .'  /--.--\  `. . __
//               ."" '<  `.___\_<|>_/___.'  >'"".
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |
//              \  \ `-.   \_ __\ /__ _/   .-` /  /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
package zd.dms.repositories.document;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.FolderArea;

import java.util.List;

public interface FolderAreaRepository extends BaseRepository<FolderArea, Long> {

    List<FolderArea> getDefaultAreas();

    List<FolderArea> getFolderAreasByType(String areaType, Boolean enabled, boolean asc);

    List<FolderArea> getFolderAreasByTypeAndIsEnabledAndIsoArea(String areaType, boolean enabled, boolean isoArea);

    FolderArea getFolderAreaByTreeName(String treeName);

    Page getAllFolderAreaPage(int pageNumber, int pageSize);
}
