package zd.dms.repositories.document.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.Folder;
import zd.dms.entities.FolderSubscribe;
import zd.dms.entities.User;
import zd.dms.repositories.document.FolderSubscribeRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;

public class FolderSubscribeRepositoryDaoImpl extends BaseRepositoryDaoImpl<FolderSubscribe, Long> implements FolderSubscribeRepository {

    public FolderSubscribeRepositoryDaoImpl(Class<FolderSubscribe> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public FolderSubscribe getSubscribe(Folder folder, User user) {
        Specification<FolderSubscribe> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderSubscribe> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("folderId", folder.getId());
            specTools.eq("username", user.getUsername());

            return specTools.getRestriction();
        };

        return findSingleObject(spec);
    }

    @Override
    public void deleteSubscribe(Folder folder, User user) {
        String hql = "delete from FolderSubscribe where folderId = ?1 and username = ?2";
        executeUpdate(hql, folder.getId(), user.getUsername());
    }

    @Override
    public void deleteAllSubscribesByUser(User user) {
        String hql = "delete from FolderSubscribe where username = ?1";
        executeUpdate(hql, user.getUsername());
    }

    @Override
    public void deleteAllSubscribesByFolder(Folder folder) {
        String hql = "delete from FolderSubscribe where folderId = ?1";
        executeUpdate(hql, folder.getId());
    }

    @Override
    public List<FolderSubscribe> getSubscribes(Folder folder) {
        Specification<FolderSubscribe> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderSubscribe> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("folderId", folder.getId());

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public FolderSubscribe getSubscribe(String docId, User user) {
        Specification<FolderSubscribe> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderSubscribe> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("docId", docId);
            specTools.eq("username", user.getUsername());

            return specTools.getRestriction();
        };

        return findSingleObject(spec);
    }

    @Override
    public void deleteAllSubscribesByDocId(String docId) {
        String hql = "delete from FolderSubscribe where docId = ?1";
        executeUpdate(hql, docId);
    }

    @Override
    public List<FolderSubscribe> getSubscribes(String docId) {
        Specification<FolderSubscribe> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderSubscribe> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("docId", docId);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void deleteSubscribe(String docId, User user) {
        String hql = "delete from FolderSubscribe where docId = ?1 and username = ?2";
        executeUpdate(hql, docId, user.getUsername());
    }
}
