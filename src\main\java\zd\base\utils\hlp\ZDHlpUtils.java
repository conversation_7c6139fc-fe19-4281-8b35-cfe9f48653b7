package zd.base.utils.hlp;

import com.alibaba.fastjson.JSON;
import com.hankcs.hanlp.HanLP;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import zd.base.utils.es.ESUtils;
import zd.base.utils.redis.ZDRedisUtils;
import zd.dms.entities.User;
import zd.dms.utils.JSONUtils;
import zd.record.utils.RecordDBUtils;

import java.util.*;

@Slf4j
public class ZDHlpUtils {

    public static List<String> extractPhrase(String content) {
        if (StringUtils.isBlank(content)) {
            return new ArrayList<>();
        }

        try {
            return HanLP.extractPhrase(content, 10);
        } catch (Exception e) {
            log.error("extractPhrase error", e);
        }

        return null;
    }

    public static List<String> extractSummary(String content) {
        if (StringUtils.isBlank(content)) {
            return new ArrayList<>();
        }

        try {
            return HanLP.extractSummary(content, 5);
        } catch (Exception e) {
            log.error("extractSummary error", e);
        }

        return new ArrayList<>();
    }

    public static String getSummary(String content) {
        if (StringUtils.isBlank(content)) {
            return "";
        }

        try {
            return HanLP.getSummary(content, 200);
        } catch (Exception e) {
            log.error("getSummary error", e);
        }

        return "";
    }

    public static boolean updateRecordAndEs(String content, long recId, String tableName) {
        List<String> tags = extractPhrase(content);
        List<String> summarys = extractSummary(content);
        if (tags == null && CollectionUtils.isEmpty(summarys)) {
            return true;
        }

        Map<String, Object> props = new HashMap<>();
        if (tags != null) {
            props.put("ZD_TAGS", JSON.toJSONString(tags));
        }

        if (CollectionUtils.isNotEmpty(summarys)) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < summarys.size(); i++) {
                String summary = summarys.get(i);
                sb.append(i + 1).append(". ").append(summary);
                sb.append("\n");
            }

            props.put("ZD_SUMMARY", sb.toString());
        }

        RecordDBUtils.updateRecord(recId, tableName, props, false);

        return ESUtils.updateRecZDTags(recId, tableName, tags);
    }

    public static void saveReadRecordTags(Map<String, Object> recordData, User currentUser) {
        if (currentUser == null) {
            return;
        }

        String zdTags = MapUtils.getString(recordData, "ZD_TAGS", "");
        if (StringUtils.isBlank(zdTags)) {
            return;
        }

        List<String> tagsList = JSONUtils.parseObject(zdTags, List.class);
        if (CollectionUtils.isEmpty(tagsList)) {
            return;
        }

        Date date = new Date();
        String username = currentUser.getUsername();

        for (String tag : tagsList) {
            if (StringUtils.isBlank(tag)) {
                continue;
            }

            setUserTags(username, tag);
            incrMonth(username, date, tag);
        }
    }

    public static void setReadRecordHistory(Map<String, Object> recordData, String tableName, User currentUser) {
        if (StringUtils.isBlank(tableName) || currentUser == null || MapUtils.isEmpty(recordData)) {
            return;
        }

        long recId = MapUtils.getLongValue(recordData, "ID", 0L);
        if (recId <= 0) {
            return;
        }

        String indexId = ESUtils.getIndexId(tableName, recId);
        if (StringUtils.isBlank(indexId)) {
            return;
        }

        String key = "rec_read_history_" + currentUser.getUsername();
        List<Object> lrange = ZDRedisUtils.lrange(key, 0, 29);
        if (lrange.contains(indexId)) {
            return;
        }

        ZDRedisUtils.llpush(key, indexId);
        ZDRedisUtils.ltrim(key, 0, 29);
    }

    public static List<String> getReadRecordHistory(User currentUser) {
        if (currentUser == null) {
            return new ArrayList<>();
        }

        String key = "rec_read_history_" + currentUser.getUsername();
        List<Object> lrange = ZDRedisUtils.lrange(key, 0, 29);
        List<String> results = new ArrayList<>();
        for (Object obj : lrange) {
            results.add(obj + "");
        }

        return results;
    }

    private static void setUserTags(String username, String tag) {
        String key = "tags_newest_" + username;
        ZDRedisUtils.llpush(key, tag);
        ZDRedisUtils.ltrim(key, 0, 99);
    }

    private static List<String> getNewestTags(String username) {
        String key = "tags_newest_" + username;

        List<Object> lrange = ZDRedisUtils.lrange(key, 0, 99);
        List<String> results = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(lrange)) {
            for (Object obj : lrange) {
                results.add(obj + "");
            }
        }

        return results;
    }

    private static void incrMonth(String username, Date date, String tag) {
        String month = DateFormatUtils.format(date, "yyyyMM");
        String key = "tags_month_" + username + "_" + month;
        // 缓存时长2个月
        ZDRedisUtils.zincr(key, tag, 1, 2L * 31 * 24 * 60 * 60 * 1000);
    }

    private static Map<String, Double> getMonthTags(String username, Date date) {
        String month = DateFormatUtils.format(date, "yyyyMM");
        String key = "tags_month_" + username + "_" + month;
        return ZDRedisUtils.zreverseRangeWithScores(key, 0, 99);
    }

    public static List<String> getUserTags(User currentUser) {
        String username = currentUser.getUsername();

        List<String> newestTags = getNewestTags(username);
        Set<String> resultsSet = new HashSet<>(newestTags);

        Date date = new Date();
        Map<String, Double> currentMonthTagsWithScore = getMonthTags(username, date);
        resultsSet.addAll(currentMonthTagsWithScore.keySet());

        Map<String, Double> beforeMonthTagsWithScore = getMonthTags(username, DateUtils.addMonths(date, -1));
        resultsSet.addAll(beforeMonthTagsWithScore.keySet());

        return new ArrayList<>(resultsSet);
    }

    public static String getScoreScript(User currentUser) {
        String username = currentUser.getUsername();
        List<String> newestTags = getNewestTags(username);

        StringBuilder sb = new StringBuilder();
        sb.append("double score=_score;");
        sb.append("if(doc['ZD_TAGS'] == null){return score;}");

        // 1. 最近阅读的100个标签，根据阅读顺序先后，分别加算原分值的9.9倍到0倍 公式： _score*(10-(顺序号+1)/10) 则最新一个标签加9.9倍分值
        for (int i = 0; i < newestTags.size(); i++) {
            String tag = newestTags.get(i);
            double weight = 10 - (double) (i + 1) / 10;
            sb.append("if(doc['ZD_TAGS'].indexOf('" + tag + "')>-1){score=score+_score*(" + weight + ")}");
        }

        Date date = new Date();

        // 1. 本月阅读的100个标签，根据阅读次数，分别加算原分值十分之一阅读次数的分值 公式： _score*(阅读次数/10) 即阅读100次则加10倍分值
        double monthWight = 1;
        Map<String, Double> currentMonthTagsWithScore = getMonthTags(username, date);
        for (Map.Entry<String, Double> entry : currentMonthTagsWithScore.entrySet()) {
            String tag = entry.getKey();
            Double value = entry.getValue();

            double weight = (value / 10) * monthWight;
            sb.append("if(doc['ZD_TAGS'].indexOf('" + tag + "')>-1){score=score+_score*(" + weight + ")}");
        }

        // 1. 上月阅读的100个标签，根据阅读次数，分别加算原分值十分之一阅读次数分值的0.6 公式： _score*(阅读次数/10)*0.6 即阅读100次则加(10*0.6)倍分值
        double beforeMonthWight = 0.6;
        Map<String, Double> beforeMonthTagsWithScore = getMonthTags(username, DateUtils.addMonths(date, -1));
        for (Map.Entry<String, Double> entry : beforeMonthTagsWithScore.entrySet()) {
            String tag = entry.getKey();
            Double value = entry.getValue();

            double weight = (value / 10) * beforeMonthWight;
            sb.append("if(doc['ZD_TAGS'].indexOf('" + tag + "')>-1){score=score+_score*(" + weight + ")}");
        }

        // 1. 最近24小时上传的内容，直接在原所有分值的基础上，直接乘10，即10倍所有分值之和
        long curTime = date.getTime();
        sb.append("if(doc['创建时间'] != null){long creationTime=doc['创建时间'].value;if((" + curTime + "L-creationTime)<24*60*60*1000){score=score*10}}");

        sb.append("return score;");

        return sb.toString();
    }

    /*private void recordUserRecentlyRead(Map<String, Object> recordData, User currentUser) {
        // 记录档案阅读记录
        String tagsString = MapUtils.getString(recordData, "标签");

        if (StringUtils.isBlank(tagsString)) {
            return;
        }

        String[] splitTags = tagsString.split(",");
        List<String> tags = List.of(splitTags);

        String username = getCurrentUsername();
        String key = username + ":" + "recentlyRead";


        ZSetOperations<String, Object> ops = redisTemplate.opsForZSet();

        for (String tag : tags) {
            ops.incrementScore(key, tag, 1);
        }
    }*/
}
