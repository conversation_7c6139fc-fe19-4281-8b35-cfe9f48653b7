package zd.dms.services.doctpl.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.dms.entities.DocumentTemplate;
import zd.dms.entities.Group;
import zd.dms.entities.User;
import zd.dms.io.ZDIOUtils;
import zd.dms.repositories.doctpl.DocumentTemplateRepository;
import zd.dms.services.doctpl.DocumentTemplateService;
import zd.dms.services.user.GroupService;

import java.io.File;
import java.io.IOException;
import java.util.LinkedList;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class DocumentTemplateServiceImpl extends BaseJpaServiceImpl<DocumentTemplate, String> implements DocumentTemplateService {

    private final DocumentTemplateRepository documentTemplateRepository;

    private final GroupService groupService;

    @Override
    public BaseRepository<DocumentTemplate, String> getBaseRepository() {
        return documentTemplateRepository;
    }

    @Override
    public void createDocTpl(DocumentTemplate docTpl, File uploadFile) throws IOException {
        documentTemplateRepository.save(docTpl);
        ZDIOUtils.saveDocTpl(docTpl, uploadFile);
    }

    @Override
    public DocumentTemplate getDocTplById(String id) {
        return documentTemplateRepository.get(id);
    }

    @Override
    public List<DocumentTemplate> getAllDocTpls() {
        return documentTemplateRepository.getAll();
    }

    @Override
    public void updateDocTpl(DocumentTemplate docTpl, File uploadFile) throws IOException {
        documentTemplateRepository.update(docTpl);

        if (uploadFile != null) {
            ZDIOUtils.saveDocTpl(docTpl, uploadFile);
        }
    }

    @Override
    public void deleteDocTpl(DocumentTemplate docTpl) throws IOException {
        ZDIOUtils.deleteDocTpl(docTpl);
        documentTemplateRepository.delete(docTpl);
    }

    @Override
    public List<DocumentTemplate> getPermitedTpls(User user, String docType) {
        List<DocumentTemplate> allTpls = getAllDocTpls();
        List<DocumentTemplate> result = new LinkedList<DocumentTemplate>();

        for (DocumentTemplate tpl : allTpls) {
            if (StringUtils.isNotBlank(docType)) {
                if (tpl.getExtension().equalsIgnoreCase(docType.trim())) {
                    if (hasTplPermission(user, tpl)) {
                        result.add(tpl);
                    }
                } else if (docType.indexOf("wps") > -1 &&
                        tpl.getExtension().indexOf("doc") > -1) {
                    log.debug("wps格式，显示出doc模板");
                    if (hasTplPermission(user, tpl)) {
                        result.add(tpl);
                    }
                }
            } else {
                if (hasTplPermission(user, tpl)) {
                    result.add(tpl);
                }
            }
        }

        return result;
    }

    private boolean hasTplPermission(User user, DocumentTemplate tpl) {
        if (user == null) {
            return false;
        }

        if (tpl == null) {
            return false;
        }

        if (tpl.getPermissions().indexOf(user.getId()) > -1) {
            return true;
        }

        String[] permIds = tpl.getPermissions().split(",");
        for (String permId : permIds) {
            if (permId.startsWith("g-")) {
                Group g = groupService.getGroupById(NumberUtils.toLong(permId
                        .substring(2)));
                if (groupService.isUserInGroup(user, g)) {
                    return true;
                }
            }
        }

        return false;
    }

    @Override
    public boolean isValidTpl(String filename) {
        if (StringUtils.isBlank(filename)) {
            return false;
        }

        String extension = FilenameUtils.getExtension(filename);
        return "doc".equalsIgnoreCase(extension) ||
                "docx".equalsIgnoreCase(extension) ||
                "xls".equalsIgnoreCase(extension) ||
                "xlsx".equalsIgnoreCase(extension) ||
                "ppt".equalsIgnoreCase(extension) ||
                "pptx".equalsIgnoreCase(extension) ||
                "wps".equalsIgnoreCase(extension);
    }
}
