package zd.base.tasks;

import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.analysus.ja.dict.ConcurrentDictionary;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 每分钟执行任务类
 */
@Component
@Slf4j
public class ConcurrentDictionaryTask extends AbstractTask {

    @Scheduled(cron = "0/40 * * * * ?")
    @Scheduled(fixedRate = 60000, initialDelay = 60000)
    @Override
    public void work() {
//        log.debug("five minute task run");
        ConcurrentDictionary.checkOnlineUsers();
    }
}
