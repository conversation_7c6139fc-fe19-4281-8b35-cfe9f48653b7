package zd.base.utils.redis;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.ZSetOperations;
import zd.base.utils.system.SpringUtils;
import zd.dms.utils.JSONUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public class ZDRedisUtils {

    private static final ZDRedisTool zdRedisTool = SpringUtils.getBean(ZDRedisTool.class);

    public static final boolean REDIS_ENABLED = true;


    public static boolean set(String key, Object value) {
        if (!REDIS_ENABLED) {
            return true;
        }

        if (StringUtils.isBlank(key)) {
            return true;
        }

        return zdRedisTool.set(key, value);
    }

    public static boolean set(String key, Object value, long time) {
        if (!REDIS_ENABLED) {
            return true;
        }

        if (StringUtils.isBlank(key)) {
            return true;
        }

        return zdRedisTool.set(key, value, time);
    }

    public static Map<String, Object> getMapValue(String key) {
        if (!REDIS_ENABLED) {
            return null;
        }

        String value = getString(key);
        if (StringUtils.isBlank(key)) {
            return null;
        }

        Map<String, Object> map = JSONUtils.parseObject(value, Map.class);
        return map;
    }

    public static List<String> getListStringValue(String key) {
        if (!REDIS_ENABLED) {
            return null;
        }

        String value = getString(key);
        if (StringUtils.isBlank(key)) {
            return null;
        }

        List<String> list = JSONUtils.parseObject(value, List.class);
        return list;
    }

    public static List<Map<String, Object>> getListMapValue(String key) {
        if (!REDIS_ENABLED) {
            return null;
        }

        String value = getString(key);
        if (StringUtils.isBlank(key)) {
            return null;
        }

        List<Map<String, Object>> list = JSONUtils.parseObject(value, List.class);
        return list;
    }

    public static Boolean getBoolean(String key, Boolean defaultValue) {
        if (!REDIS_ENABLED) {
            return defaultValue;
        }

        String value = getString(key);
        if ("true".equals(value)) {
            return true;
        }

        if ("false".equals(value)) {
            return false;
        }

        return defaultValue;
    }

    public static String getString(String key) {
        return getString(key, "");
    }

    public static String getString(String key, String defaultValue) {
        if (!REDIS_ENABLED) {
            return defaultValue;
        }

        Object o = get(key);
        if (o == null) {
            return defaultValue;
        }

        return o + "";
    }

    public static Object get(String key) {
        if (!REDIS_ENABLED) {
            return null;
        }

        if (StringUtils.isBlank(key)) {
            return null;
        }

        return zdRedisTool.get(key);
    }

    public static void delete(String... key) {
        if (!REDIS_ENABLED) {
            return;
        }

        try {
            zdRedisTool.del(key);
        } catch (Exception e) {
            log.warn("delete redis error", e);
        }
    }

    public static void delete(long delayTime, String... key) {
        if (!REDIS_ENABLED) {
            return;
        }

        if (delayTime > 0) {
            try {
                Thread.sleep(delayTime);
            } catch (Exception ignored) {
            }
        }

        try {
            zdRedisTool.del(key);
        } catch (Exception e) {
            log.warn("delete redis error", e);
        }
    }

    public static boolean hasKey(String key) {
        if (!REDIS_ENABLED) {
            return false;
        }

        return zdRedisTool.hasKey(key);
    }

    public static String hgetString(String key, String item) {
        return hgetString(key, item, "");
    }

    public static String hgetString(String key, String item, String defaultValue) {
        Object value = hget(key, item);
        if (value == null) {
            return defaultValue;
        }

        return value + "";
    }

    public static Object hget(String key, String item) {
        if (!REDIS_ENABLED) {
            return null;
        }

        return zdRedisTool.hget(key, item);
    }

    public static boolean hset(String key, String item, Object value) {
        if (!REDIS_ENABLED) {
            return false;
        }

        return zdRedisTool.hset(key, item, value);
    }

    public static boolean hset(String key, String item, Object value, long time) {
        if (!REDIS_ENABLED) {
            return false;
        }

        return zdRedisTool.hset(key, item, value, time);
    }

    public static boolean hHasKey(String key, String item) {
        if (!REDIS_ENABLED) {
            return false;
        }

        return zdRedisTool.hHasKey(key, item);
    }

    public static void hdel(String key, String... item) {
        if (!REDIS_ENABLED) {
            return;
        }

        zdRedisTool.hdel(key, item);
    }

    public static Map<String, Object> hmget(String key) {
        Map<Object, Object> hmget = zdRedisTool.hmget(key);
        Map<String, Object> results = new HashMap<>();
        if (MapUtils.isEmpty(hmget)) {
            return results;
        }

        for (Map.Entry<Object, Object> entry : hmget.entrySet()) {
            results.put(entry.getKey() + "", entry.getValue());
        }

        return results;
    }

    public static boolean hmset(String key, Map<String, Object> map) {
        return zdRedisTool.hmset(key, map);
    }

    public static List<String> scan(String pattern) {
        return zdRedisTool.scan(pattern);
    }

    public static DataType getKeyType(String key) {
        return zdRedisTool.getKeyType(key);
    }

    public static long getExpire(String key) {
        return zdRedisTool.getExpire(key);
    }

    public static void zincr(String key, String item, double delta) {
        zdRedisTool.zincr(key, item, delta);
    }

    public static Double zincr(String key, String item, double delta, long expire) {
        Double zincr = zdRedisTool.zincr(key, item, delta);
        if (expire > 0) {
            zdRedisTool.expire(key, expire);
        }

        return zincr;
    }

    public static Set<Object> zreverseRange(String key, long start, long end) {
        return zdRedisTool.zreverseRange(key, start, end);
    }

    public static Map<String, Double> zreverseRangeWithScores(String key, long start, long end) {
        Map<String, Double> results = new HashMap<>();
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = zdRedisTool.zreverseRangeWithScores(key, start, end);
        for (ZSetOperations.TypedTuple<Object> obj : typedTuples) {
            results.put(obj.getValue() + "", obj.getScore());
        }

        return results;
    }


    public static void llpush(String key, String item) {
        zdRedisTool.llpush(key, item);
    }

    /*public static long lIndexOf(String key, String item) {
        Long l = zdRedisTool.lIndexOf(key, item);
        if (l == null) {
            return -1L;
        }

        return l;
    }*/

    public static List<Object> lrange(String key, long start, long end) {
        return zdRedisTool.lrange(key, start, end);
    }

    public static void ltrim(String key, long start, long end) {
        zdRedisTool.ltrim(key, start, end);
    }

    public static long incr(String key, long delta, long expireSecs) {
        return zdRedisTool.incr(key, delta, expireSecs);
    }

    public static long getIncr(String key) {
        return zdRedisTool.getIncr(key);
    }

    public static void delIncr(String key) {
        zdRedisTool.delIncr(key);
    }

    /**
     * 清空所有缓存
     *
     * @return true成功 false失败
     */
    public static boolean flushAll() {
        return zdRedisTool.flushAll();
    }
}
