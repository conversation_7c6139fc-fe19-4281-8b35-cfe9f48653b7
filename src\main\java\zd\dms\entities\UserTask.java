package zd.dms.entities;

import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractEntity;
import zd.base.utils.ZDMapUtils;
import zd.dms.utils.JSONUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "user_task")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class UserTask extends AbstractEntity {

    public static final String STATUS_SUCCESS = "success";

    public static final String STATUS_WAITING = "waiting";

    public static final String STATUS_FAILED = "failed";

    public static final String STATUS_PROCESSING = "processing";

    @Index(name = "i_usertask_type")
    private String type;

    @Index(name = "i_usertask_username")
    private String username;

    @Index(name = "i_usertask_lhd")
    private Date lastHandleDate;

    @Index(name = "i_usertask_times")
    private int times;

    @Index(name = "i_usertask_status")
    private String status;

    /**
     * 创建时间
     */
    @Index(name = "i_usertask_cd")
    private Date creationDate;

    @Lob
    private String msg;

    @Lob
    private String processMsg;

    @Lob
    private String errorMsg;

    @Transient
    private Map<String, Object> msgMap;

    @Transient
    private Map<String, Object> processMsgMap;

    @Transient
    private Map<String, Object> errorMsgMap;

    @Transient
    private List<Map<String, Object>> errorMsgDataList;

    public Map<String, Object> getMsgMap() {
        if (msgMap == null) {
            msgMap = JSONUtils.parseObject(msg, Map.class, new HashMap<>());
        }

        return msgMap;
    }

    public Map<String, Object> getProcessMsgMap() {
        if (processMsgMap == null) {
            processMsgMap = JSONUtils.parseObject(processMsg, Map.class, new HashMap<>());
        }

        return processMsgMap;
    }

    public Map<String, Object> getErrorMsgMap() {
        initErrorMsgMap();

        return errorMsgMap;
    }

    public List<Map<String, Object>> getErrorMsgDataList() {
        initErrorMsgMap();

        return errorMsgDataList;
    }

    private void initErrorMsgMap() {
        if (errorMsgMap == null) {
            errorMsgMap = JSONUtils.parseObject(errorMsg, Map.class, new HashMap<>());
            errorMsgDataList = ZDMapUtils.getListMapValue(errorMsgMap, "errorDataList");
        }
    }
}
