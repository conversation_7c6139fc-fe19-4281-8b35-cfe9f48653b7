package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractEntity;
import zd.dms.utils.TextUtils;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "mailserver")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class MailServerInfo extends AbstractEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = -3048856269255959119L;

    private String host;

    private int port;

    private String username;

    private String password;

    private boolean useSsl;

    private Boolean useTls;

    private boolean enabled;

    private String fromAddr;

    private int maxSendPerMin;

    /**
     * 创建时间
     */
    @Index(name = "i_msi_cd")
    @Column(nullable = false)
    private Date creationDate;

    public MailServerInfo() {
        super();
        this.useSsl = false;
        this.enabled = true;
        this.maxSendPerMin = 1;
        creationDate = new Date();
    }

    public String getHost() {
        return TextUtils.escapeXml(host);
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getUsername() {
        return TextUtils.escapeXml(username);
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isUseSsl() {
        return useSsl;
    }

    public void setUseSsl(boolean useSsl) {
        this.useSsl = useSsl;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getFromAddr() {
        return fromAddr;
    }

    public void setFromAddr(String from) {
        this.fromAddr = from;
    }

    public int getMaxSendPerMin() {
        return maxSendPerMin;
    }

    public void setMaxSendPerMin(int maxSendPerMin) {
        this.maxSendPerMin = maxSendPerMin;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Boolean getUseTls() {
        if (useTls == null) {
            return false;
        }

        return useTls;
    }

    public void setUseTls(Boolean useTls) {
        this.useTls = useTls;
    }
}
