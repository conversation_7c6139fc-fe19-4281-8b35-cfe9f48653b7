package zd.base.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 系统工具类
 */
public final class ZDDateUtils {

    /**
     * commons logging
     */
    private static final Logger log = LoggerFactory.getLogger(ZDDateUtils.class);

    /**
     * private constructor
     */
    private ZDDateUtils() {
    }

    public static Date parseDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        try {
            return DateUtils.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM-dd");
        } catch (ParseException e) {
            log.error("parseDate error", e);
        }

        return null;
    }

    public static String getDisplayDate(int min) {
        int days = min / (60 * 24);
        int hours = min % (60 * 24) / 60;
        int minutes = min % (60);

        StringBuilder sb = new StringBuilder();
        if (days > 0) {
            sb.append(days + "天");
        }
        if (hours > 0) {
            sb.append(hours + "小时");
        }
        if (minutes > 0) {
            sb.append(minutes + "分钟");
        }

        if (sb.length() == 0) {
            sb.append(minutes + "分钟");
        }

        return sb.toString();
    }

    public static int betweenInMinute(Date newDate, Date oldDate) {
        long date2m = oldDate.getTime();
        long date1m = newDate.getTime();

        long between = date1m - date2m;

        return Long.valueOf(between / 1000 / 60).intValue();
    }

    public static int betweenInHour(Date newDate, Date oldDate) {
        long date2m = oldDate.getTime();
        long date1m = newDate.getTime();

        long between = date1m - date2m;

        return Long.valueOf(between / 1000 / 3600).intValue();
    }

    public static int betweenInDay(Date newDate, Date oldDate) {
        long date2m = oldDate.getTime();
        long date1m = newDate.getTime();

        long between = date1m - date2m;

        return Long.valueOf(between / 1000 / 3600 / 24).intValue();
    }

    public static Date getStartDateByString(String startDateStr) {
        Date startDate = null;

        if (StringUtils.isNotBlank(startDateStr)) {
            startDateStr = startDateStr + " 00:00:00";
            try {
                startDate = DateUtils.parseDate(startDateStr, "yyyy-MM-dd HH:mm:ss");
            } catch (ParseException e) {
                log.error("解析时间出错：{}", startDateStr, e);
            }
            log.debug("StartDate: {}", startDate);
        }

        return startDate;
    }

    public static Date getEndDateByString(String endDateStr) {
        Date endDate = null;

        if (StringUtils.isNotBlank(endDateStr)) {
            endDateStr = endDateStr + " 23:59:59";
            try {
                endDate = DateUtils.parseDate(endDateStr, "yyyy-MM-dd HH:mm:ss");
            } catch (ParseException e) {
                log.error("解析时间出错：{}", endDateStr, e);
            }
            log.debug("EndDate: {}", endDate);
        }

        return endDate;
    }

    /**
     * 获取小时，结果为：00 01 01......23
     *
     * @return
     */
    public static List<String> getHourList() {
        List<String> hourList = new ArrayList<String>();

        for (int i = 0; i <= 23; i++) {
            hourList.add(StringUtils.leftPad(i + "", 2, "0"));
        }

        return hourList;
    }

    /**
     * 获取分钟，结果为：00 01 01......59
     *
     * @return
     */
    public static List<String> getMinuteList() {
        List<String> minuteList = new ArrayList<String>();

        for (int i = 0; i <= 59; i++) {
            minuteList.add(StringUtils.leftPad(i + "", 2, "0"));
        }

        return minuteList;
    }

    /**
     * 获取当前时间的小时数
     *
     * @return
     */
    public static String getCurrentHour() {
        String format = DateFormatUtils.format(new Date(), "HH:mm");
        return StringUtils.substringBefore(format, ":");
    }

    /**
     * 获取当前时间的分钟数
     *
     * @return
     */
    public static String getCurrentMinute() {
        String format = DateFormatUtils.format(new Date(), "HH:mm");
        return StringUtils.substringAfter(format, ":");
    }

    public static String format(Date date, String pattern) {
        if (date == null) {
            return "";
        }

        if (StringUtils.isBlank(pattern)) {
            pattern = "yyyy-MM-dd HH:mm:ss";
        }

        try {
            return DateFormatUtils.format(date, pattern);
        } catch (Exception e) {
            log.error("format error", e);
        }

        return "";
    }

    public static String format(Date date) {
        return format(date, null);
    }
}
