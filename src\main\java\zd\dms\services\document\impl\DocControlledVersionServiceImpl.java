package zd.dms.services.document.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.dms.entities.DocumentControlledVersion;
import zd.dms.repositories.document.DocumentControlledVersionRepository;
import zd.dms.services.document.DocControlledVersionService;

@RequiredArgsConstructor
@Service
@Slf4j
public class DocControlledVersionServiceImpl extends BaseJpaServiceImpl<DocumentControlledVersion, Long> implements DocControlledVersionService {

    private final DocumentControlledVersionRepository documentControlledVersionRepository;

    @Override
    public BaseRepository<DocumentControlledVersion, Long> getBaseRepository() {
        return documentControlledVersionRepository;
    }

    @Override
    public void save(DocumentControlledVersion documentControlledVersion) {
        documentControlledVersionRepository.save(documentControlledVersion);
    }

    @Override
    public DocumentControlledVersion getDocControlledVersion(String docId, int revisionNumber, String publishVersion) {
        if (StringUtils.isBlank(docId)) {
            log.error("getDocControlledVersion docId null");
            return null;
        }

        return documentControlledVersionRepository.getDocControlledVersion(docId, revisionNumber, publishVersion);
    }
}
