package zd.dms.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import zd.dms.entities.MybatisUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;
import java.util.Map;

@Mapper
public interface UserMapper extends BaseMapper<MybatisUser> {

    @SelectProvider(value = TableSqlUtils.class, method = "selectRecord")
    List<Map<String, Object>> selectRecord(@Param("tableName") String tableName, @Param("id") int id);
}
