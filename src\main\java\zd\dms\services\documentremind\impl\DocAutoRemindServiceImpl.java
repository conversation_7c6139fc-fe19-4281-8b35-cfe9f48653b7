package zd.dms.services.documentremind.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.dms.entities.*;
import zd.dms.repositories.document.DocAutoRemindRepository;
import zd.dms.repositories.document.DocumentRepository;
import zd.dms.services.docprop.DocPropDataColService;
import zd.dms.services.documentremind.DocAutoRemindService;
import zd.dms.services.mail.MailService;
import zd.dms.services.propdata.DocAdvPropsDBUtils;
import zd.dms.services.user.GroupService;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.services.user.UserService;
import zd.dms.utils.DocDBUtils;
import zd.dms.workflow.engine.ProcessService;
import zd.dms.workflow.entities.SSProcessInstance;

import java.util.*;

@RequiredArgsConstructor
@Service
@Slf4j
public class DocAutoRemindServiceImpl extends BaseJpaServiceImpl<DocAutoRemind, Long> implements DocAutoRemindService {

    private final DocAutoRemindRepository docAutoRemindRepository;

    private final UserService userService;

    private final GroupService groupService;

    private final DocPropDataColService docPropDataColService;

    private final DocumentRepository documentRepository;

    private final MailService mailService;

    private final ProcessService processService;

    @Override
    public BaseRepository<DocAutoRemind, Long> getBaseRepository() {
        return docAutoRemindRepository;
    }

    @Override
    public void doAutoReminds() {
        List<DocAutoRemind> allAutoReminds = getAllAutoReminds();
        for (DocAutoRemind ar : allAutoReminds) {
            String colName = ar.getColName();
            long folderId = ar.getFolderId();
            int advanceDay = ar.getAdvanceDay();

            log.debug("开始自动提醒：{}, 提醒字段: {}, 提前天数:{}",
                    new Object[]{ar.getTitle(), ar.getColName(), ar.getAdvanceDay()});

            boolean sendToPiCreator = false;
            boolean sendToDocCreator = false;

            String[] remindUserGroupsArray = ar.getRemindUserGroups().split(",");
            Set<User> remindUsers = new HashSet<User>();
            for (String ca : remindUserGroupsArray) {
                if (ca.trim().startsWith("u-")) {
                    String canUsername = ca.trim().substring(2);
                    User user = userService.getUserByIdOrUsername(canUsername);
                    if (user != null && user.getEnabled()) {
                        remindUsers.add(user);
                        log.debug("doAutoReminds: 候选人为user类型，用户名：{}", canUsername);
                    }
                } else if (ca.trim().startsWith("g-")) {
                    if (ca.trim().contains("-999998")) {
                        // 流程发起人
                        sendToPiCreator = true;
                    } else if (ca.trim().contains("-999997")) {
                        // 文档chuangjianr
                        sendToDocCreator = true;
                    } else {
                        Group g = groupService.getGroupById(NumberUtils.toLong(ca.trim().substring(2)));
                        if (g != null) {
                            for (User u : g.getUsers()) {
                                if (u != null && u.getEnabled()) {
                                    remindUsers.add(u);
                                }
                            }
                            log.debug("doAutoReminds: 候选人为group类型，部门名：{}", g.getName());
                        }
                    }
                }
            }

            boolean isDocCol = false;
            if ("创建时间".equals(colName)) {
                isDocCol = true;
                colName = "creationDate";
            } else if ("更新时间".equals(colName)) {
                isDocCol = true;
                colName = "modifiedDate";
            }

            // 如果是创建的属性字段，则判断该属性字段是否存在，不存在则直接跳过
            if (!isDocCol) {
                DocPropDataCol docPropDataCol = docPropDataColService.getDocPropDataColByName(colName);
                if (docPropDataCol == null) {
                    log.debug("doAutoReminds docPropDataColIsNull colName:{}", colName);
                    continue;
                }
            }

            List<String> recs = new LinkedList<String>();

            String extraWhere = DocDBUtils.doAutoRemindsSql(advanceDay, colName, ar);

            Map<String, List<String>> remindPiCreatorMap = new HashMap<String, List<String>>();
            Map<String, List<String>> remindDocCreatorMap = new HashMap<String, List<String>>();
            try {
                if (isDocCol) {
                    String searchSql = " deleted = 0 " + extraWhere;
                    List<Document> documnets = documentRepository.getDocumentByFolderAndSql(folderId, searchSql);
                    for (Document document : documnets) {
                        recs.add(document.getShortLinkUrl());
                        putUserToRemindMap(sendToPiCreator, sendToDocCreator, remindPiCreatorMap, remindDocCreatorMap,
                                document);
                    }
                } else {
                    List<String> docIds = DocAdvPropsDBUtils.getDocIdByFolderIdAndSQL(folderId, extraWhere, null);
                    for (String docId : docIds) {
                        Document document = documentRepository.get(docId);
                        if (document != null && !document.isDeleted()) {
                            recs.add(document.getShortLinkUrl());
                            putUserToRemindMap(sendToPiCreator, sendToDocCreator, remindPiCreatorMap,
                                    remindDocCreatorMap, document);
                        }
                    }
                }
            } catch (Throwable t) {
                log.error("doc doAutoReminds getDocIdByFolderIdAndSQLError", t);
                continue;
            }

            if (recs.size() <= 0) {
                log.debug("doAutoReminds, 无文档，跳过: {}", ar.getTitle());
                continue;
            }

            User adminUser = new User();
            adminUser.setUsername(UserGroupUtils.ADMIN_USERNAME);
            adminUser.setFullname("系统管理员");
            for (User u : remindUsers) {
                mailService.sendMessageAndMailForDocAutoReminds(ar.getTitle(), recs, ar.getContent(),
                        InstantMessage.TYPE_REMIND, adminUser, u);
            }

            // 提醒流程发起人
            if (remindPiCreatorMap.size() > 0) {
                Set<Map.Entry<String, List<String>>> entrySet = remindPiCreatorMap.entrySet();
                for (Map.Entry<String, List<String>> entry : entrySet) {
                    String key = entry.getKey();
                    if (StringUtils.isBlank(key)) {
                        continue;
                    }

                    User u = userService.getUserByUsername(key);
                    List<String> value = entry.getValue();
                    if (u != null && value != null && value.size() > 0) {
                        mailService.sendMessageAndMailForDocAutoReminds(ar.getTitle(), value, ar.getContent(),
                                InstantMessage.TYPE_REMIND, adminUser, u);
                    }
                }
            }

            // 提醒文档创建人
            if (remindDocCreatorMap.size() > 0) {
                Set<Map.Entry<String, List<String>>> entries = remindDocCreatorMap.entrySet();
                for (Map.Entry<String, List<String>> entry : entries) {
                    String key = entry.getKey();
                    if (StringUtils.isBlank(key)) {
                        continue;
                    }

                    User u = userService.getUserByUsername(key);
                    List<String> value = entry.getValue();
                    if (u != null && value != null && value.size() > 0) {
                        mailService.sendMessageAndMailForDocAutoReminds(ar.getTitle(), value, ar.getContent(),
                                InstantMessage.TYPE_REMIND, adminUser, u);
                    }
                }
            }
        }
    }

    private void putUserToRemindMap(boolean sendToPiCreator, boolean sendToDocCreator,
                                    Map<String, List<String>> remindPiCreatorMap, Map<String, List<String>> remindDocCreatorMap,
                                    Document document) {
        if (document == null) {
            return;
        }

        if (sendToPiCreator) {
            String piId = document.getPiId();
            if (StringUtils.isBlank(piId)) {
                return;
            }

            SSProcessInstance pi = processService.getProcessInstance(piId);
            if (pi != null) {
                String creator = pi.getCreator();
                if (StringUtils.isNotBlank(creator)) {
                    if (remindPiCreatorMap.containsKey(creator)) {
                        List<String> list = remindPiCreatorMap.get(creator);
                        list.add(document.getShortLinkUrl());
                    } else {
                        List<String> list = new ArrayList<String>();
                        list.add(document.getShortLinkUrl());
                        remindPiCreatorMap.put(creator, list);
                    }
                }
            }
        }

        if (sendToDocCreator) {
            String creator = document.getCreator();
            if (StringUtils.isNotBlank(creator)) {
                if (remindDocCreatorMap.containsKey(creator)) {
                    List<String> list = remindDocCreatorMap.get(creator);
                    list.add(document.getShortLinkUrl());
                } else {
                    List<String> list = new ArrayList<String>();
                    list.add(document.getShortLinkUrl());
                    remindDocCreatorMap.put(creator, list);
                }
            }
        }
    }

    @Override
    public void deleteById(long id) {
        docAutoRemindRepository.deleteById(id);
    }

    @Override
    public void deleteAllByFolderId(long folderId) {
        docAutoRemindRepository.deleteAllByFolderId(folderId);
    }

    @Override
    public List<DocAutoRemind> getRemindsByFolderId(long folderId) {
        return docAutoRemindRepository.getRemindsByFolderId(folderId);
    }

    @Override
    public List<DocAutoRemind> getAllAutoReminds() {
        return docAutoRemindRepository.getAll();
    }

    @Override
    public void updateAutoRemind(DocAutoRemind ar) {
        docAutoRemindRepository.update(ar);
    }

    @Override
    public DocAutoRemind getAutoRemind(long id) {
        return docAutoRemindRepository.get(id);
    }

    @Override
    public void createAutoRemind(DocAutoRemind ar) {
        docAutoRemindRepository.save(ar);
    }
}
