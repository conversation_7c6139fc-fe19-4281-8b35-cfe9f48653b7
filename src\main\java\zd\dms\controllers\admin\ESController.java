package zd.dms.controllers.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapUtils;
import zd.base.utils.es.ESDataUtils;
import zd.base.utils.es.ESUtils;
import zd.dms.dto.PageResponse;
import zd.dms.entities.Role;
import zd.dms.services.security.FolderPermissionUtils;
import zd.dms.services.security.RoleUtils;
import zd.dms.utils.JSONUtils;
import zd.record.entities.RecFolderPermission;
import zd.record.service.security.RecFolderPermissionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "ESController", description = "索引Controller")
@RequestMapping("/admin/es")
public class ESController extends ControllerSupport {

    @Operation(summary = "重建索引")
    @GetMapping("/reindex")
    @ZDLog("重建索引")
    public JSONResultUtils<Object> reindex() {
        ESDataUtils.createDefaultIndex();
        return success();
    }

    @Operation(summary = "搜索数据")
    @PostMapping("/searchRecords")
    @ZDLog("搜索数据")
    public JSONResultUtils<Object> searchRecords(@RequestBody Map<String, Object> params) {
        String keywords = MapUtils.getString(params, "keywords", "");
        if (StringUtils.isEmpty(keywords)) {
            return error("请输入关键字");
        }

        String dataType = MapUtils.getString(params, "dataType", "");

        int pageNumber = MapUtils.getIntValue(params, "pageNumber", 1);
        int pageSize = MapUtils.getIntValue(params, "pageSize", 30);

        Map<String, Object> searchParams = ESDataUtils.getSearchParams(keywords, pageNumber, pageSize);

        List<Long> recFolderIdsByPerm = null;
        List<Long> docFolderIdsByPerm = null;
        if (!RoleUtils.isUserInRole(getCurrentUser(), Role.SYSTEM_ADMIN)) {
            recFolderIdsByPerm = RecFolderPermissionUtils.getRecFolderIdsByPermWithRedis(getCurrentUser(), RecFolderPermission.LIST);
            if (CollectionUtils.isEmpty(recFolderIdsByPerm)) {
                recFolderIdsByPerm.add(-1L);
            }

            docFolderIdsByPerm = FolderPermissionUtils.getFolderIdsByPermWithRedis(getCurrentUser(), RecFolderPermission.LIST);
            if (CollectionUtils.isEmpty(docFolderIdsByPerm)) {
                docFolderIdsByPerm.add(-1L);
            }
        }

        Page searchDocument = ESUtils.searchDocument(ESUtils.RECORD_ES_INDEX, searchParams, recFolderIdsByPerm, docFolderIdsByPerm, null, 0, "",
                null, dataType, pageNumber, pageSize);
        return successData(PageResponse.ofMap(searchDocument));
    }

    @Operation(summary = "获取所有索引")
    @GetMapping("/getAllIndex")
    @ZDLog("获取所有索引")
    public JSONResultUtils<Object> getAllIndex() {
        return JSONResultUtils.successWithData(ESUtils.getAllIndex());
    }

    @Operation(summary = "获取索引mapping")
    @GetMapping("/getIndexMapping")
    @ZDLog("获取索引mapping")
    public JSONResultUtils<Object> getIndexMapping(@RequestParam String indexName) {
        List<Map<String, Object>> mappings = new ArrayList<>();

        String mapping = ESUtils.getMapping(indexName);
        if (StringUtils.isBlank(mapping)) {
            return JSONResultUtils.successWithData(mappings);
        }

        Map<String, Object> map = JSONUtils.parseObject(mapping, Map.class);
        Map<String, Object> recordMap = ZDMapUtils.getMapVlaue(map, indexName);
        Map<String, Object> mappingsMap = ZDMapUtils.getMapVlaue(recordMap, "mappings");
        Map<String, Object> propertiesMap = ZDMapUtils.getMapVlaue(mappingsMap, "properties");
        if (propertiesMap == null) {
            return JSONResultUtils.successWithData(mappings);
        }

        for (Map.Entry<String, Object> entry : propertiesMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (!(value instanceof Map)) {
                continue;
            }

            Map<String, Object> dataColMap = new HashMap<>();
            dataColMap.put("name", key);

            Map<String, Object> tempMap = (Map<String, Object>) value;
            dataColMap.put("type", MapUtils.getString(tempMap, "type", ""));
            dataColMap.put("fields", MapUtils.getString(tempMap, "fields", ""));
            dataColMap.put("analyzer", MapUtils.getString(tempMap, "analyzer", ""));
            dataColMap.put("search_analyzer", MapUtils.getString(tempMap, "search_analyzer", ""));

            mappings.add(dataColMap);
        }

        return JSONResultUtils.successWithData(mappings);
    }
}
