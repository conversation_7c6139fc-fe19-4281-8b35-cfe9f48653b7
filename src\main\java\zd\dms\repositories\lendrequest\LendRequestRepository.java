package zd.dms.repositories.lendrequest;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.LendRequest;

import java.util.List;

public interface LendRequestRepository extends BaseRepository<LendRequest, Long> {

    Page getRequestsByCreator(int pageNumber, int pageSize, String creator);

    Page getApprovedRequestsByUsername(int pageNumber, int pageSize,
                                       String approver);

    List<LendRequest> getUnapprovedRequests(String username);

    int getUnapprovedCount(String username);
}
