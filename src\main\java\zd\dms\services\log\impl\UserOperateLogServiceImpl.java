package zd.dms.services.log.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.dms.entities.UserOperateLog;
import zd.dms.repositories.log.UserOperateLogRepository;
import zd.dms.services.log.UserOperateLogService;

import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class UserOperateLogServiceImpl extends BaseJpaServiceImpl<UserOperateLog, Long> implements UserOperateLogService {

    private final UserOperateLogRepository userOperateLogRepository;

    @Override
    public BaseRepository<UserOperateLog, Long> getBaseRepository() {
        return userOperateLogRepository;
    }

    @Override
    public List<UserOperateLog> getLogs(String username, int type) {
        return userOperateLogRepository.getLogs(username, type);
    }

    @Override
    public List<UserOperateLog> getLogs(int type, int startIndex, int pageSize) {
        return userOperateLogRepository.getLogs(type, startIndex, pageSize);
    }

    @Override
    public void createOpLog(String operator, String operatorFullname, int operateType, String filename, String folderPath, String docId, long folderId) {
        userOperateLogRepository.createUserOpLog(operator, operatorFullname, operateType, filename, folderPath, docId, folderId);
    }

    @Override
    public void deleteOutdatedLogs(String operator, int operateType) {
        try {
            userOperateLogRepository.deleteOutdatedLogs(operator, operateType);
        } catch (Throwable t) {
            log.error("deleteOutdatedLogs ex", t);
        }
    }

    @Override
    public void deleteAllUserOperateLogs(String operator) {
        try {
            userOperateLogRepository.deleteAllUserOperateLogs(operator);
        } catch (Throwable t) {
            log.error("deleteAllUserOperateLogs ex", t);
        }
    }

    @Override
    public void deleteLogByUserAndTypeAndDocId(String operator, int operateType, String docId) {
        try {
            userOperateLogRepository.deleteLogByUserAndTypeAndDocId(operator,
                    operateType, docId);
        } catch (Throwable t) {
            log.error("deleteLogByUserAndTypeAndDocId ex", t);
        }
    }

    @Override
    public void deleteLogByDocId(String docId) {
        userOperateLogRepository.deleteLogByDocId(docId);
    }
}
