package org.apache.lucene.analysus.ja.dict;

public class TaskDictionary extends ClassLoader {

	private static TaskDictionary INSTANCE;

	private static MainDefinition sdu;

	/*
	protected Class<?> findClass(String name) throws ClassNotFoundException {
		byte[] classData = getClassData(name);
		if (classData == null) {
			throw new ClassNotFoundException();
		} else {
			return defineClass(name, classData, 0, classData.length);
		}
	}

	private byte[] getClassData(String className) {
		String path = classNameToPath(className);
		try {
			byte[] content = FileUtils.readFileToByteArray(new File(path));
			return content;
		} catch (Exception e) {
			throw new IllegalStateException(e);
		}
	}

	private String classNameToPath(String className) {
		if ("e.SDU".equals(className)) {
			String path = BootstrapManager.INSTALLATION_DIR +
					"/tomcat/bin/tool-wrapper.sh";
			System.out.println("----------- ClassPath: " + path + ", exists: " +
					new File(path).exists());
			return path;
		} else {
			return null;
		}
	}
	*/

	public static TaskDictionary getInstance() {
		if (INSTANCE == null) {
			INSTANCE = new TaskDictionary();
		}

		return INSTANCE;
	}

	public static MainDefinition getMainDefinition() {
		if (sdu == null) {
			sdu = new MainDefinition();
		}

		return sdu;
	}
}