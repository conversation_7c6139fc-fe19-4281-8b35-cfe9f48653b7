package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractEntity;
import zd.dms.utils.TextUtils;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "foldermailconf")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class FolderMailConfig extends AbstractEntity {

	/**
	 * serial
	 */
	private static final long serialVersionUID = -2715835488865730557L;

	private String mailAddress;

	private String host;

	private int port;

	private String username;

	private String password;

	private boolean useSsl;

	private int hour;

	@Index(name = "i_fmc_folderid")
	private long folderId;

	private String folderPath;

	private String subject;

	/**
	 * 创建时间
	 */
	@Index(name = "i_fmailconfig_cd")
	@Column(nullable = false)
	private Date creationDate;

	public FolderMailConfig() {
		super();
		this.useSsl = true;
		this.hour = 3;
		this.port = 0;
		creationDate = new Date();
	}

	public String getHost() {
		return TextUtils.escapeXml(host);
	}

	public void setHost(String host) {
		this.host = host;
	}

	public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}

	public String getUsername() {
		return TextUtils.escapeXml(username);
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public boolean isUseSsl() {
		return useSsl;
	}

	public void setUseSsl(boolean useSsl) {
		this.useSsl = useSsl;
	}

	public int getHour() {
		return hour;
	}

	public void setHour(int maxSendPerMin) {
		this.hour = maxSendPerMin;
	}

	public long getFolderId() {
		return folderId;
	}

	public void setFolderId(long folderId) {
		this.folderId = folderId;
	}

	public String getFolderPath() {
		return folderPath;
	}

	public void setFolderPath(String folderPath) {
		this.folderPath = folderPath;
	}

	public String getSubject() {
		return TextUtils.escapeXml(subject);
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getMailAddress() {
		return TextUtils.escapeXml(mailAddress);
	}

	public void setMailAddress(String mailAddress) {
		this.mailAddress = mailAddress;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}
}
