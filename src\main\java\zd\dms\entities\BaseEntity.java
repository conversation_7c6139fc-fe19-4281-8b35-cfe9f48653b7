package zd.dms.entities;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import zd.base.utils.IdGenerator;
import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.GenericGenerator;

@Getter
@Setter
@MappedSuperclass
public class BaseEntity {

  @TableId(type = IdType.ASSIGN_ID)
  @Id
  @GenericGenerator(name = "SnowflakeGenerator", type = IdGenerator.class)
  @GeneratedValue(generator = "SnowflakeGenerator")
  @Column(name = "id", nullable = false)
  private Long id;
}
