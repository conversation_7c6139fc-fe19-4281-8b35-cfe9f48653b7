package zd.dms.services.document;

import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import zd.base.service.BaseJpaService;
import zd.base.utils.JSONResultUtils;
import zd.dms.dto.document.DocumentUploadDto;
import zd.dms.entities.*;
import zd.dms.services.document.exception.*;
import zd.record.dto.record.AttachmentUploadDto;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Transactional
public interface DocumentService extends BaseJpaService<Document, String> {

    JSONResultUtils<Object> saveUploadDocument(MultipartFile uploadFile, DocumentUploadDto documentUploadDto, User user);

    JSONResultUtils<Object> saveUploadAttachment(List<MultipartFile> uploadFile, AttachmentUploadDto attachmentUploadDto, User user);

    JSONResultUtils<Object> saveUpdateDocument(MultipartFile uploadFiles, String docId, String filename, String comment, User user);

    Page<Document> getDocumentsWithoutAttach(int pageNumber, int pageSize);

    Page<Document> list(Map<String, Object> params, User user);

    Page<Document> list(long folderId, boolean isTrash, String keywords, Map<String, Object> advSearchKeywords, String sortField, boolean asc, User user, int pageNumber, int pageSize);

    void copyVersionToOtherDoc(String oriDocId, int oriDocVersion, String targetDocSn, String versionComment,
                               String creatorFullname, boolean copyWorkflow, String ip, boolean checkPerm, boolean addLog)
            throws DocumentNotFoundException, DifferentExtensionException, InWorkflowException;

    void addRecordIdAndTable(Document document, long recId, String table);

    String getUniqueFilename(String docUploadFileName, Folder folder);

    void clearPi(Document document, User operator, String ip);

    void createDocumentRelation(String docId, String docSerialNumber, User operator, String ipAddress);

    @Transactional(readOnly = true)
    List<DocumentRelation> getDocumentRelations(Document doc);

    @Transactional(readOnly = true)
    List<Map> getRelatedDocuments(Document doc);

    Document getDocumentBySerial(String serial);

    List<Document> getDocumentsByExtension(String extension);

    void deleteRelation(Document doc, Document docRelated);

    void deleteDocument(Document doc);

    List<String> moveDocsToTrash(long folderId, String[] ids, User user, String ip);

    void restoreDocs(String[] ids, User user, String ip);

    void deleteDocs(String[] ids, User user, String ip, boolean byUser);

    List<String> moveDocs(Folder folder, String[] ids, User user, String ip, boolean fromRuleManager);

    Document copyDoc(Document oriDoc, String newFilename, long folderId) throws IOException,
            DocumentAmountExceededException, ForbidExtException;

    void saveNewDoc(Document document, File uploadFile) throws IOException, DocumentAmountExceededException,
            ForbidExtException;

    void saveNewDoc(Document document, File file, Folder uploadedFolder) throws IOException,
            DocumentAmountExceededException, ForbidExtException;

    void saveDoc(Document document, File uploadFile, String creatorFullname, String versionComment, boolean addNewVersion)
            throws IOException;

    void saveDocForTemp(Document document, File uploadFile, String creatorFullname, int version, String versionComment)
            throws IOException;

    void rollbackVersion(Document document, String creator, String creatorFullname, int versionNumber, String comment)
            throws IOException;

    @Transactional(readOnly = true)
    int getVersionCount(Document document);

    @Transactional(readOnly = true)
    List<DocumentVersion> getVersions(Document document, boolean withTempVersion, boolean asc);

    @Transactional(readOnly = true)
    DocumentVersion getVersion(Document document, int version);

    @Transactional(readOnly = true)
    List<Document> getDocumentsByFolder(Folder folder, String sortField, boolean asc);

    @Transactional(readOnly = true)
    List<Document> getDocumentsByFolder(Folder folder);

    @Transactional(readOnly = true)
    List<Document> getDocumentsInTrash(String username, int type);

    @Transactional(readOnly = true)
    Page getAllTrash(int pageNumber, int pageSize);

    @Transactional(readOnly = true)
    int getDocumentCountByFolder(Folder folder);

    @Transactional(readOnly = true)
    int getDocumentCountByFolderWithDeleted(Folder folder);

    @Transactional(readOnly = true)
    boolean isDocumentInFolder(String filename, Folder folder, Document oriDoc);

    @Transactional(readOnly = true)
    Document getDocumentById(String id);

    void deleteVersion(Document document, int version);

    @Transactional(readOnly = true)
    List<DocumentNote> getDocumentNotes(Document doc);

    void createDocumentNote(DocumentNote note);

    void deleteDocumentNote(DocumentNote note);

    void deleteDocumentNote(long id);

    @Transactional(readOnly = true)
    DocumentNote getDocumentNote(Long id);

    DocumentLog addLog(Document doc, int type, String msg, String operator, String fullname, String ipAddress);

    void addLogFromAdmin(Document doc, int type, String msg);

    @Transactional(readOnly = true)
    List<DocumentLog> getLogs(Document doc, int type);

    @Transactional(readOnly = true)
    List<DocumentLog> getTop100Logs(Document doc);

    void deleteLogsBeforeDays(int days);

    @Transactional(readOnly = true)
    Page getDocumentLog(int pageNumber, int pageSize, int type, String username, Date startDate, Date endDate);

    @Transactional(readOnly = true)
    DocumentVersion getNewestDocumentVersion(Document doc);

    void updateVersion(DocumentVersion version);

    void updateDocument(Document doc);

    void updateDocument(Document doc, boolean updateModifiedDate);

    void incrementDocumentClickCount(Document doc);

    void resetEditingDocument(String username, String fullname);

    void resetAllEditingDocument();

    List<Document> getDocumentLogByUser(String username, int logType, Date afterDate);

    List<Document> getUploadDocumentLogByUser(String username, Date afterDate);

    void copyDocProps(User operator, String oriSn, Document targetDoc);
}
