package zd.dms.repositories.rule.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.FolderRule;
import zd.dms.repositories.rule.FolderRuleRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.List;

public class FolderRuleRepositoryDaoImpl extends BaseRepositoryDaoImpl<FolderRule, String> implements FolderRuleRepository {

    public FolderRuleRepositoryDaoImpl(Class<FolderRule> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<FolderRule> getRulesByFolder(long folderId) {
        Specification<FolderRule> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderRule> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("folderId", folderId);
            specTools.asc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<FolderRule> getRulesByFolderAndType(long folderId, String type) {
        Specification<FolderRule> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderRule> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.like("actionType", type);
            specTools.eq("folderId", folderId);
            specTools.eq("enabled", true);
            specTools.asc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void deleteRulesByFolder(long folderId) {
        String hql = "delete from FolderRule where folderId = ?1";
        executeUpdate(hql, folderId);
    }

    @Override
    public Page getAllRules(int page, int pageSize) {
        Specification<FolderRule> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<FolderRule> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.asc("folderId");

            return specTools.getRestriction();
        };

        return findAll(spec, page, pageSize);
    }
}
