package zd.dms.entities;

import jakarta.persistence.*;
import org.hibernate.Length;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.entities.AbstractEntity;
import zd.base.entities.PropertyAware;
import zd.dms.services.rule.condition.ConditionUtils;
import zd.dms.utils.PropertyUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "folder_rule_condition")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class FolderRuleCondition extends AbstractEntity implements PropertyAware {

    private static final Logger log = LoggerFactory.getLogger(FolderRuleCondition.class);

    /**
     * Serial
     */
    private static final long serialVersionUID = 1L;

    public static final String TYPE_FILENAME = "FilenameCondition";

    public static final String TYPE_SN = "SerialCondition";

    public static final String TYPE_FILETYPE = "FiletypeCondition";

    public static final String TYPE_ALL = "AllCondition";

    public static final String TYPE_DATE = "DateCondition";

    public static final String TYPE_USERGROUP = "UserGroupCondition";

    public static final String TYPE_PDTAGS = "PdTagsCondition";

    public static final String TYPE_PDNAME = "PdDisplayNameCondition";

    /**
     * 条件类型
     */
    private String conditionType;

    /**
     * 确定判断的时候采用匹配或不匹配
     */
    @Column(nullable = false)
    private boolean doEqual;

    @Transient
    private Map<String, String> propertiesMap;

    @Column(length = Length.LOB_DEFAULT)
    private String properties;

    @Index(name = "i_frc_ruleid")
    private String ruleId;

    @Index(name = "i_frc_folderid")
    private long folderId;

    /**
     * 创建时间
     */
    @Index(name = "i_frc_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 默认构造器
     */
    public FolderRuleCondition() {
        super();
        doEqual = true;
        creationDate = new Date();
    }

    public String getDescription() {
        return ConditionUtils.getDescrption(this);
    }

    /**
     * 设置属性
     *
     * @param name  名称
     * @param value 值
     */
    public void setProperty(String name, String value) {
        initPropertiesMap();
        propertiesMap.put(name, value);
        PropertyUtils.updateProperties(this);
    }

    /**
     * 移除属性
     *
     * @param name 要移除的属性名称
     */
    public void removeProperty(String name) {
        initPropertiesMap();
        propertiesMap.remove(name);
    }

    /**
     * 获取属性
     *
     * @param name 名称
     * @return 值
     */
    public String getProperty(String name) {
        initPropertiesMap();
        return propertiesMap.get(name);
    }

    public boolean isDoEqual() {
        return doEqual;
    }

    public void setDoEqual(boolean doEquals) {
        this.doEqual = doEquals;
    }

    public Map<String, String> getPropertiesMap() {
        initPropertiesMap();
        return propertiesMap;
    }

    public void setPropertiesMap(Map<String, String> propertiesMap) {
        this.propertiesMap = propertiesMap;
    }

    public void initPropertiesMap() {
        if (this.propertiesMap == null) {
            this.propertiesMap = new HashMap<>();

            PropertyUtils.updatePropertiesMap(this);
        }
    }

    public String getProperties() {
        return properties;
    }

    public void setProperties(String properties) {
        this.properties = properties;
    }

    public String getConditionType() {
        return conditionType;
    }

    public void setConditionType(String conditionType) {
        this.conditionType = conditionType;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public long getFolderId() {
        return folderId;
    }

    public void setFolderId(long folderId) {
        this.folderId = folderId;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
