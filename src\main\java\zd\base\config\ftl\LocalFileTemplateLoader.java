package zd.base.config.ftl;

import freemarker.cache.TemplateLoader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Slf4j
@RequiredArgsConstructor
public class LocalFileTemplateLoader implements TemplateLoader {

    private final String basePath; // 模板文件的基础路径

    @Override
    public Object findTemplateSource(String name) throws IOException {
        try {
            Path templatePath = Paths.get(basePath, name);
            log.info("Finding template: {}", templatePath);
            if (Files.exists(templatePath)) {
                log.info("Finding template: {} results:{}", templatePath, "true");
                return templatePath;
            }
            return null;
        } catch (Exception e) {
            log.error("Error finding template: " + name, e);
            return null;
        }
    }

    @Override
    public long getLastModified(Object templateSource) {
        try {
            Path path = (Path) templateSource;
            return Files.getLastModifiedTime(path).toMillis();
        } catch (Exception e) {
            return -1;
        }
    }

    @Override
    public Reader getReader(Object templateSource, String encoding) throws IOException {
        Path path = (Path) templateSource;

        log.info("getReader template: {} ", path);
        try {
            return new InputStreamReader(
                    Files.newInputStream(path),
                    encoding != null ? encoding : StandardCharsets.UTF_8.name()
            );
        } catch (Exception e) {
            log.error("Error getting template reader: " + path, e);
            throw new IOException("Could not load template: " + path, e);
        }
    }

    @Override
    public void closeTemplateSource(Object templateSource) {
        // 不需要特别的清理操作，Java会自动关闭文件流
    }
}
