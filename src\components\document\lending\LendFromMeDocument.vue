<script setup lang="ts">

import { Column, DOCUMENT_LENDING_COLUMNS } from 'config/columns';
import { onMounted, ref, watch } from 'vue';
import SimplePage from '@/components/common/SimplePage.vue';
import CommonTableLayout from '@/components/common/CommonTableLayout.vue';
import { documentLendingApi } from 'utils/api/document/document-lending-api';
import type { DocumentLendingDTO, SearchLendFromMeRequest } from '@/types/document/document-lending-api-types';
import { QNotify } from 'utils/common/qusar-notify-utils';
import { useQuasar } from 'quasar';
import { Validator } from 'utils/common/validate-utils';
import { mapNameArrayToColumnArray } from 'utils/common/column-utils';
import usePage from 'utils/hooks/page-hook';

const $q = useQuasar();

// 工具栏
const recallButtonLoading = ref(false);

// 搜索
const searchButtonLoading = ref(false);
const borrowerFullName = ref('');
const fileName = ref('');

// 表格
const tableLoading = ref(false);
const lendings = ref<DocumentLendingDTO[]>([]);
const selectedLendings = ref<DocumentLendingDTO[]>([]);

const columnNames: Array<keyof DocumentLendingDTO> = ['fileName', 'path', 'fileExtension', 'fileSize', 'displayBorrowers', 'returnTime', 'remainTime'];
const columns: Array<Column<DocumentLendingDTO>> = mapNameArrayToColumnArray(DOCUMENT_LENDING_COLUMNS, columnNames);

// nk
const nkDocUrl = ref('');

// 加载数据
const searchLending = async (page: number, size: number) => {
  const params: SearchLendFromMeRequest = {
    page: page - 1,
    size,
    sort: ['creationDate,desc'],
    fileName: fileName.value,
    borrowerFullName: borrowerFullName.value,
  }

  const response = await documentLendingApi.searchLendFromMe(params);
  lendings.value = response.content;
  total.value = response.totalElements;
}

// 分页
const pageDisabled = ref(false);
const handlePageChange = async (page: number, size: number) => {
  try {
    tableLoading.value = true;
    pageDisabled.value = true;
    await searchLending(page, size);
  } finally {
    tableLoading.value = false;
    pageDisabled.value = false;
  }
}
const { currentPage, pageSize, total, refresh } = usePage(handlePageChange);
const handleSearchButtonClick = async () => {
  try {
    searchButtonLoading.value = true;
    refresh();
  } finally {
    searchButtonLoading.value = false;
  }
}

const handleSelectionChange = (selection: DocumentLendingDTO[]) => {
  selectedLendings.value = selection;
};

const recallDocument = () => {
  if (Validator.isEmpty(selectedLendings)) {
    QNotify.warning('请选择要收回的文档');
    return;
  }

  const lendingIds = selectedLendings.value.map(item => item.id);
  recallButtonLoading.value = true;

  $q.dialog({
    title: '提示',
    message: '确定要收回该文档吗？',
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    try {
      await documentLendingApi.recallDocument(lendingIds);
      QNotify.positive('收回成功');
      currentPage.value = 1;
    } finally {
      recallButtonLoading.value = false;
    }
  });
}



// 页面加载时初始化
onMounted(() => {
  refresh();
})
</script>

<template>
  <CommonTableLayout>
    <template #toolbar>
      <div class="row q-gutter-x-sm">
        <q-btn class="text-main-btn-theme" label="收回" color="main-btn-theme" unelevated @click="recallDocument" />
      </div>
    </template>
    <template #search>
      <div class="row items-center q-gutter-md">
        <div class="row items-center q-gutter-sm">
          <div class="col-auto">借阅人：</div>
          <div class="col-auto">
            <q-input v-model="borrowerFullName" bottom-slots hide-bottom-space outlined dense class="search-input" />
          </div>
        </div>

        <div class="row items-center q-gutter-x-sm">
          <div class="col-auto">文件名：</div>
          <div class="col-auto">
            <q-input v-model="fileName" bottom-slots hide-bottom-space outlined dense class="search-input" />
          </div>
        </div>

        <div class="col-auto">
          <q-btn color="primary" label="搜索" @click="handleSearchButtonClick" :loading="searchButtonLoading" />
        </div>
      </div>
    </template>
    <template #default="{ height }">
      <el-table row-key="id" :height="height" :data="lendings" highlight-current-row
        @selection-change="handleSelectionChange" v-loading="tableLoading">
        <el-table-column fixed="left" type="selection" width="55">
        </el-table-column>
        <template :key="col.name" v-for="col in columns">
          <el-table-column :prop="col.name" :label="col.label" align="left" show-overflow-tooltip />
        </template>
      </el-table>

      <iframe id="iframe" :src="nkDocUrl" frameborder="0" width="0" height="0"></iframe>
    </template>

    <template #pagination>
      <div class="q-pl-md q-pr-md">
        <SimplePage :total="total" v-model:current-page="currentPage" v-model:page-size="pageSize"
          :disabled="pageDisabled" />
      </div>
    </template>
  </CommonTableLayout>
</template>

<style scoped lang="scss">
.search-input {
  width: 200px;
}
</style>
