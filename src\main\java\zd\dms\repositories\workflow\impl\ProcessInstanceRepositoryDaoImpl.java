package zd.dms.repositories.workflow.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.User;
import zd.dms.repositories.workflow.ProcessInstanceRepository;
import zd.dms.utils.PageableUtils;
import zd.dms.utils.repositories.PredicateUtils;
import zd.dms.utils.repositories.SpecTools;
import zd.dms.workflow.entities.SSProcessInstance;
import zd.record.utils.ECMUtils;
import zd.record.utils.ZDStringEscapeUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;


public class ProcessInstanceRepositoryDaoImpl extends BaseRepositoryDaoImpl<SSProcessInstance, String> implements ProcessInstanceRepository {

    public ProcessInstanceRepositoryDaoImpl(Class<SSProcessInstance> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public Page getProcessInstancesOrderByStatus(String username, String resourceType, String recPiType, int pageNumber, int pageSize) {
        if (!ECMUtils.filterSearchSql(recPiType)) {
            return PageableUtils.genPage(null, pageNumber, pageSize, 0);
        }

        Specification<SSProcessInstance> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSProcessInstance> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);
            specTools.equal("creator", username);
            specTools.equal("resourceType", resourceType);

            if (StringUtils.isNotBlank(recPiType) && !"all".equals(recPiType)) {
                if ("lend".equalsIgnoreCase(recPiType)) {
                    Predicate or1 = PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendElectric");
                    Predicate or2 = PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendPaper");

                    specTools.or(or1, or2);
                } else {
                    specTools.equal("pdType", recPiType);
                }
            }

            return specTools.getRestriction();
        };

        return findAll(spec, PageableUtils.getPageable(pageNumber, pageSize));
    }

    @Override
    public Page getProcessInstancesByRecId(long recId, int pageNumber, int pageSize, boolean includeNoEnded, String resourceTableName) {
        String hql = "from SSProcessInstance where resourceTableName='" + resourceTableName + "'";

        String hqlWhere1 = "";
        String hqlWhere2 = "";
        String hqlWhere3 = "";
        String hqlWhere4 = "";
        String hqlWhere5 = "";
        String hqlWhere6 = "";

        /*if (!RecordDBUtils.isOracle()) {
            // oracle中clob字段使用like查询结果不准确
            hqlWhere1 = " and dbms_lob.substr(resourceIdLong) like '" + recId + ",%'";
            hqlWhere2 = " and dbms_lob.substr(resourceIdLong) like '%," + recId + ",%'";
            hqlWhere3 = " and dbms_lob.substr(resourceIdLong) like '%," + recId + "'";
            hqlWhere4 = " and dbms_lob.substr(resourceIdLong) like '" + recId + ":%'";
            hqlWhere5 = " and dbms_lob.substr(resourceIdLong) like '%," + recId + ":%'";
            hqlWhere6 = " and dbms_lob.substr(resourceIdLong) like '" + recId + "'";
        } else {
            hqlWhere1 = " and resourceIdLong like '" + recId + ",%'";
            hqlWhere2 = " and resourceIdLong like '%," + recId + ",%'";
            hqlWhere3 = " and resourceIdLong like '%," + recId + "'";
            hqlWhere4 = " and resourceIdLong like '" + recId + ":%'";
            hqlWhere5 = " and resourceIdLong like '%," + recId + ":%'";
            hqlWhere6 = " and resourceIdLong like '" + recId + "'";
        }*/

        hqlWhere1 = " and (resourceIdLong like '" + recId + ",%'";
        hqlWhere2 = " or resourceIdLong like '%," + recId + ",%'";
        hqlWhere3 = " or resourceIdLong like '%," + recId + "'";
        hqlWhere4 = " or resourceIdLong like '" + recId + ":%'";
        hqlWhere5 = " or resourceIdLong like '%," + recId + ":%'";
        hqlWhere6 = " or resourceIdLong like '" + recId + "')";

        hql = hql + hqlWhere1 + hqlWhere2 + hqlWhere3 + hqlWhere4 + hqlWhere5 + hqlWhere6;

        if (!includeNoEnded) {
            hql = hql + " and endDate is not null";
        }

        hql = hql + " order by creationDate desc,priority desc";

        List<SSProcessInstance> results = findAll(hql, pageNumber, pageSize);
        String countSql = "select count(*) " + hql;
        int totalCount = NumberUtils.toInt(findObject(countSql) + "");

        return PageableUtils.genPage(results, pageNumber, pageSize, totalCount);
    }

    @Override
    public List<SSProcessInstance> getMyForwardProcessInstance(String username, String resourceType, boolean includeEnded) {
        Specification<SSProcessInstance> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSProcessInstance> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);
            specTools.isNull("endDate");
            specTools.equal("initalAgentUsername", username);
            specTools.equal("resourceType", resourceType);

            specTools.desc("creationDate");
            specTools.desc("priority");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public Page getProcessInstances(String keywords, String username, String resourceType, String pdType, int status, int pageNumber, int pageSize) {
        Specification<SSProcessInstance> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSProcessInstance> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);
            specTools.equal("creator", username);
            specTools.equal("resourceType", resourceType);
            if (status > 0) {
                specTools.equal("status", status);
            }


            String tempRecPiType = StringUtils.trim(pdType);
            if (StringUtils.isNotBlank(tempRecPiType) && !"all".equals(tempRecPiType)) {
                if ("lend".equalsIgnoreCase(tempRecPiType)) {
                    specTools.or(PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendElectric"), PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendPaper"));
                } else {
                    specTools.equal("pdType", tempRecPiType);
                }
            }

            if (StringUtils.isNotBlank(keywords)) {
                Predicate resourceNameLike = PredicateUtils.like(root, criteriaBuilder, "resourceName", keywords);
                Predicate nameLike = PredicateUtils.like(root, criteriaBuilder, "name", keywords);
                Predicate creatorLike = PredicateUtils.like(root, criteriaBuilder, "creator", keywords);
                Predicate creatorFullnameLike = PredicateUtils.like(root, criteriaBuilder, "creatorFullname", keywords);

                specTools.or(resourceNameLike, nameLike, creatorLike, creatorFullnameLike);
            }

            specTools.desc("creationDate");
            specTools.desc("priority");

            return specTools.getRestriction();
        };

        return findAll(spec, PageableUtils.getPageable(pageNumber, pageSize));
    }

    @Override
    public long getProcessInstancesCount(String username, String resourceType, String pdType, int status) {
        Specification<SSProcessInstance> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSProcessInstance> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);
            specTools.equal("creator", username);
            specTools.equal("resourceType", resourceType);
            if (status > 0) {
                specTools.equal("status", status);
            }

            String tempRecPiType = StringUtils.trim(pdType);
            if (StringUtils.isNotBlank(tempRecPiType) && !"all".equals(tempRecPiType)) {
                if ("lend".equalsIgnoreCase(tempRecPiType)) {
                    specTools.or(PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendElectric"), PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendPaper"));
                } else {
                    specTools.equal("pdType", tempRecPiType);
                }
            }

            return specTools.getRestriction();
        };

        return count(spec);
    }

    @Override
    public Page getRecordPis(long recId, String tableName, String username, String resourceType, String pdType, int pageNumber, int pageSize) {
        Specification<SSProcessInstance> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSProcessInstance> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.like("resourceIds", "#" + recId + "-" + tableName + "#");

            specTools.equal("creator", username);
            specTools.equal("resourceType", resourceType);

            String tempRecPiType = StringUtils.trim(pdType);
            if (StringUtils.isNotBlank(tempRecPiType) && !"all".equals(tempRecPiType)) {
                if ("lend".equalsIgnoreCase(tempRecPiType)) {
                    specTools.or(PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendElectric"), PredicateUtils.equal(root, criteriaBuilder, "pdType", "lendPaper"));
                } else {
                    specTools.equal("pdType", tempRecPiType);
                }
            }

            specTools.desc("creationDate");
            specTools.desc("priority");

            return specTools.getRestriction();
        };

        return findAll(spec, PageableUtils.getPageable(pageNumber, pageSize));
    }

    @Override
    public Page getProcessInstancesByResId(String resourceId, int pageNumber, int pageSize, boolean includeNoEnded) {
        Specification<SSProcessInstance> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSProcessInstance> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);
            specTools.or(PredicateUtils.equal(root, criteriaBuilder, "resourceId", resourceId), PredicateUtils.like(root, criteriaBuilder, "resourceDocIdString", "%" + resourceId + "%"));

            if (!includeNoEnded) {
                specTools.isNotNull("endDate");
            }

            specTools.desc("creationDate");
            specTools.desc("priority");

            return specTools.getRestriction();
        };

        return findAll(spec, PageableUtils.getPageable(pageNumber, pageSize));
    }

    @Override
    public List<SSProcessInstance> getProcessInstanceByResourceIdAndType(String resId, String resType) {
        Specification<SSProcessInstance> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSProcessInstance> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);
            specTools.equal("resourceId", resId);
            specTools.equal("resourceType", resType);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<SSProcessInstance> searchProcessInstance(String keywords, User operator, String resType, String recPiType, Date startDate, Date endDate) {
        if ((StringUtils.isBlank(keywords) && startDate == null && endDate == null) || operator == null) {
            return new ArrayList<>();
        }

        keywords = ZDStringEscapeUtils.escapeSql(keywords);

        String hql = "from SSProcessInstance p where creator = '" + operator.getUsername() + "' and resourceType='" +
                resType + "'";

        if (StringUtils.isNotBlank(keywords)) {
            hql += " and (resourceName like '%" + keywords + "%' OR resourceNameLong like '%" + keywords + "%')";
        }

        recPiType = StringUtils.trim(recPiType);
        String recPiTypeClause = "";
        if (StringUtils.isNotBlank(recPiType) && !"all".equals(recPiType)) {
            if ("lend".equalsIgnoreCase(recPiType)) {
                hql += " and (pdType = 'lendElectric' or pdType = 'lendPaper')";
            } else {
                hql += " and recordPiType = '" + recPiType + "'";
            }
        }

        List<Object> queryList = new LinkedList<Object>();
        if (startDate != null) {
            hql += " and creationDate > ?1 ";
            queryList.add(startDate);
        }
        if (endDate != null) {
            hql += " and creationDate < ?2 ";
            queryList.add(endDate);
        }

        Object[] array = queryList.toArray();

        return findAll(hql, queryList);
    }

    @Override
    public List<SSProcessInstance> getProcessInstanceByCreationDate(Date startDate, Date endDate, String resourceType) {
        Specification<SSProcessInstance> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSProcessInstance> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);
            specTools.equal("resourceType", resourceType);
            specTools.desc("id");

            if (startDate != null) {
                specTools.ge("creationDate", startDate);
            }
            if (endDate != null) {
                specTools.le("creationDate", endDate);
            }

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<SSProcessInstance> getNeedRemindProcessInstance() {
        Specification<SSProcessInstance> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSProcessInstance> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);
            specTools.gt("remindEveryHour", 0);
            specTools.ne("status", SSProcessInstance.STATUS_ENDED);

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<SSProcessInstance> getMyHandlePis(String username, String searchKeyword) {
        Specification<SSProcessInstance> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSProcessInstance> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotBlank(searchKeyword)) {
                predicates.add(PredicateUtils.like(root, criteriaBuilder, "resourceName", "%" + searchKeyword + "%"));
                predicates.add(PredicateUtils.like(root, criteriaBuilder, "displayName", "%" + searchKeyword + "%"));
                predicates.add(PredicateUtils.like(root, criteriaBuilder, "creatorFullname", "%" + searchKeyword + "%"));
                predicates.add(PredicateUtils.like(root, criteriaBuilder, "creator", "%" + searchKeyword + "%"));
                specTools.or(predicates);
            }

            specTools.like("nodeXml", "%<assignees>" + username + "</assignees>%");
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public SSProcessInstance getLatestProcessInstanceByRecId(long recId, String tableName) {
        Specification<SSProcessInstance> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SSProcessInstance> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);
            specTools.like("resourceIds", "#" + recId + "-" + tableName + "#");
            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        // 使用分页查询，只返回第一页的第一条记录
        Page<SSProcessInstance> page = findAll(spec, PageableUtils.getPageable(1, 1));
        List<SSProcessInstance> content = page.getContent();

        return content.isEmpty() ? null : content.get(0);
    }
}
