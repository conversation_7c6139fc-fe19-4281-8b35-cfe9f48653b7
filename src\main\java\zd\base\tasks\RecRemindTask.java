package zd.base.tasks;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zd.record.service.record.RecRemindService;

/**
 * 数据提醒定时任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RecRemindTask {

    private final RecRemindService recRemindService;

    /**
     * 每小时执行一次提醒检查
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void remindTask() {
        log.debug("执行数据提醒任务");
        try {
            recRemindService.remind();
        } catch (Exception e) {
            log.error("执行数据提醒任务出错", e);
        }
    }

    /**
     * 每天凌晨2点执行自动提醒
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void autoRemindTask() {
        log.debug("执行数据自动提醒任务");
        try {
            recRemindService.doAutoReminds();
        } catch (Exception e) {
            log.error("执行数据自动提醒任务出错", e);
        }
    }
}
