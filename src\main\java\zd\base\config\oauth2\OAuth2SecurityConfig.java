package zd.base.config.oauth2;

import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.source.ImmutableJWKSet;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.proc.SecurityContext;
import lombok.RequiredArgsConstructor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtClaimsSet;
import org.springframework.security.oauth2.jwt.NimbusJwtEncoder;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationConsentService;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configuration.OAuth2AuthorizationServerConfiguration;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configurers.OAuth2AuthorizationServerConfigurer;
import org.springframework.security.oauth2.server.authorization.settings.AuthorizationServerSettings;
import org.springframework.security.oauth2.server.authorization.InMemoryOAuth2AuthorizationConsentService;
import org.springframework.security.oauth2.server.authorization.token.DelegatingOAuth2TokenGenerator;
import org.springframework.security.oauth2.server.authorization.token.JwtGenerator;
import org.springframework.security.oauth2.server.authorization.token.OAuth2AccessTokenGenerator;
import org.springframework.security.oauth2.server.authorization.token.OAuth2RefreshTokenGenerator;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenCustomizer;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenGenerator;
import org.springframework.security.oauth2.server.authorization.token.JwtEncodingContext;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import zd.dms.services.oauth2.OAuth2ClientService;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.UUID;

/**
 * OAuth2安全配置
 */
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class OAuth2SecurityConfig {

    private final CustomOAuth2AuthenticationEntryPoint authenticationEntryPoint;
    private final CustomOAuth2AccessDeniedHandler accessDeniedHandler;
    private final CustomOAuth2TokenEndpointFilter customOAuth2TokenEndpointFilter;
    private final DefaultGrantTypeFilter defaultGrantTypeFilter;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    @Order(1)  // 提高优先级，确保OAuth2端点被正确处理
    public SecurityFilterChain authorizationServerSecurityFilterChain(HttpSecurity http) throws Exception {
        // 只匹配OAuth2相关端点
        http.securityMatcher("/oauth2/**");

        // 应用OAuth2授权服务器默认安全配置
        OAuth2AuthorizationServerConfiguration.applyDefaultSecurity(http);

        // 配置OAuth2授权服务器
        OAuth2AuthorizationServerConfigurer authorizationServerConfigurer = http.getConfigurer(OAuth2AuthorizationServerConfigurer.class);
        authorizationServerConfigurer
                // 配置令牌端点
                .tokenEndpoint(tokenEndpoint ->
                    tokenEndpoint
                        .errorResponseHandler((request, response, exception) -> {
                            authenticationEntryPoint.commence(request, response, exception);
                        })
                )
                // 配置客户端认证
                .clientAuthentication(clientAuth ->
                    clientAuth
                        .errorResponseHandler((request, response, exception) -> {
                            authenticationEntryPoint.commence(request, response, exception);
                        })
                )
                // 配置授权端点
                .authorizationEndpoint(Customizer.withDefaults())
                // 配置OIDC
                .oidc(Customizer.withDefaults());

        // 配置异常处理
        http.exceptionHandling(exceptions ->
                exceptions
                    .authenticationEntryPoint(authenticationEntryPoint)
                    .accessDeniedHandler(accessDeniedHandler));

        // 添加自定义令牌端点过滤器
        http.with(new OAuth2TokenEndpointFilterConfigurer(customOAuth2TokenEndpointFilter), configurer -> {});

        // 添加默认授权类型过滤器
        http.addFilterBefore(defaultGrantTypeFilter, UsernamePasswordAuthenticationFilter.class);

        // 允许所有OAuth2相关端点的访问
        http.csrf(AbstractHttpConfigurer::disable)
            .cors(Customizer.withDefaults());

        return http.build();
    }

    @Bean
    @Order(2)  // 调整优先级，在OAuth2授权服务器之后处理
    public SecurityFilterChain apiSecurityFilterChain(HttpSecurity http) throws Exception {
        http
            .securityMatcher("/api/v2/**")  // 只拦截/api/v2开头的请求
            .authorizeHttpRequests(authorize -> authorize
                .anyRequest().authenticated()  // 需要认证
            )
            .oauth2ResourceServer(oauth2 -> oauth2
                .jwt(Customizer.withDefaults())
                .authenticationEntryPoint(authenticationEntryPoint)
                .accessDeniedHandler(accessDeniedHandler)
            )
            .csrf(AbstractHttpConfigurer::disable)  // 禁用CSRF保护，简化API调用
            .cors(Customizer.withDefaults())  // 启用CORS支持
            .headers(headers -> headers
                .frameOptions(frameOptions -> frameOptions.sameOrigin())  // 允许同源iframe
            )
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)  // 使用无状态会话
            );

        return http.build();
    }

    @Bean
    @Order(3)  // 调整优先级，在OAuth2授权服务器和API安全配置之后处理
    public SecurityFilterChain defaultSecurityFilterChain(HttpSecurity http) throws Exception {
        http
            .securityMatcher("/**")  // 匹配所有请求，但由于优先级较低，/api/v2/**已被前一个过滤器链处理
            .authorizeHttpRequests(authorize -> authorize
                // 所有请求都放行，不需要认证
                .anyRequest().permitAll()
            )
            // 禁用默认的登录表单，使用自定义登录处理
            .formLogin(AbstractHttpConfigurer::disable)
            // 禁用默认的注销处理，使用自定义注销处理
            .logout(AbstractHttpConfigurer::disable)
            // 禁用HTTP Basic认证
            .httpBasic(AbstractHttpConfigurer::disable)
            // 禁用CSRF保护
            .csrf(AbstractHttpConfigurer::disable)
            // 启用CORS支持
            .cors(Customizer.withDefaults())
            // 禁用请求缓存
            .requestCache(AbstractHttpConfigurer::disable)
            // 禁用匿名用户
            .anonymous(AbstractHttpConfigurer::disable);

        return http.build();
    }

    @Bean
    public RegisteredClientRepository registeredClientRepository(OAuth2ClientService oAuth2ClientService) {
        return new CustomRegisteredClientRepository(oAuth2ClientService);
    }

    @Bean
    public JWKSource<SecurityContext> jwkSource() {
        KeyPair keyPair = generateRsaKey();
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        RSAKey rsaKey = new RSAKey.Builder(publicKey)
                .privateKey(privateKey)
                .keyID(UUID.randomUUID().toString())
                .build();
        JWKSet jwkSet = new JWKSet(rsaKey);
        return new ImmutableJWKSet<>(jwkSet);
    }

    private static KeyPair generateRsaKey() {
        KeyPair keyPair;
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
            keyPairGenerator.initialize(2048);
            keyPair = keyPairGenerator.generateKeyPair();
        } catch (Exception ex) {
            throw new IllegalStateException(ex);
        }
        return keyPair;
    }

    @Bean
    public JwtDecoder jwtDecoder(JWKSource<SecurityContext> jwkSource) {
        return OAuth2AuthorizationServerConfiguration.jwtDecoder(jwkSource);
    }

    @Bean
    public AuthorizationServerSettings authorizationServerSettings() {
        return AuthorizationServerSettings.builder()
                .issuer("http://www.zhidesoft.com:8081")  // 设置发行者URL，根据您的实际情况修改
                .tokenEndpoint("/oauth2/token")  // 明确指定令牌端点路径
                .tokenIntrospectionEndpoint("/oauth2/introspect")
                .authorizationEndpoint("/oauth2/authorize")
                .jwkSetEndpoint("/oauth2/jwks")
                .build();
    }

    // OAuth2授权服务已在RedisTokenStoreConfig中配置

    /**
     * 配置OAuth2授权同意服务
     */
    @Bean
    public OAuth2AuthorizationConsentService authorizationConsentService() {
        return new InMemoryOAuth2AuthorizationConsentService();
    }

    /**
     * 配置OAuth2令牌生成器
     */
    @Bean
    public OAuth2TokenGenerator<?> tokenGenerator(JWKSource<SecurityContext> jwkSource) {
        JwtGenerator jwtGenerator = new JwtGenerator(new NimbusJwtEncoder(jwkSource));
        jwtGenerator.setJwtCustomizer(jwtCustomizer());

        OAuth2AccessTokenGenerator accessTokenGenerator = new OAuth2AccessTokenGenerator();
        OAuth2RefreshTokenGenerator refreshTokenGenerator = new OAuth2RefreshTokenGenerator();

        return new DelegatingOAuth2TokenGenerator(
                jwtGenerator,
                accessTokenGenerator,
                refreshTokenGenerator
        );
    }

    /**
     * 配置JWT定制器
     */
    @Bean
    public OAuth2TokenCustomizer<JwtEncodingContext> jwtCustomizer() {
        return context -> {
            JwtClaimsSet.Builder claims = context.getClaims();

            // 添加自定义声明
            claims.claim("client_id", context.getRegisteredClient().getClientId());

            // 添加作用域
            String scopes = String.join(" ", context.getAuthorizedScopes());
            claims.claim("scope", scopes);
        };
    }
}
