package zd.dms.repositories.sms.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.SmsSendLog;
import zd.dms.repositories.sms.SendLogRepository;
import zd.dms.utils.repositories.SpecTools;

import java.util.Date;
import java.util.List;

public class SendLogRepositoryDaoImpl extends BaseRepositoryDaoImpl<SmsSendLog, String> implements SendLogRepository {

    public SendLogRepositoryDaoImpl(Class<SmsSendLog> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    public Page getSendLogs(int page, int pageSize) {
        Specification<SmsSendLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SmsSendLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec, page, pageSize);
    }

    public void clearSendLogs() {
        deleteAll();
    }

    public List<SmsSendLog> getSmsSendLogByDate(Date startDate, Date endDate) {
        Specification<SmsSendLog> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<SmsSendLog> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            if (startDate != null) {
                specTools.ge("creationDate", startDate);
            }

            if (endDate != null) {
                specTools.le("creationDate", endDate);
            }

            specTools.desc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }
}
