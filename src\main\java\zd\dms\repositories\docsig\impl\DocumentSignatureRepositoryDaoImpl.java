package zd.dms.repositories.docsig.impl;

import jakarta.persistence.EntityManager;
import org.springframework.data.jpa.domain.Specification;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.DocumentSignature;
import zd.dms.repositories.docsig.DocumentSignatureRepository;
import zd.dms.utils.repositories.SpecTools;

public class DocumentSignatureRepositoryDaoImpl extends BaseRepositoryDaoImpl<DocumentSignature, String> implements DocumentSignatureRepository {

    public DocumentSignatureRepositoryDaoImpl(Class<DocumentSignature> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public DocumentSignature getDocSigsByBelongUsername(String username) {
        Specification<DocumentSignature> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentSignature> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("belongUsername", username);

            return specTools.getRestriction();
        };

        return findSingleObject(spec);
    }

    @Override
    public DocumentSignature getSignatureByName(String name) {
        Specification<DocumentSignature> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<DocumentSignature> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.eq("name", name);

            return specTools.getRestriction();
        };

        return findSingleObject(spec);
    }
}
