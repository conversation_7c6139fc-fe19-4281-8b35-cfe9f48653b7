package zd.base.utils.es;

import org.apache.commons.lang3.StringUtils;
import zd.dms.entities.Document;
import zd.dms.utils.zdmq.ZDMQConfigUtils;
import zd.dms.utils.zdmq.ZDMQDBUtils;
import zd.record.entities.RecFolder;

import java.util.Map;

public class ESQueueUtils {

    public static final String TYPE_REC_INSERT = "rec_insert";
    public static final String TYPE_REC_DELETE = "rec_delete";
    public static final String TYPE_DOC_INSERT = "doc_insert";
    public static final String TYPE_DOC_DELETE = "doc_delete";

    public static void createDocument(Document doc) {
        boolean result = ESUtils.createDocument(doc);
        ESQueueUtils.addQueue(result, ESUtils.getIndexId(doc), ESQueueUtils.TYPE_DOC_INSERT);
    }

    public static void deleteDocument(Document doc) {
        boolean result = ESUtils.deleteDocument(doc);
        ESQueueUtils.addQueue(result, ESUtils.getIndexId(doc), ESQueueUtils.TYPE_DOC_DELETE);
    }

    public static void createDocument(Map<String, Object> recordData, long recId, String tableName) {
        boolean result = ESUtils.createDocument(recordData, recId, tableName);
        ESQueueUtils.addQueue(result, ESUtils.getIndexId(tableName, recId), ESQueueUtils.TYPE_REC_INSERT);
    }

    public static void createDocument(Map<String, Object> props, long recId, RecFolder recFolder) {
        boolean result = ESUtils.createDocument(props, recId, recFolder);
        ESQueueUtils.addQueue(result, ESUtils.getIndexId(recFolder, recId), ESQueueUtils.TYPE_REC_INSERT);
    }

    public static void deleteDocument(long recId, String tableName) {
        boolean result = ESUtils.deleteDocument(recId, tableName);
        ESQueueUtils.addQueue(result, ESUtils.getIndexId(tableName, recId), ESQueueUtils.TYPE_REC_DELETE);
    }

    public static void deleteDocument(long recId, RecFolder recFolder) {
        boolean result = ESUtils.deleteDocument(recId, recFolder);
        ESQueueUtils.addQueue(result, ESUtils.getIndexId(recFolder, recId), ESQueueUtils.TYPE_REC_DELETE);
    }

    public static void addQueue(boolean result, String msgId, String msgType) {
        if (result) {
            return;
        }

        if (StringUtils.isBlank(msgId) || StringUtils.isBlank(msgType)) {
            return;
        }

        ZDMQDBUtils.deleteMqMsgId(ZDMQConfigUtils.TYPE_ES, msgId, null, true);
        ZDMQDBUtils.addMsg(ZDMQConfigUtils.TYPE_ES, "", msgId, msgType);
    }
}
