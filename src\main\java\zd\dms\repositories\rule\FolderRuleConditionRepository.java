package zd.dms.repositories.rule;

import zd.base.repositories.BaseRepository;
import zd.dms.entities.FolderRuleCondition;

import java.util.List;

public interface FolderRuleConditionRepository extends BaseRepository<FolderRuleCondition, String> {

    List<FolderRuleCondition> getConditionsByRule(String ruleId);

    void deleteConditionsByRule(String ruleId);

    void deleteConditionsByFolder(long folderId);
}
