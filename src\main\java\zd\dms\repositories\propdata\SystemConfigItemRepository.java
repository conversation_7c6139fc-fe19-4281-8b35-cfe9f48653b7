package zd.dms.repositories.propdata;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.SystemConfigItem;

import java.util.List;

public interface SystemConfigItemRepository extends BaseRepository<SystemConfigItem, Long> {

    Page getItems(int pageNumber, int pageSize);

    SystemConfigItem getItemByName(String name);

    List<SystemConfigItem> getAllItems();
}
