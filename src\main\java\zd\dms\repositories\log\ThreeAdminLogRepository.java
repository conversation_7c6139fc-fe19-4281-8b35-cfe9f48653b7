package zd.dms.repositories.log;

import org.springframework.data.domain.Page;
import zd.base.repositories.BaseRepository;
import zd.dms.entities.ThreeAdminLog;

import java.util.Date;
import java.util.List;

public interface ThreeAdminLogRepository extends BaseRepository<ThreeAdminLog, Long> {

    List<ThreeAdminLog> getLogsByDate(Date startDate, Date endDate, int type, int targetType, String username);

    Page getLogs(int pageNumber, int pageSize, int type, int targetType, String username, Date startDate, Date endDate);
}
