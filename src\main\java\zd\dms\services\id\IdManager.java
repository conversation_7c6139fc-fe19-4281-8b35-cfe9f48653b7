package zd.dms.services.id;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.support.JdbcUtils;
import org.springframework.transaction.annotation.Transactional;
import zd.dms.entities.DmsId;
import zd.record.utils.RecordDBUtils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Transactional
public class IdManager {

    private static final Logger log = LoggerFactory.getLogger(IdManager.class);

    private static final String CREATE_ID = "INSERT INTO tdms_id (idValue, idType) VALUES (1, ?)";

    private static final String LOAD_ID = "SELECT idValue FROM tdms_id WHERE idType=?";

    private static final String LOAD_ALL_ID = "SELECT * FROM tdms_id";

    private static final String UPDATE_ID = "UPDATE tdms_id SET idValue=? WHERE idType=? AND idValue=?";

    private static final String UPDATE_ID2 = "UPDATE tdms_id SET idValue=? WHERE idType=?";

    // Statically startup a sequence manager for each of the sequence counters.
    private static Map<String, IdManager> managers = new ConcurrentHashMap<String, IdManager>();

    static {
		/*
		new SequenceManager(JiveConstants.ROSTER, 5);
		new SequenceManager(JiveConstants.OFFLINE, 1);
		new SequenceManager(JiveConstants.MUC_ROOM, 1);
		*/
    }

    public static String nextPrefixId(String idType, String prefix, int length) {
        long id = nextID(idType);
        return String.format(prefix + "%0" + length + "d", id);
    }

    public static String nextId(String idType, int length) {
        long id = nextID(idType);
        return String.format("%0" + length + "d", id);
    }

    /**
     * Returns the next ID of the specified type.
     *
     * @param type the type of unique ID.
     * @return the next unique ID of the specified type.
     */
    public static long nextID(String type) {
        if (managers.containsKey(type)) {
            return managers.get(type).nextUniqueID();
        } else {
            // Verify type is valid from the db, if so create an instance for
            // the type
            // And return the next unique id
            IdManager manager = new IdManager(type, 1);
            return manager.nextUniqueID();
        }
    }

    /**
     * Used to set the blocksize of a given SequenceManager. If no
     * SequenceManager has been registered for the type, the type is verified as
     * valid and then a new sequence manager is created.
     *
     * @param type      the type of unique id.
     * @param blockSize how many blocks of ids we should.
     */
    public static void setBlockSize(String type, int blockSize) {
        if (managers.containsKey(type)) {
            managers.get(type).blockSize = blockSize;
        } else {
            new IdManager(type, blockSize);
        }
    }

    private String type;
    private long currentID;
    private long maxID;
    private int blockSize;

    /**
     * Creates a new DbSequenceManager.
     *
     * @param seqType the type of sequence.
     * @param size    the number of id's to "checkout" at a time.
     */
    public IdManager(String seqType, int size) {
        managers.put(seqType, this);
        this.type = seqType;
        this.blockSize = size;
        currentID = 0l;
        maxID = 0l;
    }

    /**
     * Returns the next available unique ID. Essentially this provides for the
     * functionality of an auto-increment database field.
     */
    public synchronized long nextUniqueID() {
        if (!(currentID < maxID)) {
            // Get next block -- make 5 attempts at maximum.
            getNextBlock(5);
        }
        long id = currentID;
        currentID++;
        log.debug("type: {}, currentID: {}", type, currentID);
        return id;
    }

    public static List<DmsId> getIds() {
        Connection con = getConnection();
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            List<DmsId> result = new LinkedList<DmsId>();
            pstmt = con.prepareStatement(LOAD_ALL_ID);
            rs = pstmt.executeQuery();
            while (rs.next()) {
                DmsId aId = new DmsId();
                aId.setIdType(rs.getString("idType"));
                aId.setIdValue(rs.getLong("idValue"));
                result.add(aId);
            }

            return result;
        } catch (Throwable t) {
            log.error("updateId ex", t);
            return null;
        } finally {
            JdbcUtils.closeResultSet(rs);
            JdbcUtils.closeStatement(pstmt);
            JdbcUtils.closeConnection(con);
        }
    }

    public static List<DmsId> getIdsByPre(String whereSql) {
        Connection con = getConnection();
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            List<DmsId> result = new LinkedList<DmsId>();
            pstmt = con.prepareStatement(LOAD_ALL_ID + whereSql);
            rs = pstmt.executeQuery();
            while (rs.next()) {
                DmsId aId = new DmsId();
                aId.setIdType(rs.getString("idType"));
                aId.setIdValue(rs.getLong("idValue"));
                result.add(aId);
            }

            return result;
        } catch (Throwable t) {
            log.error("updateId ex", t);
            return null;
        } finally {
            JdbcUtils.closeResultSet(rs);
            JdbcUtils.closeStatement(pstmt);
            JdbcUtils.closeConnection(con);
        }
    }

    public static boolean updateId(String type, int newID) {
        Connection con = getConnection();
        PreparedStatement pstmt = null;
        boolean success = false;

        try {
            pstmt = con.prepareStatement(UPDATE_ID2);
            pstmt.setLong(1, newID);
            pstmt.setString(2, type);
            // Check to see if the row was affected. If not, some other process
            // already changed the original id that we read. Therefore, this
            // round failed and we'll have to try again.
            success = pstmt.executeUpdate() == 1;
            return success;
        } catch (Throwable t) {
            log.error("updateId ex", t);
            return false;
        } finally {
            JdbcUtils.closeStatement(pstmt);
            JdbcUtils.closeConnection(con);
        }
    }

    /**
     * Performs a lookup to get the next available ID block. The algorithm is as
     * follows:
     * <ol>
     * <li>Select currentID from appropriate db row.
     * <li>Increment id returned from db.
     * <li>Update db row with new id where id=old_id.
     * <li>If update fails another process checked out the block first; go back
     * to step 1. Otherwise, done.
     * </ol>
     */
    private void getNextBlock(int count) {
        if (count == 0) {
            log.error("Failed at last attempt to obtain an ID, aborting...");
            return;
        }

        Connection con = null;
        PreparedStatement pstmt = null;
        boolean abortTransaction = false;
        boolean success = false;

        try {
            con = getConnection();
            // Get the current ID from the database.
            pstmt = con.prepareStatement(LOAD_ID);
            pstmt.setString(1, type);
            ResultSet rs = pstmt.executeQuery();

            long currentID = 1;
            if (!rs.next()) {
                rs.close();
                pstmt.close();

                createNewID(con, type);
            } else {
                currentID = rs.getLong(1);
                rs.close();
                pstmt.close();
            }

            // Increment the id to define our block.
            long newID = currentID + blockSize;
            // The WHERE clause includes the last value of the id. This ensures
            // that an update will occur only if nobody else has performed an
            // update first.
            pstmt = con.prepareStatement(UPDATE_ID);
            pstmt.setLong(1, newID);
            pstmt.setString(2, type);
            pstmt.setLong(3, currentID);
            // Check to see if the row was affected. If not, some other process
            // already changed the original id that we read. Therefore, this
            // round failed and we'll have to try again.
            success = pstmt.executeUpdate() == 1;
            if (success) {
                this.currentID = currentID;
                this.maxID = newID;
            }
        } catch (SQLException e) {
            log.error("getNextBlock ex", e);
            abortTransaction = true;
        } finally {
            JdbcUtils.closeStatement(pstmt);
            JdbcUtils.closeConnection(con);
        }

        if (!success) {
            log.error("WARNING: failed to obtain next ID block due to " + "thread contention. Trying again...");
            // Call this method again, but sleep briefly to try to avoid thread
            // contention.
            try {
                Thread.sleep(75);
            } catch (InterruptedException ie) {
                // Ignore.
            }
            getNextBlock(count - 1);
        }
    }

    private void createNewID(Connection con, String type) throws SQLException {
        log.warn("Autocreating tdmsId row for type '" + type + "'");

        // create new ID row
        PreparedStatement pstmt = null;

        try {
            pstmt = con.prepareStatement(CREATE_ID);
            pstmt.setString(1, type);
            pstmt.execute();
        } finally {
            JdbcUtils.closeStatement(pstmt);
        }
    }

    public static Connection getConnection() {
        Connection conn = null;
        try {
            conn = RecordDBUtils.getConnection();
            conn.setAutoCommit(true);
            return conn;
        } catch (SQLException e) {
            log.error("获取Connection时出错", e);
            return null;
        }
    }
}