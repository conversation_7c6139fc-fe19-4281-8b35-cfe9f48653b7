package zd.dms.controllers.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapUtils;
import zd.dms.dto.PageResponse;
import zd.dms.utils.zdmq.ZDMQDBUtils;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@Tag(name = "消息队列Controller", description = "消息队列Controller")
@RequestMapping("/admin/mq")
public class ZDMQController extends ControllerSupport {

    @Operation(summary = "获取目录分区")
    @PostMapping("/list")
    @ZDLog("获取目录分区")
    public JSONResultUtils<Object> list(@RequestBody Map<String, Object> params) {

        List<String> types = ZDMapUtils.getListStringValue(params, "types");
        List<String> status = ZDMapUtils.getListStringValue(params, "status");

        int pageNumber = MapUtils.getIntValue(params, "pageNumber", 1);
        int pageSize = MapUtils.getIntValue(params, "pageSize", 30);

        Page<Map<String, Object>> msgsPage = ZDMQDBUtils.getMsgsPage(types, status, pageNumber, pageSize);

        return successData(PageResponse.ofMap(msgsPage));
    }

    @Operation(summary = "重置MQ消息")
    @PostMapping("/resetMqMsgs")
    @ZDLog("重置MQ消息")
    public JSONResultUtils<Object> resetMqMsgs(@RequestBody Map<String, Object> params) {
        List<String> ids = ZDMapUtils.getListStringValue(params, "ids");
        ZDMQDBUtils.resetMqMsgs(ids);

        return success();
    }

    @Operation(summary = "删除MQ消息")
    @PostMapping("/deleteMqMsgs")
    @ZDLog("删除MQ消息")
    public JSONResultUtils<Object> deleteMqMsgs(@RequestBody Map<String, Object> params) {
        List<String> ids = ZDMapUtils.getListStringValue(params, "ids");
        ZDMQDBUtils.deleteMqMsgs(ids);

        return success();
    }

}
