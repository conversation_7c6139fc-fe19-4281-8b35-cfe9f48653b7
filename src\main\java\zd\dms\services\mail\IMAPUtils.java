package zd.dms.services.mail;

import com.sun.mail.imap.IMAPFolder;
import com.sun.mail.imap.IMAPStore;
import com.sun.mail.util.MailSSLSocketFactory;
import jakarta.mail.*;
import jakarta.mail.internet.MimeMultipart;
import jakarta.mail.internet.MimeUtility;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.security.GeneralSecurityException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

public class IMAPUtils {

    private static final Logger log = LoggerFactory.getLogger(IMAPUtils.class);

    public static void main(String[] args) {
        String user = "coolmetal";
        String password = "cm111222";
        String host = "imap.163.com";

        try {
            boolean result = receiveMail(host, true, user, password, new File("d:\\temp"), null);
            log.debug("RESULT: {}", result);
        } catch (AuthenticationFailedException e) {
            log.debug("登录验证失败", e);
        } catch (MessagingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void auth(String host, boolean useSsl, String user, String password) throws MessagingException,
            AuthenticationFailedException {

        if (StringUtils.isBlank(host)) {
            throw new MessagingException("empty mail server host");
        }

        Properties prop = System.getProperties();
        prop.put("mail.store.protocol", "imap");
        prop.put("mail.imap.host", host);
        prop.put("mail.imap.partialfetch", false);

        prop.put("mail.imap.auth.plain.disable", true);
        prop.put("mail.imap.auth.login.disable", true);

        if (useSsl) {
            MailSSLSocketFactory sf = null;
            try {
                sf = new MailSSLSocketFactory();
            } catch (GeneralSecurityException e) {
                e.printStackTrace();
            }
            sf.setTrustAllHosts(true);
            prop.put("mail.imap.ssl.enable", "true");
            prop.put("mail.imap.ssl.socketFactory", sf);
        } else {
            prop.put("mail.imap.ssl.enable", "false");
            prop.put("mail.imap.port", "143");
        }

        Session session = Session.getInstance(prop);
        session.setDebug(true);

        IMAPStore store = (IMAPStore) session.getStore("imap");
        store.connect(user, password);

        Map<String, String> m = new HashMap<String, String>();
        m.put("name", "致得邮件收取程序");
        m.put("version", "1.0");
        m.put("vendor", "致得科创软件(北京)有限公司");
        try {
            store.id(m);
        } catch (Throwable t) {
        }

        try {
            if (store != null) {
                store.close();
            }
        } catch (Throwable t) {
            log.error("auth 释放资源 ex", t);
        }
    }

    public static boolean receiveMail(String host, boolean useSsl, String user, String password,
                                      File saveAttachmentPath, String subject) throws MessagingException, IOException,
            AuthenticationFailedException {

        if (saveAttachmentPath == null) {
            throw new IOException("empty saveAttachmentPath");
        }

        if (StringUtils.isBlank(host)) {
            throw new IOException("empty mail server host");
        }

        Properties prop = System.getProperties();
        prop.put("mail.store.protocol", "imap");
        prop.put("mail.imap.host", host);
        prop.put("mail.imap.partialfetch", false);

        prop.put("mail.imap.auth.plain.disable", true);
        prop.put("mail.imap.auth.login.disable", true);

        if (useSsl) {
            MailSSLSocketFactory sf = null;
            try {
                sf = new MailSSLSocketFactory();
            } catch (GeneralSecurityException e) {
                e.printStackTrace();
            }
            sf.setTrustAllHosts(true);
            prop.put("mail.imap.ssl.enable", "true");
            prop.put("mail.imap.ssl.socketFactory", sf);
        } else {
            prop.put("mail.imap.port", "143");
        }

        Session session = Session.getInstance(prop);
        session.setDebug(false);

        IMAPStore store = (IMAPStore) session.getStore("imap");
        store.connect(user, password);

        Map<String, String> m = new HashMap<String, String>();
        m.put("name", "致得邮件收取程序");
        m.put("version", "1.0");
        m.put("vendor", "致得科创软件(北京)有限公司");

        try {
            store.id(m);
        } catch (Throwable t) {
        }

        // 收件箱
        IMAPFolder folder = (IMAPFolder) store.getFolder("INBOX");
        folder.open(Folder.READ_WRITE);

        log.debug("邮箱{}的总邮件数：{}", host, folder.getMessageCount());
        log.debug("邮箱{}的未读邮件数：{}", host, folder.getUnreadMessageCount());
        log.debug("邮箱{}的新邮件数：{}", host, folder.getNewMessageCount());

        Message[] messages = folder.getMessages();
        for (Message message : messages) {
            Flags flags = message.getFlags();
            if (!flags.contains(Flags.Flag.SEEN)) {
                log.debug("receiveMail 处理未读邮件: {}", message.getSubject());

                if (StringUtils.isNotBlank(subject)) {
                    if (StringUtils.isBlank(message.getSubject())) {
                        continue;
                    } else {
                        if (!message.getSubject().contains(subject)) {
                            continue;
                        }
                    }
                }

                try {
                    Object content = message.getContent();
                    if (content instanceof MimeMultipart) {
                        MimeMultipart multipartContent = (MimeMultipart) content;
                        saveAttachments(multipartContent, saveAttachmentPath);
                    }
                    message.setFlag(Flags.Flag.SEEN, true);
                } catch (Throwable t) {
                    log.error("receiveMail parseMultipart ex", t);
                }
            }
        }

        // 释放资源
        try {
            if (folder != null) {
                folder.close(false);
            }
        } catch (Throwable t) {
            log.error("receiveMail 释放资源 ex", t);
        }

        try {
            if (store != null) {
                store.close();
            }
        } catch (Throwable t) {
            log.error("receiveMail 释放资源 ex", t);
        }

        return saveAttachmentPath.list() != null && saveAttachmentPath.list().length > 0;
    }

    public static void saveAttachments(Multipart multipart, File savePath) throws MessagingException, IOException {

        int count = multipart.getCount();
        log.debug("saveAttachments multipart count: {}", count);
        for (int i = 0; i < count; i++) {
            BodyPart bodyPart = multipart.getBodyPart(i);
            log.debug("saveAttachments ContentType: {}", bodyPart.getContentType());
            if (bodyPart.isMimeType("text/plain") || bodyPart.isMimeType("text/html")) {
                log.debug("saveAttachments content: {}", bodyPart.getContent());
            } else if (bodyPart.isMimeType("multipart/*")) {
                log.debug("saveAttachments is multipart/*");
                Multipart mpart = (Multipart) bodyPart.getContent();
                saveAttachments(mpart, savePath);
            } else if (BodyPart.ATTACHMENT.equalsIgnoreCase(bodyPart.getDisposition()) ||
                    BodyPart.INLINE.equalsIgnoreCase(bodyPart.getDisposition())) {
                log.debug("saveAttachments BodyPart.ATTACHMENT BodyPart.INLINE");
                saveFile(savePath, bodyPart);
            } else if (bodyPart.isMimeType("application/octet-stream")) {
                log.debug("saveAttachments is application/octet-stream");
                saveFile(savePath, bodyPart);
            }
        }
    }

    private static void saveFile(File savePath, BodyPart bodyPart) throws UnsupportedEncodingException,
            MessagingException, IOException {
        String filename = decodeText(bodyPart.getFileName());
        if (StringUtils.isNotBlank(filename)) {
            InputStream is = null;
            OutputStream out = null;
            try {
                is = bodyPart.getInputStream();
                savePath.mkdirs();
                File outFile = new File(savePath, filename);
                out = new FileOutputStream(outFile);
                IOUtils.copy(is, out);
                log.debug("saveAttachments 写入文件: {}", outFile.getAbsolutePath());
            } catch (Throwable t) {
                throw new IOException(t);
            } finally {
                IOUtils.closeQuietly(is);
                IOUtils.closeQuietly(out);
            }
        }
    }

    private static String decodeText(String text) throws UnsupportedEncodingException {

        if (StringUtils.isBlank(text))
            return "";
        if (text.startsWith("=?")) {
            text = MimeUtility.decodeText(text);
        } else {
            text = new String(text.getBytes("ISO8859_1"));
        }
        return text;
    }
}
