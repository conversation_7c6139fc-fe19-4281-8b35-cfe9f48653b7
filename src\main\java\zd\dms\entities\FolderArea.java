//                    佛祖保佑       永无BUG
//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                           O\  =  /O
//                        ____/`---'\____
//                      .'  \\|     |//  `.
//                     /  \\|||  :  |||//  \
//                    /  _||||| -:- |||||-  \
//                    |   | \\\  -  /// |   |
//                    | \_|  ''\---/''  |   |
//                    \  .-\__  `-`  ___/-. /
//                  ___`. .'  /--.--\  `. . __
//               ."" '<  `.___\_<|>_/___.'  >'"".
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |
//              \  \ `-.   \_ __\ /__ _/   .-` /  /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
package zd.dms.entities;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.dms.utils.TextUtils;

import java.util.Date;

@Entity
@Table(name = "folderarea")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class FolderArea extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = 8088177337328395523L;

    // 文档区域
    public static final String TYPE_DOC = "doc";

    // 数据区域
    public static final String TYPE_DATA = "data";

    public static final String TREENAME_DOC = "pubDocTree";

    public static final String TREENAME_DATA = "recordMainTree";

    /**
     * 目录分区所属的tree
     */
    private String treeName;

    /**
     * 目录分区名
     */
    private String areaName;

    /**
     * 排序号
     */
    @Index(name = "i_fa_numIndex")
    private int numIndex;

    /**
     * 区域类型：文档区域、数据区域
     */
    @Index(name = "i_fa_areaType")
    private String areaType;

    private Date creationDate;

    private Date modifiedDate;

    private String creatorFullname;

    private String updateFullname;

    @Index(name = "i_fa_enabled")
    private boolean enabled;

    /**
     * 是否是文控分区
     */
    @Index(name = "i_fa_isoArea")
    private Boolean isoArea;

    @Index(name = "i_fa_defaultArea")
    private Boolean defaultArea;

    /**
     * 分区图标文件名
     */
    private String iconFilename;

    /**
     * 默认构造器
     */
    public FolderArea() {
        super();
        creationDate = new Date();
        numIndex = 999;
        areaType = TYPE_DOC;
        enabled = true;
    }

    public FolderArea(String treeName, String areaName, int numIndex, String areaType, Date creationDate,
                      Date modifiedDate, String creatorFullname, String updateFullname, boolean enabled, String iconFilename) {
        super();
        this.treeName = treeName;
        this.areaName = areaName;
        this.numIndex = numIndex;
        this.areaType = areaType;
        this.creationDate = creationDate;
        this.modifiedDate = modifiedDate;
        this.creatorFullname = creatorFullname;
        this.updateFullname = updateFullname;
        this.enabled = enabled;
        this.iconFilename = iconFilename;
    }

    public String getTreeName() {
        return TextUtils.escapeXml(treeName);
    }

    public void setTreeName(String treeName) {
        this.treeName = treeName;
    }

    public String getAreaName() {
        return TextUtils.escapeXml(areaName);
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public int getNumIndex() {
        return numIndex;
    }

    public void setNumIndex(int numIndex) {
        this.numIndex = numIndex;
    }

    public String getAreaType() {
        return areaType;
    }

    public void setAreaType(String areaType) {
        this.areaType = areaType;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Date getModifiedDate() {
        return modifiedDate;
    }

    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getCreatorFullname() {
        return creatorFullname;
    }

    public void setCreatorFullname(String creatorFullname) {
        this.creatorFullname = creatorFullname;
    }

    public String getUpdateFullname() {
        return updateFullname;
    }

    public void setUpdateFullname(String updateFullname) {
        this.updateFullname = updateFullname;
    }

    public Boolean getIsoArea() {
        // 兼容旧版本： 未设置文控的文档分区，默认为非文控分区
        if (isoArea == null) {
            return false;
        }

        return isoArea;
    }

    public void setIsoArea(Boolean isoArea) {
        this.isoArea = isoArea;
    }

    public String getIconFilename() {
        // 兼容旧版用户: 文档区使用默认的07.png，信息区使用默认的03.png
        if (StringUtils.isBlank(iconFilename)) {
            if (TYPE_DOC.equals(areaType)) {
                return "default/docLogo.png";
            }
            return "default/recLogo.png";
        }

        return iconFilename;
    }

    public void setIconFilename(String iconFilename) {
        this.iconFilename = iconFilename;
    }

    public Boolean isDefaultArea() {
        if (defaultArea == null) {
            defaultArea = false;
        }

        return defaultArea;
    }

    public void setDefaultArea(boolean defaultArea) {
        this.defaultArea = defaultArea;
    }
}
