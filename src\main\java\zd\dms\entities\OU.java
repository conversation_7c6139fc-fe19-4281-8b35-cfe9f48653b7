package zd.dms.entities;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import zd.base.entities.AbstractEntity;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "ou")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class OU extends AbstractEntity {

    private static final Logger log = LoggerFactory.getLogger(OU.class);

    /**
     * Serial
     */
    private static final long serialVersionUID = -6031887844714527497L;

    @Column(unique = true, nullable = false)
    private String userId;

    private String username;

    private String sessionId;

    private Date loginDate;

    /**
     * 创建时间
     */
    @Index(name = "i_ou_cd")
    @Column(nullable = false)
    private Date creationDate;

    private String status;

    /**
     * 强制登出状态
     */
    public static final String STATUS_FORCELOGOUT = "forceLogout";

    /**
     * 默认构造器
     */
    public OU() {
        super();
        creationDate = new Date();
    }

    public boolean isStatusForceLogout() {
        return STATUS_FORCELOGOUT.equals(status);
    }

    /**
     * @see Object#equals(Object)
     */
    @Override
    public boolean equals(Object o) {
        if (!(o instanceof OU)) {
            return false;
        }

        final OU u = (OU) o;

        return new EqualsBuilder().appendSuper(super.equals(u)).append(userId, u.getUserId()).isEquals();
    }

    /**
     * @see Object#hashCode()
     */
    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(userId).toHashCode();
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Date getLoginDate() {
        return loginDate;
    }

    public void setLoginDate(Date loginDate) {
        this.loginDate = loginDate;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

}
