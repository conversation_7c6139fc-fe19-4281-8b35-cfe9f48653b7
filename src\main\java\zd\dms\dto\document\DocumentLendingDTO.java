package zd.dms.dto.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@Schema(description = "文档借阅信息")
public class DocumentLendingDTO {

    @Schema(description = "借阅记录ID")
    private Long id;

    @Schema(description = "文档ID")
    private String documentId;

    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "文件后缀")
    private String fileExtension;

    @Schema(description = "文件大小")
    private String fileSize;

    @Schema(description = "借出人展示名称")
    private String displayLender;

    @Schema(description = "借阅人展示名称")
    private String displayBorrowers;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Schema(description = "借出时间")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Schema(description = "归还时间")
    private Date returnTime;

    @Schema(description = "剩余时间")
    private String remainTime;

    @Schema(description = "文档路径")
    private String path;

    @Schema(description = "权限字符串")
    private String permissions;

    @Schema(description = "目录ID")
    private Long folderId;
}
