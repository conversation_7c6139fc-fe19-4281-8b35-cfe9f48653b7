package zd.base.utils.system;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.analysus.ja.dict.TaskDictionary;
import zd.base.utils.ZDUtils;
import zd.base.utils.file.PropertiesFileUtils;
import zd.base.utils.redis.ZDRedisUtils;
import zd.dms.io.ZDIOUtils;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.user.OuService;

import java.io.File;
import java.util.Properties;
import java.util.TimeZone;

public class BootStrapUtils {

    public static boolean isInit = false;

    public static void beforeInit() {
        Properties props = PropertiesFileUtils.getProperties("app.properties");
        String homeDir = props.get(SystemInitUtils.DEFAULT_HOME_DIR_KEY) + "";
        if (StringUtils.isBlank(homeDir)) {
            String installationDir = props.get("installationDir") + "";
            if (StringUtils.isBlank(installationDir)) {
                String middlewareDir = props.get("middlewareDir") + "";
                if (StringUtils.isBlank(middlewareDir)) {
                    middlewareDir = System.getProperty("catalina.home");
                }

                if (StringUtils.isNotBlank(middlewareDir)) {
                    installationDir = new File(middlewareDir).getParent();
                    homeDir = installationDir + File.separator + SystemInitUtils.DEFAULT_HOME_DIR;
                }
            } else {
                homeDir = installationDir + File.separator + SystemInitUtils.DEFAULT_HOME_DIR;
            }
        }

        File localConfigFile = new File(homeDir, "application.yml");
        if (localConfigFile.exists()) {
            ZDUtils.info("ZDECM - beforeInit localConfigFile: " + localConfigFile.getAbsolutePath());
            System.setProperty("spring.config.additional-location", "file:" + localConfigFile.getAbsolutePath());
        }
    }

    public static void init() {
        // 加载applaction文件配置
        SpringUtils.getEnvironment();

        try {
            ZDUtils.beforeBootstrapListener();
        } catch (Throwable t) {
            ZDUtils.info("ZDECM - beforeBootstrapListener error!");
        }

        // 初始化ecm.properties
        PropsUtils.loadProps();

        // 初始化项目各目录路径
        SystemInitUtils.init();

        // 初始化/home/<USER>
        PropsUtils.loadHomeProps();

        ZDRedisUtils.flushAll();

        SystemConfigManager scm = SystemConfigManager.getInstance();

        scm.loadDefaultConfig();

        SystemInitUtils.configDocumentDir();

        // 删除exports目录
        File exportsDir = SystemInitUtils.getExportDir();
        try {
            FileUtils.deleteDirectory(exportsDir);
            exportsDir.mkdirs();
        } catch (Throwable t) {
            t.printStackTrace();
        }

        int result2 = -1;
        if (StringUtils.isNotBlank(scm.getProperty("sk")) ||
                StringUtils.isNotBlank(PropsUtils.getProps("lic"))) {
            result2 = TaskDictionary.getMainDefinition().lskxc();
        }

        SystemInitUtils.updateLoginFlash();

        String timeZone = "Asia/Shanghai";
        if (StringUtils.isNotBlank(scm.getProperty("timeZone"))) {
            timeZone = scm.getProperty("timeZone");
        }
        System.setProperty("user.timezone", timeZone);
        TimeZone tz = TimeZone.getTimeZone(timeZone);
        TimeZone.setDefault(tz);
        ZDUtils.info("ZDECM - Timezone: " + timeZone);

        try {
            // 检查加密锁
            boolean cdResult = TaskDictionary.getMainDefinition().cd();
            ZDUtils.info("BackupManager Init result: " + cdResult);
        } catch (Throwable t) {
            ZDUtils.error("key ex", t);
        }

        // doRemid();

        if (StringUtils.isNotBlank(TaskDictionary.getMainDefinition().skorg())) {
            scm.setProperty("companyName", TaskDictionary.getMainDefinition().skorg());
        }

        scm.save();

        // 清除临时目录
        ZDIOUtils.clearTempDir();

        // 清楚登陆用户
        OuService ouService = SpringUtils.getBean(OuService.class);
        ouService.deleteAll();

        isInit = true;

        // 初始化系统
        try {
            ZDUtils.postBootstrapListener();
        } catch (Throwable t) {
            ZDUtils.info("ZDECM - postBootstrapListener error!");
        }

        ZDUtils.info("ZDECM - Started!");


    }
}
