package zd.base.repositories;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.NoRepositoryBean;

import java.util.List;
import java.util.Map;

@NoRepositoryBean
public interface BaseRepository<T, ID> extends JpaRepository<T, ID> {

    Page<T> getPage(int pageNumber, int pageSize);

    Page<T> getPage(int pageNumber, int pageSize, Sort sort);

    Page<T> getPage(int page, int pageSize, String orderProperty, boolean asc);

    Page<T> findAll(Specification<T> spec, int pageNumber, int pageSize);

    <S extends T> S update(S entity);

    List<T> getAll();

    T get(ID id);

    T findObjectById(ID id);

    T findSingleObject(String hql);

    T findSingleObject(String hql, Object... params);

    T findSingleObject(String hql, List<Object> params);

    T findSingleObject(String hql, Map<String, Object> params);

    List<T> findAll(String hql);

    List<T> findAll(String hql, Object... params);

    List<T> findAll(String hql, List<Object> params);

    List<T> findAll(String hql, Map<String, Object> params);

    List<T> findAll(String hql, int pageNumber, int pageSize);

    T findSingleObject(Specification<T> spec);

    int executeUpdate(String hql);

    int executeUpdate(String hql, Object... params);

    int executeUpdate(String hql, List<Object> params);

    int executeUpdate(String hql, Map<String, Object> params);

    Object findObject(String hql);

    Object findObject(String hql, Object... params);

    Object findObject(String hql, List<Object> params);

    Object findObject(String hql, Map<String, Object> params);
}
