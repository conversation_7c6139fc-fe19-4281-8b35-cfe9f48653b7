package zd.base.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.StandardReflectionParameterNameDiscoverer;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import zd.base.annotation.ZDLog;
import zd.base.context.UserContextHolder;
import zd.base.utils.system.PropsUtils;
import zd.dms.entities.User;
import zd.dms.utils.WebUtils;

import java.lang.reflect.Method;
import java.util.Objects;

@Component
@Aspect
@Slf4j
public class OperationLoggerAspect {

    // 初始化本地变量表参数发现器
    private final StandardReflectionParameterNameDiscoverer discoverer = new StandardReflectionParameterNameDiscoverer();

    @Around("@annotation(zdlog)") // 对标有 @Log 注解的方法进行处理
    public Object logMethodExecutionTime(ProceedingJoinPoint joinPoint, ZDLog zdlog) throws Throwable {
        if (!PropsUtils.getBoolProps("executionTimeEnabled")) {
            return joinPoint.proceed(); // 调用目标方法
        }

        long startTime = System.currentTimeMillis();

        String methodInfo = "";
        try {
            String className = joinPoint.getTarget().getClass().getName();
            Signature signature = joinPoint.getSignature();
            Method method = ((MethodSignature) signature).getMethod();
            methodInfo = getMethodInfo(method, zdlog, className);

            String[] parameterNames = this.discoverer.getParameterNames(method);
            Object[] args = joinPoint.getArgs();

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < Objects.requireNonNull(parameterNames).length; i++) {
                sb.append(parameterNames[i]).append(":{} ");
            }

            log.info(methodInfo + "- start " + sb, args);

            return joinPoint.proceed(); // 调用目标方法
        } finally {
            long endTime = System.currentTimeMillis();
            log.info(methodInfo + "- Execution time: {}ms.", (endTime - startTime));
        }
    }

    private String getMethodInfo(Method method, ZDLog zdlog, String className) {
        String methodName = method.getName();
        StringBuilder sb = new StringBuilder("[");
        if (StringUtils.hasText(zdlog.value())) {
            sb.append(zdlog.value()).append(", ");
        }

        sb.append(WebUtils.getIPAddress()).append(", ");

        User user = UserContextHolder.getUser();
        if (user != null) {
            sb.append(user.getFullnameAndUsername()).append(", ");
        }

        sb.append(className).append(".").append(methodName);
        sb.append("] ");

        return sb.toString();
    }
}
