package zd.dms.services.document.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.analysus.ja.dict.ModeDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import zd.base.context.UserContextHolder;
import zd.base.repositories.BaseRepository;
import zd.base.service.impl.BaseJpaServiceImpl;
import zd.base.utils.JSONResultUtils;
import zd.dms.entities.*;
import zd.dms.io.ZDIOUtils;
import zd.dms.repositories.document.DocumentRepository;
import zd.dms.repositories.document.FolderAreaRepository;
import zd.dms.repositories.document.FolderRepository;
import zd.dms.repositories.document.FolderSubscribeRepository;
import zd.dms.repositories.log.FolderLogRepository;
import zd.dms.repositories.security.PermissionRepository;
import zd.dms.repositories.user.GroupRepository;
import zd.dms.repositories.user.UserRepository;
import zd.dms.services.config.SystemConfigManager;
import zd.dms.services.docprop.DocPropDataColService;
import zd.dms.services.document.*;
import zd.dms.services.documentremind.DocAutoRemindService;
import zd.dms.services.log.LogService;
import zd.dms.services.mail.FolderMailService;
import zd.dms.services.report.JdbcReportService;
import zd.dms.services.rule.FolderRuleService;
import zd.dms.services.security.FolderPermissionUtils;
import zd.dms.services.security.PermissionService;
import zd.dms.services.security.RoleService;
import zd.dms.services.security.RoleUtils;
import zd.dms.services.user.UserGroupUtils;
import zd.dms.utils.TextUtils;
import zd.dms.utils.WebUtils;
import zd.dms.utils.folder.FolderPermRedisUtils;
import zd.record.entities.RecFolder;
import zd.record.service.datastruct.DataStructService;
import zd.record.service.folder.RecFolderService;

import java.io.IOException;
import java.util.*;

@RequiredArgsConstructor
@Service
@Slf4j
public class FolderServiceImpl extends BaseJpaServiceImpl<Folder, Long> implements FolderService {

    private final FolderRepository folderRepository;

    private final FolderLogRepository folderLogRepository;

    private final JdbcReportService jdbcReportService;

    private final DocumentRepository documentRepository;

    private final FolderSubscribeRepository folderSubscribeRepository;

    private final DocumentLinkService documentLinkService;

    private final DocumentService documentService;

    private final FolderRuleService folderRuleService;

    @Lazy
    @Autowired
    private PermissionService permissionService;

    @Lazy
    @Autowired
    private FolderMailService folderMailService;

    private final DataStructService dataStructService;

    private final GroupRepository groupRepository;

    private final UserRepository userRepository;

    private final FolderAreaRepository folderAreaRepository;

    private final LogService logService;

    private final FolderAreaService folderAreaService;

    private final PermissionRepository permissionRepository;

    private final ThreeAdminLogService threeAdminLogService;

    @Lazy
    @Autowired
    private DocPropDataColService docPropDataColService;

    @Lazy
    @Autowired
    private DocAutoRemindService docAutoRemindService;

    @Lazy
    @Autowired
    private RecFolderService recFolderService;

    @Override
    public BaseRepository<Folder, Long> getBaseRepository() {
        return folderRepository;
    }

    @Override
    public JSONResultUtils<Object> create(Map<String, Object> params, User user) {
        if (user == null) {
            return JSONResultUtils.error("用户不存在");
        }

        long parentId = MapUtils.getLongValue(params, "parentId", 0L);
        if (parentId == 0) {
            return JSONResultUtils.error("请选择上级目录");
        }

        Folder parentFolder = null;
        int parentFolderType = MapUtils.getIntValue(params, "parentFolderType", 0);
        if (parentFolderType != -1) {
            parentFolder = getById(parentId);
            if (!RoleUtils.isInRole(RoleService.IF_ANY_GRANTED, Role.SYSTEM_ADMIN, Role.RECEPTION_ADMIN) &&
                    parentId == -1) {
                if (parentFolder == null) {
                    return JSONResultUtils.error("请选择正确的上级目录");
                }
            }
        }

        String name = MapUtils.getString(params, "name", "");
        name = TextUtils.filterFilename(name);
        if (StringUtils.isBlank(name)) {
            return JSONResultUtils.error("目录名为空或包含特殊字符，请重新填写");
        }

        if (name.length() > 100) {
            return JSONResultUtils.error("目录名最多100字");
        }

        if (isFolderNameInFolder(name, parentFolder, null) > 0) {
            return JSONResultUtils.error("同名目录已存在，请重新填写目录名");
        }

        int numIndex = MapUtils.getIntValue(params, "numIndex", 0);
        if (numIndex <= 0) {
            return JSONResultUtils.error("请输入合法的目录排序1-9999999");
        }

        if (numIndex > 9999999) {
            return JSONResultUtils.error("目录排序最大9999999");
        }

        // 设置folderType
        int folderType = Folder.TYPE_PUBLIC;
        if (-2 == parentId) {
            folderType = Folder.TYPE_MYDOCUMENT;
        }

        Folder folder = new Folder();
        folder.setCreator(user.getUsername());
        folder.setCreatorFullname(user.getFullname());
        folder.setFolderType(folderType);
        folder.setName(name);
        folder.setPrintLimit(-1L);

        String treeName = FolderArea.TREENAME_DOC;
        if (parentFolderType == -1) {
            FolderArea folderArea = folderAreaService.getFolderAreaById(parentId);
            if (folderArea != null) {
                treeName = folderArea.getTreeName();
            }
        } else {
            if (parentFolder != null) {
                folder.setParentId(parentFolder.getId());
                treeName = parentFolder.getTreeName();
            } else {
                FolderArea folderArea = folderAreaService.getFolderAreaById(parentId);
                if (folderArea != null) {
                    treeName = folderArea.getTreeName();
                }
            }
        }
        folder.setTreeName(treeName);

        String docprop = folder.getDocProperty();
        log.debug("docprop:{}", docprop);
        if (StringUtils.isNotBlank(docprop)) {
            String[] prop = docprop.split("\r\n");
            ArrayList<String> propList = new ArrayList<String>();
            for (int i = 0; i < prop.length; i++) {
                int count = docPropDataColService.getNumByColName(prop[i]);
                if (count > 0) {
                    propList.add(prop[i]);
                }
            }
            String existProp = StringUtils.join(propList, "\r\n");
            log.debug("existProp:{}", existProp);
            folder.setDocProperty(existProp);
        } else {
            folder.setDocProperty(null);
        }

        create(folder);
        addLog(folder, FolderLog.TYPE_CREATE, "创建子目录: " +
                folder.getName(), user.getUsername(), user.getFullname(), WebUtils.getIPAddress(), true);

        return JSONResultUtils.success();
    }

    @Override
    public JSONResultUtils<Object> update(Map<String, Object> params, User user) {
        if (user == null) {
            return JSONResultUtils.error("用户不存在");
        }

        long id = MapUtils.getLongValue(params, "id", 0L);
        Folder folder = getById(id);
        if (folder == null) {
            return JSONResultUtils.error("请选择目录");
        }

        // 管理员可以删除或修改目录
        // 具有管理一个目录的用户无权修改和删除这个目录
        // 只可以修改和删除这个目录的子级目录
        Folder oldParentFolder = folder.getParent();
        boolean canMoveFolder = false;
        if (folder.getFolderType() == Folder.TYPE_PUBLIC) {
            if (oldParentFolder == null) {
                if (!user.isSystemAdmin()) {
                    return JSONResultUtils.error("无权修改本目录");
                } else {
                    canMoveFolder = true;
                }
            } else {
                if (!FolderPermissionUtils.checkPermission(user, folder.getParent(), FolderPermission.ALL) &&
                        !FolderPermissionUtils.checkPermission(user, folder.getParent(), FolderPermission.EDIT)) {
                    return JSONResultUtils.error("无权修改本目录");
                } else if (FolderPermissionUtils.checkPermission(user, folder.getParent(), FolderPermission.ALL)) {
                    canMoveFolder = true;
                }
            }
        } else if (folder.getFolderType() == Folder.TYPE_MYDOCUMENT) {
            canMoveFolder = true;
        }

        // 移动前的目录的一级父目录名
        String oldFolderTopFolderName = folder.getTopFolder().getName();
        log.debug("updateFolder oldFolderTopFolderName:{}", oldFolderTopFolderName);

        String oldName = folder.getName();
        // 过滤目录名
        String name = MapUtils.getString(params, "name", "");
        name = TextUtils.filterFilename(name);
        if (StringUtils.isBlank(name)) {
            return JSONResultUtils.error("目录名为空或包含特殊字符，请重新填写");
        }

        if (name.length() > 100) {
            return JSONResultUtils.error("目录名最多100字");
        }

        int numIndex = MapUtils.getIntValue(params, "numIndex", 0);
        if (numIndex <= 0) {
            return JSONResultUtils.error("请输入合法的目录排序1-9999999");
        }

        if (numIndex > 9999999) {
            return JSONResultUtils.error("目录排序最大9999999");
        }

        boolean parentChanged = false;
        Folder parentFolder = null;

        long parentId = MapUtils.getLongValue(params, "parentId", 0L);
        if (oldParentFolder == null && parentId > 0) {
            parentChanged = true;
        }

        if (oldParentFolder != null && parentId != oldParentFolder.getId()) {
            parentChanged = true;
        }

        // 改变上级目录
        if (parentChanged) {
            parentChanged = true;

            parentFolder = getById(parentId);

            // 检查是否有同名目录存在
            if (isFolderNameInFolder(name, parentFolder, folder, folder.getFolderType(), user.getUsername()) > 0) {
                return JSONResultUtils.error("同名目录已存在，请重新填写目录名");
            }

            log.debug("改变上级目录至：{}, {}", parentId, parentFolder);

            if (parentFolder != null && !parentFolder.equals(folder) && !parentFolder.equals(folder.getParent())) {
                // parent不能是group的子组,否则可能出现意想不到的后果
                if (!DocumentUtils.isFolderChild(folder, parentFolder)) {
                    folder.setParentId(parentFolder.getId());
                } else {
                    return JSONResultUtils.error("无法将子目录设置为上级目录");
                }
            } else {
                if (folder.equals(parentFolder)) {
                    return JSONResultUtils.error("无法将本目录设置为上级目录");
                }

                if (folder.getParent() != null) {
                    folder.setParentId(null);
                }
            }

            if (parentId == 0) {
                if (folder.getFolderType() == Folder.TYPE_PUBLIC) {
                    parentId = -1;
                } else if (folder.getFolderType() == Folder.TYPE_MYDOCUMENT) {
                    parentId = -2;
                }
            }
        } else {
            log.debug("parentNotChanged");
            // 检查是否有同名目录存在
            if (isFolderNameInFolder(name, folder.getParent(), folder, folder.getFolderType(), user.getUsername()) >
                    0) {
                return JSONResultUtils.error("同名目录已存在，请重新填写目录名");
            }
        }

        folder.setName(name);
        folder.setNumIndex(numIndex);

        // 如果父级目录改变了，将folder的treeName修改为新的父级目录的treeName
        String parentFolderTreeName = null;
        if (parentChanged) {
            if (parentFolder != null) {
                parentFolderTreeName = parentFolder.getTreeName();
            }
            FolderUtils.changeFolderAndChildrenTreeName(folder, parentFolderTreeName);
        }

        // TODO 目录权限
//        updateFolderAndPermission(folder, oldFolderTopFolderName, parentFolder);

        String logMsg = "更新目录: " + folder.getName();
        if (!oldName.equals(folder.getName())) {
            logMsg = logMsg + ", 目录名从: " + oldName + " 改为 " + folder.getName();
        }
        if (parentChanged) {
            if (parentFolder != null && oldParentFolder == null) {
                logMsg = logMsg + ", 父级目录从: /" + DocumentUtils.getDocLeftMenuTitle() + " 改为: " +
                        parentFolder.getAreaDisplayFolder();
            } else if (parentFolder != null && oldParentFolder != null) {
                logMsg = logMsg + ", 父级目录从: " + oldParentFolder.getAreaDisplayFolder() + " 改为: " +
                        parentFolder.getAreaDisplayFolder();
            } else if (parentFolder == null && oldParentFolder != null) {
                logMsg = logMsg + ", 父级目录从: " + oldParentFolder.getAreaDisplayFolder() + " 改为: /" +
                        DocumentUtils.getDocLeftMenuTitle();
            }
        }
        addLog(folder, FolderLog.TYPE_UPDATE, logMsg, user.getUsername(), user.getFullname(), WebUtils.getIPAddress(), false);

        return JSONResultUtils.success();
    }

    @Override
    public JSONResultUtils<Object> delete(long id, User user) {
        if (id <= 0) {
            return JSONResultUtils.error("根目录不能被删除");
        }

        Folder folder = getById(id);
        if (folder == null) {
            return JSONResultUtils.error("目录不存在");
        }

        Folder parent = folder.getParent();
        if (parent == null) {
            return JSONResultUtils.error("禁止删除顶级目录");
        }

        // 管理员可以删除或修改目录
        // 具有管理一个目录的用户无权修改和删除这个目录
        // 只有拥有一个目录的管理权或删除权和其父目录的管理权及删除权，才可删除这个目录
        if (folder.getFolderType() == Folder.TYPE_PUBLIC) {
            if (!user.isSystemAdmin() && !user.isReceptionAdmin() &&
                    !((FolderPermissionUtils.checkPermission(user, folder.getParent(), "*") ||
                            FolderPermissionUtils.checkPermission(user, folder.getParent(), "D")) &&
                            (FolderPermissionUtils.checkPermission(user, folder, "*") ||
                                    FolderPermissionUtils.checkPermission(user, folder, "D")))) {
                return JSONResultUtils.error("无权删除本目录");
            }
        }

        if (folder.getFolderType() == Folder.TYPE_MYDOCUMENT && folder.getParent() == null) {
            return JSONResultUtils.error("非系统管理员，无法删除一级目录");
        }

        if (folder.getChildCount() > 0) {
            return JSONResultUtils.error("存在子目录，无法删除");
        }

        if (documentService.getDocumentCountByFolderWithDeleted(folder) > 0) {
            return JSONResultUtils.error("目录中有文档，无法删除");
        }

        FolderUtils.clearFoldersWithListPermissionAndCountCacheMap();

        // 删除权限
        deleteAllPermission(folder);

        // 本目录的订阅
        folderSubscribeRepository.deleteAllSubscribesByFolder(folder);

        // 邮箱集成配置信息
        folderMailService.deleteFolderMailconfigByFolderId(folder.getId());

        // 删除本目录内的文档链接
        documentLinkService.delete(folder);

        // 删除目录审计
        folderLogRepository.deleteAllLogsByFolder(folder);

        // 删除目录自动提醒
        docAutoRemindService.deleteAllByFolderId(folder.getId());

        delete(folder);

        // 删除目录规则
        folderRuleService.deleteRulesByFolder(folder.getId());

        addLog(folder, FolderLog.TYPE_DELETE, "删除子目录: " +
                folder.getName(), user.getUsername(), user.getFullname(), WebUtils.getIPAddress(), true);

        return JSONResultUtils.success();
    }

    @Override
    public Map<Long, Long> getChildCount(List<Long> parentIds) {
        List<Map<String, Object>> childCount = folderRepository.getChildCount(parentIds);

        Map<Long, Long> results = new HashMap<>();
        childCount.forEach(item -> {
            long parendId = MapUtils.getLongValue(item, "parentId");
            long count = MapUtils.getLongValue(item, "count");
            results.put(parendId, count);
        });

        return results;
    }

    @Override
    public List<Folder> getTopFolderListByFolderAreaId(long folderAreaId) {
        if (folderAreaId <= 0) {
            return new ArrayList<>();
        }

        FolderArea folderArea = folderAreaRepository.get(folderAreaId);
        if (folderArea == null) {
            return new ArrayList<>();
        }

        return folderRepository.getTopFolderListByFolderAreaId(folderArea);
    }

    @Override
    public List<Folder> findFolders(String keywords) {
        return folderRepository.findFolders(keywords);
    }

    @Override
    public List<Folder> getChildren(long parentId) {
        if (parentId <= 0) {
            return getTopFolders();
        }

        return folderRepository.getChildren(parentId);
    }

    @Override
    public List<Long> getFolderIdsByLimit(int limit) {
        return folderRepository.getFolderIdsByLimit(limit);
    }

    @Override
    public List<Long> getSubFolderIds(List<Long> parentIds) {
        return folderRepository.getSubFolderIds(parentIds);
    }

    @Override
    public List<Folder> getFoldersByNameAndParent(Folder parentFolder, String name) {
        return folderRepository.getFoldersByNameAndParent(parentFolder, name);
    }

    @Override
    public List<Folder> getPubTopFoldersByTreeName(String treeName) {
        return folderRepository.getPubTopFoldersByTreeName(treeName);
    }

    @Override
    public List<Folder> findFoldersByName(String[] folderNameKeyWordArray) {
        return folderRepository.findFoldersByName(folderNameKeyWordArray);
    }

    @Override
    public List<Folder> getFoldersByName(String folderName) {
        return folderRepository.getFoldersByName(folderName);
    }

    @Override
    public FolderLog addLog(Folder folder, int type, String msg, String operator, String fullname, String ipAddress, boolean logToParent) {
        // 如果开启了三员管理，则记录前台管理员对于文档目录的操作日志
        threeAdminLogService.addLog(folder, null, null, type, msg, operator, fullname, ipAddress, logToParent);

        if (folder == null) {
            log.debug("addLog null folder");
            return null;
        }

        if (folder.getFolderType() == Folder.TYPE_MYDOCUMENT) {
            return null;
        }

        if (logToParent && folder.getParent() == null) {
            return null;
        }

        FolderLog log = new FolderLog();

        if (logToParent) {
            log.setFolderId(folder.getParent().getId());
        } else {
            log.setFolderId(folder.getId());
        }

        log.setOperateType(type);
        log.setMsg(msg);
        log.setOperator(operator);
        log.setOperatorFullname(fullname);
        log.setIp(ipAddress);

        folderLogRepository.save(log);

        return log;
    }

    @Override
    public void addLogFromAdmin(Folder folder, int type, String msg, boolean logToParent) {
        addLog(folder, type, msg, UserGroupUtils.ADMIN_USERNAME, "系统管理员", "127.0.0.1", logToParent);
    }

    @Override
    public List<FolderLog> getLogs(Folder folder, int type) {
        return folderLogRepository.getLogsByFolder(folder, type);
    }

    @Override
    public List<FolderLog> getTop100Logs(Folder folder) {
        return folderLogRepository.getTop100LogsByFolder(folder);
    }

    @Override
    public void deleteLogsBeforeDays(int days) {
        folderLogRepository.deleteLogsBeforeDays(days);
    }

    @Override
    public Page getLogs(int pageNumber, int pageSize, int type, String username, Date startDate, Date endDate) {
        return folderLogRepository.getLogs(pageNumber, pageSize, type, username, startDate, endDate);
    }

    @Override
    public Page getLogsByFolder(int pageNumber, int pageSize, int type, Folder folder) {
        return folderLogRepository.getLogsByFolder(pageNumber, pageSize, type, folder);
    }

    @Override
    public void copySubFolderStructure(Folder sourceFolder, Folder destFolder, boolean copyPermission, boolean includeParentFolder) {
        if (sourceFolder == null || destFolder == null) {
            log.warn("copySubDirStructure null folder");
            return;
        }

        // 如果不复制父目录，且源目录没有子目录，则直接返回
        if ((sourceFolder.getChildren() == null || sourceFolder.getChildren().size() == 0) && !includeParentFolder) {
            return;
        }

        // 如果选择复制父目录结构，那么目标目录就是复制后的父目录
        if (includeParentFolder) {
            destFolder = createFolderAndPermission(sourceFolder, destFolder, copyPermission);
        }

        // 复制子目录
        createSubFolder(sourceFolder, destFolder, copyPermission);
    }

    private void createSubFolder(Folder sourceFolder, Folder destFolder, boolean copyPermission) {
        if (destFolder == null || sourceFolder == null) {
            return;
        }

        log.debug("createSubFolder, src: {}, dest: {}, copyPerm: {}, sourceChildren: {}", new Object[]{sourceFolder.getName(), destFolder.getName(), copyPermission, sourceFolder.getChildren().size()});

        if (sourceFolder.getChildren() != null) {
            for (Folder subFolder : sourceFolder.getChildren()) {
                Folder newSubFolder = createFolderAndPermission(subFolder, destFolder, copyPermission);
                if (newSubFolder == null) {
                    continue;
                }
                createSubFolder(subFolder, newSubFolder, copyPermission);
            }
        }
    }

    /**
     * 将源目录和权限复制到目标目录中
     *
     * @param subFolder      源目录
     * @param destFolder     目录目录
     * @param copyPermission 是否复制权限
     * @return
     */
    private Folder createFolderAndPermission(Folder subFolder, Folder destFolder, boolean copyPermission) {
        if (subFolder == null || destFolder == null) {
            return null;
        }

        if (isFolderNameInFolder(subFolder.getName(), destFolder, null, destFolder.getFolderType(), null) > 0) {
            log.debug("createSubFolder, 同名目录已存在，跳过：{}", subFolder.getName());
            return null;
        }

        FolderUtils.clearFoldersWithListPermissionAndCountCacheMap();

        Folder newSubFolder = new Folder();
        newSubFolder.setCreatorFullname(UserContextHolder.getUser().getFullname());
        newSubFolder.setDescription(subFolder.getDescription());
        newSubFolder.setFolderType(subFolder.getFolderType());
        newSubFolder.setInheritPermission(subFolder.getInheritPermission());
        newSubFolder.setName(subFolder.getName());
        newSubFolder.setNumIndex(subFolder.getNumIndex());
        newSubFolder.setProperties(subFolder.getProperties());
        // 复制目录结构得到的新目录的treeName和目标目录的treeName保持一致
        String destFolderTreeName = destFolder.getTreeName();
        if (StringUtils.isBlank(destFolderTreeName)) {
            destFolderTreeName = null;
        }
        newSubFolder.setTreeName(destFolderTreeName);

        newSubFolder.setParentId(destFolder.getId());

        folderRepository.save(newSubFolder);

        if (copyPermission) {
            List<FolderPermission> permissionsByFolderId = permissionService.getPermissionsByFolderId(subFolder.getId());
            if (CollectionUtils.isNotEmpty(permissionsByFolderId)) {
                for (FolderPermission fp : permissionsByFolderId) {
                    log.debug("createSubFolder copy perm: {}, pos: {}", fp.getPermission(), fp.getPosition());
                    FolderPermission newFp = new FolderPermission();
                    newFp.setFolderId(newSubFolder.getId());
                    newFp.setUsername(fp.getUsername());
                    newFp.setPermission(fp.getPermission());
                    newFp.setPosition(fp.getPosition());
                    newFp.setGroupCode(fp.getGroupCode());
                    permissionService.saveFolderPermission(newFp);
                }
            }
        }

        return newSubFolder;
    }

    @Override
    public void updateNullNumIndex(int numIndex) {
        SystemConfigManager scm = SystemConfigManager.getInstance();
        if (scm.getBooleanProperty("folderIndexUpdated")) {
            log.debug("FolderIndex已更新过");
            return;
        }

        folderRepository.updateNullNumIndex(numIndex);

        // 更新folderIndexUpdated属性
        scm.setProperty("folderIndexUpdated", true);
        scm.save();
    }

    @Override
    public void createDefaultMyFolders(User user) {
        try {
            Folder f = new Folder();
            f.setCreator(user.getUsername());
            f.setCreatorFullname(user.getFullname());
            f.setFolderType(Folder.TYPE_MYDOCUMENT);
            f.setName("我的文件");

            Folder f2 = new Folder();
            f2.setCreator(user.getUsername());
            f2.setCreatorFullname(user.getFullname());
            f2.setFolderType(Folder.TYPE_MYDOCUMENT);
            f2.setName("我的视频");

            Folder f3 = new Folder();
            f3.setCreator(user.getUsername());
            f3.setCreatorFullname(user.getFullname());
            f3.setFolderType(Folder.TYPE_MYDOCUMENT);
            f3.setName("我的图片");

            create(f);
            create(f2);
            create(f3);
        } catch (Exception e) {
            // ignore
        }
    }

    @Override
    public void createDefaultFolders() {
        if (getTopFolders().size() == 0) {
            try {
                if (ModeDefinition.sd()) {
                    Folder f = new Folder();
                    f.setCreator(UserGroupUtils.ADMIN_USERNAME);
                    f.setCreatorFullname("系统管理员");
                    f.setFolderType(Folder.TYPE_PUBLIC);
                    f.setTreeName(FolderArea.TREENAME_DOC);
                    f.setName("附件目录");

                    create(f);
                } else {
                    Folder f = new Folder();
                    f.setCreator(UserGroupUtils.ADMIN_USERNAME);
                    f.setCreatorFullname("系统管理员");
                    f.setFolderType(Folder.TYPE_PUBLIC);
                    f.setTreeName(FolderArea.TREENAME_DOC);
                    f.setName("财务部文档");

                    Folder f2 = new Folder();
                    f2.setCreator(UserGroupUtils.ADMIN_USERNAME);
                    f2.setCreatorFullname("系统管理员");
                    f2.setFolderType(Folder.TYPE_PUBLIC);
                    f2.setTreeName(FolderArea.TREENAME_DOC);
                    f2.setName("人事部文档");

                    Folder f3 = new Folder();
                    f3.setCreator(UserGroupUtils.ADMIN_USERNAME);
                    f3.setCreatorFullname("系统管理员");
                    f3.setFolderType(Folder.TYPE_PUBLIC);
                    f3.setTreeName(FolderArea.TREENAME_DOC);
                    f3.setName("行政部文档");

                    Folder f4 = new Folder();
                    f4.setCreator(UserGroupUtils.ADMIN_USERNAME);
                    f4.setCreatorFullname("系统管理员");
                    f4.setFolderType(Folder.TYPE_PUBLIC);
                    f4.setTreeName(FolderArea.TREENAME_DOC);
                    f4.setName("销售部文档");

                    Folder f5 = new Folder();
                    f5.setCreator(UserGroupUtils.ADMIN_USERNAME);
                    f5.setCreatorFullname("系统管理员");
                    f5.setFolderType(Folder.TYPE_PUBLIC);
                    f5.setTreeName(FolderArea.TREENAME_DOC);
                    f5.setName("项目文档");

                    Folder f6 = new Folder();
                    f6.setCreator(UserGroupUtils.ADMIN_USERNAME);
                    f6.setCreatorFullname("系统管理员");
                    f6.setFolderType(Folder.TYPE_PUBLIC);
                    f6.setTreeName(FolderArea.TREENAME_DOC);
                    f6.setName("附件目录");

                    create(f);
                    create(f2);
                    create(f3);
                    create(f4);
                    create(f5);
                    create(f6);
                }
            } catch (Exception e) {
                // ignore
            }
        }
    }

    @Override
    public Folder getById(long id) {
        return folderRepository.get(id);
    }

    @Override
    public List<Long> getTopFolderIds() {
        return folderRepository.getTopFolderIdsByType(Folder.TYPE_PUBLIC);
    }

    @Override
    public List<Folder> getTopFolders() {
        return folderRepository.getTopFoldersByType(Folder.TYPE_PUBLIC);
    }

    @Override
    public List<Folder> getTopMyFolders(String username) {
        return folderRepository.getTopFoldersByType(Folder.TYPE_MYDOCUMENT, username);
    }

    @Override
    public List<Folder> getFoldersByParent(Folder folder) {
        return folderRepository.getSubFoldersByFolder(folder);
    }

    @Override
    public List<Folder> getAllFolders() {
        return folderRepository.getAll();
    }

    @Override
    public void create(Folder folder) {
        FolderUtils.clearFoldersWithListPermissionAndCountCacheMap();
        folderRepository.save(folder);
        FolderPermRedisUtils.deleteAllPermissions();
    }

    @Override
    public void update(Folder folder) {
        FolderUtils.clearFoldersWithListPermissionAndCountCacheMap();
        folderRepository.update(folder);
        FolderPermRedisUtils.deleteAllPermissions();
    }

    @Override
    public void deleteAllContents(Folder folder) {
        log.debug("完整删除目录：{}", folder.getDisplayFolder());

        // 删除子目录
        List<Folder> children = folder.getChildren();
        List<Folder> daoSubFolders = folderRepository.getSubFoldersByFolder(folder);
        children.addAll(daoSubFolders);

        Folder[] subFolders = children.toArray(new Folder[]{});
        for (Folder subFolder : subFolders) {
            deleteAllContents(subFolder);
        }

        // 删除在回收站的文档
        List<Document> docs = documentRepository.getAllDocumentsByFolder(folder);
        for (Document doc : docs) {
            documentService.deleteDocument(doc);
        }

        FolderUtils.clearFoldersWithListPermissionAndCountCacheMap();

        // 删除权限
        deleteAllPermission(folder);

        // 本目录的订阅
        folderSubscribeRepository.deleteAllSubscribesByFolder(folder);

        // 邮箱集成配置信息
        folderMailService.deleteFolderMailconfigByFolderId(folder.getId());

        // 删除本目录内的文档链接
        documentLinkService.delete(folder);

        // 删除目录审计
        folderLogRepository.deleteAllLogsByFolder(folder);

        // 删除目录自动提醒
        docAutoRemindService.deleteAllByFolderId(folder.getId());

        delete(folder);

        // 删除目录规则
        folderRuleService.deleteRulesByFolder(folder.getId());
    }

    private void deleteAllPermission(Folder folder) {
        FolderUtils.clearFoldersWithListPermissionAndCountCacheMap();
        FolderPermissionUtils.deletePermissionsByFolderId(folder.getId());
        FolderPermRedisUtils.deleteAllPermissions();
    }

    @Override
    public void deleteAllMyFolders(String username) {
        FolderUtils.clearFoldersWithListPermissionAndCountCacheMap();
        Folder[] myFolders = getTopMyFolders(username).toArray(new Folder[]{});
        for (Folder f : myFolders) {
            deleteAllContents(f);
        }
    }

    @Override
    public void updateOldMyDoc() {
        List<Folder> myFolders = folderRepository.getTopFoldersByType(Folder.TYPE_MYDOCUMENT);
        for (Folder f : myFolders) {
            doUpdateOldMyDoc(f);
        }
    }

    private void doUpdateOldMyDoc(Folder f) {
        if (f.getFolderType() != Folder.TYPE_MYDOCUMENT) {
            log.debug("doUpdateOldMyDoc is not MyDocument: {}", f.getName());
            return;
        }

        List<Document> docs = documentRepository.getAllDocumentsByFolder(f);
        for (Document doc : docs) {
            try {
                ZDIOUtils.updateOldMyDocToNew(doc);
            } catch (IOException e) {
                log.debug("doUpdateOldMyDoc updateDoc error: " + doc.getDisplayFolderWithoutLink(), e);
            }
        }

        for (Folder subFolder : f.getChildren()) {
            doUpdateOldMyDoc(subFolder);
        }
    }

    private String split3Char(String num) {
        if (StringUtils.isBlank(num)) {
            return "0 字节";
        }

        StringBuffer sb = new StringBuffer(" 字节");
        while (num.length() > 3) {
            sb.insert(0, "," + num.substring(num.length() - 3));
            num = num.substring(0, num.length() - 3);
        }

        if (num.length() > 0) {
            sb.insert(0, num);
        } else {
            sb.substring(1);
        }

        return sb.toString();
    }

    @Override
    public List<Folder> getFoldersInRole() {
        return null;
    }

    @Override
    public boolean isChildren(Folder folder, Folder folder2) {
        if (folder.getChildren().size() != 0) {
            if (folder.getChildren().contains(folder2)) {
                return true;
            }

            for (Folder c : folder.getChildren()) {
                if (isChildren(c, folder2)) {
                    return true;
                }
            }
        }

        return false;
    }

    private List<Folder> getTopFolders(String isoFolderType) {
        List<Folder> topFoldersByType = folderRepository.getTopFoldersByType(Folder.TYPE_PUBLIC);
        if (StringUtils.isBlank(isoFolderType)) {
            return topFoldersByType;
        }

        if (topFoldersByType != null && topFoldersByType.size() > 0) {
            Iterator<Folder> topFolderIt = topFoldersByType.iterator();
            while (topFolderIt.hasNext()) {
                if (Folder.TYPE_ISOFOLDER.equals(isoFolderType)) {
                    topFolderIt.remove();
                } else if (Folder.TYPE_ORDINARYFOLDER.equals(isoFolderType)) {
                    topFolderIt.remove();
                }
            }
        }

        return topFoldersByType;
    }

    @Override
    public int isFolderNameInFolder(String name, Folder parent, Folder oriFolder, int folderType, String creator) {
        return folderRepository.isFolderNameInFolder(name, parent, oriFolder, folderType, creator);
    }

    @Override
    public List<Folder> searchFoldersByNameAndDescription(String name, String description) {
        return folderRepository.searchFoldersByNameAndDescription(name, description);
    }

    @Override
    public void subscribe(Folder folder, User user) {
        if (user == null || folder == null) {
            return;
        }

        if (folderSubscribeRepository.getSubscribe(folder, user) == null) {
            FolderSubscribe sub = new FolderSubscribe();
            sub.setFolderId(folder.getId());
            sub.setUsername(user.getUsername());
            folderSubscribeRepository.save(sub);
        }
    }

    @Override
    public void subscribe(String docId, User user) {
        if (user == null || StringUtils.isBlank(docId)) {
            return;
        }

        if (folderSubscribeRepository.getSubscribe(docId, user) == null) {
            FolderSubscribe sub = new FolderSubscribe();
            sub.setDocId(docId);
            sub.setUsername(user.getUsername());
            folderSubscribeRepository.save(sub);
        }
    }

    @Override
    public FolderSubscribe getSubscribe(Folder folder, User user) {
        return folderSubscribeRepository.getSubscribe(folder, user);
    }

    @Override
    public FolderSubscribe getSubscribe(String docId, User user) {
        return folderSubscribeRepository.getSubscribe(docId, user);
    }

    @Override
    public void deleteSubscribe(Folder folder, User user) {
        folderSubscribeRepository.deleteSubscribe(folder, user);
    }

    @Override
    public void deleteSubscribe(String docId, User user) {
        folderSubscribeRepository.deleteSubscribe(docId, user);
    }

    @Override
    public void deleteAllSubscribesByUser(User user) {
        folderSubscribeRepository.deleteAllSubscribesByUser(user);
    }

    @Override
    public List<FolderSubscribe> getSubscribes(Folder folder) {
        return folderSubscribeRepository.getSubscribes(folder);
    }

    @Override
    public List<FolderSubscribe> getSubscribes(String docId) {
        return folderSubscribeRepository.getSubscribes(docId);
    }

    @Override
    public String importFolder(Folder importFolder, Folder parentFolder) {
        List<String> results = new LinkedList<String>();
        if (importFolder == null) {
            return "目录不存在或目录导出文件已损坏";
        }

        if (isFolderNameInFolder(importFolder.getName(), parentFolder, null) > 0) {
            return "同名目录已存在";
        }

        createImportFolder(importFolder, parentFolder, results);

        results.add("目录: " + importFolder.getName() + " 导入完毕");
        return StringUtils.join(results, "<br/>");
    }

    private int isFolderNameInFolder(String name, Folder parent, Folder oriFolder) {
        return folderRepository.isFolderNameInFolder(name, parent, oriFolder);
    }

    private void createImportFolder(Folder importFolder, Folder parentFolder, List<String> results) {
        Folder newFolder = new Folder();
        newFolder.setName(importFolder.getName());
        newFolder.setDefaultSortField(importFolder.getDefaultSortField());
        newFolder.setDefaultSortType(importFolder.getDefaultSortType());
        newFolder.setDescription(importFolder.getDescription());
        newFolder.setDisplayCols(importFolder.getDisplayCols());
        newFolder.setFolderType(importFolder.getFolderType());
        newFolder.setNumIndex(importFolder.getNumIndex());
        newFolder.setProperties(importFolder.getProperties());
        newFolder.setTreeName(importFolder.getTreeName());
        if (parentFolder != null) {
            newFolder.setParentId(parentFolder.getId());
        }

        try {
            create(newFolder);
            results.add("目录: " + newFolder.getDisplayFolder() + " 保存成功");

        } catch (Throwable t) {
            results.add("目录: " + newFolder.getDisplayFolder() + " 保存失败，原因：" + t.getMessage());
        }

        if (importFolder.getChildren() != null && importFolder.getChildren().size() > 0) {
            for (Folder c : importFolder.getChildren()) {
                createImportFolder(c, newFolder, results);
            }
        }
    }

    @Override
    public void doCreateDefaultAttachFolder() {
        List<RecFolder> recFolders = recFolderService.getAllFolders();
        List<Folder> folders = getFoldersByName("附件目录");
        Folder pFolder = null;
        if (folders != null && folders.size() > 0) {
            for (int i = 0; i < folders.size(); i++) {
                Folder folder = folders.get(i);
                Folder parentFolder = folder.getParent();
                if (parentFolder == null) {
                    pFolder = folder;
                }
            }
        } else {
            log.debug("doCreateDefaultAttachFolder 默认附件目录未创建");
            return;
        }
        log.debug("doCreateDefaultAttachFolder attchFolderId：{}", pFolder.getId());
        for (int i = 0; i < recFolders.size(); i++) {
            RecFolder recFolder = recFolders.get(i);
            if (recFolder != null && (!recFolder.isDataFolder())) {
                log.debug("doCreateDefaultAttachFolder 非数据目录,recFolderId:{},recFolderName:{}", recFolder.getId(), recFolder.getName(), recFolder.getName());
                continue;
            }
            String recFolderName = recFolder.getName();
            String attachmentFolderName = recFolder.getAttachmentFolderName();
            if (StringUtils.isNotBlank(attachmentFolderName)) {
                log.debug("doCreateDefaultAttachFolder 该数据目录已存在附件目录,recFolderName:{},attachmentFolderName:{}", recFolderName, attachmentFolderName);
                continue;
            }

            if (isFolderNameInFolder(recFolderName, pFolder, null, Folder.TYPE_PUBLIC, null) > 0) {
                log.debug("doCreateDefaultAttachFolder 目录已存在,foldername:{}", recFolderName);
                continue;
            }

            Folder f = new Folder();
            f.setCreator(UserGroupUtils.ADMIN_USERNAME);
            f.setCreatorFullname("系统管理员");
            f.setFolderType(Folder.TYPE_PUBLIC);
            f.setName(recFolderName);
            f.setDocProperty(null);
            f.setPrintLimit(-1L);
            f.setTreeName(null);
            f.setParentId(pFolder.getId());
            create(f);

            recFolder.setAttachmentFolderId(f.getId());
            recFolder.setAttachmentFolderName(f.getName());
            recFolderService.update(recFolder);
        }
    }
}
