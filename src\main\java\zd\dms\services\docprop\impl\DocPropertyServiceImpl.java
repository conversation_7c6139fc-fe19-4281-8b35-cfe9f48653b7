//                    佛祖保佑       永无BUG
//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                           O\  =  /O
//                        ____/`---'\____
//                      .'  \\|     |//  `.
//                     /  \\|||  :  |||//  \
//                    /  _||||| -:- |||||-  \
//                    |   | \\\  -  /// |   |
//                    | \_|  ''\---/''  |   |
//                    \  .-\__  `-`  ___/-. /
//                  ___`. .'  /--.--\  `. . __
//               ."" '<  `.___\_<|>_/___.'  >'"".
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |
//              \  \ `-.   \_ __\ /__ _/   .-` /  /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
package zd.dms.services.docprop.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import zd.dms.entities.DocPropDataCol;
import zd.dms.entities.Document;
import zd.dms.entities.Folder;
import zd.dms.repositories.document.DocumentRepository;
import zd.dms.services.docprop.DocPropDataColService;
import zd.dms.services.docprop.DocPropertyService;
import zd.dms.services.propdata.DocAdvPropsDBUtils;

import java.math.BigDecimal;
import java.util.*;

@RequiredArgsConstructor
@Service
@Slf4j
public class DocPropertyServiceImpl implements DocPropertyService {

    private final DocumentRepository documentRepository;

    private final DocPropDataColService docPropDataColService;

    @Override
    public List<Document> getDocumentByDocProp(String sql) {
        return documentRepository.getDocumentByDocProp(sql);
    }

    @Override
    public List<Map<String, Object>> getDocProp(Document document) {
        Folder folder = document.getFolder();
        List<Map<String, Object>> docProps = null;
        if (folder == null) {
            log.debug("folder is null");
            return null;
        }

        String docPropty = folder.getDocProperty();
        log.debug("docPropty:{},folderId：{}", docPropty, folder.getId());
        if (StringUtils.isBlank(docPropty)) {
            log.debug("docPropty is null");
            return null;
        }
        String[] arrayDocProp = docPropty.split("\r\n");

        Map<String, Object> docPorpMap = DocAdvPropsDBUtils.getDocPorpMapByDocId(document.getId());
        docProps = new ArrayList<Map<String, Object>>();
        for (String propName : arrayDocProp) {
            DocPropDataCol dataCol = docPropDataColService.getDocPropDataColByName(propName);
            if (dataCol == null) {
                log.debug("属性[" + propName + "]不存在");
                continue;
            }

            Object colValue = MapUtils.getObject(docPorpMap, propName);
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("docPropDataCol", dataCol);
            Date date = null;
            if (colValue != null) {
                if ("date".equals(dataCol.getColType()) || "datetime".equals(dataCol.getColType())) {
                    date = (Date) colValue;
                    map.put("dataValue", date);
                } else if ("number".equals(dataCol.getColType())) {
                    BigDecimal bigDecimal = (BigDecimal) colValue;
                    String dataValue = bigDecimal.toString();
                    map.put("dataValue", dataValue);
                } else if ("int".equals(dataCol.getColType())) {
                    int intColvalue = (int) colValue;
                    String dataValue = Integer.toString(intColvalue);
                    map.put("dataValue", dataValue);
                } else if ("text".equals(dataCol.getColType()) || "textarea".equals(dataCol.getColType())) {
                    String dataValue = colValue.toString();
                    map.put("dataValue", dataValue);
                }
            } else {
                map.put("dataValue", null);
            }
            docProps.add(map);
        }

        return docProps;
    }
}
