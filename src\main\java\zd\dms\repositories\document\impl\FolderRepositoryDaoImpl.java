package zd.dms.repositories.document.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.JdbcTemplate;
import zd.base.repositories.impl.BaseRepositoryDaoImpl;
import zd.dms.entities.Folder;
import zd.dms.entities.FolderArea;
import zd.dms.repositories.document.FolderRepository;
import zd.dms.utils.PageableUtils;
import zd.dms.utils.repositories.QueryParamsUtils;
import zd.dms.utils.repositories.SpecTools;
import zd.record.utils.ECMUtils;
import zd.record.utils.RecordDBUtils;
import zd.record.utils.SearchUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class FolderRepositoryDaoImpl extends BaseRepositoryDaoImpl<Folder, Long> implements FolderRepository {

    public FolderRepositoryDaoImpl(Class<Folder> domainClass, EntityManager entityManager) {
        super(domainClass, entityManager);
    }

    @Override
    public List<Map<String, Object>> getChildCount(List<Long> parentIds) {
        if (CollectionUtils.isEmpty(parentIds)) {
            return new ArrayList<>();
        }

        JdbcTemplate jt = RecordDBUtils.checkJdbcTemplate();
        String sql = String.format("select count(*) count,parentId from folder where parentId in (%s) group by parentId", StringUtils.join(parentIds, ","));

        return jt.queryForList(sql);
    }

    public List<Folder> getTopFolderListByFolderAreaId(FolderArea folderArea) {
        if (folderArea == null) {
            return new ArrayList<>();
        }

        Specification<Folder> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<Folder> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            specTools.isNull("parentId");
            String treeName = folderArea.getTreeName();
            if (StringUtils.isBlank(treeName) || "pubDocTree".equals(treeName)) {
                specTools.eqWithNull("treeName", "pubDocTree");
            } else {
                specTools.eq("treeName", folderArea.getTreeName());
            }

            specTools.asc("numIndex");
            specTools.asc("creationDate");

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<Folder> getChildren(long parentId) {
        if (parentId <= 0) {
            return new ArrayList<>();
        }

        Specification<Folder> spec = (root, criteriaQuery, criteriaBuilder) -> {
            /*Join recFolder = root.join("parent", JoinType.LEFT);
            Predicate parentIdEq = criteriaBuilder.equal(recFolder.get("id"), parentId);*/

            Predicate parentIdEq = criteriaBuilder.equal(root.get("parentId"), parentId);

            Order numIndexAsc = criteriaBuilder.asc(root.get("numIndex"));
            Order creationDateAsc = criteriaBuilder.asc(root.get("creationDate"));

            return criteriaQuery.where(parentIdEq).orderBy(numIndexAsc, creationDateAsc).getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public List<Long> getFolderIdsByLimit(int limit) {
        Query query = this.entityManager.createNativeQuery("select f.id from Folder f order by creationDate asc", Long.class);

        query.setFirstResult(0);
        query.setMaxResults(limit);

        return query.getResultList();
    }

    @Override
    public List<Long> getSubFolderIds(List<Long> parentIds) {
        if (parentIds == null || parentIds.size() <= 0) {
            return new ArrayList<Long>();
        }

        log.debug("getSubFolderIds size: {}", parentIds.size());
        StringBuilder hqlSb = new StringBuilder();
        hqlSb.append("select f.id from Folder f where ");

        if (RecordDBUtils.isSQLServer()) {
            String inIdsSql = " f.parentId in (" + StringUtils.join(parentIds, ",") + ")";
            hqlSb.append(inIdsSql);
        } else {
            hqlSb.append(SearchUtils.splitColInSql("f.parentId", parentIds));
        }

        hqlSb.append(" and (inheritPermission is null or inheritPermission != 0)");

        TypedQuery<Long> query = this.entityManager.createQuery(hqlSb.toString(), Long.class);

        return query.getResultList();
    }

    @Override
    public List<Folder> getFoldersByNameAndParent(Folder parentFolder, String name) {
        if (StringUtils.isBlank(name) || parentFolder == null) {
            return new ArrayList<Folder>();
        }

        Specification<Folder> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("parentId"), parentFolder.getId()));
            predicates.add(criteriaBuilder.equal(root.get("name"), name));

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates);
        };

        return findAll(spec);
    }

    @Override
    public List<Folder> findFolders(String keywords) {
        Map<String, Object> params = new HashMap<>();

        String keywordsLike = QueryParamsUtils.getLikeHqlParamsName("keywords");
        String hql = "from Folder f where (name like " + keywordsLike + ") ";
        params.put("keywords", keywords);
        return findAll(hql, params);
    }

    @Override
    public List<Folder> getPubTopFoldersByTreeName(String treeName) {
        Specification<Folder> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.isNull(root.get("parentId")));
            if (StringUtils.isNotBlank(treeName) && !FolderArea.TREENAME_DOC.equals(treeName)) {
                predicates.add(criteriaBuilder.equal(root.get("treeName"), treeName));
            } else {
                predicates.add(criteriaBuilder.isNull(root.get("treeName")));
            }
            predicates.add(criteriaBuilder.equal(root.get("folderType"), Folder.TYPE_PUBLIC));

            Order numIndexAsc = criteriaBuilder.asc(root.get("numIndex"));
            Order idAsc = criteriaBuilder.asc(root.get("id"));

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, numIndexAsc, idAsc);
        };

        return findAll(spec);
    }

    @Override
    public Folder getChildFolderByName(Folder parentFolder, String childFolderName) {
        Specification<Folder> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(criteriaBuilder.equal(root.get("name"), childFolderName));
            predicates.add(criteriaBuilder.equal(root.get("parentId"), parentFolder.getId()));

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates);
        };

        List<Folder> folders = findAll(spec);
        if (!folders.isEmpty()) {
            return folders.get(0);
        } else {
            return null;
        }
    }

    @Override
    public Folder getTrashFolder() {
        Specification<Folder> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(criteriaBuilder.equal(root.get("name"), Folder.TRASHFOLDERNAME));
            predicates.add(criteriaBuilder.isNull(root.get("parentId")));

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates);
        };

        List<Folder> folders = findAll(spec);
        if (!folders.isEmpty()) {
            return folders.get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<Folder> findFoldersByName(String[] folderNameKeyWordArray) {
        Specification<Folder> spec = (root, criteriaQuery, criteriaBuilder) -> {
            SpecTools<Folder> specTools = new SpecTools<>(root, criteriaQuery, criteriaBuilder);

            if (folderNameKeyWordArray != null && folderNameKeyWordArray.length > 0) {
                List<Predicate> predicates = new ArrayList<>();
                for (int i = 0; i < folderNameKeyWordArray.length; i++) {
                    predicates.add(criteriaBuilder.like(root.get("name"), "%" + folderNameKeyWordArray[i] + "%"));
                }

                specTools.or(predicates);
            }

            return specTools.getRestriction();
        };

        return findAll(spec);
    }

    @Override
    public void updateNullNumIndex(int numIndex) {
        String hql = "update Folder f set f.numIndex=" + numIndex + " where f.numIndex is null";
        executeUpdate(hql);
    }

    @Override
    public List<Long> getTopFolderIdsByType(int type) {
        String sql = "select f.id from Folder f where f.parentId is null and folderType=" + type +
                " order by numIndex asc,id asc";

        TypedQuery<Long> query = this.entityManager.createQuery(sql, Long.class);
        return query.getResultList();
    }

    @Override
    public List<Folder> getTopFoldersByType(int type) {
        Specification<Folder> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.isNull(root.get("parentId")));
            predicates.add(criteriaBuilder.equal(root.get("folderType"), type));

            Order numIndexOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "numIndex", true);
            Order idOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "id", true);

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, numIndexOrder, idOrder);
        };

        return findAll(spec);
    }

    @Override
    public List<Folder> getTopFoldersByType(int type, String username) {
        Specification<Folder> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.isNull(root.get("parentId")));
            predicates.add(criteriaBuilder.equal(root.get("folderType"), type));
            predicates.add(criteriaBuilder.equal(root.get("creator"), username));

            Order numIndexOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "numIndex", true);
            Order idOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "id", true);

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, numIndexOrder, idOrder);
        };

        return findAll(spec);
    }

    @Override
    public List<Folder> getSubFoldersByFolder(Folder folder) {
        Specification<Folder> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("parentId"), folder.getId()));

            Order numIndexOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "numIndex", true);
            Order idOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "id", true);

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, numIndexOrder, idOrder);
        };

        return findAll(spec);
    }

    @Override
    public int getPublicFolderCount() {
        return NumberUtils.toInt(String
                .valueOf(findObject("select count(*) from Folder as f where f.folderType = 1")));
    }

    @Override
    public int isFolderNameInFolder(String name, Folder parent, Folder oriFolder, int folderType, String creator) {
        if (folderType == Folder.TYPE_MYDOCUMENT) {
            return isFolderNameInFolderMyDoc(name, parent, oriFolder, creator);
        }

        if (oriFolder != null) {
            if (parent != null) {
                return NumberUtils
                        .toInt(String
                                .valueOf(findObject(
                                        "select count(*) from Folder as f where f.name = ?1 and f.parentId = ?2 and f <> ?3 and f.folderType = ?4",
                                        new Object[]{name, parent.getId(), oriFolder, oriFolder.getFolderType()})));
            } else {
                return NumberUtils
                        .toInt(String
                                .valueOf(findObject(
                                        "select count(*) from Folder as f where f.name = ?1 and f.parentId is null and f <> ?2 and f.folderType = ?3",
                                        new Object[]{name, oriFolder, oriFolder.getFolderType()})));
            }
        }

        // 创建新目录
        if (parent != null) {
            return NumberUtils.toInt(String.valueOf(findObject(
                    "select count(*) from Folder as f where f.name = ?1 and f.parentId = ?2 and f.folderType = ?3",
                    new Object[]{name, parent.getId(), folderType})));
        } else {
            return NumberUtils.toInt(String.valueOf(findObject(
                    "select count(*) from Folder as f where f.name = ?1 and f.parentId is null and f.folderType = ?2",
                    new Object[]{name, folderType})));
        }
    }

    private int isFolderNameInFolderMyDoc(String name, Folder parent, Folder oriFolder, String creator) {

        if (oriFolder != null) {
            if (parent != null) {
                return NumberUtils
                        .toInt(String
                                .valueOf(findObject(
                                        "select count(*) from Folder as f where f.name = ? and f.parentId = ? and f <> ? and f.folderType = ? and f.creator = ?",
                                        new Object[]{name, parent.getId(), oriFolder, oriFolder.getFolderType(), creator})));
            } else {
                return NumberUtils
                        .toInt(String
                                .valueOf(findObject(
                                        "select count(*) from Folder as f where f.name = ? and f.parentId is null and f <> ? and f.folderType = ? and f.creator = ?",
                                        new Object[]{name, oriFolder, oriFolder.getFolderType(), creator})));
            }
        }

        // 创建新目录
        if (parent != null) {
            return NumberUtils
                    .toInt(String
                            .valueOf(findObject(
                                    "select count(*) from Folder as f where f.name = ? and f.parentId = ? and f.folderType = ? and f.creator = ?",
                                    new Object[]{name, parent.getId(), 2, creator})));
        } else {
            return NumberUtils
                    .toInt(String
                            .valueOf(findObject(
                                    "select count(*) from Folder as f where f.name = ? and f.parentId is null and f.folderType = ? and f.creator = ?",
                                    new Object[]{name, 2, creator})));
        }
    }

    @Override
    public List<Folder> searchFoldersByNameAndDescription(String name, String description) {
        if (!ECMUtils.filterSearchSql(name) || !ECMUtils.filterSearchSql(description)) {
            return new ArrayList<Folder>(0);
        }

        String hql = "";

        if (StringUtils.isNotBlank(name) && StringUtils.isNotBlank(description)) {
            hql = "from Folder f where name like '%" + name + "%' and description like '%" + description + "%'";
        } else if (StringUtils.isNotBlank(name)) {
            hql = "from Folder f where name like '%" + name + "%'";
        } else if (StringUtils.isNotBlank(description)) {
            hql = "from Folder f where description like '%" + description + "%'";
        }
        return findAll(hql);
    }

    @Override
    public List<Folder> getFoldersByName(String folderName) {
        Specification<Folder> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("name"), folderName));

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates);
        };

        return findAll(spec);
    }

    @Override
    public List<Folder> getAllMyFolders(String username) {
        Specification<Folder> spec = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("folderType"), Folder.TYPE_MYDOCUMENT));
            predicates.add(criteriaBuilder.equal(root.get("creator"), username));

            Order numIndexOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "numIndex", true);
            Order idOrder = QueryParamsUtils.getSpecOrder(root, criteriaBuilder, "id", true);

            return QueryParamsUtils.getRestrictionByCriteriaQuery(criteriaQuery, predicates, numIndexOrder, idOrder);
        };

        return findAll(spec);
    }

    @Override
    public List<Folder> getFoldersByIndex(int startIndex, int pageSize) {
        pageSize = PageableUtils.getPageSize(pageSize);
        int pageNumber = PageableUtils.getPageNumberByStartIndex(startIndex, pageSize);

        Page<Folder> page = getPage(pageNumber, pageSize);

        return page.getContent();
    }

    @Override
    public long getSumFileSize(List<Long> folderIds) {
        String sql = "select sum(fileSize) from Document where deleted = false";
        String sqlWhere = "";
        if (!folderIds.isEmpty()) {
            sqlWhere = " and (";
            sqlWhere += "folder.id in (" + StringUtils.join(folderIds, ",") + ")";
            sqlWhere += ")";
        }

        TypedQuery<Long> query = this.entityManager.createQuery(sql + sqlWhere, Long.class);
        List<Long> result = query.getResultList();
        if (!result.isEmpty() && result.get(0) != null) {
            return result.get(0);
        }

        return 0;
    }

    @Override
    public int isFolderNameInFolder(String name, Folder parent, Folder oriFolder) {
        if (oriFolder != null) {
            if (parent != null) {
                return NumberUtils.toInt(String.valueOf(findObject(
                        "select count(*) from Folder as f where f.name = ?1 and f.parentId = ?2 and f <> ?3", new Object[]{
                                name, parent.getId(), oriFolder})));
            } else {
                return NumberUtils.toInt(String.valueOf(findObject(
                        "select count(*) from Folder as f where f.name = ?1 and f.parentId is null and f <> ?2",
                        new Object[]{name, oriFolder})));
            }
        }

        // 创建新目录
        if (parent != null) {
            return NumberUtils.toInt(String
                    .valueOf(findObject("select count(*) from Folder as f where f.name = ?1 and f.parentId = ?2",
                            new Object[]{name, parent.getId()})));
        } else {
            return NumberUtils.toInt(String.valueOf(findObject(
                    "select count(*) from Folder as f where f.name = ?1 and f.parentId is null", new Object[]{name})));
        }
    }
}
