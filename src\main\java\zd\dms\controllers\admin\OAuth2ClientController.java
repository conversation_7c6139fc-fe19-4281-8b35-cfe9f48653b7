package zd.dms.controllers.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import zd.base.annotation.ZDLog;
import zd.base.controllers.ControllerSupport;
import zd.base.utils.JSONResultUtils;
import zd.base.utils.ZDMapUtils;
import zd.base.utils.model.ObjectMapperUtils;
import zd.dms.dto.PageResponse;
import zd.dms.entities.oauth2.OAuth2Client;
import zd.dms.services.oauth2.OAuth2ClientService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-05-06
 */
@RequiredArgsConstructor
@RestController
@Tag(name = "OAuth2ClientController", description = "OAuth Client Controller")
@RequestMapping("/admin/oauth2/client")
public class OAuth2ClientController extends ControllerSupport {

    private final OAuth2ClientService oauth2ClientService;

    @Operation(summary = "保存OAuth2 Client")
    @PostMapping("/save")
    @ZDLog("获取OAuth2 Client")
    public JSONResultUtils<Object> save(@RequestBody Map<String, Object> params) {
        return oauth2ClientService.save(params);
    }

    @Operation(summary = "更新OAuth2 Client")
    @PostMapping("/update")
    @ZDLog("更新OAuth2 Client")
    public JSONResultUtils<Object> update(@RequestBody Map<String, Object> params) {
        return oauth2ClientService.update(params);
    }

    @Operation(summary = "获取OAuth2 Client")
    @GetMapping("/get/{id}")
    @ZDLog("获取OAuth2 Client")
    public JSONResultUtils<Object> getById(@PathVariable String id) {
        OAuth2Client oauth2Client = oauth2ClientService.findById(id);

        return successData(ObjectMapperUtils.toMap(oauth2Client, "info"));
    }

    @Operation(summary = "获取OAuth2 Client列表")
    @PostMapping("/list")
    @ZDLog("获取OAuth2 Client列表")
    public JSONResultUtils<Object> list(@RequestBody Map<String, Object> params) {
        Page page = oauth2ClientService.list(params);
        return successData(PageResponse.of(page, "list"));
    }

    @Operation(summary = "删除OAuth2 Client")
    @GetMapping("/delete/{id}")
    @ZDLog("删除OAuth2 Client")
    public JSONResultUtils<Object> delete(@PathVariable String id) {
        oauth2ClientService.deleteById(id);

        return JSONResultUtils.successWithMsg("删除成功");
    }

    @Operation(summary = "删除流程定义")
    @PostMapping("/batchDelete")
    @ZDLog("删除流程定义")
    public JSONResultUtils<Object> batchDelete(@RequestBody Map<String, Object> params) {
        List<String> ids = ZDMapUtils.getListStringValue(params, "ids");
        ids.forEach(oauth2ClientService::deleteById);

        return success();
    }
}
