package zd.dms.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;
import zd.base.entities.AbstractSequenceEntity;
import zd.base.utils.system.SpringUtils;
import zd.dms.services.document.DocumentService;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "document_relation")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class DocumentRelation extends AbstractSequenceEntity {

    /**
     * serial
     */
    private static final long serialVersionUID = 7559575623901464298L;

    private String creatorFullname;

    @Index(name = "i_drelation_docId")
    private String docId;

    @Index(name = "i_drelation_rdocId")
    private String relatedDocId;

    /**
     * 创建时间
     */
    @Index(name = "i_drelation_cd")
    @Column(nullable = false)
    private Date creationDate;

    /**
     * 默认构造器
     */
    public DocumentRelation() {
        super();
        creationDate = new Date();
    }

    public Document getDocument() {
        return SpringUtils.getBean(DocumentService.class).getDocumentById(docId);
    }

    public Document getDocumentRelated() {
        return SpringUtils.getBean(DocumentService.class).getDocumentById(relatedDocId);
    }

    public String getCreatorFullname() {
        return creatorFullname;
    }

    public void setCreatorFullname(String creatorFullname) {
        this.creatorFullname = creatorFullname;
    }

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getRelatedDocId() {
        return relatedDocId;
    }

    public void setRelatedDocId(String relatedDocId) {
        this.relatedDocId = relatedDocId;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
